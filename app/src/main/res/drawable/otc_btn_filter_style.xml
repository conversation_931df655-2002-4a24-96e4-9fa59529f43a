<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: otc_btn_filter_style.xml
  ~   @Date: 19-1-15 下午4:42
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true" android:drawable="@drawable/btn_corner_blue" />
    <item android:state_focused="true" android:drawable="@drawable/btn_corner_blue" />
    <item android:state_pressed="true" android:drawable="@drawable/btn_corner_blue" />
    <item android:state_checked="true" android:drawable="@drawable/btn_corner_blue" />
    <item android:drawable="@drawable/btn_bg_wathet" />
</selector>