<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: book_item_press_style.xml
  ~   @Date: 19-4-18 下午5:06
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:drawable="@drawable/red_select" android:state_selected="true"/>
    <item android:drawable="@drawable/red_select" android:state_focused="true"/>
    <item android:drawable="@drawable/red_select" android:state_pressed="true"/>
    <item android:drawable="@drawable/red_select" android:state_checked="true"/>
    <item android:drawable="@drawable/trans_select"/>
</selector>