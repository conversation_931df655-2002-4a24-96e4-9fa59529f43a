<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <layer-list>
            <!-- 相当于padding -->
            <item>
                <shape>
                    <solid android:color="@color/dark5" />
                    <corners android:radius="2dip" />
                </shape>
            </item>
        </layer-list>
    </item>
    <item>
        <layer-list>
            <!-- SHADOW LAYER -->
            <item>
                <shape>
                    <solid android:color="@color/dark5" />
                    <corners android:radius="@dimen/dip_2" />
                </shape>
            </item>
            <!-- CONTENT LAYER -->
            <!-- 相当于padding -->
            <item android:bottom="@dimen/dip_9" android:left="@dimen/dip_9" android:right="@dimen/dip_9" android:top="@dimen/dip_10">
                <shape>
                    <solid android:color="@color/dark10" />
                    <corners android:radius="@dimen/dip_2" />
                </shape>
            </item>
            <item android:bottom="@dimen/dip_10" android:left="@dimen/dip_10" android:right="@dimen/dip_10" android:top="@dimen/dip_10">
                <shape>
                    <solid android:color="@color/white" />
                    <corners android:radius="@dimen/dip_2" />
                </shape>
            </item>
        </layer-list>
    </item>
</selector>