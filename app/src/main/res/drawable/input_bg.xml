<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:drawable="@drawable/input_bg_blue" android:state_focused="true">
        <!--<layer-list>-->
            <!--&lt;!&ndash; 相当于padding &ndash;&gt;-->
            <!--<item android:left="1dp" android:top="1dp">-->
                <!--<shape>-->
                    <!--<solid android:color="@color/dark5" />-->
                    <!--<corners android:radius="2dip" />-->
                <!--</shape>-->
            <!--</item>-->
        <!--</layer-list>-->
    </item>
    <!--<item android:drawable="@drawable/input_bg_blue" android:state_selected="true"/>-->
    <item >
        <layer-list>
            <!-- SHADOW LAYER -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/dark10" />
                    <corners android:radius="2dip" />
                    <gradient android:startColor="@color/dark50" android:endColor="@color/dark80" android:angle="-90" android:gradientRadius="@dimen/dip_10"/>
                </shape>
            </item>
            <!-- CONTENT LAYER -->
            <!-- 相当于padding -->
            <item android:bottom="@dimen/dip_2" android:top="@dimen/dip_1" android:left="@dimen/dip_1" android:right="@dimen/dip_1">
                <shape android:shape="rectangle">

                    <solid android:color="@color/white" />
                    <corners android:radius="2dip" />
                </shape>
            </item>
        </layer-list>

    </item>
</selector>