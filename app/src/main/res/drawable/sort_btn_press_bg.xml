<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <layer-list>
            <!-- 相当于padding -->
            <item android:left="1dp" android:top="1dp">
                <shape>
                    <solid android:color="@color/dark5" />
                    <corners android:radius="2dip"/>
                </shape>
            </item>
        </layer-list>
    </item>
    <item>
        <layer-list>
            <!-- SHADOW LAYER -->
            <item>
                <shape>
                    <solid android:color="@color/dark10" />
                    <corners android:radius="2dip"/>
                </shape>
            </item>
            <!-- CONTENT LAYER -->
            <!-- 相当于padding -->
            <item android:bottom="1dp" android:right="1dp" android:top="@dimen/dip_1" android:left="@dimen/dip_1">
                <shape>
                    <solid android:color="@color/blue" />
                    <corners android:radius="2dip"/>
                </shape>
            </item>
        </layer-list>
    </item>
</selector>