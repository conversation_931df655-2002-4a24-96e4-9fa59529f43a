<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout
        android:id="@+id/header_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintStart_toEndOf="parent"
        android:orientation="horizontal">
        <TextView
            android:layout_width="@dimen/dip_0"
            android:layout_height="@dimen/dip_16"
            android:layout_weight="1"
            android:layout_marginTop="@dimen/dip_8"
            android:layout_marginRight="@dimen/dip_8"
            android:layout_marginLeft="@dimen/dip_24"
            android:layout_marginBottom="@dimen/dip_8"
            android:textSize="@dimen/sp_12"
            android:textColor="@color/third_text_color"
            android:text="@string/string_margin_token">

        </TextView>
        <TextView
            android:layout_width="@dimen/dip_0"
            android:layout_height="@dimen/dip_16"
            android:layout_weight="1"
            android:layout_marginTop="@dimen/dip_8"
            android:layout_marginBottom="@dimen/dip_8"
            android:layout_marginRight="@dimen/dip_8"
            android:text="@string/string_margin_remain_to_repay1"
            android:textColor="@color/third_text_color"
            android:textSize="@dimen/sp_12">

        </TextView>
        <TextView
            android:layout_width="@dimen/dip_0"
            android:layout_height="@dimen/dip_16"
            android:layout_weight="1"
            android:layout_marginTop="@dimen/dip_8"
            android:layout_marginBottom="@dimen/dip_8"
            android:layout_marginRight="@dimen/dip_16"
            android:text="@string/string_margin_remain_interest"
            android:textColor="@color/third_text_color"
            android:textSize="@dimen/sp_12"
            tools:text="已借"
            android:gravity="right">

        </TextView>

    </LinearLayout>
    <View
        android:id="@+id/divider_line_recycler_header"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header_ll"
        android:layout_marginLeft="@dimen/dip_24"
        android:background="@color/divider_line_color">

    </View>
</androidx.constraintlayout.widget.ConstraintLayout>