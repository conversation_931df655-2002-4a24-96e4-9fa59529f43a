<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="400dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dip_24"
        android:layout_marginLeft="@dimen/dip_8"
        android:visibility="gone"
        />
    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/app_name"
        android:textAppearance="@style/H4_Grey"
        android:layout_toRightOf="@id/icon"
        android:layout_marginTop="@dimen/dip_22"
        android:layout_marginLeft="@dimen/dip_2"
        android:visibility="gone"
        />
    
    <TextView
        android:id="@+id/domain"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="@style/Caption_Grey"
        android:layout_below="@id/icon"
        android:layout_marginLeft="@dimen/dip_12"
        android:layout_marginBottom="@dimen/dip_60"
        android:visibility="gone"
        />

    <!--<io.bhex.chart.KChartView
        android:id="@+id/chartView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        />-->
    <com.bhex.kline.KLineChartView
        android:id="@+id/chartView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingBottom="5dp"
        app:kc_select_text_color="@color/color_white"
        app:kc_point_width="7dp"
        app:kc_candle_width="6dp"
        app:kc_text_color="@color/current_price_color"
        app:kc_max_text_color="@color/white"
        app:kc_candle_up_color="@color/chart_green"
        app:kc_candle_down_color="@color/chart_red"
        app:kc_line_width="1dp"
        />

</RelativeLayout>