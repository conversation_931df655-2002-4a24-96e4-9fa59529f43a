<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    >

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dip_10"
        android:orientation="horizontal"
        android:layout_marginLeft="@dimen/app_margin_left"
        >
        <TextView
            android:id="@+id/option_coinPairName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:layout_marginRight="@dimen/dip_6"
            android:layout_alignParentBottom="true"
            android:textSize="@dimen/font_24"
            android:visibility="gone"
            android:textColor="@color/font_color1"
            android:text="@string/string_placeholder" />
        <TextView
            android:id="@+id/option_latestPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@id/option_coinPairName"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:ellipsize="end"
            android:textAppearance="@style/H4_Green"
            android:text="@string/string_placeholder" />


        <TextView
            android:id="@+id/option_latestPrice2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/option_latestPrice"
            android:layout_marginLeft="@dimen/dip_10"
            android:maxLines="1"
            android:ellipsize="end"
            android:textAppearance="@style/Body_Green_night"
            android:text="￥ --" />

        <TextView
            android:id="@+id/option_lever"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/option_latestPrice2"
            android:layout_marginLeft="@dimen/dip_24"
            android:maxLines="1"
            android:ellipsize="end"
            android:textAppearance="@style/Body_Dark_night"
            android:text="x" />

        <TextView
            android:id="@+id/option_riseFallRatio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="@dimen/dip_64"
            android:minHeight="@dimen/dip_24"
            android:paddingLeft="@dimen/dip_8"
            android:paddingRight="@dimen/dip_8"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:layout_marginRight="@dimen/app_margin_right"
            android:background="@drawable/bg_corner_green"
            android:gravity="center"
            android:maxLines="1"
            android:textAppearance="@style/Body_White_Default"
            android:text="@string/string_placeholder" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:orientation="horizontal"
        android:layout_marginLeft="@dimen/app_margin_left"
        >
        <TextView
            android:id="@+id/option_volume_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:textAppearance="@style/Body_Dark_night"
            android:text="@string/string_option_24_volume" />
        <TextView
            android:id="@+id/option_volume"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dip_10"
            android:layout_toRightOf="@id/option_volume_txt"
            android:maxLines="1"
            android:ellipsize="end"
            android:textAppearance="@style/Body_Dark_night"
            android:text="@string/string_placeholder" />

        <TextView
            android:id="@+id/option_delivery_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:layout_marginRight="@dimen/app_margin_right"
            android:gravity="center"
            android:maxLines="1"
            android:textAppearance="@style/Body_Dark_night"
            android:text="@string/string_placeholder" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:layout_marginTop="@dimen/dip_10"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:background="@color/divider_line_color_night"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_10"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginRight="@dimen/app_margin"
        android:orientation="horizontal"
        >
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_weight="1.2"
            >

            <TextView
                android:id="@+id/option_exercise_point_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:textAppearance="@style/BodyS_Grey_night"
                android:text="@string/string_exercise_point" />

            <TextView
                android:id="@+id/option_exercise_point"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_3"
                android:textAppearance="@style/Body_Dark_Bold_night"
                android:text="@string/string_placeholder" />


        </LinearLayout>
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_weight="1.3"
            >

            <TextView
                android:id="@+id/option_exercise_price_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/BodyS_Grey_night"
                android:singleLine="true"
                android:text="@string/string_exercise_price" />

            <TextView
                android:id="@+id/option_exercise_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_3"
                android:textAppearance="@style/Body_Dark_Bold_night"
                android:text="@string/string_placeholder" />


        </LinearLayout>
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_weight="1"
            >

            <TextView
                android:id="@+id/option_maxPayOff_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:textAppearance="@style/BodyS_Grey_night"
                android:text="@string/string_option_maxPayOff" />

            <TextView
                android:id="@+id/option_maxPayOff"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:layout_marginTop="@dimen/dip_3"
                android:textAppearance="@style/Body_Dark_Bold_night"
                android:text="@string/string_placeholder" />


        </LinearLayout>

    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_10"
        android:visibility="gone"
        android:orientation="horizontal"
        android:layout_marginLeft="@dimen/app_margin_left"
        >
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_weight="1"
            >

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/Caption_Grey"
                android:text="@string/string_high" />

            <TextView
                android:id="@+id/option_highPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_3"
                android:textAppearance="@style/Body_Dark_Bold"
                android:text="@string/string_placeholder" />


        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_weight="1"
            >
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:ellipsize="end"
                android:textAppearance="@style/Caption_Grey"
                android:text="@string/string_low" />
            <TextView
                android:id="@+id/option_lowPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_3"
                android:maxLines="1"
                android:ellipsize="end"
                android:textAppearance="@style/Body_Dark_Bold"
                android:text="@string/string_placeholder" />

        </LinearLayout>

    </LinearLayout>
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_8"
        android:layout_marginTop="@dimen/dip_8"
        android:background="@color/divider_line_color_night"
        />

</LinearLayout>