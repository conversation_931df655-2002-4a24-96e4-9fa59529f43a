<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <include layout="@layout/include_asset_header_layout" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_45"
        android:layout_marginTop="@dimen/dip_10"
        >

        <CheckBox
            android:id="@+id/btn_invisible_other_entrust"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:button="@drawable/order_show_style"
            android:checked="false"
            android:gravity="center_vertical"
            android:text="@string/string_invisible_zero"
            android:textColor="@color/font_color3"
            android:textSize="@dimen/font_15"
            android:visibility="visible" />

        <ImageButton
            android:id="@+id/input_clear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/app_margin"
            android:layout_toLeftOf="@id/btn_invisible_other_entrust"
            android:background="@drawable/btn_clear"
            android:gravity="center"
            android:visibility="gone" />

        <EditText
            android:id="@+id/search_edit"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_toLeftOf="@id/input_clear"
            android:background="@null"
            android:drawableLeft="@mipmap/icon_search"
            android:drawablePadding="@dimen/dip_6"
            android:hint="@string/string_hint_search_coin"
            android:lines="1"
            android:paddingLeft="@dimen/app_padding"
            android:textColor="@color/font_color1"
            android:textColorHint="@color/font_color3"
            android:textSize="@dimen/font_15" />
    </RelativeLayout>
    <io.bhex.app.skin.view.SkinPercentRelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_36"
        android:paddingLeft="@dimen/app_paddingLeft"
        android:paddingRight="@dimen/app_paddingRight"
        >
        <TextView
            android:id="@+id/title_coin_name"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_16"
            app:layout_widthPercent="30%"
            android:gravity="center_vertical"
            android:text="@string/string_coin_name"
            android:textAppearance="@style/Caption_Grey" />
        <TextView
            android:id="@+id/title_frozen"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_16"
            android:layout_toRightOf="@id/title_coin_name"
            app:layout_widthPercent="30%"
            android:gravity="center_vertical"
            android:text="@string/string_frozen"
            android:textAppearance="@style/Caption_Grey" />
        <TextView
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_16"
            android:layout_toRightOf="@+id/title_frozen"
            app:layout_widthPercent="40%"
            android:gravity="center_vertical|right"
            android:text="@string/string_total_asset"
            android:textAppearance="@style/Caption_Grey" />

    </io.bhex.app.skin.view.SkinPercentRelativeLayout>
</LinearLayout>