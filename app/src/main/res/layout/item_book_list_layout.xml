<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_32"
    android:orientation="horizontal"
    android:paddingLeft="@dimen/dip_12"
    android:paddingRight="@dimen/dip_12"
    >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dip_3"
        android:layout_weight="1"
        >
        <ProgressBar
            android:id="@+id/progress_buy"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_32"
            style="@android:style/Widget.ProgressBar.Horizontal"
            android:secondaryProgress="100"
            android:max="100"
            android:progress="50"
            android:progressDrawable="@drawable/progress_left_green_style"
            />

        <TextView
            android:id="@+id/item_amount_buy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="--"
            android:textAppearance="@style/BodyS_Dark_night"/>

        <TextView
            android:id="@+id/item_price_buy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:layout_marginRight="@dimen/dip_5"
            android:text="--"
            android:textAppearance="@style/BodyS_Green"/>

    </RelativeLayout>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginLeft="@dimen/dip_3"
        >
        <ProgressBar
            android:id="@+id/progress_sell"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_32"
            style="@android:style/Widget.ProgressBar.Horizontal"
            android:secondaryProgress="0"
            android:max="100"
            android:progress="0"
            android:background="@color/color_4d4d5d"
            android:progressDrawable="@drawable/progress_right_red_style"
            />

        <TextView
            android:id="@+id/item_price_sell"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/dip_5"
            android:text="--"
            android:textAppearance="@style/BodyS_Red"/>

        <TextView
            android:id="@+id/item_amount_sell"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:text="--"
            android:textAppearance="@style/BodyS_Dark_night"/>

    </RelativeLayout>

</LinearLayout>