<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_60">
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:background="@color/divider_line_color"
        android:layout_alignParentBottom="true"
        />
    <CheckBox
        android:id="@+id/selectCB"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:text="@string/string_select_all"
        android:button="@drawable/checkbox_square_style"
        android:textAppearance="@style/Body_Dark"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:textColor="@color/dark"
        />

    <ImageView
        android:id="@+id/slideBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/icon_slide_bar"
        android:layout_alignParentRight="true"
        android:layout_marginRight="@dimen/app_margin_right"
        android:layout_centerVertical="true"
        android:padding="@dimen/dip_8"
        />

    <ImageView
        android:id="@+id/btnPlacement"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/icon_placement"
        android:layout_toLeftOf="@id/slideBar"
        android:layout_marginRight="@dimen/dip_30"
        android:layout_centerVertical="true"
        android:padding="@dimen/dip_8"
        />

</RelativeLayout>