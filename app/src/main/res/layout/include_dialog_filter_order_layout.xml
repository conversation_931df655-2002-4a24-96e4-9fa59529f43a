<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/dark"
        android:alpha="0.4"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/color_bg_2"
            android:orientation="vertical"
            android:paddingLeft="@dimen/app_paddingLeft"
            android:paddingTop="@dimen/dip_10"
            android:paddingRight="@dimen/app_paddingRight"
            android:paddingBottom="@dimen/dip_20">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_coin_pair"
                android:textColor="@color/font_color1"
                android:textSize="@dimen/font_15" />

            <io.bhex.app.skin.view.SkinPercentRelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_10">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_corner_rect_line_gray"
                    app:layout_widthPercent="45%">

                    <EditText
                        android:id="@+id/baseToken"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@null"
                        android:hint="@string/string_coin"
                        android:maxLines="1"
                        android:padding="@dimen/dip_10"
                        android:textColor="@color/dark"
                        android:textColorHint="@color/dark50"
                        android:textSize="@dimen/font_15" />

                </RelativeLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:text="/"
                    android:textColor="@color/font_color1"
                    android:textSize="@dimen/font_20"
                    app:layout_widthPercent="10%" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:background="@drawable/bg_corner_rect_line_gray"
                    app:layout_widthPercent="45%">

                    <EditText
                        android:id="@+id/quoteToken"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@null"
                        android:hint="@string/string_charge_coin"
                        android:maxLines="1"
                        android:padding="@dimen/dip_10"
                        android:textColor="@color/dark"
                        android:textColorHint="@color/dark50"
                        android:textSize="@dimen/font_15" />

                </RelativeLayout>


            </io.bhex.app.skin.view.SkinPercentRelativeLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_20"
                android:text="@string/string_trade_direction"
                android:textColor="@color/font_color1"
                android:textSize="@dimen/font_15" />

            <RadioGroup
                android:id="@+id/order_status_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_10"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/order_status_all"
                    android:layout_width="@dimen/dip_90"
                    android:layout_height="@dimen/dip_30"
                    android:background="@drawable/btn_filter_style"
                    android:button="@null"
                    android:checked="true"
                    android:gravity="center"
                    android:text="@string/string_all"
                    android:textColor="@color/btn_filter_text_color"
                    android:textSize="@dimen/font_13" />

                <RadioButton
                    android:id="@+id/order_status_buy"
                    android:layout_width="@dimen/dip_90"
                    android:layout_height="@dimen/dip_30"
                    android:layout_marginLeft="@dimen/dip_10"
                    android:background="@drawable/btn_filter_style"
                    android:button="@null"
                    android:gravity="center"
                    android:text="@string/string_purchase"
                    android:textColor="@color/btn_filter_text_color"
                    android:textSize="@dimen/font_13" />

                <RadioButton
                    android:id="@+id/order_status_sell"
                    android:layout_width="@dimen/dip_90"
                    android:layout_height="@dimen/dip_30"
                    android:layout_marginLeft="@dimen/dip_10"
                    android:background="@drawable/btn_filter_style"
                    android:button="@null"
                    android:gravity="center"
                    android:text="@string/string_sellout"
                    android:textColor="@color/btn_filter_text_color"
                    android:textSize="@dimen/font_13" />

            </RadioGroup>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_20"
                android:text="@string/string_type"
                android:textColor="@color/font_color1"
                android:textSize="@dimen/font_15"
                android:visibility="gone" />

            <RadioGroup
                android:id="@+id/price_mode_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_10"
                android:orientation="horizontal"
                android:visibility="gone">

                <RadioButton
                    android:id="@+id/price_mode_all"
                    android:layout_width="@dimen/dip_90"
                    android:layout_height="@dimen/dip_30"
                    android:background="@drawable/btn_filter_style"
                    android:button="@null"
                    android:checked="true"
                    android:gravity="center"
                    android:text="@string/string_all"
                    android:textColor="@color/btn_filter_text_color"
                    android:textSize="@dimen/font_13" />

                <RadioButton
                    android:id="@+id/price_mode_limited"
                    android:layout_width="@dimen/dip_90"
                    android:layout_height="@dimen/dip_30"
                    android:layout_marginLeft="@dimen/dip_10"
                    android:background="@drawable/btn_filter_style"
                    android:button="@null"
                    android:gravity="center"
                    android:text="@string/string_limited_price"
                    android:textColor="@color/btn_filter_text_color"
                    android:textSize="@dimen/font_13" />

                <RadioButton
                    android:id="@+id/price_mode_market"
                    android:layout_width="@dimen/dip_90"
                    android:layout_height="@dimen/dip_30"
                    android:layout_marginLeft="@dimen/dip_10"
                    android:background="@drawable/btn_filter_style"
                    android:button="@null"
                    android:gravity="center"
                    android:text="@string/string_market_price"
                    android:textColor="@color/btn_filter_text_color"
                    android:textSize="@dimen/font_13" />

            </RadioGroup>

        </LinearLayout>

        <io.bhex.app.skin.view.SkinPercentRelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <Button
                android:id="@+id/btn_reset"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_corner_rect_line_gray"
                android:text="@string/string_reset"
                android:textColor="@color/dark50"
                android:textSize="@dimen/font_15"
                android:textStyle="bold"
                app:layout_widthPercent="50%" />

            <Button
                android:id="@+id/btn_complete"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:background="@color/blue"
                android:text="@string/string_complete"
                android:textColor="@color/color_white"
                android:textSize="@dimen/font_15"
                android:textStyle="bold"
                app:layout_widthPercent="50%" />


        </io.bhex.app.skin.view.SkinPercentRelativeLayout>


    </LinearLayout>
</FrameLayout>