<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/itemView"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_28"
    android:gravity="center_vertical"
    android:background="@drawable/book_item_press_red_style"
    android:orientation="vertical">

    <io.bhex.app.view.BookView
        android:id="@+id/bookView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_27" />

    <!--app:priceTextRedColor="@color/red"-->
    <!--app:priceTextGreenColor="@color/green"-->

</LinearLayout>