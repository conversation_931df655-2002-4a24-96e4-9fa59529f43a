<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:overScrollMode="always"
        android:orientation="vertical">

        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/app_line"
            android:visibility="gone"
            android:layout_marginStart="@dimen/dip_12"
            android:background="@color/kline_line_dark5" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingStart="@dimen/app_paddingLeft"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tokenName"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_60"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/H4_Dark_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/title1"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_50"
                android:gravity="center_vertical"
                android:text="@string/string_publish_date"
                android:textAppearance="@style/Body_Grey_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider1" />

            <TextView
                android:id="@+id/value1"
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_50"
                android:layout_marginStart="@dimen/dip_20"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title1"
                app:layout_constraintTop_toBottomOf="@id/divider1" />

            <TextView
                android:id="@+id/title2"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_50"
                android:gravity="center_vertical"
                android:text="@string/string_publish_total_quantity"
                android:textAppearance="@style/Body_Grey_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider2" />

            <TextView
                android:id="@+id/value2"
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_50"
                android:layout_marginStart="@dimen/dip_20"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title2"
                app:layout_constraintTop_toBottomOf="@id/divider2" />

            <TextView
                android:id="@+id/title3"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_50"
                android:gravity="center_vertical"
                android:text="@string/string_circulate_total_quantity"
                android:textAppearance="@style/Body_Grey_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider3" />

            <TextView
                android:id="@+id/value3"
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_50"
                android:layout_marginStart="@dimen/dip_20"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title3"
                app:layout_constraintTop_toBottomOf="@id/divider3" />

            <TextView
                android:id="@+id/title4"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_50"
                android:gravity="center_vertical"
                android:text="@string/string_white_paper"
                android:textAppearance="@style/Body_Grey_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider4" />

            <TextView
                android:id="@+id/value4"
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_50"
                android:layout_marginStart="@dimen/dip_20"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title4"
                app:layout_constraintTop_toBottomOf="@id/divider4" />

            <TextView
                android:id="@+id/title5"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_50"
                android:gravity="center_vertical"
                android:text="@string/string_official_website"
                android:textAppearance="@style/Body_Grey_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider5" />

            <TextView
                android:id="@+id/value5"
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_50"
                android:layout_marginStart="@dimen/dip_20"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title5"
                app:layout_constraintTop_toBottomOf="@id/divider5" />

            <TextView
                android:id="@+id/title6"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_50"
                android:gravity="center_vertical"
                android:text="@string/string_block_query"
                android:textAppearance="@style/Body_Grey_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider6" />

            <TextView
                android:id="@+id/value6"
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_50"
                android:layout_marginStart="@dimen/dip_20"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title6"
                app:layout_constraintTop_toBottomOf="@id/divider6" />

            <TextView
                android:id="@+id/tokenBriefIntroduction"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_60"
                android:layout_marginTop="@dimen/dip_20"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_brief_introduction"
                android:textAppearance="@style/H4_Dark_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider7" />

            <TextView
                android:id="@+id/tokenBriefIntroductionContent"
                android:layout_width="@dimen/dip_0"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                android:lineSpacingExtra="@dimen/dip_4"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tokenBriefIntroduction"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginBottom="@dimen/dip_40"
                />

            <View
                android:id="@+id/divider1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/app_line"
                android:background="@color/divider_line_color_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tokenName" />

            <View
                android:id="@+id/divider2"
                android:layout_width="match_parent"
                android:layout_height="@dimen/app_line"
                android:background="@color/divider_line_color_night"
                android:paddingEnd="@dimen/app_paddingRight"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title1" />

            <View
                android:id="@+id/divider3"
                android:layout_width="match_parent"
                android:layout_height="@dimen/app_line"
                android:background="@color/divider_line_color_night"
                android:paddingEnd="@dimen/app_paddingRight"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title2" />

            <View
                android:id="@+id/divider4"
                android:layout_width="match_parent"
                android:layout_height="@dimen/app_line"
                android:background="@color/divider_line_color_night"
                android:paddingEnd="@dimen/app_paddingRight"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title3" />

            <View
                android:id="@+id/divider5"
                android:layout_width="match_parent"
                android:layout_height="@dimen/app_line"
                android:background="@color/divider_line_color_night"
                android:paddingEnd="@dimen/app_paddingRight"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title4" />

            <View
                android:id="@+id/divider6"
                android:layout_width="match_parent"
                android:layout_height="@dimen/app_line"
                android:background="@color/divider_line_color_night"
                android:paddingEnd="@dimen/app_paddingRight"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title5" />


            <View
                android:id="@+id/divider7"
                android:layout_width="match_parent"
                android:layout_height="@dimen/app_line"
                android:background="@color/divider_line_color_night"
                android:paddingEnd="@dimen/app_paddingRight"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title6" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</androidx.core.widget.NestedScrollView>