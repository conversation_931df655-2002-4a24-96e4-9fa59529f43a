<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/item_style"
    android:orientation="vertical"
    >
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginRight="@dimen/app_margin_right"
        android:paddingTop="@dimen/dip_12"
        android:paddingBottom="@dimen/dip_8"
        android:orientation="vertical">
        <TextView
            android:id="@+id/token_remark_name"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_24"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark_Bold"/>
        <TextView
            android:id="@+id/chain_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:textAppearance="@style/Body_Dark_Bold"/>

        <TextView
            android:id="@+id/token_address"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/token_remark_name"
            android:minHeight="@dimen/dip_24"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Grey"/>

        <TextView
            android:id="@+id/token_address_tag"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_24"
            android:layout_below="@id/token_address"
            android:text=""
            android:textAppearance="@style/Body_Grey"/>
    </RelativeLayout>
    <View
        android:id="@+id/item_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:background="@color/divider_line_color"
        />

</LinearLayout>