<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: header_point_card_layout.xml
  ~   @Date: 18-12-2 下午3:31
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_bg_1"
    >

    <TextView
        android:id="@+id/hp_surplus_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="24dp"
        android:text="@string/string_surplus_pointcard"
        android:textAppearance="@style/BodyS_Grey"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/hp_surplus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/app_margin_top"
        android:layout_marginEnd="@dimen/app_margin_right"
        android:text=""
        android:textAppearance="@style/Body_Dark"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/hp_available_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="8dp"
        android:text="@string/string_available"
        android:textAppearance="@style/BodyS_Grey"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/hp_surplus_title" />

    <TextView
        android:id="@+id/hp_available"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="24dp"
        android:text=""
        android:textAppearance="@style/Body_Dark"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/hp_surplus" />

    <TextView
        android:id="@+id/hp_frozen_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="8dp"
        android:text="@string/string_frozen"
        android:textAppearance="@style/BodyS_Grey"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/hp_available_title" />

    <TextView
        android:id="@+id/hp_frozen"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="24dp"
        android:text=""
        android:textAppearance="@style/Body_Dark"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/hp_available" />
    <TextView
        android:id="@+id/hp_fee_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="24dp"
        android:text="@string/string_format_point_value_fee"
        android:textAppearance="@style/BodyS_Grey"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/hp_frozen" />
    <View
        android:id="@+id/header_divider1"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_8"
        android:background="@color/dark5"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/hp_fee_tips"/>
    <TextView
        android:id="@+id/point_detail_tilte"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="@dimen/dip_16"
        android:text="@string/string_pointcard_detail"
        android:textAppearance="@style/BodyL_Dark"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header_divider1" />
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:background="@color/dark5"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/point_detail_tilte"/>
</androidx.constraintlayout.widget.ConstraintLayout>