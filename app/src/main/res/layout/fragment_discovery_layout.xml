<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="false"
    android:orientation="vertical">

    <io.bhex.app.view.TopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:left_visiblity="gone"
        app:title_text="@string/title_discovery" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/app_padding">

            <ImageView
                android:id="@+id/img1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_10"
                android:scaleType="fitXY"
                android:background="@mipmap/banner_img"
                />

            <ImageView
                android:id="@+id/img2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_10"
                android:background="@mipmap/banner_img"
                android:scaleType="fitXY"
                />

            <ImageView
                android:id="@+id/img3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_10"
                android:background="@mipmap/banner_img"
                android:scaleType="fitXY"/>

            <ImageView
                android:id="@+id/img4"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_10"
                android:background="@mipmap/banner_img"
                android:scaleType="fitXY"/>
        </LinearLayout>

    </ScrollView>


</LinearLayout>