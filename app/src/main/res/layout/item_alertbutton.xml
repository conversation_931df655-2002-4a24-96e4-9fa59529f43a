<?xml version="1.0" encoding="utf-8"?>
<!--
  ~
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_alertbutton.xml
  ~   @Date: 11/29/18 3:21 PM
  ~   @Author: chenjun
  ~   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  ~
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_divier"
        android:background="@color/bgColor_divier" />

    <TextView
        android:id="@+id/tvAlert"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_alert_button"
        android:gravity="center"
        android:textColor="@color/textColor_alert_button_others"
        android:textStyle="bold"
        android:textSize="@dimen/textSize_alert_button" />
</LinearLayout>