<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/item_style"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/app_line"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:background="@color/divider_line_color" />

    <TextView
        android:id="@+id/item_asset_coin_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginTop="@dimen/dip_8"
        android:layout_marginBottom="@dimen/dip_8"
        android:singleLine="true"
        android:text="@string/string_placeholder"
        android:textAppearance="@style/Body_Dark_Bold" />

    <io.bhex.app.skin.view.SkinPercentRelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/app_paddingLeft"
        android:paddingRight="@dimen/app_paddingRight">

        <TextView
            android:id="@+id/title_available"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_16"
            android:layout_toRightOf="@id/title_coin_name"
            android:gravity="center_vertical"
            android:text="@string/string_available"
            android:textAppearance="@style/Caption_Grey"
            app:layout_widthPercent="35%" />

        <TextView
            android:id="@+id/title_loaned"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_16"
            android:layout_toRightOf="@id/title_available"
            android:gravity="center_vertical"
            android:paddingLeft="@dimen/dip_8"
            android:text="@string/string_loaned_amount"
            android:textAppearance="@style/Caption_Grey"
            app:layout_widthPercent="35%" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_16"
            android:layout_toRightOf="@+id/title_loaned"
            android:gravity="center_vertical|right"
            android:text="@string/string_frozen"
            android:textAppearance="@style/Caption_Grey"
            app:layout_widthPercent="30%" />

    </io.bhex.app.skin.view.SkinPercentRelativeLayout>

    <io.bhex.app.skin.view.SkinPercentRelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dip_8"
        android:paddingLeft="@dimen/app_paddingLeft"
        android:paddingRight="@dimen/app_paddingRight">

        <TextView
            android:id="@+id/item_asset_available"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:singleLine="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark_Bold"
            app:layout_widthPercent="35%" />

        <TextView
            android:id="@+id/item_loaned"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/item_asset_available"
            android:gravity="center_vertical"
            android:paddingLeft="@dimen/dip_8"
            android:singleLine="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark_Bold"
            app:layout_widthPercent="35%" />

        <TextView
            android:id="@+id/item_asset_frozen"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@+id/item_loaned"
            android:gravity="center_vertical|right"
            android:singleLine="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark_Bold"
            app:layout_widthPercent="30%" />


    </io.bhex.app.skin.view.SkinPercentRelativeLayout>

    <TextView
        android:id="@+id/item_asset_total_asset_about"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginBottom="@dimen/dip_8"
        android:gravity="center_vertical|left"
        android:paddingLeft="@dimen/app_paddingLeft"
        android:singleLine="true"
        android:text="@string/string_placeholder"
        android:textColor="@color/third_text_color"
        android:textSize="@dimen/sp_12"
        tools:text="折合 0.00 CNY" />
</LinearLayout>