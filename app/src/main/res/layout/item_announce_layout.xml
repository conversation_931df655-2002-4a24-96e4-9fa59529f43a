<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/item_style"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/account_auth_rela"
        style="@style/config_item_style"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        >
        <TextView
            android:id="@+id/announce_title"
            style="@style/account_item_name_style"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_placeholder" />
        <TextView
            android:id="@+id/announce_time"
            android:textAppearance="@style/BodyS_Grey"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/announce_title"
            android:layout_marginTop="@dimen/dip_4"
            android:layout_marginBottom="@dimen/dip_12"
            android:text="@string/string_placeholder" />


    </RelativeLayout>

    <View
        android:id="@+id/item_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:background="@color/divider_line_color"
        />
</LinearLayout>