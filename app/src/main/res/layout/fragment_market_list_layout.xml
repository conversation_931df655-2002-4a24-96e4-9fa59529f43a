<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/color_bg_2"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    
    <include
        android:id="@+id/sortLayoutView"
        layout="@layout/header_market_list_layout"
        />

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/sortLayoutView"
        >
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            >

            <TextView
                android:id="@+id/selectCoinpair"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/font_15"
                android:textColor="@color/font_color2"
                android:text="@string/string_current_select"
                android:layout_marginTop="@dimen/dip_8"
                android:layout_marginBottom="@dimen/dip_4"
                android:visibility="gone"
                android:layout_marginLeft="@dimen/app_margin_left"
                />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="true"
                app:layout_behavior="@string/appbar_scrolling_view_behavior"
                tools:listitem="@layout/item_rise_fall_list_layout"
                />

        </LinearLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</RelativeLayout>