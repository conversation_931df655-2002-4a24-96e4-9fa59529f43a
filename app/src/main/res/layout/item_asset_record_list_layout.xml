<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_60">

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_0.5"
        android:background="@color/divider_line_color"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="@dimen/app_padding"
        />
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="@dimen/app_padding"
        >

        <TextView
            android:id="@+id/item_record_coin_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/string_placeholder"
            android:layout_marginBottom="@dimen/dip_5"
            android:textColor="@color/font_color1"
            android:textSize="@dimen/font_15" />

        <TextView
            android:id="@+id/item_record_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dip_5"
            android:layout_alignParentRight="true"
            android:layout_marginLeft="@dimen/dip_5"
            android:text="@string/string_placeholder"
            android:textColor="@color/font_color2"
            android:textSize="@dimen/font_13" />
        <TextView
            android:id="@+id/item_record_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/item_record_coin_name"
            android:text="@string/string_placeholder"
            android:textColor="@color/font_color2"
            android:textSize="@dimen/font_12" />
        <TextView
            android:id="@+id/item_record_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_below="@+id/item_record_amount"
            android:text="@string/string_placeholder"
            android:textColor="@color/font_color2"
            android:textSize="@dimen/font_12" />
    </RelativeLayout>
</RelativeLayout>