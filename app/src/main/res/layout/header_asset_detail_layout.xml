<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_below="@+id/topBar"
    android:background="@color/color_bg_2"
    android:orientation="vertical"
    android:paddingTop="@dimen/dip_4">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_40"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginRight="@dimen/app_margin_right"
        android:visibility="gone">

        <ImageView
            android:id="@+id/coin_icon"
            android:layout_width="@dimen/dip_35"
            android:layout_height="@dimen/dip_35"
            android:layout_alignParentBottom="true" />

        <TextView
            android:id="@+id/asset_token_fullname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="@dimen/dip_10"
            android:layout_toRightOf="@+id/coin_icon"
            android:text="@string/string_placeholder"
            android:textColor="@color/font_color2"
            android:textSize="@dimen/font_13" />

        <TextView
            android:id="@+id/asset_token"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/asset_token_fullname"
            android:layout_centerVertical="true"
            android:layout_marginBottom="@dimen/dip_5"
            android:layout_marginLeft="@dimen/dip_10"
            android:layout_toRightOf="@+id/coin_icon"
            android:text="@string/string_placeholder"
            android:textColor="@color/font_color1"
            android:textSize="@dimen/font_15" />

    </RelativeLayout>

    <TextView
        android:id="@+id/asset_total"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginRight="@dimen/app_margin_right"
        android:layout_centerVertical="true"
        android:text="@string/string_placeholder"
        android:textAppearance="@style/H2_Blue" />

    <RelativeLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"

        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginRight="@dimen/app_margin_right"
        android:layout_marginTop="@dimen/dip_8">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/string_available"
            android:textAppearance="@style/Body_Grey" />

        <TextView
            android:id="@+id/asset_available"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginRight="@dimen/app_margin_right"
        android:layout_marginTop="@dimen/dip_8">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/string_frozen"
            android:textAppearance="@style/Body_Grey" />

        <TextView
            android:id="@+id/asset_frozen"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_8"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginRight="@dimen/app_margin_right"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/string_abount_btc"
            android:textAppearance="@style/Body_Grey" />

        <TextView
            android:id="@+id/asset_abount_btc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginRight="@dimen/app_margin_right"
        android:layout_marginBottom="@dimen/dip_12"
        android:layout_marginTop="@dimen/dip_8">

        <TextView
            android:id="@+id/asset_abount_curreny_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/string_abount_curreny"
            android:textAppearance="@style/Body_Grey" />

        <TextView
            android:id="@+id/asset_abount_curreny"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark" />

    </RelativeLayout>
    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_8"
        android:layout_below="@id/topBar"
        android:background="@color/dark5"
        />
</LinearLayout>