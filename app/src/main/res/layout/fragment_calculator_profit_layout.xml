<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/app_margin_left"
        android:paddingRight="@dimen/app_margin_right"
        android:paddingTop="@dimen/dip_12"
        android:paddingBottom="@dimen/dip_20"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/select_contract_rela"
            android:layout_width="fill_parent"
            android:layout_height="@dimen/dip_50"
            android:layout_marginTop="@dimen/dip_4"
            android:paddingLeft="@dimen/dip_12"
            android:paddingRight="@dimen/dip_12"
            android:background="@drawable/item_style">

            <TextView
                style="@style/Body_Dark"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/string_contract" />

            <ImageView
                android:id="@+id/arrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/dip_4"
                android:src="@mipmap/icon_arrow_right" />

            <TextView
                android:id="@+id/contract_name"
                style="@style/Body_Dark"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toLeftOf="@id/arrow"
                android:layout_marginRight="@dimen/dip_8"
                android:layout_centerVertical="true"
                android:text="@string/string_placeholder" />

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="fill_parent"
            android:layout_height="@dimen/dip_50"
            android:layout_marginTop="@dimen/dip_16"
            android:paddingLeft="@dimen/dip_12"
            >

            <TextView
                style="@style/Body_Dark"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/string_type" />

            <LinearLayout
                android:layout_width="@dimen/dip_250"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:orientation="horizontal"
                >
                <include
                    android:id="@+id/tab"
                    layout="@layout/long_short_tab_layout" />

            </LinearLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rela_lever"
            android:layout_width="fill_parent"
            android:layout_height="@dimen/dip_50"
            android:layout_marginTop="@dimen/dip_16"
            android:paddingLeft="@dimen/dip_12"
            android:paddingRight="@dimen/dip_12"
            android:background="@drawable/item_style">

            <TextView
                android:id="@+id/titleLever"
                style="@style/Body_Dark"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="@dimen/dip_80"
                android:layout_centerVertical="true"
                android:text="@string/string_lever" />

            <TextView
                android:id="@+id/leverUnit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="@dimen/dip_80"
                android:gravity="right"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:textAppearance="@style/Body_Dark_Bold"
                android:text="X"
                />

            <EditText
                android:id="@+id/lever"
                style="@style/Body_Dark_Bold"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical|right"
                android:minWidth="@dimen/dip_100"
                android:background="@null"
                android:textColorHint="@color/dark50"
                android:hint="@string/string_input_please"
                android:paddingEnd="@dimen/dip_88"
                android:paddingStart="@dimen/dip_80"
                android:layout_centerVertical="true"
                android:text=""
                android:inputType="numberDecimal"
                />

        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/rela_open_price"
            android:layout_width="fill_parent"
            android:layout_height="@dimen/dip_50"
            android:layout_marginTop="@dimen/dip_16"
            android:paddingLeft="@dimen/dip_12"
            android:paddingRight="@dimen/dip_12"
            android:background="@drawable/item_style">

            <TextView
                android:id="@+id/titleOpenPrice"
                style="@style/Body_Dark"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="@dimen/dip_80"
                android:layout_centerVertical="true"
                android:text="@string/string_open_price" />

            <TextView
                android:id="@+id/openPriceUnit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="@dimen/dip_80"
                android:gravity="right"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:textAppearance="@style/Body_Dark_Bold"
                android:text="@string/string_placeholder"
                />

            <EditText
                android:id="@+id/openPrice"
                style="@style/Body_Dark_Bold"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical|right"
                android:minWidth="@dimen/dip_100"
                android:background="@null"
                android:textColorHint="@color/dark50"
                android:hint="@string/string_input_please"
                android:paddingEnd="@dimen/dip_88"
                android:paddingStart="@dimen/dip_80"
                android:layout_centerVertical="true"
                android:text=""
                android:inputType="numberDecimal"
                />

        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/rela_open_quantity"
            android:layout_width="fill_parent"
            android:layout_height="@dimen/dip_50"
            android:layout_marginTop="@dimen/dip_16"
            android:paddingLeft="@dimen/dip_12"
            android:paddingRight="@dimen/dip_12"
            android:background="@drawable/item_style">

            <TextView
                android:id="@+id/titleOpenQuantity"
                style="@style/Body_Dark"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="@dimen/dip_80"
                android:layout_centerVertical="true"
                android:text="@string/string_open_quantity" />

            <TextView
                android:id="@+id/openQuantityUnit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="@dimen/dip_80"
                android:gravity="right"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:textAppearance="@style/Body_Dark_Bold"
                android:text="@string/string_futures_unit"
                />

            <EditText
                android:id="@+id/openQuantity"
                style="@style/Body_Dark_Bold"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical|right"
                android:minWidth="@dimen/dip_100"
                android:background="@null"
                android:textColorHint="@color/dark50"
                android:hint="@string/string_input_please"
                android:paddingEnd="@dimen/dip_88"
                android:paddingStart="@dimen/dip_80"
                android:layout_centerVertical="true"
                android:text=""
                android:inputType="numberDecimal"
                />

        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/rela_close_price"
            android:layout_width="fill_parent"
            android:layout_height="@dimen/dip_50"
            android:layout_marginTop="@dimen/dip_16"
            android:paddingLeft="@dimen/dip_12"
            android:paddingRight="@dimen/dip_12"
            android:background="@drawable/item_style">

            <TextView
                android:id="@+id/titleClosePrice"
                style="@style/Body_Dark"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="@dimen/dip_80"
                android:layout_centerVertical="true"
                android:text="@string/string_close_price" />

            <TextView
                android:id="@+id/closePriceUnit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="@dimen/dip_80"
                android:gravity="right"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:textAppearance="@style/Body_Dark_Bold"
                android:text="@string/string_placeholder"
                />

            <EditText
                android:id="@+id/closePrice"
                style="@style/Body_Dark_Bold"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical|right"
                android:minWidth="@dimen/dip_100"
                android:background="@null"
                android:textColorHint="@color/dark50"
                android:hint="@string/string_input_please"
                android:paddingEnd="@dimen/dip_88"
                android:paddingStart="@dimen/dip_80"
                android:layout_centerVertical="true"
                android:text=""
                android:inputType="numberDecimal"
                />

        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/rela_close_quantity"
            android:layout_width="fill_parent"
            android:layout_height="@dimen/dip_50"
            android:layout_marginTop="@dimen/dip_16"
            android:paddingLeft="@dimen/dip_12"
            android:paddingRight="@dimen/dip_12"
            android:background="@drawable/item_style">

            <TextView
                android:id="@+id/titleCloseQuantity"
                style="@style/Body_Dark"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="@dimen/dip_80"
                android:layout_centerVertical="true"
                android:text="@string/string_close_quantity" />

            <TextView
                android:id="@+id/closeQuantityUnit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="@dimen/dip_80"
                android:gravity="right"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:textAppearance="@style/Body_Dark_Bold"
                android:text="@string/string_futures_unit"
                />

            <EditText
                android:id="@+id/closeQuantity"
                style="@style/Body_Dark_Bold"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical|right"
                android:minWidth="@dimen/dip_100"
                android:background="@null"
                android:textColorHint="@color/dark50"
                android:hint="@string/string_input_please"
                android:paddingEnd="@dimen/dip_88"
                android:paddingStart="@dimen/dip_80"
                android:layout_centerVertical="true"
                android:text=""
                android:inputType="numberDecimal"
                />

        </RelativeLayout>

        <Button
            android:id="@+id/btn_calculator"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/btn_corner"
            style="@style/btn_style"
            android:text="@string/string_calculate"
            android:layout_marginTop="@dimen/dip_16"
            />

        <LinearLayout
            android:id="@+id/rela_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_16"
            android:padding="@dimen/app_padding"
            android:orientation="vertical"
            >
            <TextView
                style="@style/BodyL_Dark_Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/string_calculate_result" />

            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="@dimen/dip_50"
                android:layout_marginTop="@dimen/dip_8"
                android:background="@drawable/item_style">

                <TextView
                    style="@style/Body_Dark"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="@string/string_open_margin" />

                <TextView
                    android:id="@+id/openMarginUnit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:minWidth="@dimen/dip_80"
                    android:gravity="right"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:textAppearance="@style/Body_Dark_Bold"
                    android:text="@string/string_placeholder"
                    />

                <TextView
                    android:id="@+id/openMargin"
                    style="@style/Body_Blue_Bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="right"
                    android:minWidth="@dimen/dip_100"
                    android:background="@null"
                    android:textColorHint="@color/dark50"
                    android:layout_toLeftOf="@id/openMarginUnit"
                    android:layout_marginRight="@dimen/dip_8"
                    android:layout_centerVertical="true"
                    android:text="@string/string_placeholder"
                    />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="@dimen/dip_50"
                android:background="@drawable/item_style">

                <TextView
                    style="@style/Body_Dark"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="@string/string_open_taker_fee" />

                <TextView
                    android:id="@+id/openTakerFeeUnit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:minWidth="@dimen/dip_80"
                    android:gravity="right"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:textAppearance="@style/Body_Dark_Bold"
                    android:text="@string/string_placeholder"
                    />

                <TextView
                    android:id="@+id/openTakerFee"
                    style="@style/Body_Blue_Bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="right"
                    android:minWidth="@dimen/dip_100"
                    android:background="@null"
                    android:textColorHint="@color/dark50"
                    android:layout_toLeftOf="@id/openTakerFeeUnit"
                    android:layout_marginRight="@dimen/dip_8"
                    android:layout_centerVertical="true"
                    android:text="@string/string_placeholder"
                    />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="@dimen/dip_50"
                android:background="@drawable/item_style">

                <TextView
                    style="@style/Body_Dark"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="@string/string_open_maker_fee" />

                <TextView
                    android:id="@+id/openMakerFeeUnit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:minWidth="@dimen/dip_80"
                    android:gravity="right"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:textAppearance="@style/Body_Dark_Bold"
                    android:text="@string/string_placeholder"
                    />

                <TextView
                    android:id="@+id/openMakerFee"
                    style="@style/Body_Blue_Bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="right"
                    android:minWidth="@dimen/dip_100"
                    android:background="@null"
                    android:textColorHint="@color/dark50"
                    android:layout_toLeftOf="@id/openMakerFeeUnit"
                    android:layout_marginRight="@dimen/dip_8"
                    android:layout_centerVertical="true"
                    android:text="@string/string_placeholder"
                    />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="@dimen/dip_50"
                android:background="@drawable/item_style">

                <TextView
                    style="@style/Body_Dark"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="@string/string_close_taker_fee" />

                <TextView
                    android:id="@+id/closeTakerFeeUnit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:minWidth="@dimen/dip_80"
                    android:gravity="right"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:textAppearance="@style/Body_Dark_Bold"
                    android:text="@string/string_placeholder"
                    />

                <TextView
                    android:id="@+id/closeTakerFee"
                    style="@style/Body_Blue_Bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="right"
                    android:minWidth="@dimen/dip_100"
                    android:background="@null"
                    android:textColorHint="@color/dark50"
                    android:layout_toLeftOf="@id/closeTakerFeeUnit"
                    android:layout_marginRight="@dimen/dip_8"
                    android:layout_centerVertical="true"
                    android:text="@string/string_placeholder"
                    />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="@dimen/dip_50"
                android:background="@drawable/item_style">

                <TextView
                    style="@style/Body_Dark"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="@string/string_close_maker_fee" />

                <TextView
                    android:id="@+id/closeMakerFeeUnit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:minWidth="@dimen/dip_80"
                    android:gravity="right"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:textAppearance="@style/Body_Dark_Bold"
                    android:text="@string/string_placeholder"
                    />

                <TextView
                    android:id="@+id/closeMakerFee"
                    style="@style/Body_Blue_Bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="right"
                    android:minWidth="@dimen/dip_100"
                    android:background="@null"
                    android:textColorHint="@color/dark50"
                    android:layout_toLeftOf="@id/closeMakerFeeUnit"
                    android:layout_marginRight="@dimen/dip_8"
                    android:layout_centerVertical="true"
                    android:text="@string/string_placeholder"
                    />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="@dimen/dip_50"
                android:background="@drawable/item_style">

                <TextView
                    style="@style/Body_Dark"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="@string/string_asset_coinplus_get" />

                <TextView
                    android:id="@+id/profitUnit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:minWidth="@dimen/dip_80"
                    android:gravity="right"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:textAppearance="@style/Body_Dark_Bold"
                    android:text="@string/string_placeholder"
                    />

                <TextView
                    android:id="@+id/profit"
                    style="@style/Body_Blue_Bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="right"
                    android:minWidth="@dimen/dip_100"
                    android:background="@null"
                    android:textColorHint="@color/dark50"
                    android:layout_toLeftOf="@id/profitUnit"
                    android:layout_marginRight="@dimen/dip_8"
                    android:layout_centerVertical="true"
                    android:text="@string/string_placeholder"
                    />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="@dimen/dip_50"
                android:background="@drawable/item_style">

                <TextView
                    style="@style/Body_Dark"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="@string/string_profit" />

                <TextView
                    android:id="@+id/profitRateUnit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:minWidth="@dimen/dip_80"
                    android:gravity="right"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:textAppearance="@style/Body_Dark_Bold"
                    android:text="%"
                    />

                <TextView
                    android:id="@+id/profitRate"
                    style="@style/Body_Blue_Bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="right"
                    android:minWidth="@dimen/dip_100"
                    android:background="@null"
                    android:textColorHint="@color/dark50"
                    android:layout_toLeftOf="@id/profitRateUnit"
                    android:layout_marginRight="@dimen/dip_8"
                    android:layout_centerVertical="true"
                    android:text="@string/string_placeholder"
                    />

            </RelativeLayout>

        </LinearLayout>


    </LinearLayout>


</androidx.core.widget.NestedScrollView>