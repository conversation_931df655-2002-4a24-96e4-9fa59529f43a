<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/item_style"
    android:orientation="vertical"
    >

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/app_line"
        android:background="@color/divider_line_color"
        android:layout_marginLeft="@dimen/app_margin_left"
        />
    <TextView
        android:id="@+id/item_asset_coin_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:layout_marginTop="@dimen/dip_8"
        android:layout_marginBottom="@dimen/dip_8"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:text="@string/string_placeholder"
        android:textAppearance="@style/Body_Dark_Bold" />

    <io.bhex.app.skin.view.SkinPercentRelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/app_paddingLeft"
        android:paddingRight="@dimen/app_paddingRight"
        >

        <TextView
            android:id="@+id/title_available"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_16"
            android:layout_toRightOf="@id/title_coin_name"
            app:layout_widthPercent="35%"
            android:gravity="center_vertical"
            android:text="@string/string_available"
            android:textAppearance="@style/Caption_Grey" />
        <TextView
            android:id="@+id/title_frozen"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_16"
            android:layout_toRightOf="@id/title_available"
            app:layout_widthPercent="35%"
            android:gravity="center_vertical"
            android:paddingLeft="@dimen/dip_8"
            android:text="@string/string_frozen"
            android:textAppearance="@style/Caption_Grey" />
        <TextView
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_16"
            android:layout_toRightOf="@+id/title_frozen"
            app:layout_widthPercent="30%"
            android:gravity="center_vertical|right"
            android:text="@string/string_total_asset_about_rate"
            android:textAppearance="@style/Caption_Grey" />

    </io.bhex.app.skin.view.SkinPercentRelativeLayout>
    <io.bhex.app.skin.view.SkinPercentRelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dip_8"
        android:paddingLeft="@dimen/app_paddingLeft"
        android:paddingRight="@dimen/app_paddingRight"
        >
        <TextView
            android:id="@+id/item_asset_available"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:singleLine="true"
            app:layout_widthPercent="35%"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark_Bold" />
        <TextView
            android:id="@+id/item_asset_frozen"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:paddingLeft="@dimen/dip_8"
            android:layout_toRightOf="@id/item_asset_available"
            app:layout_widthPercent="35%"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark_Bold" />
        <TextView
            android:id="@+id/item_asset_total_asset_about"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:layout_alignParentRight="true"
            android:layout_toRightOf="@+id/asset_frozen"
            app:layout_widthPercent="30%"
            android:layout_centerVertical="true"
            android:gravity="center_vertical|right"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark_Bold" />

    </io.bhex.app.skin.view.SkinPercentRelativeLayout>
</LinearLayout>