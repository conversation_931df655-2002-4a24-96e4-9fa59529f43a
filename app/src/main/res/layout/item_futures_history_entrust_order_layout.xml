<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_futures_history_entrust_order_layout.xml
  ~   @Date: 19-7-29 下午3:17
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/itemView"
    android:paddingTop="@dimen/dip_16">

    <RelativeLayout
        android:id="@+id/titleRela"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="@dimen/app_margin_left"
        android:layout_marginEnd="@dimen/app_margin_right">

        <ImageView
            android:id="@+id/order_status_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dip_8"
            android:src="@mipmap/icon_arrow_right"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            />

        <TextView
            android:id="@+id/order_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toLeftOf="@id/order_status_arrow"
            android:layout_centerVertical="true"
            android:textAppearance="@style/Body_Dark_Bold" />

        <TextView
            android:id="@+id/orderSideAndLever"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Green_Bold" />

        <TextView
            android:id="@+id/symbolName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBaseline="@id/orderSideAndLever"
            android:layout_marginLeft="@dimen/dip_8"
            android:layout_toRightOf="@id/orderSideAndLever"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark_Bold" />

    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/app_margin_left"
        android:layout_marginEnd="@dimen/app_margin_right"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleRela">

        <TextView
            android:id="@+id/priceTitle"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            app:layout_constraintStart_toStartOf="parent"
            android:text="@string/string_price"
            android:textAppearance="@style/BodyS_Grey"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.33" />

        <TextView
            android:id="@+id/aboutClosePriceTilte"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_amount"
            android:textAppearance="@style/BodyS_Grey"
            app:layout_constraintStart_toEndOf="@id/priceTitle"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.33" />

        <TextView
            android:id="@+id/holdAmountTilte"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:gravity="right"
            android:text="@string/string_type"
            android:textAppearance="@style/BodyS_Grey"
            app:layout_constraintStart_toEndOf="@id/aboutClosePriceTilte"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.33" />

        <TextView
            android:id="@+id/value1"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            app:layout_constraintTop_toBottomOf="@id/priceTitle"
            app:layout_constraintWidth_percent="0.33" />

        <TextView
            android:id="@+id/aboutClosePrice"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            app:layout_constraintStart_toEndOf="@id/value1"
            app:layout_constraintTop_toBottomOf="@id/priceTitle"
            app:layout_constraintWidth_percent="0.33" />

        <TextView
            android:id="@+id/holdAmount"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_8"
            android:gravity="right"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            app:layout_constraintStart_toEndOf="@id/aboutClosePrice"
            app:layout_constraintTop_toBottomOf="@id/priceTitle"
            app:layout_constraintWidth_percent="0.33" />
        <!-- 第二行 -->
        <TextView
            android:id="@+id/marginTitle"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_traded_average_price"
            android:textAppearance="@style/BodyS_Grey"
            app:layout_constraintTop_toBottomOf="@id/value1"
            app:layout_constraintWidth_percent="0.33" />

        <TextView
            android:id="@+id/unrealizedProfitAndLossTilte"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_dealed_num"
            android:textAppearance="@style/BodyS_Grey"
            app:layout_constraintStart_toEndOf="@id/marginTitle"
            app:layout_constraintTop_toBottomOf="@id/value1"
            app:layout_constraintWidth_percent="0.33" />

        <TextView
            android:id="@+id/position_value"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:gravity="right"
            android:text="@string/string_time"
            android:textAppearance="@style/BodyS_Grey"
            app:layout_constraintStart_toEndOf="@id/unrealizedProfitAndLossTilte"
            app:layout_constraintTop_toBottomOf="@id/value1"
            app:layout_constraintWidth_percent="0.33" />

        <TextView
            android:id="@+id/positionMargin"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            app:layout_constraintTop_toBottomOf="@id/marginTitle"
            app:layout_constraintWidth_percent="0.33" />

        <TextView
            android:id="@+id/unrealizedProfitAndLoss"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            app:layout_constraintStart_toEndOf="@id/positionMargin"
            app:layout_constraintTop_toBottomOf="@id/marginTitle"
            app:layout_constraintWidth_percent="0.33" />

        <TextView
            android:id="@+id/positionValue"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_8"
            android:gravity="right"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Caption_Dark"
            app:layout_constraintBaseline_toBaselineOf="@id/unrealizedProfitAndLoss"
            app:layout_constraintStart_toEndOf="@id/unrealizedProfitAndLoss"
            app:layout_constraintTop_toBottomOf="@id/marginTitle"
            app:layout_constraintWidth_percent="0.33" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:layout_marginStart="@dimen/app_margin_left"
        android:layout_marginTop="@dimen/dip_12"
        android:background="@color/divider_line_color"
        app:layout_constraintTop_toBottomOf="@id/content" />

</androidx.constraintlayout.widget.ConstraintLayout>