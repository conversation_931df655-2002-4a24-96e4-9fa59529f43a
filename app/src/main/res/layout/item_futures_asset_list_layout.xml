<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_futures_asset_list_layout.xml
  ~   @Date: 19-7-18 下午7:11
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/item_style"
    android:orientation="vertical"
    >

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/app_line"
        android:background="@color/divider_line_color"
        android:layout_marginLeft="@dimen/app_margin_left"
        />
    <TextView
        android:id="@+id/item_asset_coin_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:layout_marginTop="@dimen/dip_8"
        android:layout_marginBottom="@dimen/dip_8"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:text="@string/string_placeholder"
        android:textAppearance="@style/Body_Dark_Bold" />

    <io.bhex.app.skin.view.SkinPercentRelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/app_paddingLeft"
        android:paddingRight="@dimen/app_paddingRight"
        >

        <TextView
            android:id="@+id/title_available"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_16"
            android:layout_toRightOf="@id/title_coin_name"
            app:layout_widthPercent="35%"
            android:gravity="center_vertical"
            android:text="@string/string_available_margin"
            android:textAppearance="@style/Caption_Grey" />
        <TextView
            android:id="@+id/title_frozen"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_16"
            android:layout_toRightOf="@id/title_available"
            android:paddingLeft="@dimen/dip_8"
            app:layout_widthPercent="35%"
            android:gravity="center_vertical"
            android:text="@string/string_position_margin"
            android:textAppearance="@style/Caption_Grey" />
        <TextView
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_16"
            android:layout_toRightOf="@+id/title_frozen"
            app:layout_widthPercent="30%"
            android:gravity="center_vertical|right"
            android:text="@string/string_entrust_margin"
            android:textAppearance="@style/Caption_Grey" />

    </io.bhex.app.skin.view.SkinPercentRelativeLayout>
    <io.bhex.app.skin.view.SkinPercentRelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dip_8"
        android:paddingLeft="@dimen/app_paddingLeft"
        android:paddingRight="@dimen/app_paddingRight"
        >
        <TextView
            android:id="@+id/item_asset_available"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:singleLine="true"
            app:layout_widthPercent="35%"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark_Bold" />
        <TextView
            android:id="@+id/item_asset_position_margin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:layout_toRightOf="@id/item_asset_available"
            android:paddingLeft="@dimen/dip_8"
            app:layout_widthPercent="35%"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark_Bold" />
        <TextView
            android:id="@+id/item_asset_entrust_margin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:layout_alignParentRight="true"
            android:layout_toRightOf="@+id/item_asset_position_margin"
            app:layout_widthPercent="30%"
            android:layout_centerVertical="true"
            android:gravity="center_vertical|right"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark_Bold" />

    </io.bhex.app.skin.view.SkinPercentRelativeLayout>
</LinearLayout>