<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: include_otc_filter_ads_layout.xml
  ~   @Date: 19-1-15 下午4:15
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/color_bg_1"
    android:orientation="vertical">

    <!--<View-->
    <!--android:layout_width="match_parent"-->
    <!--android:layout_height="match_parent"-->
    <!--android:alpha="0.4"-->
    <!--android:background="@color/dark" />-->

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <io.bhex.app.view.TopBar
            android:id="@+id/filterTopBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:left_visiblity="gone"
            app:right_icon="@mipmap/icon_close"
            app:title_text="@string/string_otc_ads_filter" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/filterTopBar"
            android:background="@color/color_bg_2"
            android:orientation="vertical"
            android:paddingLeft="@dimen/app_paddingLeft"
            android:paddingTop="@dimen/dip_10"
            android:paddingRight="@dimen/app_paddingRight"
            android:paddingBottom="@dimen/dip_20">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_20"
                android:text="@string/string_otc_pay_way"
                android:textColor="@color/font_color1"
                android:textSize="@dimen/font_15" />

            <LinearLayout
                android:id="@+id/order_status_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_10"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <CheckBox
                        android:id="@+id/filter_all"
                        android:layout_width="@dimen/dip_90"
                        android:layout_height="@dimen/dip_30"
                        android:background="@drawable/otc_btn_filter_style"
                        android:button="@null"
                        android:checked="true"
                        android:gravity="center"
                        android:text="@string/string_all"
                        android:textColor="@color/otc_filter_text_color"
                        android:textSize="@dimen/font_13" />

                    <CheckBox
                        android:id="@+id/filter_union"
                        android:layout_width="@dimen/dip_90"
                        android:layout_height="@dimen/dip_30"
                        android:layout_marginLeft="@dimen/dip_10"
                        android:background="@drawable/otc_btn_filter_style"
                        android:button="@null"
                        android:gravity="center"
                        android:text="@string/string_pay_union"
                        android:textColor="@color/otc_filter_text_color"
                        android:textSize="@dimen/font_13" />

                    <CheckBox
                        android:id="@+id/filter_alipay"
                        android:layout_width="@dimen/dip_90"
                        android:layout_height="@dimen/dip_30"
                        android:layout_marginLeft="@dimen/dip_10"
                        android:background="@drawable/otc_btn_filter_style"
                        android:button="@null"
                        android:gravity="center"
                        android:text="@string/string_pay_alipay"
                        android:textColor="@color/otc_filter_text_color"
                        android:textSize="@dimen/font_13" />

                </LinearLayout>

                <CheckBox
                    android:id="@+id/filter_wechat"
                    android:layout_width="@dimen/dip_90"
                    android:layout_height="@dimen/dip_30"
                    android:layout_marginTop="@dimen/dip_8"
                    android:background="@drawable/otc_btn_filter_style"
                    android:button="@null"
                    android:gravity="center"
                    android:text="@string/string_pay_wechat"
                    android:textColor="@color/otc_filter_text_color"
                    android:textSize="@dimen/font_13" />
            </LinearLayout>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_20"
                android:text="@string/string_otc_legal_currency"
                android:textColor="@color/font_color1"
                android:textSize="@dimen/font_15" />
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/currencyRv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_12"
                android:layout_marginStart="-8dp"
                android:overScrollMode="always"
                app:fastScrollVerticalThumbDrawable="@style/Body_Dark"
                tools:listitem="@layout/item_text_layout"
                />
        </LinearLayout>


        <io.bhex.app.skin.view.SkinPercentRelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            >

            <Button
                android:id="@+id/btn_reset"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_corner_rect_line_gray"
                android:text="@string/string_reset"
                android:textAppearance="@style/Body_Grey_Bold"
                app:layout_widthPercent="50%" />

            <Button
                android:id="@+id/btn_complete"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:background="@color/blue"
                android:text="@string/string_complete"
                android:textAppearance="@style/Body_White_Default"
                app:layout_widthPercent="50%" />


        </io.bhex.app.skin.view.SkinPercentRelativeLayout>


    </RelativeLayout>
</FrameLayout>