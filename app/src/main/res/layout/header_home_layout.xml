<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: header_home_layout.xml
  ~   @Date: 19-4-25 下午3:35
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.youth.banner.Banner
        android:id="@+id/banner"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_175"
        android:layout_marginTop="@dimen/dip_0"
        app:banner_default_image="@mipmap/icon_default_banner"
        app:indicator_drawable_selected="@mipmap/page_dot_checked"
        app:indicator_drawable_unselected="@mipmap/page_dot"
        app:indicator_width="@dimen/dip_12"
        app:indicator_height="@dimen/dip_4"
        app:indicator_margin="@dimen/dip_4" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_40"
        >

        <ImageView
            android:id="@+id/iconNotice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/app_margin_left"
            android:src="@mipmap/icon_notice"/>
        <ImageView
            android:id="@+id/moreNotice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dip_4"
            android:layout_marginRight="@dimen/dip_8"
            android:src="@mipmap/icon_more_actions"
            android:visibility="visible"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:textAppearance="@style/BodyS_Grey"
            />
        <io.bhex.app.skin.view.SkinTextBannerView
            android:id="@+id/notice"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_40"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/iconNotice"
            android:layout_toLeftOf="@id/moreNotice"
            android:gravity="center_vertical"
            app:setInterval="6000"
            app:setAnimDuration="500"
            app:setSingleLine="true"
            app:setDirection="bottom_to_top"
            app:setTextColor="@color/dark"
            app:setTextSize="@dimen/font_12" />
    </RelativeLayout>
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:background="@color/divider_line_color" />

    <!--<<androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_click"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:text=""
        android:visibility="gone"
        android:textColor="@color/black"
        android:textSize="20sp"
        />-->

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/symbolsVp"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_110"
        android:visibility="gone"
        android:layout_gravity="center"
        android:clipChildren="false"
        android:layerType="software"
        android:focusable="false" />
    <LinearLayout
        android:id="@+id/circyle_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_10"
        android:visibility="gone"
        android:orientation="horizontal"
        android:layout_above="@+id/btn_start"
        android:layout_marginBottom="@dimen/dip_4"
        android:gravity="center"/>

    <View
        android:id="@+id/symbolsVp_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_8"
        android:visibility="gone"
        android:background="@color/divider_line_color" />

    //入金广告入口
    <ImageView
        android:id="@+id/home_ads_deposit"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_90"
        android:layout_marginTop="@dimen/dip_8"
        android:layout_marginStart="@dimen/app_margin_left"
        android:layout_marginEnd="@dimen/app_margin_right"
        android:src="@mipmap/ads_deposit"
        android:scaleType="fitXY"
        android:visibility="gone" />
    //8个广告位入口
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/home_ads_recyclerview"
        android:layout_marginTop="@dimen/dip_16"
        android:layout_marginStart="@dimen/dip_4"
        android:layout_marginEnd="@dimen/dip_4"
        android:layout_marginBottom="@dimen/dip_8"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        />
    <View
        android:id="@+id/home_ads_recyclerview_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_8"
        android:visibility="gone"
        android:background="@color/divider_line_color"
        />

    <TextView
        android:id="@+id/market_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/string_24h_rise_list"
        android:textAppearance="@style/BodyL_Dark_Bold"
        android:layout_marginStart="@dimen/app_margin_left"
        android:layout_marginTop="@dimen/dip_12"
        />
    <com.flyco.tablayout.CommonTabLayout
        android:id="@+id/tab"
        android:layout_width="wrap_content"
        android:layout_height="48dp"
        app:tl_tab_width="@dimen/dip_100"
        app:tl_tab_space_equal="false"
        app:tl_textSelectColor="@color/blue"
        app:tl_textUnselectColor="@color/dark50"
        app:tl_indicator_color="@color/blue"
        app:tl_iconVisible="false"
        app:tl_indicator_corner_radius="2dp"
        app:tl_indicator_height="@dimen/dip_2"
        app:tl_indicator_width="@dimen/dip_10"
        app:tl_textBold="SELECT"
        app:tl_textsize="@dimen/sp_16" />
    <View
        android:id="@+id/tab_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:background="@color/divider_line_color"
        />

</LinearLayout>