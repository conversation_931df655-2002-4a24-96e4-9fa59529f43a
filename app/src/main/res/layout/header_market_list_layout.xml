<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/sortLayoutView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dip_24"
        android:layout_marginRight="@dimen/dip_24"
        android:layout_marginBottom="@dimen/dip_1"
        android:layout_marginTop="@dimen/dip_10">

        <RelativeLayout
            android:id="@+id/radio_sort_vol"
            android:layout_width="@dimen/dip_96"
            android:layout_height="@dimen/dip_30"
            android:paddingBottom="@dimen/dip_1"
            >

            <ImageView
                android:id="@+id/sort_vol_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/dip_10"
                android:layout_marginLeft="@dimen/dip_2"
                android:src="@mipmap/icon_sort" />

            <TextView
                android:id="@+id/sort_vol"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:layout_toLeftOf="@+id/sort_vol_icon"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/string_24_volume"
                android:textAppearance="@style/Caption_Dark"/>



        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/radio_sort_price"
            android:layout_width="@dimen/dip_96"
            android:layout_height="@dimen/dip_30"
            android:layout_centerInParent="true"
            android:paddingBottom="@dimen/dip_1"
            >
            <ImageView
                android:id="@+id/sort_price_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/dip_8"
                android:layout_marginLeft="@dimen/dip_2"
                android:src="@mipmap/icon_sort" />
            <TextView
                android:id="@+id/sort_price"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_toLeftOf="@id/sort_price_icon"
                android:gravity="center"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/string_latest_price"
                android:textAppearance="@style/Caption_Dark"/>



        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/radio_sort_change"
            android:layout_width="@dimen/dip_96"
            android:layout_height="@dimen/dip_30"
            android:paddingBottom="@dimen/dip_1"
            android:layout_alignParentRight="true"
            >
            <ImageView
                android:id="@+id/sort_change_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/dip_8"
                android:layout_marginLeft="@dimen/dip_2"
                android:src="@mipmap/icon_sort" />
            <TextView
                android:id="@+id/sort_change"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_toLeftOf="@+id/sort_change_icon"
                android:gravity="center"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/string_change_rate"
                android:textAppearance="@style/Caption_Dark"/>



        </RelativeLayout>


    </RelativeLayout>
</LinearLayout>