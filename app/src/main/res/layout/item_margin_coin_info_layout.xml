<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout
        android:id="@+id/header_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintStart_toEndOf="parent"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_token"
            android:layout_width="@dimen/dip_0"
            android:layout_height="@dimen/dip_16"
            android:layout_weight="1"
            android:layout_marginTop="@dimen/dip_8"
            android:layout_marginRight="@dimen/dip_8"
            android:layout_marginLeft="@dimen/dip_24"
            android:layout_marginBottom="@dimen/dip_8"
            android:textColor="@color/third_text_color"
            android:textSize="@dimen/sp_12"
            android:text="BTC">

        </TextView>
        <TextView
            android:id="@+id/tv_remain_to_repay_value"
            android:layout_width="@dimen/dip_0"
            android:layout_height="@dimen/dip_16"
            android:layout_weight="1"
            android:layout_marginTop="@dimen/dip_8"
            android:layout_marginBottom="@dimen/dip_8"
            android:layout_marginRight="@dimen/dip_8"
            android:textColor="@color/dark"
            android:textSize="@dimen/sp_12"
            android:text="100.05253076">

        </TextView>
        <TextView
            android:id="@+id/tv_remain_interest_value"
            android:layout_width="@dimen/dip_0"
            android:layout_height="@dimen/dip_16"
            android:layout_weight="1"
            android:layout_marginTop="@dimen/dip_8"
            android:layout_marginBottom="@dimen/dip_8"
            android:layout_marginRight="@dimen/dip_16"
            android:textColor="@color/dark"
            android:textSize="@dimen/sp_12"
            android:text="1.05253076"
            android:gravity="right">

        </TextView>

    </LinearLayout>
    <View
        android:id="@+id/divider_line_recycler_header"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header_ll"
        android:layout_marginLeft="@dimen/dip_24"
        android:background="@color/divider_line_color">

    </View>
</androidx.constraintlayout.widget.ConstraintLayout>