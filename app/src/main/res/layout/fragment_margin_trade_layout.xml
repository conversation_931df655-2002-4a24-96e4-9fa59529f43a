<?xml version="1.0" encoding="utf-8"?>
<com.scwang.smartrefresh.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/refreshLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:srlEnableLoadMore="false"
    app:srlEnableNestedScrolling="true">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            app:elevation="@dimen/dip_0">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/divider_line_color"
                app:layout_scrollFlags="scroll|enterAlwaysCollapsed"
                app:statusBarScrim="@android:color/transparent">

                <RelativeLayout
                    android:id="@+id/margin_header_layout"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dip_40"
                    android:layout_marginTop="@dimen/dip_4"
                    android:layout_marginBottom="@dimen/dip_4"
                    android:background="@color/white">
                    <TextView
                        android:id="@+id/safety_title_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:textColor="@color/dark50"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="@dimen/dip_16"
                        android:text="@string/string_safety_title">

                    </TextView>


                    <TextView
                        android:id="@+id/safety_value_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dip_24"
                        android:gravity="center_vertical"
                        android:textAppearance="@style/Body_Green"
                        android:layout_toRightOf="@id/safety_title_tv"
                        android:textSize="@dimen/sp_12"
                        android:layout_marginLeft="@dimen/dip_4"
                        android:layout_centerVertical="true"
                        android:text="@string/string_placeholder"
                        tools:text="345.67%" />

                    <ImageView
                        android:id="@+id/safety_tip_img"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingLeft="@dimen/dip_12"
                        android:paddingRight="@dimen/dip_12"
                        android:paddingTop="@dimen/dip_10"
                        android:paddingBottom="@dimen/dip_10"
                        android:layout_toRightOf="@id/safety_value_tv"
                        android:src="@mipmap/icon_instruction" />

                    <LinearLayout
                        android:id="@+id/margin_replay_ll"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_toRightOf="@id/safety_tip_img"
                        android:gravity="right"
                        android:layout_centerVertical="true">

                        <TextView
                            android:id="@+id/margin_replay_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/dip_24"
                            android:layout_marginLeft="@dimen/dip_4"
                            android:background="@drawable/bg_corner_blue10"
                            android:paddingLeft="@dimen/dip_10"
                            android:paddingRight="@dimen/dip_10"
                            android:textSize="@dimen/sp_12"
                            android:textColor="@color/blue"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="@dimen/dip_16"
                            android:text="@string/string_margin_repay"
                            android:gravity="center_vertical"
                            tools:text="借币/还币" />
                    </LinearLayout>
                </RelativeLayout>
            </com.google.android.material.appbar.CollapsingToolbarLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <ImageView
                    android:id="@+id/settings"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBaseline="@id/topBar"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="@dimen/dip_10"
                    android:src="@mipmap/icon_more_actions" />

                <io.bhex.app.view.TopBar
                    android:id="@+id/topBar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:left_text=""
                    app:left_visiblity="gone"
                    app:right_text=""
                    app:title_text="@string/title_trade"
                    android:layout_toLeftOf="@id/settings"/>
            </RelativeLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/trade_scrollLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/socketTips"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/orange5"
                    android:textAppearance="@style/Caption_Dark"
                    android:visibility="gone" />

                <include
                    android:id="@+id/header_trade_layout"
                    layout="@layout/header_trade_layout" />
                <LinearLayout
                    android:id="@+id/secondTabLinear"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <com.flyco.tablayout.SegmentTabLayout
                        android:id="@+id/secondTab"
                        android:layout_width="match_parent"
                        android:layout_height="32dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginStart="@dimen/app_margin_left"
                        android:layout_marginTop="@dimen/dip_8"
                        android:layout_marginEnd="@dimen/app_margin_right"
                        app:tl_bar_color="@color/white"
                        app:tl_indicator_color="@color/blue"
                        app:tl_indicator_corner_radius="2dp"
                        app:tl_tab_padding="20dp"
                        app:tl_tab_space_equal="true" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dip_1"
                        android:layout_marginTop="@dimen/dip_8"
                        android:background="@color/divider_line_color" />
                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:overScrollMode="always"
                    app:fastScrollVerticalThumbDrawable="@style/Body_Dark"
                    tools:listitem="@layout/item_current_entrust_order__layout" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</com.scwang.smartrefresh.layout.SmartRefreshLayout>
