<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_6_corner"
    android:orientation="vertical">


    <LinearLayout
        android:id="@+id/layout_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/layout_bottom"
        android:layout_alignParentTop="true"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="24dp"
            android:layout_marginRight="24dp"
            android:layout_marginLeft="24dp"
            android:text="@string/margin_service_agreement"
            android:gravity="center"
            android:textColor="@color/dark"
            android:textSize="@dimen/sp_20" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_24"
            android:layout_gravity="center"
            android:layout_marginBottom="@dimen/dip_8"
            android:gravity="center"
            android:text="@string/margin_agreement_sub_title"
            android:textColor="@color/dark50"
            android:textSize="@dimen/sp_12" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:overScrollMode="never"
            >


        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="left"
            android:layout_marginStart="@dimen/dip_24"
            android:layout_marginTop="@dimen/dip_8"
            android:layout_marginEnd="@dimen/dip_24"
            android:lineHeight="20dp"
            android:paddingTop="2dp"
            android:paddingBottom="@dimen/dip_16"
            android:text="@string/margin_agreement_context"
            android:textColor="@color/dark"
            android:textSize="@dimen/sp_12" />
        </ScrollView>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/layout_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical">

        <CheckedTextView
            android:id="@+id/check_agreement"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_24"
            android:layout_gravity="center"
            android:gravity="center"
            android:layout_marginTop="@dimen/dip_16"
            android:drawableLeft="@drawable/checkbox_style"
            android:drawablePadding="4dp"
            android:layout_marginBottom="20dp"
            android:text="@string/is_agree_text"
            android:textAlignment="center"
            android:textColor="@color/dark"
            android:textSize="@dimen/sp_12" />


        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_40"
            android:layout_marginStart="@dimen/dip_16"
            android:layout_marginEnd="@dimen/dip_16"
            android:background="@drawable/btn_bg_selector"
            android:gravity="center"
            android:enabled="false"
            android:text="@string/string_confirm_open"
            android:textColor="@color/btn_blue_or_gray_text_color"
            android:textSize="@dimen/sp_14"
            android:textStyle="bold"/>


        <TextView
            android:id="@+id/btn_cancel"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_40"
            android:layout_marginStart="@dimen/dip_16"
            android:layout_marginEnd="@dimen/dip_16"
            android:layout_marginBottom="@dimen/dip_16"
            android:layout_marginTop="@dimen/dip_8"
            android:gravity="center"
            android:background="@drawable/btn_white_selector"
            android:text="@string/string_cancel_open"
            android:textColor="@color/secondary_text_color"
            android:textSize="@dimen/sp_14"
            android:textStyle="bold"/>
    </LinearLayout>

</RelativeLayout>