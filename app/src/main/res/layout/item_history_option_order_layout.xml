<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/item_style"
    android:orientation="vertical"
    android:paddingLeft="@dimen/app_paddingLeft">


    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_16"
        android:layout_marginRight="@dimen/app_margin_right">

        <TextView
            android:id="@+id/order_buy_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark" />
        <TextView
            android:id="@+id/order_coin_name_point"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@id/order_buy_type"
            android:layout_centerVertical="true"
            android:text="@string/string_option_point"
            android:textAppearance="@style/Body_Dark" />
        <TextView
            android:id="@+id/order_coin_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@id/order_coin_name_point"
            android:layout_centerVertical="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark" />

        <TextView
            android:id="@+id/order_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Caption_Grey" />


    </RelativeLayout>

    <io.bhex.app.skin.view.SkinPercentRelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_8">


        <LinearLayout
            android:id="@+id/linear1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:orientation="vertical"
            app:layout_widthPercent="30%">

            <TextView
                android:id="@+id/order_price_title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_16"
                android:gravity="center_vertical"
                android:text="@string/string_name_price"
                android:textAppearance="@style/BodyS_Grey" />

            <TextView
                android:id="@+id/order_deal_price_title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_16"
                android:gravity="center_vertical"
                android:text="@string/string_format_deal_average_price"
                android:textAppearance="@style/BodyS_Grey" />

            <TextView
                android:id="@+id/order_entrust_amount_title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_16"
                android:gravity="center_vertical"
                android:text="@string/string_name_entrust_amount"
                android:textAppearance="@style/BodyS_Grey" />

            <TextView
                android:id="@+id/order_deal_amount_title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_16"
                android:gravity="center_vertical"
                android:text="@string/string_name_amount_deal"
                android:textAppearance="@style/BodyS_Grey" />


        </LinearLayout>

        <LinearLayout
            android:id="@+id/linear2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@+id/linear1"
            android:orientation="vertical"
            app:layout_widthPercent="70%">


            <TextView
                android:id="@+id/order_price"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_16"
                android:gravity="center_vertical"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Dark" />
            <TextView
                android:id="@+id/order_deal_price"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_16"
                android:gravity="center_vertical"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Dark" />

            <TextView
                android:id="@+id/order_entrust_amount"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_16"
                android:gravity="center_vertical"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Dark" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/order_deal_amount"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="@dimen/dip_16"
                    android:gravity="center_vertical"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/BodyS_Dark" />
                <TextView
                    android:id="@+id/order_deal_status"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dip_16"
                    android:gravity="center_vertical"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:layout_marginRight="@dimen/app_margin"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/BodyS_Dark" />
            </LinearLayout>

        </LinearLayout>

    </io.bhex.app.skin.view.SkinPercentRelativeLayout>


    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/app_line"
        android:layout_marginTop="@dimen/dip_10"
        android:background="@color/divider_line_color" />

</LinearLayout>