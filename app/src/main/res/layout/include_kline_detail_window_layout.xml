<?xml version="1.0" encoding="utf-8"?>
<!--<androidx.constraintlayout.widget.ConstraintLayout -->
    <!--android:layout_width="match_parent"-->
    <!--android:layout_height="match_parent">-->
    <RelativeLayout
        android:id="@+id/left_window"
        android:layout_width="90dp"
        android:padding="5dp"
        android:layout_height="wrap_content"
        android:background="@drawable/kline_detail_window_bg"
        xmlns:android="http://schemas.android.com/apk/res/android">

        <TextView
            android:id="@+id/shijianTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:text="@string/string_time"
            android:textColor="@color/font_color2"
            android:textSize="9dp" />

        <TextView
            android:id="@+id/shijian"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="3dp"
            android:textColor="@color/white"
            android:textSize="9dp" />

        <TextView
            android:id="@+id/jiageTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/shijianTv"
            android:layout_marginTop="3dp"
            android:text="@string/string_price"
            android:textColor="@color/font_color1"
            android:textSize="9dp" />

        <TextView
            android:id="@+id/jiage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_below="@id/shijian"
            android:layout_marginTop="3dp"
            android:textColor="@color/white"
            android:textSize="9dp" />

        <TextView
            android:id="@+id/junjiaTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/jiageTv"
            android:layout_marginTop="3dp"
            android:text="@string/string_average_price"
            android:textColor="@color/font_color2"
            android:textSize="9dp" />

        <TextView
            android:id="@+id/junjia"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_below="@+id/jiage"
            android:layout_marginTop="3dp"
            android:textColor="#56c156"
            android:textSize="9dp" />

        <TextView
            android:id="@+id/zhangdieeTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/junjiaTv"
            android:layout_marginTop="3dp"
            android:text="@string/string_change_amount"
            android:textColor="@color/font_color2"
            android:textSize="9dp" />

        <TextView
            android:id="@+id/zhangdiee"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_below="@+id/junjia"
            android:layout_marginTop="3dp"
            android:textColor="#56c156"
            android:textSize="9dp" />

        <TextView
            android:id="@+id/zhangdiefuTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/zhangdieeTv"
            android:layout_marginTop="3dp"
            android:text="@string/string_change_rate"
            android:textColor="@color/font_color2"
            android:textSize="9dp" />

        <TextView
            android:id="@+id/zhangdiefu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_below="@+id/zhangdiee"
            android:layout_marginTop="3dp"
            android:textColor="#56c156"
            android:textSize="9dp" />

        <TextView
            android:id="@+id/chengjiaoeTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/zhangdiefuTv"
            android:layout_marginTop="3dp"
            android:text="@string/string_deal_amount_of_money"
            android:textColor="@color/font_color2"
            android:textSize="9dp" />

        <TextView
            android:id="@+id/chengjiaoe"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_below="@+id/zhangdiefu"
            android:textColor="@color/white"
            android:layout_marginTop="3dp"
            android:textSize="9dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/chengjiaoe"
            android:layout_marginTop="3dp"
            android:text="@string/string_deal_amount"
            android:textColor="@color/font_color2"
            android:textSize="9dp" />

        <TextView
            android:id="@+id/chengjiaoliang"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_below="@+id/chengjiaoe"
            android:textColor="@color/white"
            android:layout_marginBottom="3dp"
            android:layout_marginTop="3dp"
            android:textSize="9dp" />
    </RelativeLayout>
<!--</androidx.constraintlayout.widget.ConstraintLayout>-->