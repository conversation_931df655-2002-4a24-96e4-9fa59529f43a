<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/asset_rela"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/dip_24"
        android:paddingTop="@dimen/dip_16"
        android:paddingBottom="@dimen/app_paddingBottom"
        android:background="@color/white"
        android:orientation="vertical">

        <CheckBox
            android:id="@+id/asset_eye"
            android:layout_width="@dimen/dip_25"
            android:layout_height="@dimen/dip_20"
            android:layout_alignParentRight="true"
            android:background="@null"
            android:button="@drawable/btn_show"
            android:checked="true" />

        <TextView
            android:id="@+id/asset_trade_account"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_24"
            android:layout_below="@+id/asset_eye"
            android:gravity="center_vertical"
            android:text="@string/string_trade_account"
            android:textAppearance="@style/Body_Dark_Bold" />

        <TextView
            android:id="@+id/asset_total_txt"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_16"
            android:layout_below="@+id/asset_trade_account"
            android:gravity="center_vertical"
            android:text="@string/string_total_asset_about"
            android:textAppearance="@style/Caption_Grey"
            />

        <TextView
            android:id="@+id/asset_total"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/asset_total_txt"
            android:layout_marginTop="@dimen/dip_8"
            android:gravity="center_vertical"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/H2_Blue"/>

        <TextView
            android:id="@+id/asset_total_currency"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_16"
            android:layout_below="@+id/asset_total"
            android:gravity="center_vertical"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Caption_Blue"/>

    </RelativeLayout>
</LinearLayout>