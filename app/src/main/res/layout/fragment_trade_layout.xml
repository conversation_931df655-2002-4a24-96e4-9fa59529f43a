<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
<LinearLayout
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <io.bhex.app.view.TopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:left_text=""
            app:left_visiblity="gone"
            app:right_text=""
            app:title_text="@string/title_trade" />

        <RelativeLayout
            android:id="@+id/rela_search"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_68"
            android:paddingLeft="@dimen/dip_24"
            android:paddingRight="@dimen/dip_24"
            android:paddingTop="@dimen/dip_18"
            android:visibility="gone">

            <EditText
                android:id="@+id/edit_search"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_30"
                android:layout_marginTop="@dimen/dip_10"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="@string/string_hint_search"
                android:maxLines="1"
                android:textAppearance="@style/BodyL_Dark"
                android:textColor="@color/dark"
                android:textColorHint="@color/dark50" />

            <ImageView
                android:id="@+id/close_search"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:padding="@dimen/dip_10"
                android:src="@mipmap/icon_close" />

        </RelativeLayout>

    </RelativeLayout>
    <TextView
        android:id="@+id/socketTips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:textAppearance="@style/Caption_Dark"
        android:background="@color/orange5"/>

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:srlEnableNestedScrolling="true"
        app:srlEnableLoadMore="false">
        <io.bhex.app.view.CustomFreshHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_0"
            app:srlFinishDuration="500"
            app:srlEnableLastTime="true"
            app:srlClassicsSpinnerStyle="FixedBehind"
            app:srlTextSizeTitle="16sp"
            app:srlTextSizeTime="10dp"
            app:srlTextTimeMarginTop="2dp"
            app:srlDrawableArrowSize="20dp"
            app:srlDrawableProgressSize="20dp"
            app:srlDrawableMarginRight="20dp"
            />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/trade_scrollLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <include
                android:id="@+id/header_trade_layout"
                layout="@layout/header_trade_layout" />

            <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:overScrollMode="never"
            app:fastScrollVerticalThumbDrawable="@style/Body_Dark"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:listitem="@layout/item_current_entrust_order__layout"
            />
        </LinearLayout>
        </androidx.core.widget.NestedScrollView>
        <com.scwang.smartrefresh.layout.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </com.scwang.smartrefresh.layout.SmartRefreshLayout>
</LinearLayout>

</RelativeLayout>