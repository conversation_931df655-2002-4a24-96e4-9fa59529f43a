<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_invite_record_layout.xml
  ~   @Date: 19-8-26 下午3:41
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    >
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginRight="@dimen/app_margin_right"
        android:layout_marginTop="@dimen/dip_12"
        android:layout_marginBottom="@dimen/dip_8"
        android:orientation="vertical">
        <TextView
            android:id="@+id/invite_form"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_24"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark_Bold"/>

        <TextView
            android:id="@+id/invite_friend"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_24"
            android:layout_alignParentRight="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark_Bold"/>
        <TextView
            android:id="@+id/invite_date"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_24"
            android:layout_below="@id/invite_form"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Caption_Grey"/>

        <TextView
            android:id="@+id/invite_status"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_24"
            android:layout_alignParentRight="true"
            android:layout_below="@id/invite_form"
            android:visibility="gone"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Grey"/>

    </RelativeLayout>
    <View
        android:id="@+id/item_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:background="@color/divider_line_color"
        />

</LinearLayout>