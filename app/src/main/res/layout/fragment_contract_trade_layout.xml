<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dip_0"
        android:orientation="vertical"
        >
        <com.flyco.tablayout.SegmentTabLayout
            android:id="@+id/tab"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_32"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dip_8"
            android:layout_marginBottom="@dimen/dip_8"
            app:tl_tab_space_equal="false"
            app:tl_indicator_color="@color/blue"
            app:tl_indicator_corner_radius="@dimen/dip_2"
            app:tl_tab_padding="@dimen/dip_8"
            app:tl_tab_width="@dimen/dip_120"
            app:tl_textSelectColor="@color/white"
            app:tl_textUnselectColor="@color/dark"
            app:tl_textsize="@dimen/sp_12"
            app:tl_textBold="BOTH"/>

        <!--<io.bhex.app.skin.view.SkinTabLayout-->
        <!--android:id="@+id/tab"-->
        <!--android:layout_width="match_parent"-->
        <!--android:layout_height="@dimen/dip_40"-->
        <!--app:tabIndicatorColor="@color/blue"-->
        <!--app:tabIndicatorHeight="@dimen/dip_0"-->
        <!--app:tabIndicatorFullWidth="false"-->
        <!--app:tabMinWidth="@dimen/dip_50"-->
        <!--app:tabMode="scrollable"-->
        <!--app:tabSelectedTextColor="@color/blue"-->
        <!--app:tabTextAppearance="@style/BodyL_Dark_Bold"-->
        <!--app:tabTextColor="@color/dark" />-->
        <View
            android:id="@+id/tabline"
            android:layout_width="match_parent"
            android:layout_height="@dimen/app_line"
            android:background="@color/divider_line_color"
            />

        <io.bhex.app.view.NoScrollViewPager
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    </LinearLayout>

</RelativeLayout>