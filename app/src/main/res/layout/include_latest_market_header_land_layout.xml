<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/layout_land_ticker"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_45"
    android:orientation="horizontal"
    android:paddingLeft="@dimen/dip_10"
    android:paddingRight="@dimen/dip_10"
    android:background="@color/color_bg_2_night"
    >

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_centerVertical="true"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/coinPairName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dip_6"
            android:layout_alignParentBottom="true"
            android:textAppearance="@style/Body_Dark_Bold_night"
            android:text="@string/string_placeholder" />
        <TextView
            android:id="@+id/latestPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_toRightOf="@+id/coinPairName"
            android:textAppearance="@style/Body_Green_night"
            android:text="@string/string_placeholder" />
        <ImageView
            android:id="@+id/riseFallIcon"
            android:layout_width="@dimen/dip_8"
            android:layout_height="@dimen/dip_20"
            android:layout_marginLeft="@dimen/dip_2"
            android:layout_alignParentBottom="true"
            android:layout_toRightOf="@+id/latestPrice"
            android:layout_gravity="center_vertical"
            android:background="@mipmap/icon_rise"
            android:visibility="gone"
            />
        <TextView
            android:id="@+id/latestPrice2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_toRightOf="@id/riseFallIcon"
            android:layout_marginLeft="@dimen/dip_8"
            android:textAppearance="@style/Body_Green_night"
            android:visibility="gone"
            android:text="￥ --" />

        <TextView
            android:id="@+id/riseFallAmount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_toRightOf="@+id/latestPrice2"
            android:layout_marginLeft="@dimen/dip_8"
            android:textAppearance="@style/Caption_Grey_night"
            android:visibility="gone"
            android:text="@string/string_placeholder" />
        <TextView
            android:id="@+id/riseFallRatio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="@dimen/dip_8"
            android:paddingBottom="3dp"
            android:paddingTop="3dp"
            android:paddingStart="5dp"
            android:paddingEnd="5dp"
            android:layout_toRightOf="@+id/riseFallAmount"
            android:textAppearance="@style/Body_White_Default_12"
            android:text="@string/string_placeholder" />

    </LinearLayout>

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        >
        <LinearLayout
            android:id="@+id/tab_verticalscreen"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:paddingLeft="@dimen/app_paddingLeft"
            android:paddingRight="@dimen/dip_8"
            android:paddingTop="@dimen/dip_5"
            android:paddingBottom="@dimen/dip_5"
            android:orientation="horizontal"
            >
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/icon_close_white"
                />

        </LinearLayout>


        <TextView
            android:id="@+id/lowPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toLeftOf="@+id/tab_verticalscreen"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/dip_15"
            android:layout_marginLeft="@dimen/dip_2"
            android:textAppearance="@style/Caption_Dark_night"
            android:text="@string/string_placeholder" />
        <TextView
            android:id="@+id/lowPriceTxt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@+id/lowPrice"
            android:textAppearance="@style/Caption_Grey_night"
            android:text="@string/string_low" />

        <TextView
            android:id="@+id/highPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_toLeftOf="@id/lowPriceTxt"
            android:layout_marginRight="@dimen/dip_15"
            android:layout_marginLeft="@dimen/dip_2"
            android:textAppearance="@style/Caption_Dark_night"
            android:text="@string/string_placeholder" />
        <TextView
            android:id="@+id/highPriceTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@+id/highPrice"
            android:textAppearance="@style/Caption_Grey_night"
            android:text="@string/string_high" />

        <TextView
            android:id="@+id/volume"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dip_15"
            android:layout_marginLeft="@dimen/dip_2"
            android:visibility="visible"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/highPriceTitle"
            android:textAppearance="@style/Caption_Dark_night"
            android:text="@string/string_placeholder" />
        <TextView
            android:id="@+id/volume_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dip_16"
            android:visibility="visible"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@+id/volume"
            android:textAppearance="@style/Caption_Grey_night"
            android:text="@string/string_24H" />

    </RelativeLayout>

</RelativeLayout>