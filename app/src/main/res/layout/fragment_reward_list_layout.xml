<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: fragment_invite_records_layout.xml
  ~   @Date: 19-8-26 下午3:33
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<com.scwang.smartrefresh.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/swipeRefresh"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="gone"
    app:srlEnableLoadMore="false">

    <io.bhex.app.view.CustomFreshHeader
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_0"
        app:srlClassicsSpinnerStyle="FixedBehind"
        app:srlDrawableArrowSize="20dp"
        app:srlDrawableMarginRight="20dp"
        app:srlDrawableProgressSize="20dp"
        app:srlEnableLastTime="true"
        app:srlFinishDuration="500"
        app:srlTextSizeTime="10dp"
        app:srlTextSizeTitle="16sp"
        app:srlTextTimeMarginTop="2dp" />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        >

        <RelativeLayout
            android:id="@+id/topTips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_circle_blue"
            android:layout_marginLeft="@dimen/app_margin_left"
            android:layout_marginRight="@dimen/app_margin_right"
            android:layout_marginTop="@dimen/dip_8"
            android:padding="@dimen/dip_8"
            >
            <ImageView
                android:id="@+id/closeBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/icon_close_blue"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:padding="@dimen/dip_8"
                />
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toLeftOf="@id/closeBtn"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/dip_8"
                android:textAppearance="@style/Body_Blue"
                android:text="@string/string_reward_return_time_tips"
                />
        </RelativeLayout>


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            tools:listitem="@layout/item_invitation_reward_layout" />

    </LinearLayout>

    <com.scwang.smartrefresh.layout.footer.ClassicsFooter
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
</com.scwang.smartrefresh.layout.SmartRefreshLayout>