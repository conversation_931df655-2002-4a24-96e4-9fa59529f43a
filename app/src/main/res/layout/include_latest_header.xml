<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40"
        android:gravity="center_vertical"
        android:paddingRight="@dimen/dip_12"
        android:paddingLeft="@dimen/dip_12">

        <TextView
            android:id="@+id/title_market"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:text="@string/string_deal_time"
            android:textAppearance="@style/Caption_Grey_night"/>
        <TextView
            android:id="@+id/title_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:text="@string/string_trade_direction"
            android:textAppearance="@style/Caption_Grey_night"/>

        <TextView
            android:id="@+id/title_deal_price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:gravity="center"
            android:text="@string/string_price_ph"
            android:textAppearance="@style/Caption_Grey_night"/>

        <TextView
            android:id="@+id/title_deal_amount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:gravity="right"
            android:text="@string/string_deal_amount"
            android:textAppearance="@style/Caption_Grey_night"/>

    </LinearLayout>
</LinearLayout>