<?xml version="1.0" encoding="utf-8"?>
<io.bhex.app.bottom.LottieTabLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/tab_lottie_layout"
    android:layout_width="match_parent"
    android:layout_height="49dp"
    android:background="@color/dark1"
    android:orientation="horizontal"
    android:layout_alignParentBottom="true"
    >

    <io.bhex.app.bottom.LottieTabView
        android:id="@+id/main_home_tab"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        app:icon_normal="@mipmap/tab_ic_home"
        app:tab_name="@string/app_channel_home"/>
<!--        app:lottie_path="tab_home_light.json"-->
<!--        />-->

    <io.bhex.app.bottom.LottieTabView
        android:id="@+id/main_market_tab"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        app:icon_normal="@mipmap/tab_ic_market"
        app:tab_name="@string/app_channel_market"/>
<!--        app:lottie_path="tab_hangqing_light.json"-->
<!--        />-->

    <io.bhex.app.bottom.LottieTabView
        android:id="@+id/main_trade_tab"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        app:icon_normal="@mipmap/tab_ic_trade"
        app:tab_name="@string/app_channel_trade"/>
<!--        app:lottie_path="tab_trade_light.json"-->
<!--        />-->

    <io.bhex.app.bottom.LottieTabView
        android:id="@+id/main_contract_trade_tab"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        app:icon_normal="@mipmap/tab_ic_contract"
        app:tab_name="@string/string_contract"/>
<!--        app:lottie_path="tab_contract_light.json"-->
<!--        />-->

    <io.bhex.app.bottom.LottieTabView
        android:id="@+id/main_asset"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        app:icon_normal="@mipmap/tab_ic_asset"
        app:tab_name="@string/string_my_asset"
        app:tab_login="true"/>
<!--        app:lottie_path="tab_asset_light.json"-->
<!--        />-->

</io.bhex.app.bottom.LottieTabLayout>