<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: fragment_pointcard_detail_layout.xml
  ~   @Date: 18-12-14 下午5:33
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/swipeRefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:srlEnableLoadMore="false">
        <io.bhex.app.view.CustomFreshHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_0"
            app:srlFinishDuration="500"
            app:srlEnableLastTime="true"
            app:srlClassicsSpinnerStyle="FixedBehind"
            app:srlTextSizeTitle="16sp"
            app:srlTextSizeTime="10dp"
            app:srlTextTimeMarginTop="2dp"
            app:srlDrawableArrowSize="20dp"
            app:srlDrawableProgressSize="20dp"
            app:srlDrawableMarginRight="20dp"
            />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            />
        <com.scwang.smartrefresh.layout.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>