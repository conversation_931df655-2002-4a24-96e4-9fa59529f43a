<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_divier"
        android:background="@color/bgColor_divier" />
    <LinearLayout
        android:id="@+id/item_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center">

        <TextView
            android:id="@+id/tvAlert"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/height_alert_button"
            android:gravity="center"
            android:textColor="@color/textColor_alert_button_others"
            android:textStyle="bold"
            tools:text="BTC"
            android:textSize="@dimen/textSize_alert_button" />


        <TextView
            android:id="@+id/leverage_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@color/blue10"
            android:layout_marginLeft="@dimen/dip_4"
            android:paddingTop="@dimen/dip_2"
            android:paddingBottom="@dimen/dip_2"
            android:paddingLeft="@dimen/dip_8"
            android:paddingRight="@dimen/dip_8"
            android:text="@string/string_placeholder"
            android:textStyle="bold"
            android:textColor="@color/blue"
            android:textSize="@dimen/sp_10"/>
    </LinearLayout>

</LinearLayout>