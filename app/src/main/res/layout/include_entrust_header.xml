<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_40"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:paddingRight="@dimen/dip_12"
        android:paddingLeft="@dimen/dip_12">

        <TextView
            android:id="@+id/title_buy_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableRight="@mipmap/icon_arrow_down_gray"
            android:text="@string/string_amount_buy_ph"
            android:textAppearance="@style/Caption_Grey_night"/>

        <TextView
            android:id="@+id/title_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/string_price_ph"
            android:textAppearance="@style/Caption_Grey_night"/>

        <TextView
            android:id="@+id/title_sell_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:drawableRight="@mipmap/icon_arrow_down_gray"
            android:text="@string/string_amount_sell_ph"
            android:textAppearance="@style/Caption_Grey_night"/>

    </RelativeLayout>
</LinearLayout>