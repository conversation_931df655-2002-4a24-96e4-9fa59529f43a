<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <io.bhex.app.view.TopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_58"
        app:title_text="@string/app_name"
        app:left_icon="@null"
        app:left_text="@string/title_my"
        app:divider_visiblity="visible"
        app:right_icon="@mipmap/icon_search"
        />

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/smartRefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:descendantFocusability="blocksDescendants"
        app:srlEnableLoadMore="false">

        <io.bhex.app.view.CustomFreshHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_24"
            app:srlClassicsSpinnerStyle="FixedBehind"
            app:srlDrawableArrowSize="20dp"
            app:srlDrawableMarginRight="20dp"
            app:srlDrawableProgressSize="20dp"
            app:srlEnableLastTime="true"
            app:srlFinishDuration="500"
            app:srlTextSizeTime="10dp"
            app:srlTextSizeTitle="16sp"
            app:srlTextTimeMarginTop="2dp" />
        <androidx.core.widget.NestedScrollView
            android:id="@+id/trade_scrollLayout"
            android:fillViewport="true"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="always">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">
                <include
                    android:id="@+id/homeHeader"
                    layout="@layout/header_home_layout"/>

                <FrameLayout
                    android:id="@+id/fragment_container"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    />

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
        <com.scwang.smartrefresh.layout.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </com.scwang.smartrefresh.layout.SmartRefreshLayout>
</LinearLayout>