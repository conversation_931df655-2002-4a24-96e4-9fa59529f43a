<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_futures_history_trade_order_layout.xml
  ~   @Date: 19-7-26 下午6:54
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/item_style"
    android:orientation="vertical"
    android:paddingLeft="@dimen/app_paddingLeft">

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/app_line"
        android:background="@color/divider_line_color" />

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_16"
        android:layout_marginRight="@dimen/app_margin_right">

        <TextView
            android:id="@+id/order_buy_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark" />
        <TextView
            android:id="@+id/order_coin_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@id/order_buy_type"
            android:layout_marginLeft="@dimen/dip_16"
            android:layout_centerVertical="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark" />

        <TextView
            android:id="@+id/order_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Caption_Grey" />


    </RelativeLayout>

    <io.bhex.app.skin.view.SkinPercentRelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_8">

        <LinearLayout
            android:id="@+id/linear1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:orientation="vertical"
            app:layout_widthPercent="30%">

            <TextView
                android:id="@+id/order_price_title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_16"
                android:gravity="center_vertical"
                android:text="@string/string_name_price"
                android:textAppearance="@style/BodyS_Grey" />

            <TextView
                android:id="@+id/order_entrust_amount_title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_16"
                android:gravity="center_vertical"
                android:text="@string/string_name_amount"
                android:textAppearance="@style/BodyS_Grey" />
            <TextView
                android:id="@+id/order_profit_loss_title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_16"
                android:gravity="center_vertical"
                android:text="@string/string_profit_loss"
                android:textAppearance="@style/BodyS_Grey" />

            <TextView
                android:id="@+id/order_deal_amount_title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_16"
                android:gravity="center_vertical"
                android:visibility="gone"
                android:text="@string/string_order_deal_money_point"
                android:textAppearance="@style/BodyS_Grey" />
            <TextView
                android:id="@+id/order_fee_title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_16"
                android:gravity="center_vertical"
                android:text="@string/string_fee_point"
                android:textAppearance="@style/BodyS_Grey" />


        </LinearLayout>

        <LinearLayout
            android:id="@+id/linear2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@+id/linear1"
            android:orientation="vertical"
            app:layout_widthPercent="40%">


            <TextView
                android:id="@+id/order_price"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_16"
                android:gravity="center_vertical"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Dark" />

            <TextView
                android:id="@+id/order_entrust_amount"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_16"
                android:gravity="center_vertical"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Dark" />
            <TextView
                android:id="@+id/order_profit_loss"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_16"
                android:gravity="center_vertical"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Dark" />


            <TextView
                android:id="@+id/order_deal_amount"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_16"
                android:gravity="center_vertical"
                android:ellipsize="end"
                android:maxLines="1"
                android:visibility="gone"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Dark" />

            <TextView
                android:id="@+id/order_fee"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_16"
                android:gravity="center_vertical"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Dark" />


        </LinearLayout>

    </io.bhex.app.skin.view.SkinPercentRelativeLayout>
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:background="@color/divider_line_color"
        android:layout_marginTop="@dimen/dip_8"
        />

</LinearLayout>