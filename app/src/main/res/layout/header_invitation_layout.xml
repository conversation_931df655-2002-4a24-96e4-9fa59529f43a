<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: header_invitation_layout.xml
  ~   @Date: 19-1-23 上午11:58
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/top_poster"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scaleType="fitXY"
            android:background="@mipmap/img_header_poster"
            android:gravity="center"
            android:textAppearance="@style/H3_White" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_60"
            android:layout_marginLeft="@dimen/app_margin_left"
            >

            <TextView
                android:id="@+id/invite_total_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/string_invite_total_num"
                android:textAppearance="@style/BodyL_Dark_Bold" />

            <TextView
                android:id="@+id/invite_total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/dip_8"
                android:layout_toRightOf="@id/invite_total_title"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyL_Blue_Bold" />

            <ImageView
                android:id="@+id/arrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="visible"
                android:paddingRight="@dimen/dip_8"
                android:paddingLeft="@dimen/dip_30"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:src="@mipmap/icon_more_arrow" />

            <TextView
                android:id="@+id/btn_invite_detail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="visible"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/dip_0"
                android:layout_toLeftOf="@id/arrow"
                android:text="  "
                android:textAppearance="@style/BodyS_Grey" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_1"
            android:layout_marginLeft="@dimen/app_margin_left"
            android:background="@color/divider_line_color" />

        <androidx.percentlayout.widget.PercentRelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/app_margin_left"
            android:layout_marginRight="@dimen/app_margin_right"
            android:paddingTop="@dimen/dip_16"
            android:paddingBottom="@dimen/dip_16">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_widthPercent="50%">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="@string/string_invite_direct_num"
                    android:textAppearance="@style/Body_Grey" />

                <TextView
                    android:id="@+id/invite_direct_num"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="@dimen/dip_8"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/Body_Dark_Bold" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:orientation="vertical"
                app:layout_widthPercent="50%">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="@string/string_invite_indirect_num"
                    android:textAppearance="@style/Body_Grey" />

                <TextView
                    android:id="@+id/invite_indirect_num"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="@dimen/dip_8"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/Body_Dark_Bold" />

            </LinearLayout>

        </androidx.percentlayout.widget.PercentRelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_8"
            android:background="@color/divider_line_color" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/title_reward_detail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/app_margin_left"
                android:layout_marginTop="@dimen/dip_16"
                android:layout_marginBottom="@dimen/dip_12"
                android:layout_marginRight="@dimen/app_margin_right"
                android:lineSpacingExtra="@dimen/dip_4"
                android:text="@string/string_title_reward_detail"
                android:textAppearance="@style/Body_Dark_Bold" />

            <ImageView
                android:id="@+id/arrow_reward"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="visible"
                android:paddingRight="@dimen/dip_8"
                android:paddingLeft="@dimen/dip_30"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:src="@mipmap/icon_more_arrow" />

            <TextView
                android:id="@+id/btn_reward_detail_more"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="visible"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/dip_0"
                android:layout_toLeftOf="@id/arrow_reward"
                android:text="  "
                android:textAppearance="@style/BodyS_Grey"/>

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_1"
            android:layout_marginLeft="@dimen/app_margin_left"
            android:background="@color/divider_line_color" />

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>