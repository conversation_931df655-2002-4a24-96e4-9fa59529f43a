<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/titleRela"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginLeft="@dimen/app_margin"
        android:background="@color/divider_line_color" />

    <!-- 顶部参考值 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_10"
        android:layout_marginBottom="@dimen/dip_10"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/app_margin"
        android:paddingRight="@dimen/app_margin">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_alignParentLeft="true"
            android:layout_weight="1.1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/reference_title1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:padding="@dimen/dip_2"
                android:singleLine="true"
                android:text="@string/string_settle_time"
                android:textAppearance="@style/BodyS_Grey" />

            <TextView
                android:id="@+id/reference_value1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:padding="@dimen/dip_2"
                android:text="@string/string_placeholder"
                android:singleLine="true"
                android:textAppearance="@style/BodyS_Dark_Bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_alignParentLeft="true"
            android:layout_weight="1.2"
            android:orientation="vertical">

            <TextView
                android:id="@+id/reference_title2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:padding="@dimen/dip_2"
                android:singleLine="true"
                android:text="@string/string_funding_rate"
                android:textAppearance="@style/BodyS_Grey" />

            <TextView
                android:id="@+id/reference_value2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:singleLine="true"
                android:padding="@dimen/dip_2"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Dark_Bold" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="right"
            android:orientation="vertical">

            <TextView
                android:id="@+id/reference_title3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:padding="@dimen/dip_2"
                android:text="@string/string_exercise_point"
                android:textAppearance="@style/BodyS_Grey" />

            <TextView
                android:id="@+id/reference_value3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:gravity="right"
                android:padding="@dimen/dip_2"
                android:singleLine="true"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Dark_Bold" />
        </LinearLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_8"
        android:background="@color/divider_line_color" />

</LinearLayout>