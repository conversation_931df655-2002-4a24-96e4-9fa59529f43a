<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_futures_stop_entrust_order_layout.xml
  ~   @Date: 19-8-8 下午7:32
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:paddingTop="@dimen/dip_16"
    android:id="@+id/itemView"
    >

    <RelativeLayout
        android:id="@+id/titleRela"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginEnd="@dimen/app_margin_right"
        android:layout_marginStart="@dimen/app_margin_left">
        <TextView
            android:id="@+id/orderSideAndLever"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Green_Bold" />
        <TextView
            android:id="@+id/symbolName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBaseline="@id/orderSideAndLever"
            android:text="@string/string_placeholder"
            android:layout_toRightOf="@id/orderSideAndLever"
            android:layout_marginLeft="@dimen/dip_8"
            android:textAppearance="@style/Body_Dark_Bold" />

    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="@dimen/app_margin_right"
        android:layout_marginStart="@dimen/app_margin_left"
        app:layout_constraintTop_toBottomOf="@id/titleRela">

        <TextView
            android:id="@+id/title1"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/Caption_Grey"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_trigger_price"
            />
        <TextView
            android:id="@+id/title2"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/Caption_Grey"
            app:layout_constraintStart_toEndOf="@id/title1"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_type"
            />
        <TextView
            android:id="@+id/title3"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/Caption_Grey"
            app:layout_constraintStart_toEndOf="@id/title2"
            app:layout_constraintTop_toTopOf="parent"
            android:gravity="right"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_time"
            />

        <TextView
            android:id="@+id/value1"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/BodyS_Dark"
            app:layout_constraintTop_toBottomOf="@id/title1"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            />
        <TextView
            android:id="@+id/value2"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/BodyS_Dark"
            app:layout_constraintStart_toEndOf="@id/value1"
            app:layout_constraintTop_toBottomOf="@id/title2"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            />
        <TextView
            android:id="@+id/value3"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/BodyS_Dark"
            app:layout_constraintStart_toEndOf="@id/value2"
            app:layout_constraintTop_toBottomOf="@id/title3"
            android:gravity="right"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            />
        <!-- 第二行 -->
        <TextView
            android:id="@+id/title4"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            app:layout_constraintStart_toStartOf="parent"
            android:textAppearance="@style/Caption_Grey"
            app:layout_constraintTop_toBottomOf="@id/value1"
            android:layout_marginTop="@dimen/dip_12"
            android:visibility="visible"
            android:text="@string/string_entrust_price"
            />

        <TextView
            android:id="@+id/title5"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/Caption_Grey"
            app:layout_constraintStart_toEndOf="@id/title4"
            app:layout_constraintTop_toBottomOf="@id/value1"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_entrust_quantity"
            />

        <TextView
            android:id="@+id/title6"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/Caption_Grey"
            app:layout_constraintStart_toEndOf="@id/title5"
            app:layout_constraintTop_toBottomOf="@id/value1"
            app:layout_constraintEnd_toEndOf="parent"
            android:gravity="right"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_entrust_status"
            />

        <TextView
            android:id="@+id/value4"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/BodyS_Dark"
            app:layout_constraintTop_toBottomOf="@id/title4"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            />

        <TextView
            android:id="@+id/value5"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/BodyS_Dark"
            app:layout_constraintTop_toBottomOf="@id/title4"
            app:layout_constraintStart_toEndOf="@id/value4"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            />

        <TextView
            android:id="@+id/value6"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/BodyS_Dark_Bold"
            app:layout_constraintBaseline_toBaselineOf="@id/value5"
            app:layout_constraintTop_toBottomOf="@id/title4"
            app:layout_constraintEnd_toEndOf="parent"
            android:gravity="right"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:background="@color/divider_line_color"
        app:layout_constraintTop_toBottomOf="@id/content"
        android:layout_marginStart="@dimen/app_margin_left"
        android:layout_marginTop="@dimen/dip_12"
        />

</androidx.constraintlayout.widget.ConstraintLayout>