<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_32"
    android:background="@color/white"
    >

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:layout_alignParentBottom="true"
        android:background="@color/divider_line_color"/>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/tab_a_rela"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="vertical"
            >
            <TextView
                android:id="@+id/tab_a_name"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_31"
                android:gravity="center_vertical"
                android:text="@string/string_passwd_login"
                android:textAppearance="@style/BodyL_Blue_Bold" />

            <View
                android:id="@+id/tab_a_indicator"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_1"
                android:visibility="visible"
                android:background="@color/blue" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/tab_b_rela"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toRightOf="@+id/tab_a_rela"
            android:layout_marginLeft="@dimen/dip_40"
            android:orientation="vertical"
            >

            <TextView
                android:id="@+id/tab_b_name"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_31"
                android:gravity="center"
                android:text="@string/string_quick_login"
                android:textAppearance="@style/BodyL_Dark_Bold"/>

            <View
                android:id="@+id/tab_b_indicator"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_1"
                android:visibility="gone"
                android:background="@color/blue" />
        </LinearLayout>



    </RelativeLayout>


</RelativeLayout>