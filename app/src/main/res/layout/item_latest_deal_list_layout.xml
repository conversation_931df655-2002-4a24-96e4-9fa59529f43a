<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_40"
    android:background="@color/color_bg_2_night"
    android:orientation="horizontal"
    android:paddingLeft="@dimen/dip_12"
    android:paddingRight="@dimen/dip_12"
    android:gravity="center_vertical"
    >

    <TextView
        android:id="@+id/item_deal_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="@string/string_placeholder"
        android:textAppearance="@style/BodyS_Dark_night"/>
    <TextView
        android:id="@+id/item_deal_type"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="@string/string_placeholder"
        android:textAppearance="@style/BodyS_Dark_night" />

    <TextView
        android:id="@+id/item_deal_price"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center"
        android:layout_weight="1"
        android:text="@string/string_placeholder"
        android:textAppearance="@style/BodyS_Dark_night"/>

    <TextView
        android:id="@+id/item_deal_amount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="right"
        android:layout_weight="1"
        android:text="@string/string_placeholder"
        android:textAppearance="@style/BodyS_Dark_night" />

</LinearLayout>