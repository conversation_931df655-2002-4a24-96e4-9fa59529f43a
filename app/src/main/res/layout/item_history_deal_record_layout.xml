<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/item_style"
    android:orientation="vertical"
    >

    <RelativeLayout
        android:id="@+id/rela1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_16"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginRight="@dimen/app_margin_right"
        >

        <TextView
            android:id="@+id/order_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark"/>
        <TextView
            android:id="@+id/order_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@id/order_type"
            android:layout_marginLeft="@dimen/dip_12"
            android:layout_centerVertical="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark"/>

        <TextView
            android:id="@+id/order_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:layout_marginLeft="@dimen/dip_5"
            android:visibility="gone"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Caption_Grey"/>

    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="@dimen/app_margin_right"
        android:layout_marginStart="@dimen/app_margin_left"
        >

        <TextView
            android:id="@+id/title"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/Caption_Grey"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_placeholder"
            />
        <TextView
            android:id="@+id/title2"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/Caption_Grey"
            app:layout_constraintStart_toEndOf="@id/title"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_placeholder"
            />
        <TextView
            android:id="@+id/title3"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/Caption_Grey"
            app:layout_constraintStart_toEndOf="@id/title2"
            app:layout_constraintTop_toTopOf="parent"
            android:gravity="right"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_placeholder"
            />

        <TextView
            android:id="@+id/value"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/Caption_Dark"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            />
        <TextView
            android:id="@+id/value2"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/Caption_Dark"
            app:layout_constraintStart_toEndOf="@id/value"
            app:layout_constraintTop_toBottomOf="@id/title"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            />
        <TextView
            android:id="@+id/value3"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/Caption_Dark"
            app:layout_constraintStart_toEndOf="@id/value2"
            app:layout_constraintTop_toBottomOf="@id/title"
            android:gravity="right"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            />
        <!-- 第二行 -->
        <TextView
            android:id="@+id/title4"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/Caption_Grey"
            app:layout_constraintTop_toBottomOf="@id/value"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_placeholder"
            />
        <TextView
            android:id="@+id/title5"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/Caption_Grey"
            app:layout_constraintStart_toEndOf="@id/title4"
            app:layout_constraintTop_toBottomOf="@id/value"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_placeholder"
            />
        <TextView
            android:id="@+id/title6"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/Caption_Grey"
            app:layout_constraintStart_toEndOf="@id/title5"
            app:layout_constraintTop_toBottomOf="@id/value"
            android:gravity="right"
            android:layout_marginTop="@dimen/dip_12"
            android:text="@string/string_placeholder"
            android:drawableRight="@mipmap/icon_copy_content"
            android:drawablePadding="@dimen/dip_2"
            />

        <TextView
            android:id="@+id/value4"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/Caption_Dark"
            app:layout_constraintTop_toBottomOf="@id/title4"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            />
        <TextView
            android:id="@+id/value5"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/Caption_Dark"
            app:layout_constraintStart_toEndOf="@id/value4"
            app:layout_constraintTop_toBottomOf="@id/title4"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            />
        <TextView
            android:id="@+id/value6"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.33"
            android:textAppearance="@style/Caption_Dark"
            app:layout_constraintBaseline_toBaselineOf="@id/value5"
            app:layout_constraintStart_toEndOf="@id/value5"
            app:layout_constraintTop_toBottomOf="@id/title4"
            android:gravity="right"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            android:maxLines="1"
            android:ellipsize="end"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>
    <View
        android:id="@+id/item_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:layout_marginTop="@dimen/dip_12"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:background="@color/divider_line_color"
        android:layout_alignParentBottom="true"
        />
</LinearLayout>