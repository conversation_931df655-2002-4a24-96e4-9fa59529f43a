<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/itemView"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_80"
    android:background="@drawable/item_style">


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:orientation="vertical">

        <TextView
            android:id="@+id/item_record_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark" />

        <TextView
            android:id="@+id/item_record_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Grey" />

    </LinearLayout>

    <ImageView
        android:id="@+id/arrow_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="@dimen/app_margin_right"
        android:background="@mipmap/icon_arrow_right" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toLeftOf="@id/arrow_image"
        android:layout_marginRight="@dimen/app_margin_right"
        android:orientation="vertical">

        <TextView
            android:id="@+id/item_record_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:layout_centerHorizontal="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark" />

        <TextView
            android:id="@+id/item_record_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Grey" />


    </LinearLayout>


    <View
        android:id="@+id/item_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:background="@color/divider_line_color" />

</RelativeLayout>