<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_bg_1"
    android:orientation="vertical"
    android:visibility="visible">
        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_1"
            android:background="@color/divider_line_color"
            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dip_20"
            android:layout_marginRight="@dimen/dip_20"
            android:layout_marginTop="@dimen/dip_32"
            android:layout_marginBottom="@dimen/dip_24"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/shareWx"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="@dimen/dip_50"
                    android:layout_height="@dimen/dip_50"
                    android:src="@mipmap/icon_share_wx" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dip_16"
                    android:text="@string/string_weichat"
                    android:textAppearance="@style/BodyS_Dark" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/shareWxFriends"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="@dimen/dip_50"
                    android:layout_height="@dimen/dip_50"
                    android:src="@mipmap/icon_share_wx_friends" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dip_16"
                    android:text="@string/string_weichat_friends"
                    android:textAppearance="@style/BodyS_Dark" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/shareSaveImg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="@dimen/dip_50"
                    android:layout_height="@dimen/dip_50"
                    android:src="@mipmap/icon_save_pic" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dip_16"
                    android:text="@string/string_save_picture"
                    android:textAppearance="@style/BodyS_Dark" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/btnMore"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="@dimen/dip_50"
                    android:layout_height="@dimen/dip_50"
                    android:src="@mipmap/icon_more" />

                <TextView
                    android:id="@+id/moreBtnTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dip_16"
                    android:text="@string/string_more"
                    android:textAppearance="@style/BodyS_Dark" />
            </LinearLayout>

        </LinearLayout>
        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_1"
            android:background="@color/divider_line_color"
            />

        <TextView
            android:id="@+id/cancel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/dip_16"
            android:layout_alignParentRight="true"
            android:gravity="center"
            android:text="@string/string_cancel"
            android:textAppearance="@style/Body_Dark_Bold" />
</LinearLayout>