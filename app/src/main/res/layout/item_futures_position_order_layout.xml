<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_futures_position_order_layout.xml
  ~   @Date: 19-7-25 上午11:58
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/dip_16">

    <RelativeLayout
        android:id="@+id/titleRela"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/app_margin_left"
        android:layout_marginEnd="@dimen/app_margin_right"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/symbolName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyL_Dark_Bold" />

        <TextView
            android:id="@+id/orderSideAndLever"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/dip_8"
            android:layout_toRightOf="@id/symbolName"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyL_Green" />

        <ImageView
            android:id="@+id/order_share"
            android:layout_width="@dimen/dip_30"
            android:layout_height="@dimen/dip_30"
            android:layout_alignParentRight="true"
            android:padding="@dimen/dip_4"
            android:src="@mipmap/icon_go_share" />

    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mainContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/app_margin_left"
        android:layout_marginEnd="@dimen/app_margin_right"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleRela">

        <TextView
            android:id="@+id/mainTitle1"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:singleLine="true"
            android:text="@string/string_open_average_price"
            android:textAppearance="@style/BodyS_Grey"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.5" />

        <TextView
            android:id="@+id/mainTitle2"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:singleLine="true"
            android:text="@string/string_unrealized_profit_and_loss"
            android:textAppearance="@style/BodyS_Grey"
            app:layout_constraintStart_toEndOf="@id/mainTitle1"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.5" />

        <TextView
            android:id="@+id/mainValue1"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_6"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark_Bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/mainTitle1"
            app:layout_constraintWidth_percent="0.5" />

        <TextView
            android:id="@+id/mainValue2"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_6"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            app:layout_constraintStart_toEndOf="@id/mainValue1"
            app:layout_constraintTop_toBottomOf="@id/mainTitle2"
            app:layout_constraintWidth_percent="0.5" />

        <TextView
            android:id="@+id/mainTitle3"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:singleLine="true"
            android:text="@string/string_abount_close_price"
            android:textAppearance="@style/BodyS_Grey"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/mainValue1"
            app:layout_constraintWidth_percent="0.5" />

        <TextView
            android:id="@+id/mainTitle4"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:singleLine="true"
            android:text="@string/string_profit"
            android:textAppearance="@style/BodyS_Grey"
            app:layout_constraintStart_toEndOf="@id/mainTitle3"
            app:layout_constraintTop_toBottomOf="@id/mainValue2"
            app:layout_constraintWidth_percent="0.5" />

        <TextView
            android:id="@+id/mainValue3"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_6"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark_Bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/mainTitle3"
            app:layout_constraintWidth_percent="0.5" />

        <TextView
            android:id="@+id/mainValue4"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_6"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            app:layout_constraintStart_toEndOf="@id/mainValue3"
            app:layout_constraintTop_toBottomOf="@id/mainTitle4"
            app:layout_constraintWidth_percent="0.5" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/middleLine"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginTop="@dimen/dip_8"
        android:layout_marginRight="@dimen/app_margin_right"
        android:background="@color/divider_line_color"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mainContent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/app_margin_left"
        android:layout_marginEnd="@dimen/app_margin_right"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/middleLine">

        <TextView
            android:id="@+id/title1"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:singleLine="true"
            android:text="@string/string_position_margin"
            android:textAppearance="@style/BodyS_Grey"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.4" />

        <TextView
            android:id="@+id/title2"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:singleLine="true"
            android:text="@string/string_futures_hold_amount_with_unit"
            android:textAppearance="@style/BodyS_Grey"
            app:layout_constraintStart_toEndOf="@id/title1"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.3" />

        <TextView
            android:id="@+id/title3"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:gravity="right"
            android:singleLine="true"
            android:text="@string/string_position_value"
            android:textAppearance="@style/BodyS_Grey"
            app:layout_constraintStart_toEndOf="@id/title2"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.3" />

        <LinearLayout
            android:id="@+id/value1_ll"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_6"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title1"
            app:layout_constraintWidth_percent="0.4">

            <TextView
                android:id="@+id/value1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Dark" />

            <LinearLayout
                android:layout_width="@dimen/dip_16"
                android:layout_height="@dimen/dip_16"
                android:background="@drawable/bg_corner_light_blue"
                android:layout_marginLeft="@dimen/dip_4"
                android:gravity="center">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/icon_adjust">

                </ImageView>
            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/value2"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_6"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            app:layout_constraintStart_toEndOf="@id/value1_ll"
            app:layout_constraintTop_toBottomOf="@id/title2"
            app:layout_constraintWidth_percent="0.3" />

        <TextView
            android:id="@+id/value3"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_6"
            android:gravity="right"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            app:layout_constraintStart_toEndOf="@id/value2"
            app:layout_constraintTop_toBottomOf="@id/title3"
            app:layout_constraintWidth_percent="0.3" />
        <!-- 第二行 -->
        <TextView
            android:id="@+id/title4"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:singleLine="true"
            android:text="@string/string_margin_rate"
            android:textAppearance="@style/BodyS_Grey"
            android:visibility="visible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/value1_ll"
            app:layout_constraintWidth_percent="0.4" />

        <TextView
            android:id="@+id/title5"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:singleLine="true"
            android:text="@string/string_can_close_with_unit"
            android:textAppearance="@style/BodyS_Grey"
            app:layout_constraintStart_toEndOf="@id/title4"
            app:layout_constraintTop_toBottomOf="@id/value1_ll"
            app:layout_constraintWidth_percent="0.3" />

        <TextView
            android:id="@+id/title6"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:gravity="right"
            android:singleLine="true"
            android:text="@string/string_exercise_point"
            android:textAppearance="@style/BodyS_Grey"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/title5"
            app:layout_constraintTop_toBottomOf="@id/value1_ll"
            app:layout_constraintWidth_percent="0.3" />

        <TextView
            android:id="@+id/value4"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_6"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title4"
            app:layout_constraintWidth_percent="0.4" />

        <TextView
            android:id="@+id/value5"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_6"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            app:layout_constraintStart_toEndOf="@id/value4"
            app:layout_constraintTop_toBottomOf="@id/title4"
            app:layout_constraintWidth_percent="0.3" />

        <TextView
            android:id="@+id/value6"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_6"
            android:gravity="right"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark_Bold"
            app:layout_constraintBaseline_toBaselineOf="@id/value5"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title4"
            app:layout_constraintWidth_percent="0.3" />
        <!-- 第三行 -->
        <TextView
            android:id="@+id/title7"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:singleLine="true"
            android:text="@string/string_margin_rate"
            android:textAppearance="@style/BodyS_Grey"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/value4"
            app:layout_constraintWidth_percent="0.4" />

        <TextView
            android:id="@+id/title8"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:singleLine="true"
            android:text="@string/string_profit"
            android:textAppearance="@style/BodyS_Grey"
            android:visibility="gone"
            app:layout_constraintStart_toEndOf="@id/title7"
            app:layout_constraintTop_toBottomOf="@id/value4"
            app:layout_constraintWidth_percent="0.3" />

        <TextView
            android:id="@+id/title9"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:gravity="right"
            android:singleLine="true"
            android:text="@string/string_unrealized_profit_and_loss"
            android:textAppearance="@style/BodyS_Grey"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/title8"
            app:layout_constraintTop_toBottomOf="@id/value4"
            app:layout_constraintWidth_percent="0.3" />

        <TextView
            android:id="@+id/value7"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_6"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title7"
            app:layout_constraintWidth_percent="0.4" />

        <TextView
            android:id="@+id/value8"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_6"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            android:visibility="gone"
            app:layout_constraintStart_toEndOf="@id/value7"
            app:layout_constraintTop_toBottomOf="@id/title7"
            app:layout_constraintWidth_percent="0.3" />

        <TextView
            android:id="@+id/value9"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_6"
            android:gravity="right"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark_Bold"
            android:visibility="gone"
            app:layout_constraintBaseline_toBaselineOf="@id/value8"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title7"
            app:layout_constraintWidth_percent="0.3" />
        <!-- 第四行 -->
        <TextView
            android:id="@+id/title10"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:singleLine="true"
            android:text="@string/string_abount_close_price"
            android:textAppearance="@style/BodyS_Grey"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/value7"
            app:layout_constraintWidth_percent="0.33" />

        <TextView
            android:id="@+id/title11"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_12"
            android:singleLine="true"
            android:text="@string/string_exercise_point"
            android:textAppearance="@style/BodyS_Grey"
            android:visibility="gone"
            app:layout_constraintStart_toEndOf="@id/title10"
            app:layout_constraintTop_toBottomOf="@id/value7"
            app:layout_constraintWidth_percent="0.33" />

        <TextView
            android:id="@+id/value10"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_6"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title10"
            app:layout_constraintWidth_percent="0.33" />

        <TextView
            android:id="@+id/value11"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_6"
            android:maxLines="1"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyS_Dark"
            android:visibility="gone"
            app:layout_constraintStart_toEndOf="@id/value10"
            app:layout_constraintTop_toBottomOf="@id/title10"
            app:layout_constraintWidth_percent="0.33" />

    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/btnCL"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/content"
        android:layout_marginStart="@dimen/app_margin_left"
        android:layout_marginEnd="@dimen/app_margin_right"
        android:layout_marginTop="@dimen/dip_12"
        >
        <TextView
            android:id="@+id/order_stop"
            android:layout_width="@dimen/dip_0"
            android:layout_height="@dimen/dip_32"
            android:gravity="center"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.3"
            android:background="@drawable/bg_corner_light_blue"
            android:textAppearance="@style/BodyS_Blue_Bold"
            android:text="@string/string_stop_profit_stop_loss"/>

        <LinearLayout
            android:id="@+id/order_quick_close_ll"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@id/order_stop"
            app:layout_constraintEnd_toStartOf="@id/order_close"
            app:layout_constraintWidth_percent="0.3"
            android:background="@drawable/bg_corner_light_blue">

            <TextView
                android:id="@+id/order_quick_close"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_32"
                android:visibility="visible"
                android:gravity="center"
                android:textAppearance="@style/BodyS_Blue_Bold"
                android:drawableLeft="@mipmap/icon_flash"
                android:text="@string/string_order_quick_close"/>
        </LinearLayout>

        <TextView
            android:id="@+id/order_close"
            android:layout_width="@dimen/dip_0"
            android:layout_height="@dimen/dip_32"
            android:gravity="center"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintWidth_percent="0.3"
            android:background="@drawable/bg_corner_light_blue"
            android:textAppearance="@style/BodyS_Blue_Bold"
            android:text="@string/string_option_close"/>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_8"
        android:layout_marginTop="@dimen/dip_12"
        android:background="@color/divider_line_color"
        app:layout_constraintTop_toBottomOf="@id/btnCL" />

</androidx.constraintlayout.widget.ConstraintLayout>