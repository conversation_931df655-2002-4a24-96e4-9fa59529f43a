<?xml version="1.0" encoding="utf-8"?>
<!--
  ~
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: include_alertheader.xml
  ~   @Date: 11/29/18 3:21 PM
  ~   @Author: chenjun
  ~   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  ~
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/loAlertHeader"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvAlertTitle"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_alert_title"
        android:gravity="center"
        android:textColor="@color/textColor_alert_title"
        android:textSize="@dimen/textSize_alert_title" />

    <TextView
        android:id="@+id/tvAlertMsg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/marginBottom_alert_msg"
        android:gravity="center"
        android:textColor="@color/textColor_alert_msg"
        android:textSize="@dimen/textSize_alert_msg" />
</LinearLayout>