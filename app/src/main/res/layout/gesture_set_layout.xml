<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/blue">

    <io.bhex.app.view.TopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:left_icon="@mipmap/icon_close_white"
        app:bgcolor="@color/blue"
        app:title_text=""
        app:left_text="  "
        />

    <TextView
        android:id="@+id/username"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/topBar"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dip_32"
        android:gravity="center"
        android:textAppearance="@style/H3_White"
        android:text="@string/string_title_set_gesture" />

    <TextView
        android:id="@+id/text_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/username"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dip_10"
        android:gravity="center_vertical"
        android:text="@string/setup_gesture_tips"
        android:textColor="@color/white50" />

    <FrameLayout
        android:id="@+id/gesture_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_30"
        android:layout_centerInParent="true"
        android:layout_gravity="center" />

    <TextView
        android:id="@+id/text_reset"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_5"
        android:layout_below="@id/gesture_container"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center_horizontal"
        android:gravity="center_horizontal"
        android:textColor="@color/white50" />

    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="@dimen/dip_25"
        android:layout_height="@dimen/dip_25"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginTop="@dimen/app_margin_top"
        android:padding="@dimen/dip_5"
        android:visibility="gone"
        android:src="@mipmap/icon_close_white" />


    <io.bhex.app.gesture.view.LockIndicator
        android:id="@+id/lock_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="@dimen/dip_50" />

</RelativeLayout>