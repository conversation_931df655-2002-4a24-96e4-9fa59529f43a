<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:paddingTop="15dp"
    android:layout_marginStart="15dp"
    >

    <LinearLayout
        android:id="@+id/ll_ck_show"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="3dp"
        android:layout_marginRight="10dp"
        android:background="@drawable/shape_indicator"
        >

        <CheckBox
            android:id="@+id/cb_can_show"
            android:layout_width="9dp"
            android:layout_height="9dp"
            android:button="@null"
            android:background="@drawable/shape_ck_indicator"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_indictor"
        android:layout_width="100dp"
        android:layout_height="30dp"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:background="@drawable/shape_indicator_bg">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_indicator_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="15sp"
            android:textColor="#5C698C"
            android:layout_gravity="center_vertical"
            tools:text="M1"/>

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/et_indicator_value"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            android:inputType="number"
            android:maxLength="3"
            android:textSize="13sp"
            android:gravity="center|right"
            android:textColor="#5C698C"
            android:cursorVisible="true"
            android:textCursorDrawable="@drawable/shape_cursor"
            android:layout_gravity="center_vertical"/>

    </LinearLayout>

</LinearLayout>
