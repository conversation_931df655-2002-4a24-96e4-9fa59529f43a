<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingLeft="@dimen/app_paddingLeft"
    android:paddingRight="@dimen/app_paddingRight"
    android:paddingBottom="@dimen/dip_8"
    >

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        >
        <TextView
            android:id="@+id/coinPairName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dip_6"
            android:layout_alignParentBottom="true"
            android:textSize="@dimen/font_24"
            android:visibility="gone"
            android:textColor="@color/font_color1"
            android:text="@string/string_placeholder" />
        <TextView
            android:id="@+id/latestPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@id/coinPairName"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:ellipsize="end"
            android:textAppearance="@style/H2_Green"
            android:text="@string/string_placeholder" />
        <TextView
            android:id="@+id/latestPrice2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/latestPrice"
            android:layout_marginTop="@dimen/dip_12"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="￥ --"
            android:textAppearance="@style/Caption_Grey_night" />
        <TextView
            android:id="@+id/riseFallRatio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dip_16"
            android:layout_toRightOf="@id/latestPrice2"
            android:layout_alignBaseline="@id/latestPrice2"
            android:maxLines="1"
            android:textAppearance="@style/Body_Green_Bold"
            android:text="@string/string_placeholder" />

        <LinearLayout
            android:layout_width="@dimen/dip_100"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_10"
            android:layout_alignParentRight="true"
            android:orientation="vertical"
            >

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                >

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="@style/BodyS_Grey_night"
                    android:text="@string/string_high" />

                <TextView
                    android:id="@+id/highPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:textAppearance="@style/BodyS_Dark_night"
                    android:text="@string/string_placeholder" />


            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_8"
                >
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textAppearance="@style/BodyS_Grey_night"
                    android:text="@string/string_low" />
                <TextView
                    android:id="@+id/lowPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textAppearance="@style/BodyS_Dark_night"
                    android:text="@string/string_placeholder" />

            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_8"
                >

                <TextView
                    android:id="@+id/volume_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/string_24H"
                    android:textAppearance="@style/BodyS_Grey_night" />

                <TextView
                    android:id="@+id/volume"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/BodyS_Dark_night" />

            </RelativeLayout>

        </LinearLayout>
    </RelativeLayout>

</LinearLayout>