<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_bg_2"
    android:orientation="vertical"
    android:paddingLeft="@dimen/app_paddingLeft"
    android:paddingRight="@dimen/app_paddingRight">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/string_time"
            android:textColor="@color/font_color2"
            android:textSize="@dimen/font_15" />

        <TextView
            android:id="@+id/detail_order_deal_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:text="@string/string_placeholder"
            android:textColor="@color/font_color1"
            android:textSize="@dimen/font_15" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_3">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/string_deal_price"
            android:textColor="@color/font_color2"
            android:textSize="@dimen/font_15" />

        <TextView
            android:id="@+id/detail_order_deal_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:text="@string/string_placeholder"
            android:textColor="@color/font_color1"
            android:textSize="@dimen/font_15" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_3">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/string_amount"
            android:textColor="@color/font_color2"
            android:textSize="@dimen/font_15" />

        <TextView
            android:id="@+id/detail_order_deal_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:text="@string/string_placeholder"
            android:textColor="@color/font_color1"
            android:textSize="@dimen/font_15" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/detail_order_profit_loss_rela"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_3">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/string_profit_loss"
            android:textColor="@color/font_color2"
            android:textSize="@dimen/font_15" />

        <TextView
            android:id="@+id/detail_order_profit_loss"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:text="@string/string_placeholder"
            android:textColor="@color/font_color1"
            android:textSize="@dimen/font_15" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_marginTop="@dimen/dip_3">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/string_order_deal_money"
            android:textColor="@color/font_color2"
            android:textSize="@dimen/font_15" />

        <TextView
            android:id="@+id/detail_order_deal_money"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:text="@string/string_placeholder"
            android:textColor="@color/font_color1"
            android:textSize="@dimen/font_15" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_3">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/string_fee"
            android:textColor="@color/font_color2"
            android:textSize="@dimen/font_15" />

        <TextView
            android:id="@+id/detail_order_fee"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:text="@string/string_placeholder"
            android:textColor="@color/font_color1"
            android:textSize="@dimen/font_15" />

    </RelativeLayout>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_3">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/string_trade_order_id"
            android:textColor="@color/font_color2"
            android:textSize="@dimen/font_15" />
        <ImageView
            android:id="@+id/trade_id_copy_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/icon_copy_content"
            android:layout_alignParentRight="true"
            />

        <TextView
            android:id="@+id/detail_order_id"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toLeftOf="@+id/trade_id_copy_btn"
            android:text="@string/string_placeholder"
            android:textColor="@color/font_color1"
            android:textSize="@dimen/font_15" />

    </RelativeLayout>
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_0.5"
        android:layout_marginTop="@dimen/dip_10"
        android:background="@color/divider_line_color"/>

</LinearLayout>