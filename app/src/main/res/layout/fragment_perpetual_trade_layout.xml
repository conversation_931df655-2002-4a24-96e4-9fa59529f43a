<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: fragment_trade_futures_layout.xml
  ~   @Date: 19-6-13 下午2:54
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/rootView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_48">

            <ImageView
                android:id="@+id/settings"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBaseline="@id/topBar"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:padding="@dimen/dip_10"
                android:src="@mipmap/icon_more_actions" />

            <io.bhex.app.view.TopBar
                android:id="@+id/topBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toLeftOf="@id/settings"
                android:layout_centerVertical="true"
                app:divider_visiblity="visible"
                app:left_text=""
                app:left_visiblity="gone"
                app:right_text=""
                app:title_text="@string/title_trade" />

        </RelativeLayout>

        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:srlEnableLoadMore="false"
            app:srlEnableNestedScrolling="true">

            <io.bhex.app.view.CustomFreshHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_0"
                app:srlClassicsSpinnerStyle="FixedBehind"
                app:srlDrawableArrowSize="20dp"
                app:srlDrawableMarginRight="20dp"
                app:srlDrawableProgressSize="20dp"
                app:srlEnableLastTime="true"
                app:srlFinishDuration="500"
                app:srlTextSizeTime="10dp"
                app:srlTextSizeTitle="16sp"
                app:srlTextTimeMarginTop="2dp" />

            <androidx.coordinatorlayout.widget.CoordinatorLayout
                android:id="@+id/main_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.google.android.material.appbar.AppBarLayout
                    android:id="@+id/app_bar_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:elevation="@dimen/dip_0">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_scrollFlags="scroll|enterAlwaysCollapsed">
                        <!--app:layout_collapseMode="pin"-->
                        <include
                            android:id="@+id/futures_trade_title"
                            layout="@layout/future_trade_title_layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:visibility="visible"
                            app:layout_collapseMode="pin" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:orientation="vertical">

                        <com.flyco.tablayout.CommonTabLayout
                            android:id="@+id/createTab"
                            android:layout_width="match_parent"
                            android:layout_height="48dp"
                            app:tl_tab_space_equal="true"
                            app:tl_iconVisible="false"
                            app:tl_indicator_color="@color/blue"
                            app:tl_indicator_corner_radius="2dp"
                            app:tl_indicator_height="@dimen/dip_2"
                            app:tl_indicator_width="@dimen/dip_40"
                            app:tl_textBold="SELECT"
                            app:tl_textSelectColor="@color/blue"
                            app:tl_textUnselectColor="@color/dark"
                            app:tl_textsize="@dimen/sp_16"
                            app:tl_underline_color="@color/divider_line_color"
                            app:tl_underline_height="@dimen/dip_1" />

                    </LinearLayout>

                </com.google.android.material.appbar.AppBarLayout>

                <androidx.core.widget.NestedScrollView
                    android:id="@+id/nestedScrollView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layout_behavior="@string/appbar_scrolling_view_behavior">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <include
                            android:id="@+id/futures_header_trade_layout"
                            layout="@layout/header_contract_trade_layout" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recyclerView"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:overScrollMode="never"
                            app:fastScrollVerticalThumbDrawable="@style/Body_Dark"
                            tools:listitem="@layout/item_current_entrust_order__layout" />
                    </LinearLayout>
                </androidx.core.widget.NestedScrollView>
            </androidx.coordinatorlayout.widget.CoordinatorLayout>

            <com.scwang.smartrefresh.layout.footer.ClassicsFooter
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </com.scwang.smartrefresh.layout.SmartRefreshLayout>
    </LinearLayout>

</RelativeLayout>