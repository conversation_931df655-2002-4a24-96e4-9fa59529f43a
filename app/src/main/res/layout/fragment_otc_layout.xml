<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: fragment_otc_layout.xml
  ~   @Date: 19-1-11 下午2:13
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/otcRootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include
        android:id="@+id/otc_title_view"
        layout="@layout/include_otc_title_layout"/>
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:background="@color/divider_line_color"
        />

    <io.bhex.app.view.BHTabView
        android:id="@+id/tokenTabs"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginStart="@dimen/dip_8"
        android:layout_marginEnd="@dimen/app_margin_right"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/rightIcon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:selectTextAppearance="@style/Body_Blue_Bold"
        app:unselectTextAppearance="@style/Body_Grey_Bold"
        app:selectTextAppearanceNight="@style/Body_Blue_Bold_night"
        app:unselectTextAppearanceNight="@style/Body_Grey_Bold_night"
        app:indicatorHeight="@dimen/dip_2"
        app:marginBottom="@dimen/dip_2"
        android:layout_marginBottom="@dimen/dip_2"
        />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/otc_reference_index"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_40"
            android:textAppearance="@style/BodyS_Dark"
            android:background="@color/blue10"
            android:text="@string/string_placeholder"
            android:gravity="center"
            />

        <TextView
            android:id="@+id/otc_pending_order_tips"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_40"
            android:textAppearance="@style/BodyS_Red"
            android:background="@color/orange10"
            android:layout_below="@id/otc_reference_index"
            android:visibility="gone"
            android:text="@string/string_tips_pengding_order"
            android:gravity="center"
            />
    </RelativeLayout>

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:srlEnableLoadMore="false"
        app:srlEnableNestedScrolling="true">

        <io.bhex.app.view.CustomFreshHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_0"
            app:srlClassicsSpinnerStyle="FixedBehind"
            app:srlDrawableArrowSize="20dp"
            app:srlDrawableMarginRight="20dp"
            app:srlDrawableProgressSize="20dp"
            app:srlEnableLastTime="true"
            app:srlFinishDuration="500"
            app:srlTextSizeTime="10dp"
            app:srlTextSizeTitle="16sp"
            app:srlTextTimeMarginTop="2dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="always"
                app:fastScrollVerticalThumbDrawable="@style/Body_Dark"
                tools:listitem="@layout/item_otc_list_layout" />

        </LinearLayout>

        <com.scwang.smartrefresh.layout.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

</LinearLayout>