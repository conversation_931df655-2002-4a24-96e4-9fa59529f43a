<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_point_records_list_layout.xml
  ~   @Date: 18-12-2 下午10:13
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_56"
    android:paddingLeft="@dimen/app_paddingLeft"
    >

    <TextView
        android:id="@+id/item_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:text="@string/string_placeholder"
        android:textAppearance="@style/BodyS_Grey" />

    <TextView
        android:id="@+id/item_token"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="@dimen/dip_50"
        android:layout_centerInParent="true"
        android:text="@string/string_placeholder"
        android:textAppearance="@style/Body_Dark" />
    <TextView
        android:id="@+id/item_amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dip_24"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:text="@string/string_placeholder"
        android:textAppearance="@style/Body_Dark" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:background="@color/divider_line_color"
        android:layout_alignParentBottom="true"
        />

</RelativeLayout>