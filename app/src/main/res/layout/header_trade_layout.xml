<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="-8dp"
    android:background="@color/white"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_0.5"
        android:layout_marginTop="@dimen/dip_8"
        android:background="@color/divider_line_color" />

    <include
        android:id="@+id/options_trade_title"
        layout="@layout/option_trade_title_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone" />

    <io.bhex.app.skin.view.SkinPercentRelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_450"
        android:layout_marginTop="@dimen/dip_8"
        android:background="@color/white">

        <LinearLayout
            android:id="@+id/trade_linear"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingLeft="@dimen/app_paddingLeft"
            android:paddingBottom="@dimen/dip_5"
            android:visibility="visible"
            app:layout_widthPercent="55%">

            <LinearLayout
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_0"
                android:focusable="true"
                android:focusableInTouchMode="true">

                <requestFocus />
            </LinearLayout>

            <include
                android:id="@+id/tab"
                layout="@layout/trade_tab_layout" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- 委托单类型 -->
                <RelativeLayout
                    android:id="@+id/placeOrderModeRela"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dip_24"
                    android:layout_marginTop="@dimen/dip_8"
                    android:layout_marginBottom="@dimen/dip_8"
                    android:layout_weight="3"
                    android:visibility="gone"
                    android:layout_marginEnd="@dimen/dip_16">

                    <TextView
                        android:id="@+id/placeOrderMode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:text="@string/string_ordinary_order"
                        android:textAppearance="@style/BodyS_Grey_Bold" />

                    <ImageView
                        android:id="@+id/placeOrderModeArrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_alignParentRight="true"
                        android:src="@mipmap/icon_arrow_down_black" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/priceModeRela"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dip_24"
                    android:layout_marginTop="@dimen/dip_8"
                    android:layout_weight="2"
                    android:layout_marginBottom="@dimen/dip_8">

                    <TextView
                        android:id="@+id/priceMode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:text="@string/string_limited_price"
                        android:textAppearance="@style/BodyS_Grey_Bold" />

                    <ImageView
                        android:id="@+id/price_type_iv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/dip_4"
                        android:src="@mipmap/icon_arrow_down_black" />

                </RelativeLayout>
            </LinearLayout>

            <!-- 触发价格输入 -->
            <RelativeLayout
                android:id="@+id/trigger_price_rela"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_43"
                android:visibility="visible">

                <TextView
                    android:id="@+id/trigger_price_unit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="@dimen/dip_8"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/Body_Grey" />

                <EditText
                    android:id="@+id/trigger_price"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_toLeftOf="@id/trigger_price_unit"
                    android:background="@null"
                    android:hint="@string/string_trigger_price"
                    android:inputType="numberDecimal"
                    android:maxLines="1"
                    android:paddingLeft="@dimen/dip_8"
                    android:paddingRight="@dimen/dip_8"
                    android:textAppearance="@style/Body_Dark_Bold"
                    android:textColor="@color/dark"
                    android:textColorHint="@color/dark50" />
            </RelativeLayout>

            <TextView
                android:id="@+id/priceMarket"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_43"
                android:layout_marginTop="@dimen/dip_8"
                android:background="@drawable/bg_market_price_gray"
                android:gravity="center"
                android:text="@string/string_market_price_hint"
                android:textColor="@color/font_color2"
                android:textSize="@dimen/font_15"
                android:textStyle="bold"
                android:visibility="gone" />

            <io.bhex.app.view.PriceEditView
                android:id="@+id/priceLimitedEditView"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_43"
                android:layout_marginTop="@dimen/dip_8"
                android:visibility="visible" />

            <TextView
                android:id="@+id/priceAbout"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_24"
                android:layout_marginLeft="@dimen/dip_1"
                android:layout_marginTop="@dimen/dip_2"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Grey_Bold" />

            <RelativeLayout
                android:id="@+id/edit_amount_rela"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_43">

                <TextView
                    android:id="@+id/edit_amount_unit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="@dimen/dip_8"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/Body_Grey" />

                <EditText
                    android:id="@+id/edit_amount"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_toLeftOf="@id/edit_amount_unit"
                    android:background="@null"
                    android:hint="@string/string_amount"
                    android:inputType="numberDecimal"
                    android:maxLines="1"
                    android:paddingLeft="@dimen/dip_8"
                    android:paddingRight="@dimen/dip_8"
                    android:textAppearance="@style/Body_Dark_Bold"
                    android:textColor="@color/dark"
                    android:textColorHint="@color/dark50" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_4">

                <TextView
                    android:id="@+id/balance_available_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:text="@string/string_use_available"
                    android:textAppearance="@style/BodyS_Grey_Bold" />

                <TextView
                    android:id="@+id/balance_available"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:maxLines="1"
                    android:text="@string/string_balance_available_format"
                    android:textAppearance="@style/BodyS_Grey" />

                <TextView
                    android:id="@+id/balance_available_about"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/balance_available"
                    android:layout_alignParentRight="true"
                    android:maxLines="1"
                    android:text="@string/string_balance_available_format"
                    android:textAppearance="@style/Caption_Grey" />

            </RelativeLayout>


            <io.bhex.app.view.StepView
                android:id="@+id/stepView"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_28"
                android:layout_marginTop="@dimen/dip_8"
                app:background_line_color="@color/grey"
                app:background_line_hight="@dimen/dip_4"
                app:background_small_circle_color="@color/grey"
                app:background_small_circle_radius="@dimen/dip_9"
                app:foreground_big_circle_color="@color/blue"
                app:foreground_big_circle_radius="@dimen/dip_9"
                app:foreground_line_color="@color/blue"
                app:foreground_line_hight="@dimen/dip_4"
                app:foreground_small_circle_color="@color/blue"
                app:foreground_small_circle_radius="@dimen/dip_9"
                app:progress="0"
                app:steps_num="5" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_4"
                android:visibility="gone">

                <TextView
                    android:id="@+id/stepView_minTxt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textColor="@color/font_color2"
                    android:textSize="@dimen/font_10" />


                <TextView
                    android:id="@+id/stepView_maxTxt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:text="@string/string_stepview_max_txt_format"
                    android:textColor="@color/font_color2"
                    android:textSize="@dimen/font_10" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/trade_money_ll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_12"
                android:visibility="gone">

                <TextView
                    android:id="@+id/trade_total_amount_title"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dip_16"
                    android:text="@string/string_amount_of_money"
                    android:textAppearance="@style/BodyS_Grey">

                </TextView>

                <TextView
                    android:id="@+id/trade_total_money_title"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dip_16"
                    android:layout_alignParentRight="true"
                    android:layout_toRightOf="@id/trade_total_amount_title"
                    android:gravity="right"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/BodyS_Dark" />

                <TextView
                    android:id="@+id/trade_total_money"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dip_16"
                    android:layout_below="@id/trade_total_money_title"
                    android:layout_alignParentRight="true"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/Caption_Grey" />


                <TextView
                    android:id="@+id/btnTradeTransfer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/trade_total_money"
                    android:layout_marginTop="@dimen/dip_8"
                    android:layout_marginBottom="@dimen/dip_8"
                    android:drawableLeft="@mipmap/icon_transfer_blue"
                    android:text="@string/string_asset_transfer"
                    android:textAppearance="@style/BodyS_Blue_Bold"
                    android:visibility="gone" />
            </RelativeLayout>

            <LinearLayout
                android:id="@+id/option_money_ll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="visible">

                <LinearLayout
                    android:id="@+id/get_option_amount_ll"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dip_4">

                    <TextView
                        android:id="@+id/get_option_amount_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/dip_8"
                        android:text="@string/string_option_get"
                        android:textAppearance="@style/BodyS_Grey" />

                    <TextView
                        android:id="@+id/get_option_amount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:text="@string/string_placeholder"
                        android:textAppearance="@style/BodyS_Grey" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/get_earnest_money_ll"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dip_4">

                    <TextView
                        android:id="@+id/get_earnest_money_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/dip_8"
                        android:text="@string/string_asset_earnest_money"
                        android:textAppearance="@style/BodyS_Grey" />

                    <TextView
                        android:id="@+id/get_earnest_money"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:text="@string/string_placeholder"
                        android:textAppearance="@style/BodyS_Dark" />
                </LinearLayout>

                <TextView
                    android:id="@+id/get_earnest_money_legal_money"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="@dimen/dip_4"
                    android:gravity="right"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/BodyS_Grey" />

                <LinearLayout
                    android:id="@+id/get_option_money_ll"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dip_4">

                    <TextView
                        android:id="@+id/get_option_money_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/dip_2"
                        android:text="@string/string_option_money"
                        android:textAppearance="@style/BodyS_Grey" />

                    <TextView
                        android:id="@+id/get_option_money"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:text="@string/string_placeholder"
                        android:textAppearance="@style/BodyS_Dark" />
                </LinearLayout>

                <TextView
                    android:id="@+id/get_option_legal_money"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="@dimen/dip_4"
                    android:gravity="right"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/BodyS_Grey" />

                <TextView
                    android:id="@+id/btnTransfer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/balance_available_title"
                    android:layout_gravity="right"
                    android:layout_marginTop="@dimen/dip_8"
                    android:layout_marginBottom="@dimen/dip_8"
                    android:drawableLeft="@mipmap/icon_transfer_blue"
                    android:gravity="center_vertical"
                    android:text="@string/string_asset_transfer"
                    android:textAppearance="@style/BodyS_Blue_Bold"
                    android:visibility="gone" />
            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_gravity="bottom"
                android:layout_weight="1">

                <Button
                    android:id="@+id/btn_create_order"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dip_40"
                    android:layout_alignParentBottom="true"
                    android:background="@drawable/bg_corner_green"
                    android:gravity="center"
                    android:paddingBottom="@dimen/dip_5"
                    android:text="@string/string_login"
                    android:textAppearance="@style/Body_White_Bold_Default" />

            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:layout_toRightOf="@+id/trade_linear"
            android:orientation="vertical"
            android:paddingLeft="@dimen/dip_16"
            android:paddingBottom="@dimen/dip_5"
            app:layout_widthPercent="45%">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingRight="@dimen/app_paddingRight">

                <TextView
                    android:id="@+id/title_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/string_price_ph"
                    android:textAppearance="@style/Caption_Grey" />

                <TextView
                    android:id="@+id/title_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginLeft="@dimen/dip_4"
                    android:layout_toRightOf="@id/title_price"
                    android:drawableRight="@mipmap/icon_arrow_down_gray"
                    android:ellipsize="end"
                    android:gravity="right"
                    android:maxLines="1"
                    android:text="@string/string_amount_format"
                    android:textAppearance="@style/Caption_Grey" />
            </RelativeLayout>

            <io.bhex.app.view.InnerScrollView
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_290"
                android:fillViewport="true"
                android:scrollbars="vertical"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <io.bhex.app.view.BookListView
                        android:id="@+id/bookListView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="@dimen/dip_8"
                        android:orientation="vertical" />

                </LinearLayout>

            </io.bhex.app.view.InnerScrollView>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_8"
                android:layout_marginBottom="@dimen/dip_4"
                android:descendantFocusability="blocksDescendants">

                <io.bhex.app.view.InnerRecyclerView
                    android:id="@+id/bookList"
                    android:layout_width="match_parent"
                    android:layout_height="414dp" />
                <!--                    android:layout_height="@dimen/dip_290" />-->
            </RelativeLayout>

        </LinearLayout>

    </io.bhex.app.skin.view.SkinPercentRelativeLayout>

    <RelativeLayout
        android:id="@+id/riskTokenTipsRela"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginTop="@dimen/dip_8"
        android:layout_marginRight="@dimen/app_margin_right"
        android:layout_marginBottom="@dimen/dip_8"
        android:background="@drawable/bg_corner_rect_light_blue"
        android:paddingLeft="@dimen/dip_8"
        android:paddingTop="@dimen/dip_8"
        android:paddingBottom="@dimen/dip_8"
        android:visibility="gone">

        <ImageView
            android:id="@+id/closeTokenRiskTipsBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:padding="@dimen/dip_8"
            android:src="@mipmap/icon_close_blue" />

        <TextView
            android:id="@+id/riskTokenTips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/dip_8"
            android:layout_toLeftOf="@id/closeTokenRiskTipsBtn"
            android:drawableLeft="@mipmap/icon_tips"
            android:drawablePadding="@dimen/dip_4"
            android:text="@string/string_risk_token_tips"
            android:textAppearance="@style/BodyS_Blue" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_8"
        android:background="@color/dark5" />

    <RelativeLayout
        android:id="@+id/orderTabRela"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_56"
        android:background="@color/white"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/app_paddingLeft"
        android:paddingRight="@dimen/app_paddingRight">

        <CheckBox
            android:id="@+id/btn_invisible_other_entrust"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:button="@drawable/order_show_style"
            android:checked="true"
            android:gravity="center_vertical"
            android:text="@string/string_invisible_other_entrust"
            android:textColor="@color/font_color3"
            android:textSize="@dimen/font_15"
            android:visibility="gone" />

        <io.bhex.app.skin.view.SkinTabLayout
            android:id="@+id/tabLayout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_55"
            android:layout_marginRight="@dimen/dip_40"
            app:tabIndicatorColor="@color/blue"
            app:tabIndicatorFullWidth="false"
            app:tabIndicatorHeight="@dimen/dip_2"
            app:tabMode="scrollable"
            app:tabSelectedTextColor="@color/blue"
            app:tabTextAppearance="@style/TabLayoutTextStyle"
            app:tabTextColor="@color/font_color2" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_1"
            android:layout_below="@id/tabLayout"
            android:background="@color/divider_line_color" />

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/clViewPager"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone" />

        <TextView
            android:id="@+id/look_all_order"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:drawableLeft="@mipmap/icon_order"
            android:text="@string/string_all"
            android:textAppearance="@style/Body_Dark_Bold" />


    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/orderOperateViews"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_48"
        android:layout_below="@id/orderTabRela">

        <CheckBox
            android:id="@+id/showAllSymbolsCB"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/app_margin_left"
            android:button="@drawable/checkbox_square_style"
            android:text="@string/string_show_all_symbols"
            android:gravity="center_vertical"
            android:textAppearance="@style/BodyS_Dark"
            android:textColor="@color/dark" />

        <TextView
            android:id="@+id/revoke_all_orders"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center"
            android:layout_marginRight="@dimen/app_margin_right"
            android:background="@drawable/bg_corner_blue_rect"
            android:paddingLeft="@dimen/dip_8"
            android:paddingTop="@dimen/dip_4"
            android:paddingRight="@dimen/dip_8"
            android:paddingBottom="@dimen/dip_4"
            android:text="@string/string_revoke_all_orders"
            android:textAppearance="@style/BodyS_Blue" />
    </RelativeLayout>

</LinearLayout>