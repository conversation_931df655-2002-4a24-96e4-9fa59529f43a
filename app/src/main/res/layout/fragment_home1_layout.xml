<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="false"
    >

    <io.bhex.app.view.TopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:left_visiblity="gone"
        app:title_text="@string/title_home" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/topBar"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.youth.banner.Banner
                android:id="@+id/banner"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_150"
                android:layout_marginTop="@dimen/dip_10"
                app:indicator_drawable_selected="@color/blue"
                app:indicator_drawable_unselected="@color/white"
                app:indicator_margin="@dimen/dip_2" />
            <TextView
                android:id="@+id/notice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="公告内容"
                android:drawablePadding="@dimen/dip_8"
                android:paddingLeft="@dimen/app_paddingLeft"
                android:drawableLeft="@mipmap/icon_notice"
                android:gravity="center_vertical"
                />

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/color_bg_2"
                app:tabIndicatorColor="@color/transparent"
                app:tabMode="scrollable"
                app:tabTextAppearance="@style/TabLayoutTextStyle" />


            <io.bhex.app.view.NoScrollViewPager
                android:id="@+id/viewPager"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_behavior="@string/appbar_scrolling_view_behavior" />

        </LinearLayout>

    </ScrollView>

</RelativeLayout>