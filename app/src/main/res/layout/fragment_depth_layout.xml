<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_bg_1_night"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">


        <Button
            android:id="@+id/btn_add"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="增加深度左数据"
            android:visibility="gone" />

        <Button
            android:id="@+id/btn_add_right"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="增加深度右数据"
            android:visibility="gone" />

        <Button
            android:id="@+id/btn_add_scale"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="放大比例"
            android:visibility="gone" />

        <Button
            android:id="@+id/btn_reduce_scale"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="缩小比例"
            android:visibility="gone" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/empty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dip_12"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="@dimen/dip_16"
                android:textAppearance="@style/Caption_Grey_night" />
            <TextView
                android:id="@+id/domain"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@id/empty"
                android:layout_marginLeft="@dimen/dip_16"
                android:visibility="gone"
                android:textAppearance="@style/Caption_Grey_night" />

            <ImageView
                android:id="@+id/icon"
                android:layout_width="122dp"
                android:layout_height="22dp"
                android:layout_above="@id/domain"
                android:layout_marginLeft="@dimen/dip_12"
                android:textAppearance="@style/H3_Grey_night" />

            <TextView
                android:id="@+id/name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@id/domain"
                android:layout_marginLeft="@dimen/dip_2"
                android:layout_toRightOf="@id/icon"
                android:visibility="gone"
                android:text="@string/app_name"
                android:textAppearance="@style/H4_Grey_night" />

            <!--android:background="@color/color_bg_2"-->
            <com.bhex.depth.DepthView
                android:id="@+id/depth_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="visible"
                android:background="@color/color_bg_1_night"
                app:bgGridColumnNum="2"
                app:bgGridLineColor="@color/divider_line_color20_night"
                app:bgGridRowNum="4"
                app:crossLineColor="@color/dark10"
                app:fontColor="@color/color_white"
                app:fontSize="@dimen/sp_10"
                app:gradient2EndColor="@color/red20_night"
                app:gradient2StartColor="@color/red20_night"
                app:gradientEndColor="@color/green20_night"
                app:gradientStartColor="@color/green20_night"
                app:isNeedCrossLine="true"
                app:isNeedGrid="true"
                app:line2Color="@color/red_night"
                app:lineColor="@color/green_night"
                app:marginBottom="@dimen/dip_12"
                app:marginLeft="@dimen/dip_5"
                app:marginMiddle="@dimen/dip_10"
                app:marginRight="@dimen/dip_5"
                app:marginTop="@dimen/dip_5"
                app:scale="0.9" />
        </RelativeLayout>


    </LinearLayout>
</LinearLayout>