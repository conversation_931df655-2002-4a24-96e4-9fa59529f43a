<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tv_token"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dip_24"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:text="BTC"
        tools:text="BTC"
        android:textColor="@color/dark"
        android:textSize="@dimen/sp_14"
        android:textStyle="bold"
        android:layout_marginTop="@dimen/dip_8"
        android:gravity="center_vertical"
        android:layout_marginStart="@dimen/dip_24"/>

    <LinearLayout
        android:id="@+id/title_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_token"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="@dimen/dip_8">

        <TextView
            android:id="@+id/tv_amount_title"
            android:layout_width="@dimen/dip_0"
            android:layout_height="@dimen/dip_16"
            android:text="@string/string_margin_repay_amount"
            android:textColor="@color/third_text_color"
            android:textSize="@dimen/sp_12"
            android:gravity="center_vertical"
            android:layout_marginStart="@dimen/dip_24"
            android:layout_weight="1"/>

        <TextView
            android:id="@+id/tv_record_type_title"
            android:layout_width="@dimen/dip_0"
            android:layout_height="@dimen/dip_16"
            android:text="@string/string_type"
            android:textColor="@color/third_text_color"
            android:textSize="@dimen/sp_12"
            android:gravity="center_vertical"
            android:layout_marginStart="@dimen/dip_8"
            android:layout_weight="1"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/tv_repay_interest_title"
            android:layout_width="@dimen/dip_0"
            android:layout_height="@dimen/dip_16"
            android:text="@string/string_repay_interest_amount"
            android:textColor="@color/third_text_color"
            android:textSize="@dimen/sp_12"
            android:gravity="center_vertical|right"
            android:layout_marginEnd="@dimen/dip_24"
            android:layout_marginStart="@dimen/dip_8"
            android:layout_weight="1"/>
    </LinearLayout>
    <LinearLayout
        android:id="@+id/value_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/title_ll"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/tv_amount_value"
            android:layout_width="@dimen/dip_0"
            android:layout_height="@dimen/dip_24"
            tools:text="0.01000000"
            android:textColor="@color/dark"
            android:textSize="@dimen/sp_12"
            android:gravity="center_vertical"
            android:layout_marginStart="@dimen/dip_24"
            android:layout_weight="1"/>

        <TextView
            android:id="@+id/tv_record_type_value"
            android:layout_width="@dimen/dip_0"
            android:layout_height="@dimen/dip_24"
            tools:text="申请借币"
            android:textColor="@color/dark"
            android:textSize="@dimen/sp_12"
            android:gravity="center_vertical"
            android:layout_marginStart="@dimen/dip_8"
            android:layout_weight="1"
            android:visibility="gone"/>


        <TextView
            android:id="@+id/tv_repay_interest_value"
            android:layout_width="@dimen/dip_0"
            android:layout_height="@dimen/dip_24"
            tools:text="0.01000000"
            android:textColor="@color/dark"
            android:textSize="@dimen/sp_12"
            android:gravity="center_vertical|right"
            android:layout_marginEnd="@dimen/dip_24"
            android:layout_weight="1"/>

    </LinearLayout>


    <LinearLayout
        android:id="@+id/date_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/value_ll"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/tv_date_title"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_24"
            android:text="@string/string_margin_repay_date"
            android:textColor="@color/third_text_color"
            android:textSize="@dimen/sp_12"
            android:gravity="center_vertical"
            android:layout_marginStart="@dimen/dip_24"/>

        <TextView
            android:id="@+id/tv_date_value"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_24"
            tools:text="11:56 03/12"
            android:textColor="@color/third_text_color"
            android:textSize="@dimen/sp_12"
            android:gravity="center_vertical|right"
            android:layout_marginEnd="@dimen/dip_24"
            android:layout_marginStart="@dimen/dip_8"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_token"
            app:layout_constraintBottom_toBottomOf="@id/tv_token"/>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/app_line"
        android:layout_marginStart="@dimen/dip_24"
        android:layout_marginTop="@dimen/dip_8"
        app:layout_constraintTop_toBottomOf="@id/date_ll"
        app:layout_constraintStart_toStartOf="parent"
        android:background="@color/divider_line_color" />



</androidx.constraintlayout.widget.ConstraintLayout>