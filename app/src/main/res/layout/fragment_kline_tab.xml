<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="2dp"
    android:background="@drawable/shape_kline_bg"
    >

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/kTab"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:gravity="center"
        android:tag=""
        android:text="@string/kline"
        android:textColor="#DCE1F6"
        android:textSize="@dimen/kline_tab_textSize" />


    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/k_line_fenshi"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_alignBottom="@+id/kTab"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/kTab"
        android:gravity="center"
        android:text="@string/string_times"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/kline_tab_normal"
        android:tag="kind_t_1fenshi"
        />


    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/k_line_1m"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_alignBottom="@+id/kTab"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/k_line_fenshi"
        android:gravity="center"
        android:text="@string/kline_one_minute"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/kline_tab_normal"
        android:tag="kind_t_1m"
        />


    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/k_line_5m"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_alignBottom="@+id/kTab"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/k_line_1m"
        android:gravity="center"
        android:text="@string/kline_five_minutes"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/kline_tab_normal"
        android:tag="kind_t_5m"
        />

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/k_line_30m"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_alignBottom="@+id/kTab"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/k_line_5m"
        android:gravity="center"
        android:text="@string/kline_thirty_minutes"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/kline_tab_normal"
        android:tag="kind_t_30m"
        />

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/k_line_2h"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_alignBottom="@+id/kTab"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/k_line_30m"
        android:gravity="center"
        android:text="@string/kline_two_hour"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/kline_tab_normal"
        android:tag="kind_t_2h"
        />

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/k_line_6h"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_below="@+id/kTab"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/kTab"
        android:gravity="center"
        android:text="@string/kline_six_hour"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/kline_tab_normal"
        android:tag="kind_t_6h"
        />

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/k_line_12h"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_below="@+id/kTab"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/k_line_6h"
        android:gravity="center"
        android:text="@string/kline_twelve_hour"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/kline_tab_normal"
        android:tag="kind_t_12h"/>

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/k_line_1w"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_below="@+id/kTab"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/k_line_12h"
        android:gravity="center"
        android:text="@string/kline_weeks"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/kline_tab_normal"
        android:tag="kind_t_1w"/>

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/k_line_1month"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_below="@+id/kTab"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/k_line_1w"
        android:gravity="center"
        android:text="@string/kline_months"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/kline_tab_normal"
        android:tag="kind_t_1M"/>

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/text1"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_below="@+id/k_line_1month"
        android:gravity="center"
        android:text="@string/kline_index_master_graph"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/white"
        android:tag=""/>

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/maText"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_marginLeft="10dp"
        android:layout_alignBottom="@+id/text1"
        android:layout_toRightOf="@+id/text1"
        android:gravity="center"
        android:text="@string/kline_ma"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/kline_tab_normal"
        android:tag="kind_m_ma"
        />


    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/bollText"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_marginLeft="10dp"
        android:layout_alignBottom="@+id/text1"
        android:layout_toRightOf="@+id/maText"
        android:gravity="center"
        android:text="@string/kline_boll"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/kline_tab_normal"
        android:tag="kind_m_boll"
        />

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/mainHide"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_alignBottom="@+id/text1"
        android:layout_alignLeft="@+id/subHide"
        android:gravity="center"
        android:text="@string/string_close"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/white"
        android:tag="kind_m_close"/>

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/text2"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_below="@+id/text1"
        android:gravity="center"
        android:text="@string/kline_index_sub_graph"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/white"
        android:tag=""/>

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/macdText"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_alignBottom="@+id/text2"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/text2"
        android:gravity="center"
        android:text="@string/kline_macd"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/kline_tab_normal"
        android:tag="kind_s_macd"
        />


    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/kdjText"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_alignBottom="@+id/macdText"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/macdText"
        android:gravity="center"
        android:text="@string/kline_kdj"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/kline_tab_normal"
        android:tag="kind_s_kdj"/>

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/rsiText"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_alignBottom="@+id/kdjText"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/kdjText"
        android:gravity="center"
        android:text="@string/kline_rsi"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/kline_tab_normal"
        android:tag="kind_s_rsi"
        />


    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/wrText"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_alignBottom="@+id/rsiText"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/rsiText"
        android:gravity="center"
        android:text="@string/kline_wr"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/kline_tab_normal"
        android:tag="kind_s_wr"/>

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/subHide"
        android:layout_width="50dp"
        android:layout_height="@dimen/klint_tab_height"
        android:layout_alignBottom="@+id/wrText"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/wrText"
        android:gravity="center"
        android:text="@string/string_close"
        android:textSize="@dimen/kline_tab_textSize"
        android:textColor="@color/white"
        android:tag="kind_s_close"/>


    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/subHide"
        android:background="#337284A2"
        android:layout_marginTop="10dp"
        android:visibility="gone"
        />

    <RelativeLayout
        android:id="@+id/layout_index_set"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/subHide"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="10dp"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="16dp"
        android:tag="kline_setting"
        android:visibility="gone"
        >

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/k_index_setting"
            android:textSize="12sp"
            android:textColor="#DCE1F6" />

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:src="@mipmap/ic_arrow_right" />
    </RelativeLayout>

</RelativeLayout>