<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:alpha="0.9"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/linear_kline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_14"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/dip_35"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dip_24"
            android:text="@string/kline"
            android:textAppearance="@style/BodyS_Grey"/>

        <View
            android:layout_width="@dimen/dip_0.5"
            android:layout_height="@dimen/dip_15"
            android:layout_marginLeft="@dimen/dip_20"
            android:layout_marginTop="@dimen/dip_2"
            android:background="@color/font_color3" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dip_20"
            android:orientation="vertical">

            <io.bhex.app.skin.view.SkinPercentRelativeLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_25"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tab_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/kline_minutes"
                    android:textAppearance="@style/BodyS_Dark"
                    app:layout_widthPercent="20%" />

                <TextView
                    android:id="@+id/tab_minute"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/tab_time"
                    android:text="@string/kline_one_minute"
                    android:textAppearance="@style/BodyS_Dark"
                    app:layout_widthPercent="20%" />

                <TextView
                    android:id="@+id/tab_minute_five"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/tab_minute"
                    android:text="@string/kline_five_minutes"
                    android:textAppearance="@style/BodyS_Dark"
                    app:layout_widthPercent="20%" />

                <TextView
                    android:id="@+id/tab_minute_fifteen"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/tab_minute_five"
                    android:text="@string/kline_fifteen_minutes"
                    android:textAppearance="@style/BodyS_Dark"
                    app:layout_widthPercent="20%" />

                <TextView
                    android:id="@+id/tab_minute_thirty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/tab_minute_fifteen"
                    android:text="@string/kline_thirty_minutes"
                    android:textAppearance="@style/BodyS_Dark"
                    app:layout_widthPercent="20%" />

            </io.bhex.app.skin.view.SkinPercentRelativeLayout>


            <io.bhex.app.skin.view.SkinPercentRelativeLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_25"
                android:layout_marginTop="@dimen/dip_10"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tab_hour"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/kline_one_hour"
                    android:textAppearance="@style/BodyS_Dark"
                    app:layout_widthPercent="20%" />

                <TextView
                    android:id="@+id/tab_day"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/tab_hour"
                    android:text="@string/kline_days"
                    android:textAppearance="@style/BodyS_Dark"
                    app:layout_widthPercent="20%" />

                <TextView
                    android:id="@+id/tab_week"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/tab_day"
                    android:text="@string/kline_weeks"
                    android:textAppearance="@style/BodyS_Dark"
                    app:layout_widthPercent="20%" />

                <TextView
                    android:id="@+id/tab_month"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/tab_week"
                    android:text="@string/kline_months"
                    android:textAppearance="@style/BodyS_Dark"
                    app:layout_widthPercent="20%" />

            </io.bhex.app.skin.view.SkinPercentRelativeLayout>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/index_master_graph"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dip_24"
        android:layout_marginTop="@dimen/dip_10"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/dip_35"
            android:layout_height="wrap_content"
            android:text="@string/kline_index_master_graph"
            android:textAppearance="@style/BodyS_Grey" />

        <View
            android:layout_width="@dimen/dip_0.5"
            android:layout_height="@dimen/dip_15"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="@dimen/dip_20"
            android:background="@color/font_color3" />

        <io.bhex.app.skin.view.SkinPercentRelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dip_20">

            <TextView
                android:id="@+id/tab_ma"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/kline_ma"
                android:textAppearance="@style/BodyS_Dark"
                app:layout_widthPercent="20%" />

            <TextView
                android:id="@+id/tab_ema"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@+id/tab_ma"
                android:visibility="gone"
                android:text="@string/kline_ema"
                android:textAppearance="@style/BodyS_Dark"
                app:layout_widthPercent="20%" />

            <TextView
                android:id="@+id/tab_boll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@id/tab_ma"
                android:text="@string/kline_boll"
                android:textAppearance="@style/BodyS_Dark"
                app:layout_widthPercent="20%" />

            <TextView
                android:id="@+id/tab_close_major"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:text="@string/string_close"
                android:textAppearance="@style/BodyS_Dark"
                app:layout_widthPercent="20%" />
        </io.bhex.app.skin.view.SkinPercentRelativeLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/index_sub_graph"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dip_10"
        android:layout_marginLeft="@dimen/dip_24"
        android:layout_marginTop="@dimen/dip_10"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/dip_35"
            android:layout_height="wrap_content"
            android:text="@string/kline_index_sub_graph"
            android:textAppearance="@style/BodyS_Grey" />

        <View
            android:layout_width="@dimen/dip_0.5"
            android:layout_height="@dimen/dip_15"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="@dimen/dip_20"
            android:background="@color/font_color3" />

        <io.bhex.app.skin.view.SkinPercentRelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dip_20">

            <TextView
                android:id="@+id/tab_vol"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/kline_vol"
                android:textAppearance="@style/BodyS_Dark"
                app:layout_widthPercent="20%" />

            <TextView
                android:id="@+id/tab_macd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@+id/tab_vol"
                android:text="@string/kline_macd"
                android:textAppearance="@style/BodyS_Dark"
                app:layout_widthPercent="20%" />

            <TextView
                android:id="@+id/tab_kdj"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@id/tab_macd"
                android:text="@string/kline_kdj"
                android:textAppearance="@style/BodyS_Dark"
                app:layout_widthPercent="20%" />

            <TextView
                android:id="@+id/tab_rsi"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@+id/tab_kdj"
                android:text="@string/kline_rsi"
                android:textAppearance="@style/BodyS_Dark"
                app:layout_widthPercent="20%" />

            <TextView
                android:id="@+id/tab_sub_boll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@+id/tab_rsi"
                android:text="@string/kline_boll"
                android:textAppearance="@style/BodyS_Dark"
                app:layout_widthPercent="20%" />

            <TextView
                android:id="@+id/tab_close_sub"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:text="@string/string_close"
                android:visibility="gone"
                android:textAppearance="@style/BodyS_Dark"
                app:layout_widthPercent="20%" />
        </io.bhex.app.skin.view.SkinPercentRelativeLayout>

    </LinearLayout>


</LinearLayout>