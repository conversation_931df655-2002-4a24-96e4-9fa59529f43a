<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_finance_list_item_layout.xml
  ~   @Date: 19-3-8 下午2:50
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/itemView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/app_paddingLeft"
        android:layout_marginEnd="@dimen/dip_16"
        android:layout_marginBottom="@dimen/dip_10"
        android:background="@drawable/bg_finance_product_card"
        android:paddingLeft="@dimen/dip_16"
        android:paddingTop="@dimen/dip_14"
        android:paddingRight="@dimen/dip_16"
        android:paddingBottom="@dimen/dip_14">

        <TextView
            android:id="@+id/finance_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/string_placeholder"
            android:textColor="@color/dark"
            android:textSize="@dimen/sp_19"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constrainedWidth="true"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"/>


        <TextView
            android:id="@+id/finance_rate_of_return"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_16"
            android:singleLine="true"
            android:text="@string/string_placeholder"
            android:textColor="@color/blue"
            android:textSize="@dimen/sp_24"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/finance_title"
            app:layout_constraintWidth_percent="0.49" />

        <TextView
            android:id="@+id/finance_amount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dip_4"
            android:layout_marginTop="@dimen/dip_16"
            android:singleLine="true"
            android:text="@string/string_placeholder"
            android:textColor="@color/dark"
            android:textSize="@dimen/sp_24"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/finance_title"
            app:layout_constraintWidth_percent="0.49" />

        <TextView
            android:id="@+id/finance_rate_of_return_title"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_4"
            android:singleLine="true"
            android:text="@string/string_rate_of_return"
            android:textAppearance="@style/BodyS_Grey"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/finance_rate_of_return"
            app:layout_constraintWidth_percent="0.49" />

        <TextView
            android:id="@+id/finance_amount_title"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dip_4"
            android:layout_marginTop="@dimen/dip_4"
            android:singleLine="true"
            android:text="@string/string_finance_surplus_amount"
            android:textAppearance="@style/BodyS_Grey"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/finance_rate_of_return_title"
            app:layout_constraintTop_toBottomOf="@id/finance_rate_of_return"
            app:layout_constraintWidth_percent="0.49" />


        <TextView
            android:id="@+id/finance_label"
            android:layout_width="0dp"
            android:layout_height="@dimen/dip_44"
            android:background="@drawable/bg_staking_product_status_normal"
            android:gravity="center"
            android:paddingStart="@dimen/dip_6"
            android:paddingEnd="@dimen/dip_6"
            android:text="@string/string_placeholder"
            android:textColor="@color/blue"
            android:textSize="@dimen/sp_15"
            android:textStyle="bold"
            android:visibility="visible"
            android:layout_marginTop="@dimen/dip_12"
            app:layout_constraintTop_toBottomOf="@id/finance_rate_of_return_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>