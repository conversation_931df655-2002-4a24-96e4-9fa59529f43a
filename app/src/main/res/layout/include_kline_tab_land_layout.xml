<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:alpha="0.8"
    android:layout_alignParentBottom="true"
    android:background="@color/font_color1"
    android:orientation="vertical"
    android:padding="@dimen/app_padding">

    <LinearLayout
        android:id="@+id/linear_kline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/dip_35"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center"
            android:text="@string/kline"
            android:textColor="@color/blue"
            android:textSize="@dimen/font_13" />

        <View
            android:layout_width="@dimen/dip_1"
            android:layout_height="@dimen/dip_45"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="@dimen/dip_30"
            android:background="@color/font_color2" />

        <LinearLayout

            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_25"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tab_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/kline_minutes"
                    android:textColor="@color/font_color2"
                    android:textSize="@dimen/font_13" />

                <TextView
                    android:id="@+id/tab_minute"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/kline_one_minute"
                    android:textColor="@color/font_color2"
                    android:textSize="@dimen/font_13" />

                <TextView
                    android:id="@+id/tab_minute_five"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/kline_five_minutes"
                    android:textColor="@color/font_color2"
                    android:textSize="@dimen/font_13" />

                <TextView
                    android:id="@+id/tab_minute_fifteen"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/kline_fifteen_minutes"
                    android:textColor="@color/font_color2"
                    android:textSize="@dimen/font_13" />

                <TextView
                    android:id="@+id/tab_minute_thirty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/kline_thirty_minutes"
                    android:textColor="@color/font_color2"
                    android:textSize="@dimen/font_13" />

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_25"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tab_hour"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/kline_one_hour"
                    android:textColor="@color/font_color2"
                    android:textSize="@dimen/font_13" />

                <TextView
                    android:id="@+id/tab_day"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/kline_days"
                    android:textColor="@color/font_color2"
                    android:textSize="@dimen/font_13" />

                <TextView
                    android:id="@+id/tab_week"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/kline_weeks"
                    android:textColor="@color/font_color2"
                    android:textSize="@dimen/font_13" />

                <TextView
                    android:id="@+id/tab_month"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/kline_months"
                    android:textColor="@color/font_color2"
                    android:textSize="@dimen/font_13" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text=""
                    android:textColor="@color/font_color2"
                    android:textSize="@dimen/font_13" />


            </LinearLayout>


        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/index_master_graph"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_20"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/dip_35"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center"
            android:text="@string/kline_index"
            android:textColor="@color/blue"
            android:textSize="@dimen/font_13" />

        <View
            android:layout_width="@dimen/dip_1"
            android:layout_height="@dimen/dip_20"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="@dimen/dip_30"
            android:background="@color/font_color2" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_25"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tab_vol"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/kline_vol"
                android:textColor="@color/font_color2"
                android:textSize="@dimen/font_13" />


            <TextView
                android:id="@+id/tab_boll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/kline_boll"
                android:textColor="@color/font_color2"
                android:textSize="@dimen/font_13" />

            <TextView
                android:id="@+id/tab_macd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/kline_macd"
                android:textColor="@color/font_color2"
                android:textSize="@dimen/font_13" />

            <TextView
                android:id="@+id/tab_kdj"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/kline_kdj"
                android:textColor="@color/font_color2"
                android:textSize="@dimen/font_13" />

            <TextView
                android:id="@+id/tab_rsi"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/kline_rsi"
                android:textColor="@color/font_color2"
                android:textSize="@dimen/font_13" />

        </LinearLayout>

    </LinearLayout>


</LinearLayout>