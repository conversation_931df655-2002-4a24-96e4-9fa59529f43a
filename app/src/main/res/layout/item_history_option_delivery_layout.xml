<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_128"
    android:background="@drawable/item_style"
    android:orientation="vertical"
    >

    <RelativeLayout
        android:id="@+id/rela1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_16"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginRight="@dimen/app_margin_right"
        >
        <TextView
            android:id="@+id/option_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark"/>

    </RelativeLayout>

    <io.bhex.app.skin.view.SkinPercentRelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_14"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginRight="@dimen/app_margin_right"
        android:layout_below="@id/rela1"
        >

        <LinearLayout
            android:id="@+id/linear1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_widthPercent="30%">

            <TextView
                android:id="@+id/option_time_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Grey"/>



            <TextView
                android:id="@+id/option_price_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Grey"/>

            <TextView
                android:id="@+id/option_delivery_price_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Grey"/>


            <TextView
                android:id="@+id/option_vol_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Grey"/>


        </LinearLayout>

        <LinearLayout
            android:id="@+id/linear2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_toRightOf="@+id/linear1"
            android:orientation="vertical"
            app:layout_widthPercent="50%">

            <TextView
                android:id="@+id/option_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Dark"/>

            <TextView
                android:id="@+id/option_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Dark"/>

            <TextView
                android:id="@+id/option_delivery_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Dark"/>

            <TextView
                android:id="@+id/option_vol"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Dark"/>

        </LinearLayout>

    </io.bhex.app.skin.view.SkinPercentRelativeLayout>
    <View
        android:id="@+id/item_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:background="@color/divider_line_color"
        android:layout_alignParentBottom="true"
        />
</RelativeLayout>