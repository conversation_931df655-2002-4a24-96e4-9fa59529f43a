<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/item_style"
    android:orientation="vertical"
    >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginRight="@dimen/app_margin_left"
        android:orientation="horizontal">
    <TextView
        android:id="@+id/item_asset_coinplus_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:layout_marginTop="@dimen/dip_15"
        android:layout_marginBottom="@dimen/dip_12"
        android:text="@string/string_placeholder"
        android:textSize="@dimen/sp_15"
        android:textColor="@color/dark"/>
    </RelativeLayout>


    <io.bhex.app.skin.view.SkinPercentRelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/app_paddingLeft"
        android:paddingRight="@dimen/app_paddingRight"
        >


        <TextView
            android:id="@+id/item_asset_coinplus_title"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_16"
            app:layout_widthPercent="50%"
            android:gravity="center_vertical"
            android:text="@string/string_asset_coinplus_total_profit"
            android:textAppearance="@style/BodyS_Grey" />
        <TextView
            android:id="@+id/title_sevenYearRate"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_16"
            android:layout_toRightOf="@+id/item_asset_coinplus_title"
            app:layout_widthPercent="50%"
            android:gravity="center_vertical|right"
            android:text="@string/string_asset_coinplus_seven_year_rate"
            android:textAppearance="@style/BodyS_Grey" />

    </io.bhex.app.skin.view.SkinPercentRelativeLayout>
    <io.bhex.app.skin.view.SkinPercentRelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dip_8"
        android:layout_marginTop="@dimen/dip_4"
        android:paddingLeft="@dimen/app_paddingLeft"
        android:paddingRight="@dimen/app_paddingRight"
        >
        <TextView
            android:id="@+id/item_asset_coinplus_value"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:layout_toRightOf="@id/value_totalProfit"
            app:layout_widthPercent="50%"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark_Bold" />
        <TextView
            android:id="@+id/value_sevenYearRate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:layout_alignParentRight="true"
            android:layout_toRightOf="@+id/item_asset_coinplus_value"
            app:layout_widthPercent="50%"
            android:layout_centerVertical="true"
            android:gravity="center_vertical|right"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark_Bold" />

    </io.bhex.app.skin.view.SkinPercentRelativeLayout>

    <io.bhex.app.skin.view.SkinPercentRelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/app_paddingLeft"
        android:paddingRight="@dimen/app_paddingRight"
        >

        <TextView
            android:id="@+id/title_totalProfit"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_16"
            app:layout_widthPercent="50%"
            android:gravity="center_vertical"
            android:text="@string/string_asset_coinplus_total_profit"
            android:textAppearance="@style/BodyS_Grey" />
        <TextView
            android:id="@+id/title_lastProfit"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_16"
            android:layout_toRightOf="@id/title_totalProfit"
            app:layout_widthPercent="50%"
            android:gravity="center_vertical|right"
            android:text="@string/string_asset_coinplus_last_profit"
            android:textAppearance="@style/BodyS_Grey" />

    </io.bhex.app.skin.view.SkinPercentRelativeLayout>
    <io.bhex.app.skin.view.SkinPercentRelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_4"
        android:paddingLeft="@dimen/app_paddingLeft"
        android:paddingRight="@dimen/app_paddingRight"
        >
        <TextView
            android:id="@+id/value_totalProfit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:singleLine="true"
            app:layout_widthPercent="50%"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark_Bold" />
        <TextView
            android:id="@+id/value_lastProfit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:layout_toRightOf="@id/value_totalProfit"
            app:layout_widthPercent="50%"
            android:layout_centerVertical="true"
            android:gravity="center_vertical|right"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark_Bold" />

    </io.bhex.app.skin.view.SkinPercentRelativeLayout>
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_10"
        android:layout_marginBottom="@dimen/dip_10"
        android:layout_alignParentRight="true"
        android:layout_marginRight="@dimen/app_margin_right"
        android:layout_gravity="right"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/item_asset_coinplus_buy"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_22"
            android:singleLine="true"
            android:elevation="5dp"
            android:paddingRight="@dimen/dip_20"
            android:paddingLeft="@dimen/dip_20"
            android:gravity="center"
            android:background="@color/blue"
            android:layout_marginLeft="@dimen/app_margin_left"
            android:text="@string/string_asset_coinplus_buy"
            android:textSize="@dimen/sp_12"
            android:textColor="@color/color_white" />
        <TextView
            android:id="@+id/item_asset_coinplus_sell"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_22"
            android:singleLine="true"
            android:elevation="5dp"
            android:paddingRight="@dimen/dip_20"
            android:paddingLeft="@dimen/dip_20"
            android:gravity="center"
            android:background="@color/blue"
            android:layout_marginLeft="@dimen/app_margin_left"
            android:text="@string/string_asset_coinplus_sell"
            android:textSize="@dimen/sp_12"
            android:textColor="@color/color_white"/>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/app_line"
        android:background="@color/divider_line_color"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:layout_marginRight="@dimen/app_margin_right"
        />
</LinearLayout>