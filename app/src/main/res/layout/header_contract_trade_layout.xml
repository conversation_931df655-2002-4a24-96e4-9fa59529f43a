<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="-8dp"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_420"
        android:layout_marginTop="@dimen/dip_16"
        >

        <!-- 交易下单界面 -->
        <LinearLayout
            android:id="@+id/trade_linear"
            android:layout_width="@dimen/dip_0"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_percent="0.55"
            android:orientation="vertical"
            android:paddingLeft="@dimen/app_paddingLeft"
            android:paddingBottom="@dimen/dip_5"
            android:visibility="visible"
            >

            <!-- 请求焦点 -->
            <LinearLayout
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_0"
                android:focusable="true"
                android:focusableInTouchMode="true">

                <requestFocus />
            </LinearLayout>

            <!-- 买卖tab -->
            <include
                android:id="@+id/tab"
                layout="@layout/trade_tab_layout" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- 委托单类型 -->
                <RelativeLayout
                    android:id="@+id/placeOrderModeRela"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dip_24"
                    android:layout_weight="1"
                    android:layout_marginTop="@dimen/dip_8"
                    android:layout_marginBottom="@dimen/dip_8">

                    <TextView
                        android:id="@+id/placeOrderMode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:text="@string/string_ordinary_entrument"
                        android:textAppearance="@style/BodyS_Grey_Bold" />

                    <ImageView
                        android:id="@+id/placeOrderModeArrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_toRightOf="@id/placeOrderMode"
                        android:layout_centerVertical="true"
                        android:src="@mipmap/icon_arrow_down_black" />
                </RelativeLayout>
                <RelativeLayout
                    android:id="@+id/leverRela"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dip_24"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:layout_marginTop="@dimen/dip_8"
                    android:layout_marginBottom="@dimen/dip_8">

                    <TextView
                        android:id="@+id/lerverTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        android:layout_centerVertical="true"
                        android:text="@string/string_lever"
                        android:textAppearance="@style/BodyS_Grey_Bold" />
                    <TextView
                        android:id="@+id/lerverNum"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="@dimen/dip_8"
                        android:layout_toRightOf="@id/lerverTitle"
                        android:text="--X"
                        android:textAppearance="@style/BodyS_Dark_Bold" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_toRightOf="@id/lerverNum"
                        android:layout_centerVertical="true"
                        android:src="@mipmap/icon_arrow_down_black" />
                </RelativeLayout>
            </LinearLayout>

            <!-- 触发价格输入 -->
            <RelativeLayout
                android:id="@+id/trigger_price_rela"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_43">

                <TextView
                    android:id="@+id/trigger_price_unit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="@dimen/dip_8"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/Body_Grey" />

                <EditText
                    android:id="@+id/trigger_price"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_toLeftOf="@id/trigger_price_unit"
                    android:background="@null"
                    android:hint="@string/string_trigger_price"
                    android:inputType="numberDecimal"
                    android:maxLines="1"
                    android:paddingLeft="@dimen/dip_8"
                    android:paddingRight="@dimen/dip_8"
                    android:textAppearance="@style/Body_Dark_Bold"
                    android:textColor="@color/dark"
                    android:textColorHint="@color/dark50" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_43"
                android:layout_marginTop="@dimen/dip_8">

                <!-- 市价价格输入框 -->
                <TextView
                    android:id="@+id/priceMarket"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dip_43"
                    android:background="@drawable/bg_market_price_gray"
                    android:gravity="center_vertical"
                    android:text="@string/string_market_price_hint"
                    android:paddingLeft="@dimen/dip_8"
                    android:textAppearance="@style/Body_Grey_Bold"
                    android:visibility="gone" />

                <!-- 限价价格输入 -->
                <RelativeLayout
                    android:id="@+id/edit_price_rela"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dip_43"
                    >

                    <EditText
                        android:id="@+id/edit_price"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@null"
                        android:hint="@string/string_price"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingLeft="@dimen/dip_8"
                        android:paddingRight="@dimen/dip_8"
                        android:textAppearance="@style/Body_Dark_Bold"
                        android:textCursorDrawable="@drawable/cursor_color"
                        android:textColor="@color/dark"
                        android:textColorHint="@color/dark50" />
                </RelativeLayout>
                <TextView
                    android:id="@+id/edit_price_unit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="@dimen/dip_8"
                    android:text="@string/string_limited_price"
                    android:textAppearance="@style/Body_Blue_Bold" />
            </RelativeLayout>

            <!-- 输入价格 法币估值 -->
            <TextView
                android:id="@+id/priceAbout"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_24"
                android:layout_marginLeft="@dimen/dip_1"
                android:layout_marginTop="@dimen/dip_8"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/BodyS_Grey" />

            <!-- 数量输入 -->
            <RelativeLayout
                android:id="@+id/edit_amount_rela"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_43">

                <TextView
                    android:id="@+id/edit_amount_unit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="@dimen/dip_8"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/Body_Grey" />

                <EditText
                    android:id="@+id/edit_amount"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_toLeftOf="@id/edit_amount_unit"
                    android:textCursorDrawable="@drawable/cursor_color"
                    android:background="@null"
                    android:hint="@string/string_amount"
                    android:inputType="numberDecimal"
                    android:maxLines="1"
                    android:paddingLeft="@dimen/dip_8"
                    android:paddingRight="@dimen/dip_8"
                    android:textAppearance="@style/Body_Dark_Bold"
                    android:textColor="@color/dark"
                    android:textColorHint="@color/dark50" />
            </RelativeLayout>

            <!-- 滑动杆 -->
            <io.bhex.app.view.StepView
                android:id="@+id/stepView"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_28"
                android:layout_marginTop="@dimen/dip_8"
                app:background_line_color="@color/grey"
                app:background_line_hight="@dimen/dip_4"
                app:background_small_circle_color="@color/grey"
                app:background_small_circle_radius="@dimen/dip_9"
                app:foreground_big_circle_color="@color/blue"
                app:foreground_big_circle_radius="@dimen/dip_9"
                app:foreground_line_color="@color/blue"
                app:foreground_line_hight="@dimen/dip_4"
                app:foreground_small_circle_color="@color/blue"
                app:foreground_small_circle_radius="@dimen/dip_9"
                app:progress="0"
                app:steps_num="5" />

            <!-- 滑动杆 最大最小值 -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_5"
                android:visibility="gone">

                <TextView
                    android:id="@+id/stepView_minTxt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textColor="@color/font_color2"
                    android:textSize="@dimen/font_10" />


                <TextView
                    android:id="@+id/stepView_maxTxt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:text="@string/string_stepview_max_txt_format"
                    android:textColor="@color/font_color2"
                    android:textSize="@dimen/font_10" />

            </RelativeLayout>

            <!-- 可用余额 & 可用余额法币估值 -->
            <RelativeLayout
                android:id="@+id/avaliable_rela"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_8"
                android:visibility="visible"
                >

                <TextView
                    android:id="@+id/balance_available_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:text="@string/string_use_available"
                    android:textAppearance="@style/BodyS_Grey" />

                <TextView
                    android:id="@+id/balance_available"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:maxLines="1"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/BodyS_Dark" />

                <TextView
                    android:id="@+id/balance_available_about"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:layout_below="@id/balance_available"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="@dimen/dip_4"
                    android:gravity="right"
                    android:maxLines="1"
                    android:text="@string/string_placeholder"
                    android:textAppearance="@style/BodyS_Grey" />

            </RelativeLayout>

            <!-- 应付交易金额 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="visible">

                <LinearLayout
                    android:id="@+id/can_close_ll"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:layout_marginTop="@dimen/dip_4">

                    <TextView
                        android:id="@+id/can_close_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/dip_8"
                        android:text="@string/string_can_close"
                        android:textAppearance="@style/BodyS_Grey" />

                    <TextView
                        android:id="@+id/can_close_quantity"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:text="@string/string_placeholder"
                        android:textAppearance="@style/BodyS_Dark" />
                </LinearLayout>

                <RelativeLayout
                    android:id="@+id/margin_ll"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dip_8">

                    <TextView
                        android:id="@+id/margin_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/dip_2"
                        android:text="@string/string_margin"
                        android:textAppearance="@style/BodyS_Grey" />

                    <TextView
                        android:id="@+id/margin"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:maxLines="1"
                        android:gravity="right"
                        android:text="@string/string_placeholder"
                        android:textAppearance="@style/BodyS_Dark" />

                    <TextView
                        android:id="@+id/margin_about"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        android:layout_below="@id/margin"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="@dimen/dip_5"
                        android:maxLines="1"
                        android:gravity="right"
                        android:text="@string/string_placeholder"
                        android:textAppearance="@style/BodyS_Grey" />
                </RelativeLayout>
                <TextView
                    android:id="@+id/btnTransfer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="right"
                    android:gravity="center_vertical"
                    android:drawableLeft="@mipmap/icon_transfer_blue"
                    android:text="@string/string_asset_transfer"
                    android:textAppearance="@style/BodyS_Blue_Bold"
                    android:layout_below="@id/balance_available_title"
                    android:layout_marginTop="@dimen/dip_8"
                    />

            </LinearLayout>

            <!-- 下单按钮 -->
            <Button
                android:id="@+id/btn_create_order"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_40"
                android:layout_marginTop="@dimen/dip_12"
                android:layout_marginBottom="@dimen/dip_8"
                android:background="@drawable/bg_corner_green"
                android:gravity="center"
                android:text="@string/string_login"
                android:textAppearance="@style/Body_White_Bold_Default" />

            <LinearLayout
                android:id="@+id/orderStrategyLinear"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                >
                <TextView
                    android:id="@+id/orderStrategyOnlyMaker"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:textAppearance="@style/BodyS_Dark_Bold"
                    android:background="@drawable/bg_gray_line"
                    android:paddingLeft="@dimen/dip_8"
                    android:paddingRight="@dimen/dip_8"
                    android:paddingTop="@dimen/dip_4"
                    android:paddingBottom="@dimen/dip_4"
                    android:text="@string/string_only_maker"
                    />
                <Space
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />
                <TextView
                    android:id="@+id/orderStrategyIOC"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:textAppearance="@style/BodyS_Dark_Bold"
                    android:background="@drawable/bg_gray_line"
                    android:text="@string/string_ioc"
                    android:paddingLeft="@dimen/dip_8"
                    android:paddingRight="@dimen/dip_8"
                    android:paddingTop="@dimen/dip_4"
                    android:paddingBottom="@dimen/dip_4"
                    />
                <Space
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />
                <TextView
                    android:id="@+id/orderStrategyFOK"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:textAppearance="@style/BodyS_Dark_Bold"
                    android:background="@drawable/bg_gray_line"
                    android:text="@string/string_fok"
                    android:paddingLeft="@dimen/dip_8"
                    android:paddingRight="@dimen/dip_8"
                    android:paddingTop="@dimen/dip_4"
                    android:paddingBottom="@dimen/dip_4"
                    />
                <Space
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />
                <ImageView
                    android:id="@+id/orderStrategyHelp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@mipmap/icon_help"
                    android:layout_marginLeft="-8dp"
                    />
            </LinearLayout>
        </LinearLayout>

        <!-- 2 交易盘口行情 -->
        <LinearLayout
            android:layout_width="@dimen/dip_0"
            android:layout_height="match_parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintWidth_percent="0.45"
            android:layout_alignParentRight="true"
            android:layout_toRightOf="@+id/trade_linear"
            android:orientation="vertical"
            android:paddingLeft="@dimen/dip_16"
            android:paddingBottom="@dimen/dip_5"
            >

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingRight="@dimen/app_paddingRight"
                >

                <TextView
                    android:id="@+id/title_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:text="@string/string_price_ph"
                    android:textAppearance="@style/Caption_Grey" />

                <TextView
                    android:id="@+id/title_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_toRightOf="@id/title_price"
                    android:layout_marginLeft="@dimen/dip_4"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:gravity="right"
                    android:drawableRight="@mipmap/icon_arrow_down_gray"
                    android:text="@string/string_amount_format"
                    android:textAppearance="@style/Caption_Grey" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_8"
                android:layout_marginBottom="@dimen/dip_4"
                android:descendantFocusability="blocksDescendants">

                <io.bhex.app.view.InnerRecyclerView
                    android:id="@+id/bookList"
                    android:layout_width="match_parent"
                    android:layout_height="386dp" />
            </RelativeLayout>
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_8"
        android:background="@color/divider_line_color" />
    <!-- 订单tab -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_56"
        android:background="@color/white"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/app_paddingLeft"
        android:paddingRight="@dimen/app_paddingRight">

        <io.bhex.app.skin.view.SkinTabLayout
            android:id="@+id/tabLayout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_55"
            android:layout_marginRight="@dimen/dip_40"
            app:tabIndicatorColor="@color/blue"
            app:tabIndicatorHeight="@dimen/dip_2"
            app:tabIndicatorFullWidth="false"
            app:tabMode="scrollable"
            app:tabSelectedTextColor="@color/blue"
            app:tabTextAppearance="@style/TabLayoutTextStyle"
            app:tabTextColor="@color/font_color2" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_1"
            android:layout_below="@id/tabLayout"
            android:background="@color/divider_line_color" />

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/clViewPager"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone" />

        <TextView
            android:id="@+id/look_all_order"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_alignParentRight="true"
            android:drawableLeft="@mipmap/icon_order"
            android:layout_gravity="center"
            android:text="@string/string_all"
            android:textAppearance="@style/Body_Dark_Bold"/>


    </RelativeLayout>
</LinearLayout>