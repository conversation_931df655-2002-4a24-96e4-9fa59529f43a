<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:fitsSystemWindows="false">

    <io.bhex.app.view.TopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:left_visiblity="gone"
        app:title_text="@string/title_trade" />

    <io.bhex.app.view.BookListView
        android:id="@+id/bookListView"
        android:layout_width="@dimen/dip_200"
        android:layout_height="@dimen/dip_300"
        android:orientation="vertical"
        />

    <Button
        android:id="@+id/btn1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="买"
        />
    <Button
        android:id="@+id/btn2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="卖"
        />
    <Button
        android:id="@+id/btn3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="默认"
        />

    <TextView
        android:id="@+id/progressValue"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dip_10"
        android:textSize="@dimen/font_15"
        />


    <io.bhex.app.view.StepView
        android:id="@+id/stepView"
        android:layout_width="@dimen/dip_180"
        android:layout_height="@dimen/dip_30"
        android:layout_margin="@dimen/dip_30"
        android:text="@string/string_developing"
        app:background_line_color="@color/grey"
        app:background_line_hight="@dimen/dip_2"
        app:background_small_circle_color="@color/grey"
        app:background_small_circle_radius="@dimen/dip_7"
        app:foreground_line_color="@color/red"
        app:foreground_line_hight="@dimen/dip_3"
        app:foreground_small_circle_color="@color/red"
        app:foreground_small_circle_radius="@dimen/dip_7"
        app:foreground_big_circle_color="@color/red20"
        app:foreground_big_circle_radius="@dimen/dip_14"
        app:steps_num="5"
        app:progress="0"
        />

    <LinearLayout
        android:id="@+id/bookList"
        android:layout_width="@dimen/dip_200"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical"/>


</LinearLayout>