<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_50"
    android:background="@color/color_bg_1"
    android:paddingStart="@dimen/app_paddingLeft"
    android:paddingEnd="@dimen/app_paddingRight">


    <TextView
        android:id="@+id/item_value"
        android:layout_width="@dimen/dip_0"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintWidth_percent="0.25"
        android:textAppearance="@style/Body_Dark"
        android:maxLines="1"
        android:text="" />

    <TextView
        android:id="@+id/item_api_cost_time"
        android:layout_toLeftOf="@+id/item_selected"
        android:layout_width="@dimen/dip_0"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@id/item_value"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintWidth_percent="0.25"
        android:maxLines="1"
        android:text=""
        android:layout_marginLeft="@dimen/app_margin"
        android:textAppearance="@style/Body_Dark" />
    <TextView
        android:id="@+id/item_ws_cost_time"
        android:layout_toLeftOf="@+id/item_selected"
        android:layout_width="@dimen/dip_0"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@id/item_api_cost_time"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintWidth_percent="0.25"
        android:text=""
        android:maxLines="1"
        android:layout_marginLeft="@dimen/app_margin"
        android:textAppearance="@style/Body_Dark" />

    <ImageView
        android:id="@id/item_selected"
        android:layout_width="24dp"
        android:layout_height="24dp"
        app:layout_constraintStart_toEndOf="@id/item_ws_cost_time"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_alignParentRight="true"
        android:layout_margin="@dimen/dip_4"
        android:src="@mipmap/icon_checkbox_normal"
         />

    <View
        android:layout_alignParentBottom="true"
        android:layout_width="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_height="@dimen/dip_1"
        android:background="@color/divider_line_color"
        />

</androidx.constraintlayout.widget.ConstraintLayout>