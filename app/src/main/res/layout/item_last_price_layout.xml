<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_52"
    android:orientation="vertical"
    >
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_centerVertical="true"
        android:layout_marginLeft="@dimen/dip_5"
        >
        <TextView
            android:id="@+id/averagePrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@style/BodyM_Green_Bold"
            android:text="@string/string_placeholder"
            />

        <TextView
            android:id="@+id/price2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@style/Caption_Grey"
            android:text="@string/string_placeholder"
            />
        <TextView
            android:id="@+id/netValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@mipmap/icon_tips_grey"
            android:textAppearance="@style/Caption_Grey_Bold"
            android:text="@string/string_placeholder"
            android:visibility="visible"
            android:gravity="center_vertical"
            />
    </LinearLayout>

    <ImageView
        android:id="@+id/btn_setting"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:paddingLeft="@dimen/dip_10"
        android:paddingTop="@dimen/dip_3"
        android:paddingBottom="@dimen/dip_3"
        android:layout_marginEnd="@dimen/dip_16"
        android:src="@mipmap/icon_book_setting" />

</RelativeLayout>