<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl_title"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        tools:background="#151E2E">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_indicator_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="17sp"
            android:textColor="#D8DCE5"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            tools:text="MA"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_indicator_set_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            android:textColor="#5C698C"
            android:textSize="15sp"
            android:layout_marginStart="20dp"
            android:layout_toRightOf="@+id/tv_indicator_title"
            android:layout_toLeftOf="@+id/iv_more"
            android:layout_centerVertical="true"
            android:gravity="right"
            android:singleLine="true"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="MA5&#160;&#160;MA10&#160;&#160;MA20"
            />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/ic_index_arrow_down"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"/>

    </RelativeLayout>


</LinearLayout>
