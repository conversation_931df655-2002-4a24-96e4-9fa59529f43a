<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_80"
    android:background="@drawable/item_style">

    <ImageView
        android:id="@+id/item_asset_record_type_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:visibility="gone"
        android:layout_marginLeft="@dimen/app_margin_left" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toRightOf="@id/item_asset_record_type_icon"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:orientation="vertical">

        <TextView
            android:id="@+id/item_asset_record_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark" />

        <TextView
            android:id="@+id/item_asset_record_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dip_8"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Caption_Grey" />

    </LinearLayout>
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:gravity="right"
        android:layout_alignParentRight="true"
        android:layout_marginRight="@dimen/app_margin_right"
        android:orientation="vertical">

        <TextView
            android:id="@+id/item_asset_record_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:layout_centerHorizontal="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Dark" />

        <TextView
            android:id="@+id/btnCancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_marginTop="@dimen/dip_8"
            android:textAppearance="@style/Body_Blue"
            android:text="@string/string_cancel"
            android:background="@drawable/bg_corner_rect_blue"
            android:paddingLeft="@dimen/dip_24"
            android:paddingRight="@dimen/dip_24"
            android:paddingTop="@dimen/dip_4"
            android:paddingBottom="@dimen/dip_4"
            />

        <TextView
            android:id="@+id/item_asset_record_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dip_8"
            android:visibility="gone"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Caption_Grey" />

        <TextView
            android:id="@+id/item_asset_record_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dip_8"
            android:visibility="gone"
            android:text="@string/string_detail"
            android:textAppearance="@style/Caption_Blue" />

    </LinearLayout>

    <View
        android:id="@+id/item_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_1"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="@dimen/app_margin_left"
        android:background="@color/divider_line_color" />

</RelativeLayout>