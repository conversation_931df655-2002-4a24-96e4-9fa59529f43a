<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dip_45"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_centerVertical="true"
    android:background="@color/white_night"
    >

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:paddingBottom="@dimen/dip_3"
        android:text="@string/kline_index_master_graph"
        android:textAppearance="@style/BodyS_Dark_night"/>


    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/tab_land_ma"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:paddingTop="@dimen/dip_3"
        android:paddingBottom="@dimen/dip_3"
        android:text="@string/kline_ma"
        android:textColor="@color/kline_tab_normal"
        android:textSize="@dimen/sp_12"
        android:tag="kind_m_ma"/>


    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/tab_land_ema"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_gravity="center"
        android:paddingTop="@dimen/dip_3"
        android:paddingBottom="@dimen/dip_3"
        android:text="@string/kline_ema"
        android:textColor="@color/kline_tab_normal"
        android:textSize="@dimen/sp_12" />

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/tab_land_boll"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:paddingTop="@dimen/dip_3"
        android:paddingBottom="@dimen/dip_3"
        android:text="@string/kline_boll"
        android:textColor="@color/kline_tab_normal"
        android:textSize="@dimen/sp_12"
        android:tag="kind_m_boll"/>

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/tab_land_close_major"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dip_3"
        android:paddingBottom="@dimen/dip_3"
        android:layout_gravity="center"
        android:text="@string/string_close"
        android:textColor="@color/kline_tab_normal"
        android:textSize="@dimen/sp_12"
        android:tag="kind_m_close" />


    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_0.5"
        android:background="@color/divider_line_color20"
        android:layout_marginTop="@dimen/dip_6"
        />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:layout_gravity="center"
        android:gravity="center"
        android:paddingTop="@dimen/dip_3"
        android:paddingBottom="@dimen/dip_3"
        android:text="@string/kline_index_sub_graph"
        android:textAppearance="@style/BodyS_Dark_night" />

    <TextView
        android:id="@+id/tab_land_vol"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_6"
        android:text="@string/kline_vol"
        android:visibility="gone"
        android:layout_gravity="center"
        android:textAppearance="@style/BodyS_Dark"/>

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/tab_land_macd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dip_3"
        android:paddingBottom="@dimen/dip_3"
        android:text="@string/kline_macd"
        android:layout_gravity="center"
        android:textColor="@color/kline_tab_normal"
        android:textSize="@dimen/sp_12"
        android:tag="kind_s_macd"
        />

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/tab_land_kdj"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dip_3"
        android:paddingBottom="@dimen/dip_3"
        android:text="@string/kline_kdj"
        android:layout_gravity="center"
        android:textColor="@color/kline_tab_normal"
        android:textSize="@dimen/sp_12"
        android:tag="kind_s_kdj"/>

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/tab_land_rsi"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dip_3"
        android:paddingBottom="@dimen/dip_3"
        android:text="@string/kline_rsi"
        android:layout_gravity="center"
        android:textColor="@color/kline_tab_normal"
        android:textSize="@dimen/sp_12"
        android:tag="kind_s_rsi"/>

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/tab_land_sub_wr"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dip_3"
        android:paddingBottom="@dimen/dip_3"
        android:text="@string/kline_wr"
        android:layout_gravity="center"
        android:textColor="@color/kline_tab_normal"
        android:textSize="@dimen/sp_12"
        android:tag="kind_s_wr"/>

    <com.bhex.kline.widget.tab.TabTextView
        android:id="@+id/tab_land_close_sub"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dip_3"
        android:paddingBottom="@dimen/dip_3"
        android:text="@string/string_close"
        android:layout_gravity="center"
        android:textColor="@color/kline_tab_normal"
        android:textSize="@dimen/sp_12"
        android:tag="kind_s_close"/>

</LinearLayout>