<?xml version="1.0" encoding="utf-8"?>
<RadioGroup xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/tabbar_layout"
    android:background="@color/dark1"
    android:orientation="horizontal">

    <io.bhex.baselib.view.PopupRadioButton
        android:id="@+id/main_home_tab"
        style="@style/main_tab_item"
        android:layout_height="match_parent"
        android:drawableTop="@drawable/main_tab_home"
        android:visibility="visible"
        android:text="@string/app_channel_home" />

    <io.bhex.baselib.view.PopupRadioButton
        android:id="@+id/main_market_tab"
        style="@style/main_tab_item"
        android:layout_height="match_parent"
        android:drawableTop="@drawable/main_tab_market"
        android:visibility="visible"
        android:text="@string/app_channel_market" />

    <io.bhex.baselib.view.PopupRadioButton
        android:id="@+id/main_trade_tab"
        style="@style/main_tab_item"
        android:layout_height="match_parent"
        android:drawableTop="@drawable/main_tab_trade"
        android:text="@string/app_channel_trade" />

    <io.bhex.baselib.view.PopupRadioButton
        android:id="@+id/main_contract_trade_tab"
        style="@style/main_tab_item"
        android:layout_height="match_parent"
        android:drawableTop="@drawable/main_tab_contract"
        android:text="@string/string_contract" />

    <io.bhex.baselib.view.PopupRadioButton
        android:id="@+id/main_asset"
        style="@style/main_tab_item"
        android:layout_height="match_parent"
        android:drawableTop="@drawable/main_tab_asset"
        android:text="@string/string_my_asset" />

</RadioGroup>