<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    >

    <include
        android:id="@+id/latest_header"
        layout="@layout/include_latest_header"
        />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/app_line"
        android:layout_marginStart="@dimen/dip_12"
        android:visibility="gone"
        android:background="@color/divider_line_color_night"
        />

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefresh"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:listitem="@layout/item_latest_deal_list_layout"
            />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>



</LinearLayout>