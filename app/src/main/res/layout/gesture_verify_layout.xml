<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/blue">
    <io.bhex.app.view.TopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:left_icon="@mipmap/icon_close_white"
        app:bgcolor="@color/blue"
        app:title_text=""
        app:left_text="  "
        />
    <TextView
        android:id="@+id/username"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/topBar"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dip_32"
        android:gravity="center"
        android:textAppearance="@style/H3_White"
        android:text="" />

    <TextView
        android:id="@+id/text_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/username"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dip_10"
        android:gravity="center_vertical"
        android:text="@string/string_draw_gesture_passwd"
        android:textColor="@color/white50" />

    <FrameLayout
        android:id="@+id/gesture_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"/>

    <TextView
        android:id="@+id/tvForgetPasswd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="90dip"
        android:layout_centerHorizontal="true"
        android:layout_alignParentBottom="true"
        android:gravity="center"
        android:text="@string/string_passwd_login"
        android:textAppearance="@style/BodyL_White" />

</RelativeLayout>