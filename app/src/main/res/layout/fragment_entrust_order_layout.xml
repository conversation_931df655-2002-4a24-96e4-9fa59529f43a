<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_bg_1"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/app_line"
        android:background="@color/divider_line_color" />

    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/tab_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="visible">

        <com.flyco.tablayout.SegmentTabLayout
            android:id="@+id/tabEntrust"
            android:layout_width="match_parent"
            android:layout_marginStart="@dimen/app_margin_left"
            android:layout_marginEnd="@dimen/app_margin_right"
            android:layout_height="32dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dip_8"
            app:tl_tab_space_equal="true"
            app:tl_bar_color="@color/white"
            app:tl_indicator_color="@color/blue"
            app:tl_indicator_corner_radius="2dp"
            app:tl_tab_padding="20dp" />
        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dip_1"
            android:layout_marginTop="@dimen/dip_8"
            android:background="@color/divider_line_color" />

    </LinearLayout>

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/swipeRefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:srlEnableLoadMore="false">

        <io.bhex.app.view.CustomFreshHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_0"
            app:srlFinishDuration="500"
            app:srlEnableLastTime="true"
            app:srlClassicsSpinnerStyle="FixedBehind"
            app:srlTextSizeTitle="16sp"
            app:srlTextSizeTime="10dp"
            app:srlTextTimeMarginTop="2dp"
            app:srlDrawableArrowSize="20dp"
            app:srlDrawableProgressSize="20dp"
            app:srlDrawableMarginRight="20dp"
            />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            tools:listitem="@layout/item_current_entrust_order__layout" />
        <com.scwang.smartrefresh.layout.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </com.scwang.smartrefresh.layout.SmartRefreshLayout>
</LinearLayout>