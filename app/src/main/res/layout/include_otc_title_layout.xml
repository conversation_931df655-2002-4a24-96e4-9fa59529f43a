<?xml version="1.0" encoding="utf-8"?><!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: include_otc_title_layout.xml
  ~   @Date: 19-1-11 下午6:57
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_50"
    >

    <TextView
        android:id="@+id/leftIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableTop="@mipmap/icon_otc_menu"
        android:gravity="center_vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginEnd="@dimen/app_margin_right"
        android:text="@string/string_setting"
        android:textAppearance="@style/Caption_Dark"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/rightIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dip_8"
        android:gravity="center_vertical"
        android:drawableTop="@mipmap/icon_otc_filter"
        android:text="@string/string_filter"
        android:textAppearance="@style/Caption_Dark"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/leftIcon"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        android:drawableRight="@mipmap/icon_otc_title_arrow_down"
        android:text="@string/string_placeholder"
        android:textAppearance="@style/BodyL_Dark_Bold"
        />

    <io.bhex.app.view.BHTabView
        android:id="@+id/buySellTab"
        android:layout_width="@dimen/dip_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dip_8"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/rightIcon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:selectTextAppearance="@style/H4_Dark"
        app:unselectTextAppearance="@style/Body_Grey_Bold"
        app:selectTextAppearanceNight="@style/H4_Dark_night"
        app:unselectTextAppearanceNight="@style/Body_Grey_Bold_night"
        app:marginStart="@dimen/dip_8"
        app:marginEnd="@dimen/dip_4"
        app:paddingTop="@dimen/dip_4"
        android:paddingBottom="@dimen/dip_0"
        app:showIndicator="false"
        />

</androidx.constraintlayout.widget.ConstraintLayout>