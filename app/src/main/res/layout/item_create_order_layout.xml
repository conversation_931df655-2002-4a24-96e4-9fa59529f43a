<?xml version="1.0" encoding="utf-8"?>
<!--
  ~
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: item_create_order_layout.xml
  ~   @Date: 2/4/19 8:36 PM
  ~   @Author: chenjun
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  ~
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/edit_price_rela"
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/dip_12"
        android:layout_height="@dimen/dip_43">

        <TextView
            android:id="@+id/edit_price_unit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/dip_8"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/Body_Grey" />

        <EditText
            android:id="@+id/edit_price"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_toLeftOf="@id/edit_price_unit"
            android:background="@null"
            android:hint="@string/string_price"
            android:inputType="numberDecimal"
            android:maxLines="1"
            android:paddingLeft="@dimen/dip_8"
            android:paddingRight="@dimen/dip_8"
            android:textAppearance="@style/Body_Dark_Bold"
            android:textColor="@color/dark"
            android:textColorHint="@color/dark50" />
    </RelativeLayout>

    <TextView
        android:id="@+id/priceMarket"
        android:layout_marginTop="@dimen/dip_12"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_43"
        android:background="@drawable/bg_market_price_gray"
        android:gravity="center"
        android:text="@string/string_market_price_hint"
        android:textColor="@color/font_color2"
        android:textSize="@dimen/font_15"
        android:textStyle="bold"
        android:visibility="gone" />


    <RelativeLayout
        android:id="@+id/edit_amount_rela"
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/dip_12"
        android:layout_height="@dimen/dip_43">

        <TextView
            android:id="@+id/edit_amount_unit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/dip_8"
            android:text="@string/string_option_unit"
            android:textAppearance="@style/Body_Grey" />

        <EditText
            android:id="@+id/edit_amount"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_toLeftOf="@id/edit_amount_unit"
            android:background="@null"
            android:hint="@string/string_amount"
            android:inputType="numberDecimal"
            android:maxLines="1"
            android:paddingLeft="@dimen/dip_8"
            android:paddingRight="@dimen/dip_8"
            android:textAppearance="@style/Body_Dark_Bold"
            android:textColor="@color/dark"
            android:textColorHint="@color/dark50" />
    </RelativeLayout>

    <TextView
        android:id="@+id/can_close_quantity"
        android:gravity="left"
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/dip_5"
        android:layout_height="@dimen/dip_24"
        android:layout_marginLeft="@dimen/dip_4"
        android:textAppearance="@style/BodyS_Grey_Bold" />
    <Button
        android:id="@+id/btn_create_order"
        style="@style/btn_style"
        android:layout_width="match_parent"
        android:background="@drawable/btn_corner"
        android:text="@string/string_option_close" />
</LinearLayout>