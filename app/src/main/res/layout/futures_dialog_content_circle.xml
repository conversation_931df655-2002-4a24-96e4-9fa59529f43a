<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ********************************************************************
  ~   @项目名称: BHex Android
  ~   @文件名称: futures_dialog_content_circle.xml
  ~   @Date: 19-7-26 下午5:06
  ~   @Author: ppzhao
  ~   @Description:
  ~   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
  ~   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
  ~  *******************************************************************
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:background="@drawable/shape_dialog"
              android:orientation="vertical">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_16"
        android:layout_marginRight="@dimen/app_title_icon_margin"
        android:layout_marginLeft="@dimen/app_margin_right">

        <TextView
            android:id="@+id/order_buy_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyL_Dark" />
        <TextView
            android:id="@+id/order_coin_name_point"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@id/order_buy_type"
            android:layout_centerVertical="true"
            android:text="@string/string_option_point"
            android:textAppearance="@style/BodyL_Dark" />
        <TextView
            android:id="@+id/order_coin_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@id/order_coin_name_point"
            android:layout_centerVertical="true"
            android:text="@string/string_placeholder"
            android:textAppearance="@style/BodyL_Dark" />

        <ImageView
            android:id="@+id/order_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dip_8"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@mipmap/icon_close" />


    </RelativeLayout>



    <io.bhex.app.skin.view.SkinTabLayout
        android:id="@+id/tabText"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_48"
        android:layout_marginRight="@dimen/dip_24"
        android:layout_marginLeft="@dimen/app_margin"
        android:visibility="visible"
        app:tabIndicatorColor="@color/blue"
        app:tabIndicatorHeight="@dimen/dip_2"
        app:tabIndicatorFullWidth="false"
        app:tabGravity="fill"
        app:tabMinWidth="@dimen/dip_50"
        app:tabMode="scrollable"
        app:tabSelectedTextColor="@color/blue"
        app:tabTextAppearance="@style/BodyL_Dark_Bold"
        app:tabTextColor="@color/dark" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_marginLeft="@dimen/app_margin"
        android:layout_marginRight="@dimen/app_margin"
        android:layout_height="@dimen/dip_1"
        android:background="@color/dark5" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/createOrderVp"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_200"
        android:layout_marginLeft="@dimen/app_margin"
        android:layout_marginRight="@dimen/app_margin"
        android:layout_gravity="center"
        android:clipChildren="false"
        android:layerType="software"
        android:focusable="false" />

</LinearLayout>