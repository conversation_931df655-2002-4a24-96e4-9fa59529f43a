<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/rootView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <io.bhex.app.view.TopBar
                android:id="@+id/topBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:left_text=""
                app:left_visiblity="gone"
                app:right_text=""
                app:title_text="@string/title_trade" />

            <RelativeLayout
                android:id="@+id/rela_search"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_68"
                android:paddingLeft="@dimen/dip_24"
                android:paddingTop="@dimen/dip_18"
                android:paddingRight="@dimen/dip_24"
                android:visibility="gone">

                <EditText
                    android:id="@+id/edit_search"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dip_30"
                    android:layout_marginTop="@dimen/dip_10"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="@string/string_hint_search"
                    android:maxLines="1"
                    android:textAppearance="@style/BodyL_Dark"
                    android:textColor="@color/dark"
                    android:textColorHint="@color/dark50" />

                <ImageView
                    android:id="@+id/close_search"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:padding="@dimen/dip_10"
                    android:src="@mipmap/icon_close" />

            </RelativeLayout>

        </RelativeLayout>

        <TextView
            android:id="@+id/socketTips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/orange5"
            android:textAppearance="@style/Caption_Dark"
            android:visibility="gone" />

        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:srlEnableLoadMore="false"
            app:srlEnableNestedScrolling="true">

            <io.bhex.app.view.CustomFreshHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dip_0"
                app:srlClassicsSpinnerStyle="FixedBehind"
                app:srlDrawableArrowSize="20dp"
                app:srlDrawableMarginRight="20dp"
                app:srlDrawableProgressSize="20dp"
                app:srlEnableLastTime="true"
                app:srlFinishDuration="500"
                app:srlTextSizeTime="10dp"
                app:srlTextSizeTitle="16sp"
                app:srlTextTimeMarginTop="2dp" />

            <androidx.core.widget.NestedScrollView
                android:id="@+id/trade_scrollLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <include
                        android:id="@+id/header_trade_layout"
                        layout="@layout/header_trade_layout" />

                    <LinearLayout
                        android:id="@+id/secondTabLinear"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <com.flyco.tablayout.SegmentTabLayout
                            android:id="@+id/secondTab"
                            android:layout_width="match_parent"
                            android:layout_height="32dp"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginStart="@dimen/app_margin_left"
                            android:layout_marginTop="@dimen/dip_8"
                            android:layout_marginEnd="@dimen/app_margin_right"
                            app:tl_bar_color="@color/white"
                            app:tl_indicator_color="@color/blue"
                            app:tl_indicator_corner_radius="2dp"
                            app:tl_tab_padding="20dp"
                            app:tl_tab_space_equal="true" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dip_1"
                            android:layout_marginTop="@dimen/dip_8"
                            android:background="@color/divider_line_color" />
                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:overScrollMode="never"
                        app:fastScrollVerticalThumbDrawable="@style/Body_Dark"
                        tools:listitem="@layout/item_current_entrust_order__layout" />
                </LinearLayout>
            </androidx.core.widget.NestedScrollView>

            <com.scwang.smartrefresh.layout.footer.ClassicsFooter
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </com.scwang.smartrefresh.layout.SmartRefreshLayout>
    </LinearLayout>

</RelativeLayout>