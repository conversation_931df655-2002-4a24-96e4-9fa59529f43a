<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:overScrollMode="always"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingStart="@dimen/app_paddingLeft"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <TextView
                android:id="@+id/title1"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_50"
                android:gravity="center_vertical"
                android:text="@string/string_index_price"
                android:textAppearance="@style/Body_Grey_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/value1"
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_50"
                android:layout_marginStart="@dimen/dip_20"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title1"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/title2"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_50"
                android:gravity="center_vertical"
                android:text="@string/string_funding_rate_txt"
                android:textAppearance="@style/Body_Grey_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title1" />

            <TextView
                android:id="@+id/value2"
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_50"
                android:layout_marginStart="@dimen/dip_20"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title2"
                app:layout_constraintTop_toBottomOf="@id/title1" />

            <TextView
                android:id="@+id/title3"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_50"
                android:gravity="center_vertical"
                android:text="@string/string_settle_time"
                android:textAppearance="@style/Body_Grey_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title2" />

            <TextView
                android:id="@+id/value3"
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_50"
                android:layout_marginStart="@dimen/dip_20"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title3"
                app:layout_constraintTop_toBottomOf="@id/title2" />

            <TextView
                android:id="@+id/title4"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_50"
                android:gravity="center_vertical"
                android:text="@string/string_funding_rate"
                android:textAppearance="@style/Body_Grey_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title3" />

            <TextView
                android:id="@+id/value4"
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_50"
                android:layout_marginStart="@dimen/dip_20"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title4"
                app:layout_constraintTop_toBottomOf="@id/title3" />

            <TextView
                android:id="@+id/title5"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_50"
                android:layout_marginTop="@dimen/dip_8"
                android:gravity="center_vertical"
                android:text="@string/string_start_margin"
                android:textAppearance="@style/Body_Grey_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider1" />

            <TextView
                android:id="@+id/value5"
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_50"
                android:layout_marginStart="@dimen/dip_20"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title5"
                app:layout_constraintTop_toBottomOf="@id/divider1" />

            <TextView
                android:id="@+id/title6"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_50"
                android:gravity="center_vertical"
                android:text="@string/string_keep_margin"
                android:textAppearance="@style/Body_Grey_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title5" />

            <TextView
                android:id="@+id/value6"
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_50"
                android:layout_marginStart="@dimen/dip_20"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title6"
                app:layout_constraintTop_toBottomOf="@id/title5" />

            <TextView
                android:id="@+id/title7"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_50"
                android:gravity="center_vertical"
                android:text="@string/string_24h_trade_amount"
                android:textAppearance="@style/Body_Grey_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title6" />

            <TextView
                android:id="@+id/value7"
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_50"
                android:layout_marginStart="@dimen/dip_20"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title7"
                app:layout_constraintTop_toBottomOf="@id/title6" />

            <TextView
                android:id="@+id/title8"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_50"
                android:gravity="center_vertical"
                android:text="@string/string_24h_trade_volume"
                android:textAppearance="@style/Body_Grey_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title7" />

            <TextView
                android:id="@+id/value8"
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_50"
                android:layout_marginStart="@dimen/dip_20"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title8"
                app:layout_constraintTop_toBottomOf="@id/title7" />

            <TextView
                android:id="@+id/title9"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_50"
                android:gravity="center_vertical"
                android:layout_marginTop="@dimen/dip_8"
                android:text="@string/string_min_price_change"
                android:textAppearance="@style/Body_Grey_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider2" />

            <TextView
                android:id="@+id/value9"
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_50"
                android:layout_marginStart="@dimen/dip_20"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title9"
                app:layout_constraintTop_toBottomOf="@id/divider2" />

            <TextView
                android:id="@+id/title10"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_50"
                android:gravity="center_vertical"
                android:text="@string/string_max_entrust_price"
                android:textAppearance="@style/Body_Grey_night"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title9" />

            <TextView
                android:id="@+id/value10"
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_50"
                android:layout_marginStart="@dimen/dip_20"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title10"
                app:layout_constraintTop_toBottomOf="@id/title9" />

            <TextView
                android:id="@+id/title11"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_50"
                android:gravity="center_vertical"
                android:text="@string/string_max_entrust_quantity"
                android:textAppearance="@style/Body_Grey_night"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title10" />

            <TextView
                android:id="@+id/value11"
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_50"
                android:layout_marginStart="@dimen/dip_20"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title11"
                app:layout_constraintTop_toBottomOf="@id/title10" />

            <TextView
                android:id="@+id/title12"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_50"
                android:gravity="center_vertical"
                android:text="@string/string_min_entrust_quantity"
                android:textAppearance="@style/Body_Grey_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title11" />

            <TextView
                android:id="@+id/value12"
                android:layout_width="@dimen/dip_0"
                android:layout_height="@dimen/dip_50"
                android:layout_marginStart="@dimen/dip_20"
                android:layout_marginEnd="@dimen/app_margin_right"
                android:ellipsize="end"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:text="@string/string_placeholder"
                android:textAppearance="@style/Body_Dark_night"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title12"
                app:layout_constraintTop_toBottomOf="@id/title11" />

            <View
                android:id="@+id/divider1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/app_line"
                android:layout_marginTop="@dimen/dip_8"
                android:background="@color/divider_line_color_night"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title4" />

            <View
                android:id="@+id/divider2"
                android:layout_width="match_parent"
                android:layout_height="@dimen/app_line"
                android:layout_marginTop="@dimen/dip_8"
                android:background="@color/divider_line_color_night"
                android:paddingEnd="@dimen/app_paddingRight"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title8" />


        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</androidx.core.widget.NestedScrollView>