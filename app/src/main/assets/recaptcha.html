<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" content="width=device-width"/>
    <script type="text/javascript">
    var post = function (value) {
      //window.webkit.messageHandlers.recaptcha.postMessage(value);
      app.recaptchaSuccess(value);

    };
    console.log = function (message) {
      post({ log: message });
    };

    function success(tokenId) {
      post({ tokenId: tokenId })
    }
    function fail() {
      console.log({ log: 'fail' })
    }
    function error() {
      console.log({ log: 'error' })
    }
    var recaptcha_onloadCallback = function () {
      //console.log("did load");
      grecaptcha.render('submit', {
        'sitekey': 'recaptcha 防刷验证 key',
        callback: success,
        "expired-callback": fail,
        "error-callback": error
      });
    };
    var reset = function () {
      console.log("resetting");
      grecaptcha.reset();
    };

    </script>
</head>

<body>
<span id="submit"></span>
<script src="https://www.recaptcha.net/recaptcha/api.js?onload=recaptcha_onloadCallback&render=explicit"
        async defer></script>
</body>

</html>