/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: HomeKeyEventBroadCastReceiver.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.broadcast.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

/**
 * 描   述：home键广播监听
 * ================================================
 */

public class HomeKeyEventBroadCastReceiver extends BroadcastReceiver {

    private static final String SYSTEM_REASON = "reason";
    private static final String SYSTEM_HOME_KEY = "homekey";    //home key
    private static final String SYSTEM_RECENT_APPS = "recentapps"; //long home key

    private static final String SYSTEM_DIALOG_REASON_KEY = "reason";
    private static final String SYSTEM_DIALOG_REASON_RECENT_APPS = "recentapps";
    private static final String SYSTEM_DIALOG_REASON_HOME_KEY = "homekey";
    private static final String SYSTEM_DIALOG_REASON_LOCK = "lock";
    private static final String SYSTEM_DIALOG_REASON_ASSIST = "assist";


    @Override
    public void onReceive(Context context, Intent intent) {
        try{
            String action = intent.getAction();
            if (action.equals(Intent.ACTION_CLOSE_SYSTEM_DIALOGS)) {
                String reason = intent.getStringExtra(SYSTEM_DIALOG_REASON_KEY);
//                DebugLog.e("后台：","切换----------- reason:"+reason);
                if (reason != null) {
//                    if (reason.equals(SYSTEM_HOME_KEY)) {
//                        //home key处理 TODO
//                        if (SafeUilts.isOpenSageVerify()) {
//                            AppData.isHome = true;
//                            AppData.HOME_TIME = System.currentTimeMillis();
//                        }
////                        ToastUtils.showShort("home key");
//                    }else if(reason.equals(SYSTEM_RECENT_APPS)){
//                        //long home key 处理 TODO
////                        ToastUtils.showShort("long home key");
//                    }

//                    if (SYSTEM_DIALOG_REASON_HOME_KEY.equals(reason) // 短按Home键
//                            || SYSTEM_DIALOG_REASON_RECENT_APPS.equals(reason) // 长按Home键或者activity切换键
//                            || SYSTEM_DIALOG_REASON_LOCK.equals(reason) // 锁屏
//                            || SYSTEM_DIALOG_REASON_ASSIST.equals(reason)) {// samsung长按Home键用户按了home键
//
//                        if (SafeUilts.isOpenSageVerify()) {
//                            AppData.isHome = true;
//                            AppData.HOME_TIME = System.currentTimeMillis();
//                        }
//                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }


    }
}
