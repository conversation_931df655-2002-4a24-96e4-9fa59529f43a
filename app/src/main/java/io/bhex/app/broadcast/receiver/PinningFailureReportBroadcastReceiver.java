package io.bhex.app.broadcast.receiver;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.datatheorem.android.trustkit.reporting.BackgroundReporter;
import com.datatheorem.android.trustkit.reporting.PinningFailureReport;

import io.bhex.app.R;
import io.bhex.app.app.ActivityManager;
import io.bhex.app.utils.DialogUtils;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-11-07
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class PinningFailureReportBroadcastReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
//        PinningFailureReport report = (PinningFailureReport) intent.getSerializableExtra(BackgroundReporter.EXTRA_REPORT);
////        ToastUtils.showShort("ssl pin report222  "+report.toString());
////        ToastUtils.showLong(context.getResources().getString(R.string.string_pinning_tips));
//
//        Activity currentActivity = ActivityManager.getInstance().getCurrentActivity();
//        DialogUtils.showDialogOneBtn(currentActivity, "", currentActivity.getResources().getString(R.string.string_pinning_tips), context.getResources().getString(R.string.string_logout), false, new DialogUtils.OnButtonEventListener() {
//            @Override
//            public void onConfirm() {
//                ActivityManager.getInstance().finishActivityList();
//                System.exit(0);
//            }
//
//            @Override
//            public void onCancel() {
//
//            }
//        });

    }
}
