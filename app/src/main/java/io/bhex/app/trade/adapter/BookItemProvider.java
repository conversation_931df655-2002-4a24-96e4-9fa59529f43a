/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BookItemProvider.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.trade.adapter;

import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.provider.BaseItemProvider;

import io.bhex.app.R;
import io.bhex.app.trade.bean.BookListBean;
import io.bhex.app.trade.listener.BookClickListener;
import io.bhex.app.view.BookView;
import io.bhex.app.view.bean.Book;

public class BookItemProvider extends BaseItemProvider<BookListBean,BaseViewHolder> {
    private final BookClickListener mClickListener;

    public BookItemProvider(BookClickListener clickListener) {
        mClickListener = clickListener;
    }

    @Override
    public int viewType() {
        return BookListBean.TYPE_BOOK;
    }

    @Override
    public int layout() {
        return R.layout.item_book_layout;
    }

    @Override
    public void convert(BaseViewHolder helper, BookListBean data, int position) {
        helper.setBackgroundRes(R.id.itemView,data.getBook().isBid()?R.drawable.book_item_press_green_style:R.drawable.book_item_press_red_style);
        BookView bookView = helper.getView(R.id.bookView);
        bookView.setBook(data.getBook());
        bookView.setOnClickLintener(new BookView.OnClickListener() {
            @Override
            public void onClick(Book book, boolean isClickLeft) {
                String price ="";
                String volume ="";
                String cumulativeVolume ="";
                if (book != null) {
                    price = book.getPrice();
                    volume = book.getOriginalVolume();
                    cumulativeVolume = book.getOriginalCumulativeVolume();
                }
                if (mClickListener != null) {
                    mClickListener.onItemClick(helper.getView(R.id.bookView),book,isClickLeft,price,volume,cumulativeVolume);
                }
            }
        });
    }

    @Override
    public void onClick(BaseViewHolder helper, BookListBean data, int position) {
        super.onClick(helper, data, position);
//        String price ="";
//        String volume ="";
//        String cumulativeVolume ="";
//        Book book = data.getBook();
//        if (book != null) {
//            price = book.getPrice();
//            volume = book.getVolume();
//            cumulativeVolume = book.getCumulativeVolume();
//        }
//        if (mClickListener != null) {
//            mClickListener.onItemClick(helper.getView(R.id.bookView),price,volume,cumulativeVolume);
//        }
    }
}
