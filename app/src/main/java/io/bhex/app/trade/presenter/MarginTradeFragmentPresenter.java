/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: TradeFragmentPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.trade.presenter;

import android.text.TextUtils;
import android.view.View;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.trade.ui.MarginAgreementDialog;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.LoginApi;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.data_manager.MarginDataManager;
import io.bhex.sdk.enums.ACCOUNT_TYPE;
import io.bhex.sdk.enums.ORDER_TYPE;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.socket.NetWorkObserver;
import io.bhex.sdk.trade.TradeApi;
import io.bhex.sdk.trade.bean.CreateOrderRequest;
import io.bhex.sdk.trade.bean.OpenOrderResponse;
import io.bhex.sdk.trade.bean.OrderBean;
import io.bhex.sdk.trade.bean.PlanOrderBean;
import io.bhex.sdk.trade.bean.PlanOrderResponse;
import io.bhex.sdk.trade.margin.MarginApi;
import io.bhex.sdk.trade.margin.bean.MarginAccountAssetResponse;
import io.bhex.sdk.trade.margin.bean.MarginRiskConfigRespone;
import io.bhex.sdk.trade.margin.bean.MarginSafetyResponse;


public class MarginTradeFragmentPresenter extends io.bhex.app.trade.presenter.BaseTradeFragmentPresenter<MarginTradeFragmentPresenter.MarginTradeFragmentUI> {
    private static final String LOGTAG = "MarginTradeFragmentPresenter";


    public void switchOrderList(int orderListType) {
        if (getUI() != null) {
            if (orderListType == ORDER_TYPE.ORDER_TYPE_GENERAL_ENTRUSTMENT.getOrderType()) {
                getUI().showOpenOrders(currentOrders);
            } else if (orderListType == ORDER_TYPE.ORDER_TYPE_PLANNING_ENTRUSTMENT.getOrderType()) {
                getUI().showPlanOrders(currentPlanOrders);
            } else if (orderListType == ORDER_TYPE.ORDER_TYPE_HISTOREY_GENERAL_ENTRUSTMENT.getOrderType()) {
                getUI().showHistoryOrders(historyOrders);
            } else if (orderListType == ORDER_TYPE.ORDER_TYPE_HISTOREY_PLANNING_ENTRUSTMENT.getOrderType()) {
                getUI().showHistoryPlanOrders(historyPlanOrders);
            }

        }
    }

    protected List<PlanOrderBean> currentPlanOrders = new ArrayList<>();
    protected List<PlanOrderBean> historyPlanOrders = new ArrayList<>();

    public interface MarginTradeFragmentUI extends BaseTradeFragmentPresenter.BaseTradeFragmentUI {

        void updateAccountSafety(MarginSafetyResponse response);

        void updateRiskConfig(MarginRiskConfigRespone.DataBean response);
        void showPlanOrders(List<PlanOrderBean> datas);
        void showHistoryPlanOrders(List<PlanOrderBean> datas);

        int getCurrentTabType();

        void loadMorePlanOrderComplete();

        void loadMorePlanOrderFailed();

        void loadPlanOrderEnd();
    }

    @Override
    public void resetAllData(CoinPairBean coinPairBeanParam) {
        super.resetAllData(coinPairBeanParam);
        if (currentPlanOrders != null) {
            currentPlanOrders.clear();
            getUI().showPlanOrders(currentPlanOrders);
        }
        if (historyPlanOrders != null) {
            historyPlanOrders.clear();
            getUI().showHistoryPlanOrders(historyPlanOrders);
        }
        getPlanCurrentOrders();
        getHistoryPlanOrders(false);
        subSymbolPlanOrder();
        getHistoryCloseOrders(false);
        subSafety();
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, MarginTradeFragmentUI ui) {
        super.onUIReady(activity,ui);
    }

    @Override
    public void onResume() {
        super.onResume();
        getMarginRiskConfig();
        getAccountSafety();
        subSafety();
        getPlanCurrentOrders();
        getHistoryPlanOrders(false);
        subSymbolPlanOrder();
    }


    @Override
    public void refresh() {
        super.refresh();
        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        } else {
            getHistoryCloseOrders(false);
        }
        getMarginRiskConfig();
        getAccountSafety();
        subSafety();
        getPlanCurrentOrders();
        getHistoryPlanOrders(false);
    }

    @Override
    public void reGetCurrentEntrustOrders() {
        super.reGetCurrentEntrustOrders();
        getPlanCurrentOrders();
        subSymbolPlanOrder();
    }

    /**
     * 获取当前委托
     */
    protected void getCurrentEntrustOrders() {
        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        }

        if (UserManager.getInstance().getUserInfo()==null ||!UserManager.getInstance().getUserInfo().isOpenMargin()) {
            return;
        }
        boolean isShowAllSymbols = getUI().isSelectShowAllSymbols();
        MarginApi.RequestSymbolOpenOrder(isShowAllSymbols ? "" : getUI().getSymbols(), new SimpleResponseListener<OpenOrderResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(OpenOrderResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    currentOrders = response.getArray();
                    if (currentOrders != null) {
                        getUI().showOpenOrders(currentOrders);
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    @Override
    public void getHistoryEntrustOrders(final boolean isLoadMore) {
        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        }

        if (UserManager.getInstance().getUserInfo()==null ||!UserManager.getInstance().getUserInfo().isOpenMargin()) {
            return;
        }
        if (isLoadMore) {
            if (historyOrders != null) {
                if (!historyOrders.isEmpty()) {
                    mPageId = historyOrders.get(historyOrders.size() - 1).getOrderId();
                }
            }
        } else {
            mPageId = "";
        }

        String pageId = "";

        if (isLoadMore && !TextUtils.isEmpty(mPageId)) {
            //加载更多
            pageId = mPageId;

        }

        MarginApi.RequestSymbolHistoryEntrustOrders(getUI().getSymbols(), pageId, new SimpleResponseListener<OpenOrderResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
                if (isLoadMore) {
                    getUI().loadMoreComplete();
                }
            }

            @Override
            public void onSuccess(OpenOrderResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<OrderBean> data = response.getArray();
                    if (data != null) {

                        if (isLoadMore) {
                            if (data != null) {
                                historyOrders.addAll(data);
                            }

                        } else {
                            if (data != null) {
                                historyOrders.clear();
                                historyOrders = data;
                            }
                        }
                        getUI().showHistoryOrders(historyOrders);

                        if (data.size() < AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        } else {
                            getUI().loadMoreComplete();
                        }
                    } else {
                        getUI().loadMoreComplete();
                    }

                } else {
                    getUI().loadMoreFailed();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                if (isLoadMore) {
                    getUI().loadMoreFailed();
                }
            }
        });
    }


    public void getHistoryCloseOrders(final boolean isLoadMore) {
        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        }

        if (UserManager.getInstance().getUserInfo()==null ||!UserManager.getInstance().getUserInfo().isOpenMargin()) {
            return;
        }

    }

    protected synchronized void getAccountSafety() {
        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        }

        if (UserManager.getInstance().getUserInfo()==null ||!UserManager.getInstance().getUserInfo().isOpenMargin()) {
            return;
        }
        MarginApi.RequestAccountSafety(new SimpleResponseListener<MarginSafetyResponse>() {
            @Override
            public void onSuccess(MarginSafetyResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    getUI().updateAccountSafety(response);
                }
            }
        });
    }


    /**
     * 获取安全度
     */
    protected void subSafety() {
        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        }
        if (UserManager.getInstance().getUserInfo()==null ||!UserManager.getInstance().getUserInfo().isOpenMargin()) {
            return;
        }
        MarginApi.SubMarginSafety(new NetWorkObserver<MarginSafetyResponse>() {
            @Override
            public void onShowUI(MarginSafetyResponse response) {
                if (getUI() == null || !getUI().isAlive() || response == null)
                    return;
                if (response != null) {
                    getUI().updateAccountSafety(response);
                }
            }

            @Override
            public void onError(String response) {

            }
        });
    }

    protected synchronized void getMarginRiskConfig() {
        MarginDataManager.GetInstance().getRiskConfig(new SimpleResponseListener<MarginRiskConfigRespone>() {
            @Override
            public void onSuccess(MarginRiskConfigRespone response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    if (response!=null && response.getArray()!=null && response.getArray().size()>0)
                    getUI().updateRiskConfig(response.getArray().get(0));
                }
            }
        });
    }

    /**
     * 获取资产列表
     *
     * @param token
     */
    protected synchronized void getAssetList(final String token) {
        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        }
        if (UserManager.getInstance().getUserInfo()==null ||!UserManager.getInstance().getUserInfo().isOpenMargin()) {
            return;
        }
        MarginApi.SubMarginTokenBalanceChange(token, new SimpleResponseListener<MarginAccountAssetResponse.DataBean>() {

            @Override
            public void onSuccess(MarginAccountAssetResponse.DataBean response) {
                super.onSuccess(response);
                if (getUI() == null || !getUI().isAlive() || response == null)
                    return;
                if (response != null) {
                    getUI().updateAssettByToken(token, response.getFree());
                } else {
                    //因为API接口返回的资产列表，如果没有资产，则没有改币种资产信息，所以为空，代表没有查询到余额 默认按零资产处理
                    getUI().updateAssettByToken(token, "0");
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    protected synchronized void subSymbolOrder() {
        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        }
        if (UserManager.getInstance().getUserInfo()==null ||!UserManager.getInstance().getUserInfo().isOpenMargin()) {
            return;
        }
       MarginApi.SubSymbolOrderChange(getUI().isSelectShowAllSymbols() ? "" : getUI().getSymbols(), new SimpleResponseListener<OpenOrderResponse>() {
            @Override
            public void onSuccess(OpenOrderResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<OrderBean> srcList = response.getArray();
                    boolean isOrderChange = false;
                    if (srcList != null && srcList.size() > 0) {
                        for (int i = srcList.size() - 1; i >= 0; i--) {
                            OrderBean srcBean = srcList.get(i);
                            boolean hasSrcBean = false;
                            for (int j = 0; j < currentOrders.size(); j++) {
                                if (srcBean.getOrderId().equalsIgnoreCase(currentOrders.get(j).getOrderId())) {
                                    isOrderChange = true;
                                    currentOrders.remove(j);
                                    if (getUI().getSymbols().equals(srcBean.getSymbolId()) && srcBean.getStatus() != null && !srcBean.getStatus().equalsIgnoreCase("CANCELED") && !srcBean.getStatus().equalsIgnoreCase("FILLED"))
                                        currentOrders.add(j, srcBean);
                                    hasSrcBean = true;
                                    break;
                                }
                            }
                            if (getUI().getSymbols().equals(srcBean.getSymbolId()) && hasSrcBean == false && srcBean.getStatus() != null && !srcBean.getStatus().equalsIgnoreCase("CANCELED") && !srcBean.getStatus().equalsIgnoreCase("FILLED"))
                                currentOrders.add(0, srcBean);
                            isOrderChange = true;

                        }
                        if (currentOrders != null && isOrderChange) {
                            getUI().showOpenOrders(currentOrders);
                        }
                    }


                }
            }
        });

    }

    protected synchronized void subSymbolPlanOrder() {
        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        }
        if (UserManager.getInstance().getUserInfo()==null ||!UserManager.getInstance().getUserInfo().isOpenMargin()) {
            return;
        }
        MarginApi.SubSymbolPlanOrderChange(getUI().isSelectShowAllSymbols() ? "" : getUI().getSymbols(), new SimpleResponseListener<PlanOrderResponse>() {
            @Override
            public void onSuccess(PlanOrderResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<PlanOrderBean> srcList = response.getArray();
                    boolean isOrderChange = false;
                    if (srcList != null && srcList.size() > 0) {
                        for (int i = srcList.size() - 1; i >= 0; i--) {
                            PlanOrderBean srcBean = srcList.get(i);
                            boolean hasSrcBean = false;
                            for (int j = 0; j < currentPlanOrders.size(); j++) {
                                if (srcBean.getOrderId().equalsIgnoreCase(currentPlanOrders.get(j).getOrderId())) {
                                    isOrderChange = true;
                                    currentPlanOrders.remove(j);
                                    if (getUI().getSymbols().equals(srcBean.getSymbolId()) && srcBean.getStatus() != null
                                            && !srcBean.getStatus().equalsIgnoreCase("ORDER_REJECTED")
                                            && !srcBean.getStatus().equalsIgnoreCase("ORDER_FILLED")
                                            && !srcBean.getStatus().equalsIgnoreCase("ORDER_CANCELED"))
                                        currentPlanOrders.add(j, srcBean);
                                    hasSrcBean = true;
                                    break;
                                }
                            }
                            if (getUI().getSymbols().equals(srcBean.getSymbolId()) && hasSrcBean == false
                                    && srcBean.getStatus() != null
                                    && !srcBean.getStatus().equalsIgnoreCase("ORDER_REJECTED")
                                    && !srcBean.getStatus().equalsIgnoreCase("ORDER_FILLED")
                                    && !srcBean.getStatus().equalsIgnoreCase("ORDER_CANCELED"))
                                currentPlanOrders.add(0, srcBean);
                            isOrderChange = true;

                        }
                        if (currentPlanOrders != null && isOrderChange) {
                            getUI().showPlanOrders(currentPlanOrders);
                        }
                    }


                }
            }
        });

    }
    /**
     * 获取资产
     *
     * @param tokenId
     */
    protected void getAsset(final String tokenId) {
        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        }

        if (UserManager.getInstance().getUserInfo()==null ||!UserManager.getInstance().getUserInfo().isOpenMargin()) {
            return;
        }
        MarginApi.RequestTokenIdAsset(tokenId, new SimpleResponseListener<MarginAccountAssetResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(MarginAccountAssetResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<MarginAccountAssetResponse.DataBean> data = response.getArray();
                    if (data != null) {
                        if (data.size() > 0) {
                            MarginAccountAssetResponse.DataBean assetBean = data.get(0);
                            if (assetBean != null) {
                                getUI().updateAssettByToken(tokenId, assetBean.getFree());
                                return;
                            }
                        }
                    }
                    getUI().updateAssettByToken(tokenId, "0");
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    /**
     * 下单
     *
     * @param isBuyMode
     * @param isLimitedPrice
     * @param exchangeId
     * @param symbol
     * @param price
     * @param amount
     */
    public void createOrder(boolean isBuyMode, boolean isLimitedPrice, String exchangeId, String symbol, String price, String amount) {

        CreateOrderRequest requestData = new CreateOrderRequest();
        requestData.exchangeId = exchangeId;
        requestData.symbol = symbol;
        requestData.isBuyMode = isBuyMode;
        requestData.isLimitedPrice = isLimitedPrice;
        requestData.price = price;
        requestData.amount = amount;
        MarginApi.RequestCreateOrder(requestData, new SimpleResponseListener<OrderBean>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("", "");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(OrderBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    ToastUtils.showShort(getActivity(), getString(R.string.string_create_order_success));
                    getUI().createOrderSuccess();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(getActivity(), getString(R.string.string_create_order_failed));
            }
        });
    }
    public void createPlanOrder(boolean isBuyMode, boolean isLimitedPrice, String exchangeId, String symbol, String price, String amount, String triggerPriceStr) {

        CreateOrderRequest requestData = new CreateOrderRequest();
        requestData.exchangeId = exchangeId;
        requestData.symbol = symbol;
        requestData.isBuyMode = isBuyMode;
        requestData.isLimitedPrice = isLimitedPrice;
        requestData.price = price;
        requestData.amount = amount;
        requestData.triggerPrice =triggerPriceStr;
        TradeApi.RequestCreatePlanOrder(ACCOUNT_TYPE.ASSET_MARGIN.getType(),requestData, new SimpleResponseListener<PlanOrderBean>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(PlanOrderBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    ToastUtils.showShort(getActivity(), getString(R.string.string_create_order_success));
                    getUI().createOrderSuccess();
                    getPlanCurrentOrders();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(getActivity(), getString(R.string.string_create_order_failed));
            }
        });
    }


    /**
     * 撤销全部订单
     * @param isCheckAll
     */
    public void revokeAllOrders(boolean isCheckAll) {
        if (!UserInfo.isLogin()) {
            return;
        }

        if (UserManager.getInstance().getUserInfo()==null ||!UserManager.getInstance().getUserInfo().isOpenMargin()) {
            return;
        }
        if (getUI().getCurrentTabType() ==ORDER_TYPE.ORDER_TYPE_GENERAL_ENTRUSTMENT.getOrderType()) {

            MarginApi.RequestCancelAllOrder(isCheckAll ? "" : getUI().getSymbols(), new SimpleResponseListener<ResultResponse>() {
                @Override
                public void onBefore() {
                    super.onBefore();
                    getUI().showProgressDialog("", "");
                }

                @Override
                public void onFinish() {
                    super.onFinish();
                    getUI().dismissProgressDialog();
                }

                @Override
                public void onSuccess(ResultResponse response) {
                    super.onSuccess(response);
                    if (CodeUtils.isSuccess(response, true)) {
                        DebugLog.w("Order", "撤单：" + response.isSuccess());
                        ToastUtils.showShort(getActivity(), getString(R.string.string_submit_revoke_all_orders));
//                    refreshCurrentOrders();
                    }
                }

                @Override
                public void onError(Throwable error) {
                    super.onError(error);
                    ToastUtils.showShort(getActivity(), getString(R.string.string_revoke_failed));
                }
            });
        } else if(getUI().getCurrentTabType() ==ORDER_TYPE.ORDER_TYPE_PLANNING_ENTRUSTMENT.getOrderType()){
            revokeAllPlanOrders(isCheckAll);
        }
    }

    public void getUserInfo() {
        LoginApi.GetUserInfo(new SimpleResponseListener<UserInfoBean>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(UserInfoBean data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data, true)) {
                    //保存用户数据
                    UserManager.getInstance().saveUserInfo(data);
                    // 开完户重新订阅
                    getAccountSafety();
                    subSafety();
                    getAssetList(getUI().getQuoteToken());
                    getAssetList(getUI().getBaseToken());
                    subSymbolOrder();
                    subSymbolPlanOrder();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
    public void cancelOrder(String orderId) {
        MarginApi.RequestCancelOrder(orderId, new SimpleResponseListener<OrderBean>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("", "");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(OrderBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
//                    DebugLog.w("Order", "撤单：" + response.isSuccess());
                    ToastUtils.showShort(getActivity(), getString(R.string.string_revoke_success));
//                    refreshCurrentOrders();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(getActivity(), getString(R.string.string_revoke_failed));
            }
        });
    }

    @Override
    public void onVisibleChanged(boolean visible) {
        super.onVisibleChanged(visible);
        if (visible && getUI().isSelected()) {

            getMarginRiskConfig();
            getAccountSafety();

            if (UserInfo.isLogin() && UserManager.getInstance().getUserInfo() != null && !UserManager.getInstance().getUserInfo().isOpenMargin()) {
                // 登录返回的值不带openmargin的返回值。所以在这个地方增加用户信息获取。
                LoginApi.GetUserInfo(new SimpleResponseListener<UserInfoBean>() {
                    @Override
                    public void onSuccess(UserInfoBean response) {
                        super.onSuccess(response);
                        if (CodeUtils.isSuccess(response)) {
                            UserManager.getInstance().saveUserInfo(response);
                            if (response != null && !response.isOpenMargin() && visible && getUI().isSelected())
                                MarginAgreementDialog.showDialog(getFragment().getChildFragmentManager(), "", new MarginAgreementDialog.OnClickListenter() {
                                    @Override
                                    public void onCheckClickListener(View view, boolean isCheck) {
                                        MarginApi.RequestOpenMargin(new SimpleResponseListener<ResultResponse>() {
                                            @Override
                                            public void onBefore() {
                                                super.onBefore();
                                                getUI().showProgressDialog("", "");
                                            }

                                            @Override
                                            public void onFinish() {
                                                super.onFinish();
                                                getUI().dismissProgressDialog();
                                            }

                                            @Override
                                            public void onSuccess(ResultResponse data) {
                                                super.onSuccess(data);
                                                getUserInfo();
                                            }
                                        });
                                    }

                                    @Override
                                    public void onCancelClickListener() {

                                    }
                                }, false);
                        }
                    }
                });
            }
        }
    }

    public void getPlanCurrentOrders() {
        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        }
        if (UserManager.getInstance().getUserInfo()==null ||!UserManager.getInstance().getUserInfo().isOpenMargin()) {
            return;
        }

        boolean isShowAllSymbols = getUI().isSelectShowAllSymbols();
        TradeApi.RequestSymbolPlanOpenOrder(ACCOUNT_TYPE.ASSET_MARGIN.getType(),isShowAllSymbols ? "" : getUI().getSymbols(), new SimpleResponseListener<PlanOrderResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(PlanOrderResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    currentPlanOrders = response.getArray();
                    if (currentPlanOrders != null) {
                        getUI().showPlanOrders(currentPlanOrders);
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
    public void getHistoryPlanOrders(final boolean isLoadMore) {
        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        }
        if (UserManager.getInstance().getUserInfo()==null ||!UserManager.getInstance().getUserInfo().isOpenMargin()) {
            return;
        }

        if (isLoadMore) {
            if (historyPlanOrders != null) {
                if (!historyPlanOrders.isEmpty()) {
                    mPageId = historyPlanOrders.get(historyPlanOrders.size() - 1).getOrderId();
                }
            }
        }else{
            mPageId ="";
        }

        String pageId = "";

        if (isLoadMore && !TextUtils.isEmpty(mPageId)) {
            //加载更多
            pageId = mPageId;

        }

        TradeApi.RequestSymbolHistoryPlanOrders(ACCOUNT_TYPE.ASSET_MARGIN.getType(),getUI().getSymbols(), pageId, new SimpleResponseListener<PlanOrderResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
                if (isLoadMore) {
                    getUI().loadMorePlanOrderComplete();
                }
            }

            @Override
            public void onSuccess(PlanOrderResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<PlanOrderBean> data = response.getArray();
                    if (data != null) {

                        if (isLoadMore) {
                            if (data != null) {
                                historyPlanOrders.addAll(data);
                            }

                        } else {
                            if (data != null) {
                                historyPlanOrders.clear();
                                historyPlanOrders = data;
                            }
                        }
                        getUI().showHistoryPlanOrders(historyPlanOrders);

                        if (data.size()< AppData.Config.PAGE_LIMIT) {
                            getUI().loadPlanOrderEnd();
                        }else{
                            getUI().loadMorePlanOrderComplete();
                        }
                    }else{
                        getUI().loadMorePlanOrderComplete();
                    }

                }else{
                    getUI().loadMorePlanOrderFailed();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                if (isLoadMore) {
                    getUI().loadMorePlanOrderFailed();
                }
            }
        });
    }

    /**
     * 撤销全部计划订单
     * @param isCheckAll
     */
    public void revokeAllPlanOrders(boolean isCheckAll) {
        if (!UserInfo.isLogin()) {
            return;
        }
        TradeApi.RequestCancelAllPlanOrder(ACCOUNT_TYPE.ASSET_MARGIN.getType(),isCheckAll ? "" : getUI().getSymbols(), new SimpleResponseListener<ResultResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    DebugLog.w("Order", "撤单：" + response.isSuccess());
                    ToastUtils.showShort(getActivity(), getString(R.string.string_submit_revoke_all_orders));
                    getPlanCurrentOrders();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(getActivity(), getString(R.string.string_revoke_failed));
            }
        });
    }

    /**
     * 撤销计划订单
     * @param orderId
     */
    public void cancelPlanOrder(String orderId) {
        TradeApi.RequestCancelPlanOrder(ACCOUNT_TYPE.ASSET_MARGIN.getType(),orderId, new SimpleResponseListener<PlanOrderBean>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(PlanOrderBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
//                    DebugLog.w("Order", "撤单：" + response.isSuccess());
                    ToastUtils.showShort(getActivity(), getString(R.string.string_revoke_success));
                    getPlanCurrentOrders();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(getActivity(), getString(R.string.string_revoke_failed));
            }
        });
    }


}