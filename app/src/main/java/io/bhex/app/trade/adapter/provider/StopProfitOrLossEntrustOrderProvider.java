/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: StopEntrustOrderProvider.java
 *   @Date: 19-8-8 下午6:31
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.trade.adapter.provider;

import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.provider.BaseItemProvider;

import io.bhex.app.R;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.sdk.enums.ORDER_ENTRUST_TYPE;
import io.bhex.sdk.trade.futures.bean.FuturesOrderResponse;

public class StopProfitOrLossEntrustOrderProvider extends BaseItemProvider<FuturesOrderResponse, BaseViewHolder> {
    @Override
    public int viewType() {
        return ORDER_ENTRUST_TYPE.STOP_PROFIT_OR_LOSS.getTypeId();
    }

    @Override
    public int layout() {
        return R.layout.item_futures_stop_profit_or_loss_entrust_order_layout;
    }

    @Override
    public void convert(BaseViewHolder baseViewHolder, FuturesOrderResponse itemModel, int position) {
        if (itemModel == null)
            return;
        String lever = KlineUtils.isFuturesOpenOrder(mContext,itemModel.getSide()) ? "·" + itemModel.getLeverage() + "X" :"";
        String orderSide = KlineUtils.getFuturesOrderSideTxt(mContext, itemModel.getSide());
        baseViewHolder.setText(R.id.orderSideAndLever, orderSide + lever);
        baseViewHolder.setTextColor(R.id.orderSideAndLever, KlineUtils.getFuturesOrderSideColor(mContext, itemModel.getSide()));
        baseViewHolder.setText(R.id.symbolName, itemModel.getSymbolName());
        baseViewHolder.setText(R.id.value1, KlineUtils.getFuturesTriggerPrice(mContext, itemModel));
//        baseViewHolder.setText(R.id.value2, KlineUtils.getPlanOrderTypeTxt(mContext, itemModel.getPlanOrderType()));
        baseViewHolder.setText(R.id.value3, DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getTime()), "HH:mm:ss MM/dd"));
//        baseViewHolder.setText(R.id.value4, KlineUtils.getFuturesPrice(mContext, itemModel));
        baseViewHolder.setText(R.id.value4, KlineUtils.getPlanOrderTypeTxt(mContext, itemModel.getPlanOrderType()));
//        baseViewHolder.setText(R.id.value5, itemModel.getOrigQty() + " " + mContext.getString(R.string.string_option_unit));
        baseViewHolder.setText(R.id.value6, KlineUtils.getFuturesOrderStatus(mContext, itemModel));

        baseViewHolder.addOnClickListener(R.id.revoke_order);
        baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
    }
}
