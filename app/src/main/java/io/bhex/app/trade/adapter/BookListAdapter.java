/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BookListAdapter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.trade.adapter;


import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.MultipleItemRvAdapter;

import java.util.List;

import io.bhex.app.trade.bean.BookListBean;
import io.bhex.app.trade.listener.BookClickListener;

public class BookListAdapter extends MultipleItemRvAdapter<BookListBean,BaseViewHolder> {
    private final BookClickListener mClickListener;

    public BookListAdapter(@Nullable List<BookListBean> data, BookClickListener bookClickListener) {
        super(data);
        mClickListener = bookClickListener;
        //构造函数若有传其他参数可以在调用finishInitialize()之前进行赋值，赋值给全局变量
        //这样getViewType()和registerItemProvider()方法中可以获取到传过来的值
        //getViewType()中可能因为某些业务逻辑，需要将某个值传递过来进行判断，返回对应的viewType
        //registerItemProvider()中可以将值传递给ItemProvider

        //If the constructor has other parameters, it needs to be assigned before calling finishInitialize() and assigned to the global variable
        // This getViewType () and registerItemProvider () method can get the value passed over
        // getViewType () may be due to some business logic, you need to pass a value to judge, return the corresponding viewType
        //RegisterItemProvider() can pass value to ItemProvider

        finishInitialize();
    }

    @Override
    protected int getViewType(BookListBean entity) {
        //根据实体类判断并返回对应的viewType，具体判断逻辑因业务不同，这里这是简单通过判断type属性
        //According to the entity class to determine and return the corresponding viewType,
        //the specific judgment logic is different because of the business, here is simply by judging the type attribute
        if (entity.mType == BookListBean.TYPE_BOOK) {
            return BookListBean.TYPE_BOOK;
        } else if (entity.mType == BookListBean.TYPE_LASTPRICE) {
            return BookListBean.TYPE_LASTPRICE;
        }
        return 0;
    }

    @Override
    public void registerItemProvider() {
        //注册相关的条目provider
        //Register related entries provider
        mProviderDelegate.registerProvider(new BookItemProvider(mClickListener));
        mProviderDelegate.registerProvider(new LastPriceProvider(mClickListener));
    }
}
