/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FuturesEntrustOrderAdapter.java
 *   @Date: 19-7-26 下午6:30
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.trade.adapter;

import android.content.Context;

import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.MultipleItemRvAdapter;

import java.util.List;

import io.bhex.app.trade.adapter.provider.LimitEntrustOrderProvider;
import io.bhex.app.trade.adapter.provider.StopEntrustOrderProvider;
import io.bhex.app.trade.adapter.provider.StopProfitOrLossEntrustOrderProvider;
import io.bhex.sdk.enums.ORDER_ENTRUST_TYPE;
import io.bhex.sdk.enums.PLAN_ORDER_ENTRUST_TYPE;
import io.bhex.sdk.trade.futures.bean.FuturesOrderResponse;

/**
 * 期货当前委托
 */
public class FuturesEntrustOrderAdapter extends MultipleItemRvAdapter<FuturesOrderResponse, BaseViewHolder> {

    public FuturesEntrustOrderAdapter(Context context, List<FuturesOrderResponse> data) {
        super(data);
        mContext = context;
        finishInitialize();
    }


    @Override
    protected int getViewType(FuturesOrderResponse futuresOrderResponse) {
        if (futuresOrderResponse.getType().equals(ORDER_ENTRUST_TYPE.LIMIT.getEntrustType())) {
            return ORDER_ENTRUST_TYPE.LIMIT.getTypeId();
        }else {
            //计划委托
            if (futuresOrderResponse.getPlanOrderType().equals(PLAN_ORDER_ENTRUST_TYPE.STOP_COMMON.getEntrustType())) {
                //普通计划委托
                return ORDER_ENTRUST_TYPE.STOP.getTypeId();
            }else{
                //其他-止盈止损-计划委托
                return ORDER_ENTRUST_TYPE.STOP_PROFIT_OR_LOSS.getTypeId();
            }
        }
    }

    @Override
    public void registerItemProvider() {
        mProviderDelegate.registerProvider(new LimitEntrustOrderProvider());
        mProviderDelegate.registerProvider(new StopEntrustOrderProvider());
        mProviderDelegate.registerProvider(new StopProfitOrLossEntrustOrderProvider());
    }

}
