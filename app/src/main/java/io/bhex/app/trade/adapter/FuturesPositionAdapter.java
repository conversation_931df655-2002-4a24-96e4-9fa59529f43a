/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FuturesPositionAdapter.java
 *   @Date: 19-7-24 下午9:02
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.trade.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.trade.ui.FuturesCloseOrderPopDialog;
import io.bhex.app.trade.ui.FuturesQuickCloseOrderPopDialog;
import io.bhex.app.trade.ui.MarginAdjustDialog;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.core.SPEx;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.enums.ORDER_SIDE;
import io.bhex.sdk.trade.futures.FuturesApi;
import io.bhex.sdk.trade.futures.bean.FuturesOrderResponse;
import io.bhex.sdk.trade.futures.bean.FuturesPositionOrder;

public class FuturesPositionAdapter extends BaseQuickAdapter<FuturesPositionOrder, BaseViewHolder> {

    private MarginAdjustDialog.OnDialogObserver mDialogObserver;
    private Context mContext;

    public FuturesPositionAdapter(Context context, List<FuturesPositionOrder> data, MarginAdjustDialog.OnDialogObserver onDialogObserver) {
        super(R.layout.item_futures_position_order_layout, data);
        mContext = context;
        mDialogObserver = onDialogObserver;
    }


    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final FuturesPositionOrder itemModel) {
        if (itemModel == null)
            return;
        String futuresPriceUnit = KlineUtils.getFuturesPriceUnit(itemModel.getSymbolId());
        String orderSide = KlineUtils.getFuturesOrderPositionTxtByIsLong(mContext, itemModel.getIsLong());
        baseViewHolder.setText(R.id.orderSideAndLever, orderSide + "·" + itemModel.getLeverage() + "X");
        int futuresOrderSideColor = KlineUtils.getFuturesOrderSideColorByIsLong(mContext, itemModel.getIsLong());
        baseViewHolder.setTextColor(R.id.orderSideAndLever, futuresOrderSideColor);
        baseViewHolder.setText(R.id.symbolName, itemModel.getSymbolName());

        baseViewHolder.setText(R.id.mainTitle1, mContext.getString(R.string.string_open_average_price) + "(" + futuresPriceUnit + ")");
        baseViewHolder.setText(R.id.mainValue1, itemModel.getAvgPrice());

        baseViewHolder.setText(R.id.mainTitle3, mContext.getString(R.string.string_abount_close_price) + "(" + futuresPriceUnit + ")");
        baseViewHolder.setText(R.id.mainValue3, itemModel.getLiquidationPrice());

        String profitRate = itemModel.getProfitRate();
        if (!TextUtils.isEmpty(profitRate)) {
            String profitRatePercent = NumberUtils.roundFormat(NumberUtils.mul(profitRate, "100"), 2) + "%";
            baseViewHolder.setText(R.id.mainValue4, profitRatePercent);
        }
        baseViewHolder.setText(R.id.mainTitle2, mContext.getString(R.string.string_unrealized_profit_and_loss) + "(" + itemModel.getUnit() + ")");
        baseViewHolder.setText(R.id.mainValue2, itemModel.getUnrealisedPnl());
        int profitColor = KlineUtils.getProfitColor(mContext, itemModel.getUnrealisedPnl());
        baseViewHolder.setTextColor(R.id.mainValue2, profitColor);
        baseViewHolder.setTextColor(R.id.mainValue4, profitColor);

        baseViewHolder.setText(R.id.title1, mContext.getString(R.string.string_position_margin) + "(" + itemModel.getUnit() + ")");
        baseViewHolder.setText(R.id.value1, itemModel.getMargin());
        baseViewHolder.setText(R.id.value2, itemModel.getTotal());
        baseViewHolder.setText(R.id.title3, mContext.getString(R.string.string_position_value) + "(" + itemModel.getUnit() + ")");
        baseViewHolder.setText(R.id.value3, itemModel.getPositionValues());
        String marginRate = itemModel.getMarginRate();
        if (!TextUtils.isEmpty(marginRate)) {
            String marginRatePercent = NumberUtils.roundFormat(NumberUtils.mul(marginRate, "100"), 2) + "%";
            baseViewHolder.setText(R.id.value4, marginRatePercent);
        }
        baseViewHolder.setText(R.id.value5, itemModel.getAvailable());
        baseViewHolder.setText(R.id.value6, itemModel.getIndices());

        baseViewHolder.getView(R.id.order_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //平仓
                FuturesCloseOrderPopDialog dialog = new FuturesCloseOrderPopDialog(mContext, itemModel, new FuturesCloseOrderPopDialog.OnLoadingObserver() {
                    @Override
                    public void showLoading() {
                        if (mContext instanceof BaseActivity)
                            ((BaseActivity) mContext).showProgressDialog("", "");
                    }

                    @Override
                    public void hideLoading() {
                        if (mContext instanceof BaseActivity)
                            ((BaseActivity) mContext).dismissProgressDialog();
                    }
                }, mDialogObserver);
                dialog.ShowDialog();
            }
        });
        baseViewHolder.getView(R.id.order_quick_close_ll).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                if (SPEx.get(AppData.SPKEY.QUICK_CLOSE_CONFIRM, true)) {
                    FuturesQuickCloseOrderPopDialog dialog = new FuturesQuickCloseOrderPopDialog(mContext, itemModel, new FuturesQuickCloseOrderPopDialog.OnLoadingObserver() {
                        @Override
                        public void showLoading() {
                            if (mContext instanceof BaseActivity)
                                ((BaseActivity) mContext).showProgressDialog("", "");
                        }

                        @Override
                        public void hideLoading() {
                            if (mContext instanceof BaseActivity)
                                ((BaseActivity) mContext).dismissProgressDialog();
                        }
                    }, mDialogObserver);
                    dialog.ShowDialog();
                } else {
                    //闪电平仓
                    FuturesApi.closeFuturesPromptly(itemModel.getExchangeId(), itemModel.getSymbolId(), itemModel.getIsLong(), new SimpleResponseListener<FuturesOrderResponse>() {
                        @Override
                        public void onBefore() {
                            super.onBefore();
                            if (mContext instanceof BaseActivity)
                                ((BaseActivity) mContext).showProgressDialog("", "");
                        }

                        @Override
                        public void onFinish() {
                            super.onFinish();
                            if (mContext instanceof BaseActivity)
                                ((BaseActivity) mContext).dismissProgressDialog();
                        }

                        @Override
                        public void onSuccess(FuturesOrderResponse response) {
                            super.onSuccess(response);
                            if (CodeUtils.isSuccess(response, true)) {
                                ToastUtils.showShort(mContext, mContext.getString(R.string.string_create_order_success));
                            }
                        }

                        @Override
                        public void onError(Throwable error) {
                            super.onError(error);
                            ToastUtils.showShort(mContext, mContext.getString(R.string.string_create_order_failed));
                        }
                    });
                }
            }
        });

        baseViewHolder.getView(R.id.value1_ll).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 调整保证金
                MarginAdjustDialog dialog = new MarginAdjustDialog(mContext, itemModel, new MarginAdjustDialog.OnLoadingObserver() {
                    @Override
                    public void showLoading() {
                        if (mContext instanceof BaseActivity)
                            ((BaseActivity) mContext).showProgressDialog("", "");
                    }

                    @Override
                    public void hideLoading() {
                        if (mContext instanceof BaseActivity)
                            ((BaseActivity) mContext).dismissProgressDialog();
                    }
                }, mDialogObserver);
                dialog.ShowDialog();
            }
        });
        baseViewHolder.getView(R.id.order_share).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 调整保证金
                IntentUtils.goContractShareProfit(mContext, itemModel);
            }
        });

        baseViewHolder.getView(R.id.order_stop).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 止盈止损
                IntentUtils.goStopProfitLossActivity(mContext, itemModel);
            }
        });

        baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
    }
}
