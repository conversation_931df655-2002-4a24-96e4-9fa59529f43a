package io.bhex.app.trade.adapter;

import androidx.core.util.Pair;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import java.util.ArrayList;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-09-22
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class ContractOrderTypeFragmentPagerAdapter extends FragmentPagerAdapter {

    private ArrayList<Pair<String, Fragment>> mFragments;

    public ContractOrderTypeFragmentPagerAdapter(FragmentManager fm, ArrayList<Pair<String, Fragment>> items) {
        super(fm);
        mFragments = items;
    }

    @Override
    public Fragment getItem(int position) {
        return mFragments.get(position).second;
    }

    @Override
    public int getCount() {
        return mFragments.size();
    }

    @Override
    public CharSequence getPageTitle(int position) {
        return mFragments.get(position).first;
    }


}
