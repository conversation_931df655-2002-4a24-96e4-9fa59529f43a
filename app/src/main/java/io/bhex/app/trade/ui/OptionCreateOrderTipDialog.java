/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OptionCreateOrderTipDialog.java
 *   @Date: 3/30/19 11:14 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.trade.ui;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.view.View;
import android.widget.CheckBox;
import android.widget.TextView;

import io.bhex.app.R;
import io.bhex.app.view.BasePopDialog;
import io.bhex.app.web.ui.WebActivity;
import io.bhex.baselib.core.SPEx;
import io.bhex.sdk.Urls;
import io.bhex.sdk.data_manager.RateAndLocalManager;

public class OptionCreateOrderTipDialog extends BasePopDialog {

    private CheckBox option_open_checkbox;
    private TextView dialog_title;
    private static long ONE_DAY = 24 * 3600 * 1000;

    public OptionCreateOrderTipDialog(Context context) {
        this(context, R.style.dialog);
    }

    public OptionCreateOrderTipDialog(Context context, int theme) {
        super(context, theme);
        setContentView(R.layout.option_create_tip_layout);
        setCanceledOnTouchOutside(true);
        initViews();
    }

    @Override
    protected void initViews() {
        super.initViews();
        option_open_checkbox = findViewById(R.id.option_open_checkbox);
        dialog_title = findViewById(R.id.dialog_title);

    }


    public static boolean getOptionTipSPShow(){
        long lastTime = System.currentTimeMillis() - SPEx.get("checkOptionTipTime", 0l);

        if(lastTime <  ONE_DAY)
            return false;
        else
            return true;
    }

    public static void setOptionTipSP(boolean checked){
        if (checked) {
            SPEx.set("checkOptionTipTime", System.currentTimeMillis());
        }
    }
    public boolean getTipChecked(){
        return option_open_checkbox.isChecked();
    }

    public void setShowText(String text){
        dialog_title.setText(text);
    }

}

