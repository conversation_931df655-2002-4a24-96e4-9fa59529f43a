/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OpenOptionDialog.java
 *   @Date: 2/5/19 9:59 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.trade.ui;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.Spanned;
import android.view.View;
import android.widget.CheckBox;
import android.widget.TextView;

import io.bhex.app.R;
import io.bhex.app.web.ui.WebActivity;
import io.bhex.sdk.Urls;
import io.bhex.sdk.data_manager.RateAndLocalManager;

public class OpenOptionDialog extends Dialog {

    private TextView titleView;
    private CheckBox option_open_checkbox;
    private TextView negativeButton, positiveButton , signup_protocol;
    private Context mContext;
    private String protocol;

    public OpenOptionDialog(Context context) {
        this(context, R.style.dialog);
    }

    public OpenOptionDialog(Context context, int theme) {
        super(context, theme);
        setContentView(R.layout.open_option_layout);
        setCanceledOnTouchOutside(true);
        mContext = context;
        initViews();
    }

    private void initViews() {
        option_open_checkbox = findViewById(R.id.option_open_checkbox);
        negativeButton = findViewById(R.id.update_id_cancel);
        positiveButton = findViewById(R.id.update_id_ok);
        signup_protocol = findViewById(R.id.signup_protocol);
        protocol = mContext.getString(R.string.string_option_open_protocol, mContext.getString(R.string.app_name));
        signup_protocol.setText(protocol);
        signup_protocol.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(mContext != null) {
                    /*if (RateAndLocalManager.GetInstance(mContext).getCurLocalLanguage().contains("zh")) {*/
                        WebActivity.runActivity(mContext, protocol , Urls.H5_URL_OPTION_SERVICE);
                    /*    //starWebUrl(Urls.H5_URL_OPTION_SERVICE_CN);
                    }else{
                        WebActivity.runActivity(mContext, mContext.getString(R.string.string_option_open_protocol,mContext.getString(R.string.app_name)), Urls.H5_URL_OPTION_SERVICE_EN);
                        //starWebUrl(Urls.H5_URL_OPTION_SERVICE_EN);
                    }*/
                }
            }
        });
    }

    private void starWebUrl(String url){
        Intent intent= new Intent();
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setAction(Intent.ACTION_VIEW);
        intent.setDataAndType(Uri.parse(url), "application/pdf");
        mContext.startActivity(intent);
    }

    public boolean getOpenChecked(){
        return option_open_checkbox.isChecked();
    }

    /**
     * 设置按钮
     *
     * @param resId
     * @param listener
     */
    public void setNegativeButton(int resId, final View.OnClickListener listener) {
        negativeButton.setVisibility(View.VISIBLE);
        negativeButton.setText(resId);
        negativeButton.setOnClickListener(listener);
    }

    public void setNegativeButton(String text, final View.OnClickListener listener) {
        negativeButton.setVisibility(View.VISIBLE);
        negativeButton.setText(text);
        negativeButton.setOnClickListener(listener);
    }

    public void setNegativeButtonEnable(boolean bEnable) {
        negativeButton.setEnabled(bEnable);
        if(bEnable == false)
            negativeButton.setVisibility(View.GONE);
        negativeButton.setClickable(bEnable);
    }

    public void setNegativeButton(final View.OnClickListener listener) {
        negativeButton.setVisibility(View.VISIBLE);
        negativeButton.setOnClickListener(listener);
    }

    public void setNegativeButtonColor(int color){
        negativeButton.setTextColor(color);
    }

    /**
     * 设置按钮
     *
     * @param resId
     * @param listener
     */
    public void setPositiveButton(int resId, final View.OnClickListener listener) {
        positiveButton.setVisibility(View.VISIBLE);
        positiveButton.setText(resId);
        positiveButton.setOnClickListener(listener);
    }

    public void setPositiveButton(String text, final View.OnClickListener listener) {
        positiveButton.setVisibility(View.VISIBLE);
        positiveButton.setText(text);
        positiveButton.setOnClickListener(listener);
    }

    public void setPositiveButton(View.OnClickListener listener) {
        positiveButton.setVisibility(View.VISIBLE);
        positiveButton.setOnClickListener(listener);
    }

    public void setPositiveButtonColor(int color){
        positiveButton.setTextColor(color);
    }

    @Override
    public void show() {
        if (getContext() instanceof Activity) {
            Activity activity = (Activity) getContext();
            if (!activity.isFinishing()) {
                super.show();
            }
        } else {
            try {
                super.show();
            } catch (Exception e) {
            }
        }
    }

    @Override
    public void dismiss() {
        try {
            super.dismiss();
        } catch (Exception e) {
        }
    }
}

