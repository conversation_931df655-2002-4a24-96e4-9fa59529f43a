/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CoinPairComparator.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.trade.sort;

import android.text.TextUtils;

import java.util.Comparator;

import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.TickerBean;


public class CoinPairComparator implements Comparator<CoinPairBean> {

    public static final int SORT_TYPE_DEFAULT =-1;   //默认排序
    public static final int SORT_TYPE_CHANGE_ASC =0;
    public static final int SORT_TYPE_CHANGE_DESC =1;
    public static final int SORT_TYPE_PRICE_ASC =2;
    public static final int SORT_TYPE_PRICE_DESC =3;
    public static final int SORT_TYPE_VOL_ASC =4;
    public static final int SORT_TYPE_VOL_DESC =5;
    private final int mSortType;

    private boolean isASC = true;
    public CoinPairComparator(int sortType) {
        mSortType = sortType;
        isASC = mSortType == SORT_TYPE_CHANGE_ASC || mSortType == SORT_TYPE_PRICE_ASC || mSortType == SORT_TYPE_VOL_ASC;
    }

    @Override
    public int compare(CoinPairBean o1, CoinPairBean o2) {
        if (isASC) {
            if (o1 == null){
                return -1;
            }
            if(o2 == null){
                return 1;
            }
        }else{
            if (o2 == null){
                return -1;
            }
            if(o1 == null){
                return 1;
            }
        }

        TickerBean q1 = o1.getTicker();
        TickerBean q2 = o2.getTicker();
        if (isASC) {
            if (q1 == null) {
                return -1;
            }
            if (q2 == null) {
                return 1;
            }
        }else{
            if (q2 == null) {
                return -1;
            }
            if (q1 == null) {
                return 1;
            }
        }

        String volume = q1.getV();
        String volume2 = q2.getV();
        String close = q1.getC();
        String close2 = q2.getC();

        if(mSortType == SORT_TYPE_VOL_ASC) {
            if (TextUtils.isEmpty(volume)) {
                return -1;
            }
            if (TextUtils.isEmpty(volume2)) {
                return 1;
            }
            return new Float(q1.getV()).compareTo(new Float(q2.getV()));
        }else if(mSortType == SORT_TYPE_VOL_DESC) {
            if (TextUtils.isEmpty(volume2)) {
                return -1;
            }
            if (TextUtils.isEmpty(volume)) {
                return 1;
            }
            return new Float(q2.getV()).compareTo(new Float(q1.getV()));
        }else if(mSortType == SORT_TYPE_PRICE_ASC) {
            if (TextUtils.isEmpty(close)) {
                return -1;
            }
            if (TextUtils.isEmpty(close2)) {
                return 1;
            }
            return new Float(q1.getC()).compareTo(new Float(q2.getC()));
        }else if(mSortType == SORT_TYPE_PRICE_DESC) {
            if (TextUtils.isEmpty(close2)) {
                return -1;
            }
            if (TextUtils.isEmpty(close)) {
                return 1;
            }
            return new Float(q2.getC()).compareTo(new Float(q1.getC()));
        }

        if(mSortType == SORT_TYPE_CHANGE_ASC) {
            if (TextUtils.isEmpty(q1.getM())) {
                return -1;
            }
            if (TextUtils.isEmpty(q2.getM())) {
                return 1;
            }
            return new Float(q1.getM()).compareTo(new Float(q2.getM()));
        }
        if(mSortType == SORT_TYPE_CHANGE_DESC) {
            if (TextUtils.isEmpty(q2.getM())) {
                return -1;
            }
            if (TextUtils.isEmpty(q1.getM())) {
                return 1;
            }
            return new Float(q2.getM()).compareTo(new Float(q1.getM()));
        }
        return 0;
    }
}
