/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: ContractTradeFragment.java
 *   @Date: 19-6-12 下午8:32
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.trade.ui;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.core.util.Pair;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.flyco.tablayout.SegmentTabLayout;
import com.flyco.tablayout.listener.OnTabSelectListener;

import java.util.ArrayList;

import io.bhex.app.R;
import io.bhex.app.base.BaseFragment;
import io.bhex.app.market.adapter.FragmentAdapter;
import io.bhex.app.trade.presenter.ContractTradeFragmentPresenter;
import io.bhex.app.utils.BasicFunctionsUtil;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.view.NoScrollViewPager;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.sdk.config.bean.BasicFunctionsConfig;
import io.bhex.sdk.enums.COIN_TYPE;
import io.bhex.sdk.quote.bean.CoinPairBean;

/**
 * 合约交易最外层Fragment
 */
public class ContractTradeFragment extends BaseFragment<ContractTradeFragmentPresenter, ContractTradeFragmentPresenter.ContractTradeUI> implements ContractTradeFragmentPresenter.ContractTradeUI {

    public static final int TAB_FUTURES = 0;
    public static final int TAB_OPTION = 1;
    private NoScrollViewPager viewPager;
    private SegmentTabLayout tab;
    private View tabline;
    private ArrayList<Pair<String, Fragment>> items;
    private FragmentAdapter marketAdapter;
    private int mCurrentIndex = 0;
    private CoinPairBean coinPairBean;
    //期权
    OptionTradeFragment optionTradeFragment;
    private boolean isVisible;
    private BasicFunctionsConfig basicFunctionsConfig;
    private Pair<String, Fragment> optionFragmentPair;
    private int currentTabType = -1;
    //合约
    private PerpetualContractTradeFragment perpetualContractTradeFragment;
    private Pair<String, Fragment> perpetualContractFragmentPair;

    @Override
    protected ContractTradeFragmentPresenter.ContractTradeUI getUI() {
        return this;
    }

    @Override
    protected ContractTradeFragmentPresenter createPresenter() {
        return new ContractTradeFragmentPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_contract_trade_layout, null, false);
    }

    @Override
    protected void initViews() {
        super.initViews();
        basicFunctionsConfig = BasicFunctionsUtil.getBasicFunctionsConfig();
        viewPager = viewFinder.find(R.id.viewPager);
        viewPager.setScanScroll(false);
        tab = viewFinder.find(R.id.tab);
        tabline = viewFinder.find(R.id.tabline);
        Bundle bundle = getArguments();
        if (bundle != null) {
            coinPairBean = (CoinPairBean) bundle.getSerializable(AppData.INTENT.SYMBOLS);

        }
        initFragmentTab();
    }

    private void initFragmentTab() {

        if (items != null) {
            items.clear();
        }
        ArrayList<String> titles = new ArrayList<String>();
        ArrayList<Fragment> fragments = new ArrayList<Fragment>();
        items = new ArrayList<>();

        if (!basicFunctionsConfig.isFuture()) {
            //永续合约交易
            perpetualContractTradeFragment = new PerpetualContractTradeFragment();
            perpetualContractFragmentPair = new Pair<String, Fragment>(getString(R.string.string_trade_perpetual_contract), perpetualContractTradeFragment);
            if (coinPairBean != null && coinPairBean.getCoinType() == COIN_TYPE.COIN_TYPE_PERPETUAL_CONTRACT.getCoinType() && coinPairBean.baseTokenFutures!=null){
                perpetualContractTradeFragment.setDefalutCoinPair(coinPairBean);
            }
            perpetualContractTradeFragment.setHomeControl(new HomeTradeControl() {
                @Override
                public void OnShowTab(boolean bShow) {
                    if (bShow == true)
                        ShowTab();
                    else
                        HideTab();
                }

                @Override
                public boolean IsSelected() {
//                    if(isVisible && viewPager.getCurrentItem() == 1)
                    boolean isCurrent = viewPager.getCurrentItem() == items.indexOf(perpetualContractFragmentPair);
                    DebugLog.e("XXXXXX","  外层Contract isVisible "+isVisible +"  合约 isCurrentItem " + isCurrent);
                    return viewPager.getCurrentItem() == items.indexOf(perpetualContractFragmentPair);
                }
            });
            items.add(perpetualContractFragmentPair);
        }

        if (!basicFunctionsConfig.isOption()) {
            //期权交易
            optionTradeFragment = new OptionTradeFragment();
            optionFragmentPair = new Pair<String, Fragment>(getString(R.string.string_trade_option), optionTradeFragment);
            if (coinPairBean != null && coinPairBean.getCoinType() == COIN_TYPE.COIN_TYPE_OPTION.getCoinType() && coinPairBean.baseTokenOption != null)
                optionTradeFragment.setDefalutCoinPair(coinPairBean);
            optionTradeFragment.setHomeControl(new HomeTradeControl() {
                @Override
                public void OnShowTab(boolean bShow) {
                    if (bShow == true)
                        ShowTab();
                    else
                        HideTab();
                }

                @Override
                public boolean IsSelected() {
                    boolean isCurrent = viewPager.getCurrentItem() == items.indexOf(optionFragmentPair);
                    DebugLog.e("XXXXXX","  外层Contract isVisible "+isVisible +"  期权 isCurrentItem " + isCurrent);
//                    return isVisible && viewPager.getCurrentItem() == items.indexOf(optionFragmentPair);
                    return viewPager.getCurrentItem() == items.indexOf(optionFragmentPair);
                }
            });
            items.add(optionFragmentPair);
        }

        if (items.size()>1) {
            tab.setVisibility(View.VISIBLE);
            tabline.setVisibility(View.VISIBLE);
        }else{
            tab.setVisibility(View.GONE);
            tabline.setVisibility(View.GONE);
        }

        marketAdapter = new FragmentAdapter(getChildFragmentManager(), items);
        viewPager.setAdapter(marketAdapter);
//        viewPager.setOffscreenPageLimit(3);
//        tab.setupWithViewPager(viewPager);
//        tab.setTabMode(TabLayout.MODE_FIXED);
//        tab.setTabGravity(TabLayout.GRAVITY_FILL);
//        CommonUtil.setUpIndicatorWidthByReflex2(tab,5,5);

        /** 处理titles *******/
        for (Pair<String, Fragment> item : items) {
            titles.add(item.first);
            fragments.add(item.second);
        }
        String[] strArray = new String[titles.size()];
        String[] titlesArray = titles.toArray(strArray);
        tab.setTabData(titlesArray);
        tab.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                viewPager.setCurrentItem(position);
            }

            @Override
            public void onTabReselect(int position) {

            }
        });

        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                setArgumentsOfPageSelect(position);
                tab.setCurrentTab(position);
                if (optionTradeFragment != null && position == items.indexOf(optionFragmentPair)) {
                    optionTradeFragment.getTicker();
                    optionTradeFragment.requestDepthData();
                } else if (perpetualContractTradeFragment != null && position == items.indexOf(perpetualContractFragmentPair)) {
                    perpetualContractTradeFragment.getTicker();
                    perpetualContractTradeFragment.requestDepthData();
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });

        if (currentTabType != -1) {
            if (items != null) {
                switch (currentTabType) {
                    case TAB_FUTURES:
                        mCurrentIndex = items.indexOf(perpetualContractFragmentPair);
                        break;
                    case TAB_OPTION:
                        mCurrentIndex = items.indexOf(optionFragmentPair);
                        break;
                }
            }
        }
        setArgumentsOfPageSelect(mCurrentIndex);
        viewPager.setCurrentItem(mCurrentIndex);

    }

    @Override
    public void onResume() {
        super.onResume();
        tab.setTextSelectColor(getResources().getColor(CommonUtil.isBlackMode() ? R.color.dark_night : R.color.white));
    }

    /**
     * 记录当前选择的page tab
     * @param position
     */
    private void setArgumentsOfPageSelect(int position) {
        //设置选中参数
        try{
            if (items != null) {
                for (int i = 0; i < items.size(); i++) {
                    Pair<String, Fragment> pair = items.get(i);
                    if (pair != null) {
                        Fragment secondFragment = pair.second;
                        if (secondFragment != null) {
                            Bundle bundle = new Bundle();
                            if (position == i) {
                                bundle.putBoolean("isSelect",true);
                            }else{
                                bundle.putBoolean("isSelect",false);
                            }
                            secondFragment.setArguments(bundle);
                        }
                    }
                }

            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }


    public void SetTab(int tabType) {
        int item = 0;
        currentTabType = tabType;
        if (items != null) {
            switch (tabType) {
                case TAB_FUTURES:
                    item = items.indexOf(perpetualContractTradeFragment);
                    break;
                case TAB_OPTION:
                    item = items.indexOf(optionFragmentPair);
                    break;
            }
        }
        if (tab != null && item < tab.getTabCount()){
            viewPager.setCurrentItem(item);
        }
        mCurrentIndex = item;
    }

    public void ShowTab() {
        tab.setVisibility(View.VISIBLE);
        tabline.setVisibility(View.VISIBLE);
    }

    public void HideTab() {
        tab.setVisibility(View.GONE);
        tabline.setVisibility(View.GONE);
    }


    @Override
    public void onPause() {
        super.onPause();
        //QuoteApi.UnSubTickers();
        //QuoteApi.UnSubDepthData();
    }


    @Override
    public void onVisibleChanged(boolean visible) {
        super.onVisibleChanged(visible);
        isVisible = visible;
        if (isVisible) {
            if (optionTradeFragment != null && viewPager.getCurrentItem() == items.indexOf(optionFragmentPair)) {
                optionTradeFragment.getTicker();
                optionTradeFragment.requestDepthData();
            } else if (perpetualContractTradeFragment != null && viewPager.getCurrentItem() == items.indexOf(perpetualContractFragmentPair)) {
                perpetualContractTradeFragment.getTicker();
                perpetualContractTradeFragment.requestDepthData();
            }

        } else {
//            QuoteApi.UnSubTickers();
//            QuoteApi.UnSubDepthData();
        }
    }
}
