package io.bhex.app.trade.adapter;

import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.sdk.trade.bean.PlanOrderBean;

/**
 * ================================================
 * 描   述：计划委托
 * ================================================
 */

public class HistoryPlanOrdersAdapter extends BaseQuickAdapter<PlanOrderBean, BaseViewHolder> {

    private boolean mIsShowName=true;
    private  int mAccountType;

    public HistoryPlanOrdersAdapter(List<PlanOrderBean> data,int accountType) {
        super(R.layout.item_plan_order_layout, data);
        mAccountType = accountType;
    }

    public void showCoinPairName(boolean isShowName){
        mIsShowName = isShowName;
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final PlanOrderBean itemModel) {
        baseViewHolder.setVisible(R.id.order_coin_name,mIsShowName);
        String title = KlineUtils.getBuyOrSellTxt(mContext, itemModel.getSide());
        baseViewHolder.setText(R.id.order_buy_type, title);
        baseViewHolder.setTextColor(R.id.order_buy_type,KlineUtils.getBuyOrSellColor(mContext,itemModel.getSide()));
        baseViewHolder.setText(R.id.order_coin_name,itemModel.getBaseTokenName() + " / " + itemModel.getQuoteTokenName());
        baseViewHolder.setText(R.id.order_time, DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getTime()), "HH:mm:ss yyyy/MM/dd"));


        baseViewHolder.setText(R.id.trigger_price, KlineUtils.getPlanOrderTriggerPrice(mContext, itemModel));
        baseViewHolder.setText(R.id.order_price, KlineUtils.getPrice(mContext, itemModel));
        baseViewHolder.setText(R.id.order_entrust_amount_title, KlineUtils.getEntrustTitle(mContext, itemModel)+":");
        baseViewHolder.setText(R.id.order_entrust_amount, KlineUtils.getOrderEntrustAndUnit(itemModel));

        baseViewHolder.setText(R.id.order_status, KlineUtils.getPlanOrderStatusTxt(mContext,itemModel.getStatus()));
        baseViewHolder.setGone(R.id.revoke_order,false);
        baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IntentUtils.goPlanOrderDetail(mContext,itemModel,mAccountType);
            }
        });
    }
}