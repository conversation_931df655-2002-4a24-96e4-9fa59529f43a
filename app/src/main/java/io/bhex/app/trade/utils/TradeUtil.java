package io.bhex.app.trade.utils;

import android.content.Context;
import android.text.TextUtils;

import java.util.ArrayList;

import io.bhex.app.R;
import io.bhex.app.utils.NumberUtils;
import io.bhex.sdk.quote.bean.TickerBean;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-09-23
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class TradeUtil {

    /**
     * 盘口显示模式
     *
     * @param context
     * @return
     */
    public static ArrayList<String> getShowModeList(Context context) {
        //盘口布局显示模式 默认、买单、卖单
        ArrayList<String> bookShowModeList = new ArrayList<>();
        bookShowModeList.add(context.getResources().getString(R.string.string_show_default));
        bookShowModeList.add(context.getResources().getString(R.string.string_show_buy_order));
        bookShowModeList.add(context.getResources().getString(R.string.string_show_sell_order));

        return bookShowModeList;
    }


    /**
     * 检查普通下单价格是否偏离过多
     *
     * @param isBuyMode
     * @param price
     */
    public static boolean checkCreateOrderPrice(boolean isBuyMode, String price, TickerBean currentTicker) {
        if (currentTicker != null) {
            String lastestPrice = currentTicker.getC();
            if (!TextUtils.isEmpty(lastestPrice) && !TextUtils.isEmpty(price)) {
                if (isBuyMode) {//买
                    if (NumberUtils.div(lastestPrice, NumberUtils.sub(price, lastestPrice) + "") > 0.2d) {
                        return false;
                    }
                } else {//卖
                    if (NumberUtils.div(lastestPrice, NumberUtils.sub(price, lastestPrice) + "") < -0.2d) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 检查计划委托下单价格是否偏离(触发价)过多
     *
     * @param isBuyMode
     * @param price
     */
    public static boolean checkCreateStopOrderPrice(boolean isBuyMode, String price, String triggerPrice) {
        if (!TextUtils.isEmpty(triggerPrice)) {
            if (!TextUtils.isEmpty(triggerPrice) && !TextUtils.isEmpty(price)) {
                if (isBuyMode) {//买
                    if (NumberUtils.div(triggerPrice, NumberUtils.sub(price, triggerPrice) + "") > 0.2d) {
                        return false;
                    }
                } else {//卖
                    if (NumberUtils.div(triggerPrice, NumberUtils.sub(price, triggerPrice) + "") < -0.2d) {
                        return false;
                    }
                }
            }
        }
        return true;
    }


    public static boolean checkCreatePlanOrderPrice(boolean isBuyMode, String price, String triggerPrice) {
        if (!TextUtils.isEmpty(triggerPrice) && !TextUtils.isEmpty(price)) {
            if (isBuyMode) {//买
                if (NumberUtils.div(triggerPrice, NumberUtils.sub(price, triggerPrice) + "") > 0.1d) {
                    return false;
                }
            } else {//卖
                if (NumberUtils.div(triggerPrice, NumberUtils.sub(price, triggerPrice) + "") < -0.1d) {
                    return false;
                }
            }
        }
        return true;
    }
}
