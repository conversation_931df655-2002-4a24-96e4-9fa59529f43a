/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: LastPriceProvider.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.trade.adapter;

import android.text.TextUtils;

import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.provider.BaseItemProvider;

import io.bhex.app.R;
import io.bhex.app.trade.bean.BookListBean;
import io.bhex.app.trade.listener.BookClickListener;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.utils.SkinColorUtil;

public class LastPriceProvider extends BaseItemProvider<BookListBean,BaseViewHolder> {
    private final BookClickListener mClickListener;

    public LastPriceProvider(BookClickListener clickListener) {
        mClickListener = clickListener;
    }

    @Override
    public int viewType() {
        return BookListBean.TYPE_LASTPRICE;
    }

    @Override
    public int layout() {
        return R.layout.item_last_price_layout;
    }

    @Override
    public void convert(final BaseViewHolder helper, final BookListBean data, int position) {
        helper.setText(R.id.averagePrice,data.getLastPrice());
        String change = data.getChange();
        if (!TextUtils.isEmpty(change)) {
            if (NumberUtils.sub(change,"0")>=0) {
                helper.setTextColor(R.id.averagePrice, SkinColorUtil.getGreen(mContext));
            }else{
                helper.setTextColor(R.id.averagePrice,SkinColorUtil.getRed(mContext));
            }
        }else{
            helper.setTextColor(R.id.averagePrice,SkinColorUtil.getGreen(mContext));
        }
        helper.setText(R.id.price2,"≈"+data.getLegalPrice());
        String netValue = data.getNetValue();
        if (!TextUtils.isEmpty(netValue)) {
            helper.setVisible(R.id.netValue,true);
            helper.setText(R.id.netValue,mContext.getString(R.string.string_nav) + " " + netValue);
        }else{
            helper.setGone(R.id.netValue,false);
            helper.setText(R.id.netValue,"");
        }
//        helper.setOnClickListener(R.id.btn_setting, new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//
//            }
//        });

        helper.addOnClickListener(R.id.btn_setting);
        helper.addOnClickListener(R.id.netValue);
    }

    @Override
    public void onClick(BaseViewHolder helper, BookListBean data, int position) {
        super.onClick(helper, data, position);
        if (mClickListener != null) {
            mClickListener.onItemClick(helper.getView(R.id.averagePrice), data.getBook(), true,data.getLastPrice(),"","");
        }
    }
}
