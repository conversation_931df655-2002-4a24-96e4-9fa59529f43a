/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BookListBean.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.trade.bean;

import io.bhex.app.view.bean.Book;


public class BookListBean {
    public static final int TYPE_BOOK = 1;
    public static final int TYPE_LASTPRICE = 2;
    public final int mType;
    private Book book;
    private String lastPrice;
    private String legalPrice;
    private String changeRate;
    private String change;
    private String indices; //指数
    private String netValue;    //净值
    public BookListBean(int type) {
        mType = type;
    }

    public Book getBook() {
        return book;
    }

    public void setBook(Book book) {
        this.book = book;
    }

    public String getLastPrice() {
        return lastPrice;
    }

    public void setLastPrice(String lastPrice) {
        this.lastPrice = lastPrice;
    }

    public String getLegalPrice() {
        return legalPrice;
    }

    public void setLegalPrice(String legalPrice) {
        this.legalPrice = legalPrice;
    }

    public String getChangeRate() {
        return changeRate;
    }

    public void setChangeRate(String changeRate) {
        this.changeRate = changeRate;
    }

    public String getChange() {
        return change;
    }

    public void setChange(String change) {
        this.change = change;
    }

    public String getIndices() {
        return indices;
    }

    public void setIndices(String indices) {
        this.indices = indices;
    }

    public String getNetValue() {
        return netValue;
    }

    public void setNetValue(String netValue) {
        this.netValue = netValue;
    }
}
