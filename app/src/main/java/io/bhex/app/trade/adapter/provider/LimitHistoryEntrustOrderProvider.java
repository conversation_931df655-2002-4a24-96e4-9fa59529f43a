/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: LimitEntrustOrderProvider.java
 *   @Date: 19-8-8 下午6:29
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.trade.adapter.provider;

import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.provider.BaseItemProvider;

import io.bhex.app.R;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.DialogUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.enums.ORDER_ENTRUST_TYPE;
import io.bhex.sdk.trade.futures.bean.FuturesOrderResponse;

public class LimitHistoryEntrustOrderProvider extends BaseItemProvider<FuturesOrderResponse, BaseViewHolder> {
    @Override
    public int viewType() {
        return ORDER_ENTRUST_TYPE.LIMIT.getTypeId();
    }

    @Override
    public int layout() {
        return R.layout.item_futures_history_entrust_order_layout;
    }

    @Override
    public void convert(BaseViewHolder baseViewHolder, final FuturesOrderResponse itemModel, int position) {
        if (itemModel == null)
            return;
        boolean isForceClose = KlineUtils.isFuturesForceCLoseOrder(mContext,itemModel);
        String orderSide = KlineUtils.getFuturesOrderSideTxt(mContext, itemModel.getSide());
        String lever = KlineUtils.isFuturesOpenOrder(mContext,itemModel.getSide()) ? "·" + itemModel.getLeverage() + "X" :"";
        baseViewHolder.setText(R.id.orderSideAndLever, orderSide + lever);
        baseViewHolder.setTextColor(R.id.orderSideAndLever, KlineUtils.getFuturesOrderSideColor(mContext, itemModel.getSide()));
        baseViewHolder.setText(R.id.order_status, KlineUtils.getFuturesOrderStatus(mContext,itemModel));
        baseViewHolder.setTextColor(R.id.order_status, KlineUtils.getFuturesOrderStatusColor(mContext,itemModel));
        baseViewHolder.setText(R.id.symbolName, itemModel.getSymbolName());
//        baseViewHolder.setText(R.id.priceTitle, mContext.getString(R.string.string_price)+"("+KlineUtils.getPriceTypeTxt(mContext,itemModel.getPriceType())+")");
        baseViewHolder.setText(R.id.positionValue, DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getTime()), "HH:mm:ss yy/MM/dd"));
        baseViewHolder.setText(R.id.value1, isForceClose ? "--" : KlineUtils.getFuturesPrice(mContext, itemModel));
        baseViewHolder.setText(R.id.positionMargin, isForceClose ? "--" : KlineUtils.getFuturesAvgPrice(mContext, itemModel));

        int tokenDigit = AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(itemModel.getSymbolId() + itemModel.getQuoteTokenId());
//        baseViewHolder.setText(R.id.holdAmount, NumberUtils.roundFormatDown(itemModel.getMargin(), tokenDigit) + " " + itemModel.getQuoteTokenName());
        baseViewHolder.setText(R.id.holdAmount, KlineUtils.getPriceTypeTxt(mContext,itemModel.getPriceType()));
        baseViewHolder.setText(R.id.aboutClosePrice, itemModel.getOrigQty() + " " + mContext.getString(R.string.string_option_unit));
        int baseDigit = AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(itemModel.getSymbolId() + itemModel.getBaseTokenId());
        baseViewHolder.setText(R.id.unrealizedProfitAndLoss, NumberUtils.roundFormatDown(itemModel.getExecutedQty(), baseDigit) + " " + mContext.getString(R.string.string_option_unit));

//        baseViewHolder.getView(R.id.itemView).setOnClickListener(new View.OnClickListener() {
        baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IntentUtils.goContractOrderDetail(mContext,itemModel);

            }
        });
    }

    @Override
    public void onClick(BaseViewHolder helper, FuturesOrderResponse data, int position) {
        super.onClick(helper, data, position);
        if (!KlineUtils.isFuturesForceCLoseOrder(mContext,data)) {
            IntentUtils.goContractOrderDetail(mContext,data);
        }else{
            DialogUtils.showForceCloseOrderDetail(mContext, data, false, new DialogUtils.OnButtonEventListener() {
                @Override
                public void onConfirm() {

                }

                @Override
                public void onCancel() {

                }
            });
        }
    }
}
