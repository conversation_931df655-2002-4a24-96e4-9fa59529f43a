/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: HistoryOptionDeliveryActivity.java
 *   @Date: 1/15/19 10:15 AM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.trade.ui;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.trade.presenter.HistoryOptionDeliveryPresenter;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.OptionCoinPairBean;
import io.bhex.sdk.trade.OptionApi;
import io.bhex.sdk.trade.bean.OptionHistoryResponse;

public class HistoryOptionDeliveryActivity  extends BaseActivity<HistoryOptionDeliveryPresenter,HistoryOptionDeliveryPresenter.HistoryOptionDeliveryUI> implements HistoryOptionDeliveryPresenter.HistoryOptionDeliveryUI, OnRefreshListener,BaseQuickAdapter.RequestLoadMoreListener {



    @Override
    protected int getContentView() {
        return R.layout.activity_option_delivery_layout;
    }

    @Override
    protected HistoryOptionDeliveryPresenter createPresenter() {
        return new HistoryOptionDeliveryPresenter();
    }

    @Override
    protected HistoryOptionDeliveryPresenter.HistoryOptionDeliveryUI getUI() {
        return this;
    }

    private RecyclerView recyclerView;
    private HistoryOptionDeliverAdapter adapter;
    private SmartRefreshLayout swipeRefresh;
    private View emptyView;

    @Override
    public void onStart() {
        super.onStart();
    }

    @Override
    public void onStop() {
        super.onStop();
    }

    @Override
    protected void initView() {
        super.initView();
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        recyclerView = viewFinder.find(R.id.recyclerView);

        LayoutInflater layoutInflater = LayoutInflater.from(this);
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
        emptyView.setLayoutParams(layoutParams);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        swipeRefresh.setOnRefreshListener(this);
    }

    @Override
    public void onLoadMoreRequested() {
        getPresenter().loadMore();
    }

    @Override
    public void onRefresh(RefreshLayout refreshLayout) {
        getPresenter().getHistoryEntrustOrders(false);
        refreshLayout.finishRefresh(1000);
    }

    @Override
    public void loadMoreComplete() {
        if (adapter != null) {
            adapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {
        if (adapter != null) {
            adapter.loadMoreFail();
        }
    }

    @Override
    public void loadEnd() {
        if (adapter != null) {
            adapter.loadMoreEnd();
        }
    }

    @Override
    public void showOptions(List<OptionHistoryResponse.OptionHistoryBean> listDatas) {
        if (adapter == null) {

            adapter = new HistoryOptionDeliverAdapter(listDatas);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this,recyclerView);
            adapter.setEmptyView(emptyView);
            recyclerView.setLayoutManager(new LinearLayoutManager(this));
            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(listDatas);
        }
    }

    private class HistoryOptionDeliverAdapter extends BaseQuickAdapter<OptionHistoryResponse.OptionHistoryBean,BaseViewHolder> {

        HistoryOptionDeliverAdapter(List<OptionHistoryResponse.OptionHistoryBean> listDatas) {
            super(R.layout.item_history_option_delivery_layout, listDatas);
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final OptionHistoryResponse.OptionHistoryBean itemModel) {
            //baseViewHolder.setVisible(R.id.item_divider, baseViewHolder.getAdapterPosition() != mData.size());
            baseViewHolder.setText(R.id.option_name, itemModel.symbolFullName);
            baseViewHolder.setText(R.id.option_time, DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.settlementTime), "HH:mm:ss yyyy/MM/dd"));
            baseViewHolder.setText(R.id.option_time_title, getString(R.string.string_option_time) + ":");
            baseViewHolder.setText(R.id.option_price, itemModel.strikePrice + itemModel.quoteTokenName) ;
            baseViewHolder.setText(R.id.option_price_title, getString(R.string.string_option_price) + ":");
            baseViewHolder.setText(R.id.option_delivery_price, itemModel.settlementPrice + itemModel.quoteTokenName);
            baseViewHolder.setText(R.id.option_delivery_price_title,  getString(R.string.string_option_delivery_price) + ":");
            baseViewHolder.setText(R.id.option_vol, itemModel.volume+ getString(R.string.string_option_unit));
            baseViewHolder.setText(R.id.option_vol_title,getString(R.string.string_option_vol) + ":");
            baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    RequestOptionSymbols(itemModel.symbolId);
                }
            });


        }

    }

    public void RequestOptionSymbols(final String symbolId) {
        if (!NetWorkStatus.isConnected(HistoryOptionDeliveryActivity.this) ){
            ToastUtils.showShort(HistoryOptionDeliveryActivity.this, getResources().getString(R.string.hint_network_not_connect));
            return;
        }


        OptionApi.RequestOptionSymbols(symbolId, new SimpleResponseListener<OptionCoinPairBean>() {
            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }
            @Override
            public void onSuccess(OptionCoinPairBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<CoinPairBean>  data = response.array;
                    if (data != null) {
                        for (CoinPairBean bean:data) {
                            if(bean != null && !TextUtils.isEmpty(symbolId) && symbolId.equalsIgnoreCase(bean.getSymbolId())){
                                IntentUtils.goKline(HistoryOptionDeliveryActivity.this, bean);
                                finish();
                                return;
                            }
                        }
                    }
                }
                ToastUtils.showShort(HistoryOptionDeliveryActivity.this, getResources().getString(R.string.hint_network_not_connect));
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(HistoryOptionDeliveryActivity.this, getResources().getString(R.string.hint_network_not_connect));
            }
        });
    }
}
