/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BookListSort.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.trade.sort;

import android.text.TextUtils;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Map;
import java.util.TreeMap;

import io.bhex.app.trade.presenter.TradeFragmentPresenter;

/**
 * ================================================
 * 描   述：book排序
 * ================================================
 */

public class BookListSort implements Comparator<String> {

    @Override
    public int compare(String str1, String str2) {
        //empty处理
        if (TextUtils.isEmpty(str1)) {
            str1 = "0";
        }
        if (TextUtils.isEmpty(str2)) {
            str2 = "0";
        }

        return new BigDecimal(str1).compareTo(new BigDecimal(str2));
    }

    /**
     * 使用 Map按key进行排序
     *
     * @param map
     * @return
     */
    public static Map<String, String> sortMapByKey(Map<String, String> map) {
        if (map == null || map.isEmpty()) {
//        if (map == null) {
            return new TreeMap<String, String>();
        }

        Map<String, String> sortMap = new TreeMap<String, String>(
                new BookListSort());

        sortMap.putAll(map);

        return sortMap;
    }
}
