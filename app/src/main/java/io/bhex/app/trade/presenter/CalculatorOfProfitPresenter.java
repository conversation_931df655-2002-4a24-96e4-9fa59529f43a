package io.bhex.app.trade.presenter;

import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BaseFragmentPresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.trade.bean.CalculateProfitResponse;
import io.bhex.sdk.trade.bean.FuturesCreateOrderConfig;
import io.bhex.sdk.trade.bean.FuturesCreateOrderConfigResponse;
import io.bhex.sdk.trade.futures.FuturesApi;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-02-21
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class CalculatorOfProfitPresenter extends BaseFragmentPresenter<CalculatorOfProfitPresenter.CalculatorOfProfitUI> {

    public interface CalculatorOfProfitUI extends AppUI{

        void showResult(CalculateProfitResponse response);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, CalculatorOfProfitUI ui) {
        super.onUIReady(activity, ui);
    }

    public void calculateProfit(boolean isLong, CoinPairBean currentCoinPair, String lever, String openPrice, String openQuantity, String closePrice, String closeQuantity) {
        FuturesApi.calculateProfitInfo(isLong ? 1 : 0 ,currentCoinPair.getSymbolId(),lever,openPrice,openQuantity,closePrice,closeQuantity,new SimpleResponseListener<CalculateProfitResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(CalculateProfitResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showResult(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    /**
     * 下单配置项（费率，下单设置，风险限额）
     */
    public void getOrderSetting(CoinPairBean coinPairBean) {
        if (!UserInfo.isLogin()) {
            return;
        }
        if (coinPairBean == null || !UserInfo.isLogin()) {
            return;
        }
        String exchangeId = coinPairBean.getExchangeId();
        String symbolId = coinPairBean.getSymbolId();
        FuturesApi.getFuturesCreateOrderConfig(exchangeId, symbolId, new SimpleResponseListener<FuturesCreateOrderConfigResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(FuturesCreateOrderConfigResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    List<FuturesCreateOrderConfig> orderConfigs = response.getArray();
                    if (orderConfigs != null && orderConfigs.size() > 0) {
//                        for (FuturesCreateOrderConfig orderConfig : orderConfigs) {
//                            if (orderConfig != null) {
//                                //保存下单配置实体数据
//                                orderConfigMap.put(orderConfig.getSymbolId(),orderConfig);
//                            }
//                        }
                        FuturesCreateOrderConfig futuresCreateOrderConfig = orderConfigs.get(0);
                        if (futuresCreateOrderConfig != null) {
                            //保存下单配置实体数据
//                            orderConfigMap.put(futuresCreateOrderConfig.getSymbolId(), futuresCreateOrderConfig);
//                            List<RiskLimitBean> riskLimit = futuresCreateOrderConfig.getRiskLimit();
//                            if (riskLimit != null) {
//                                for (RiskLimitBean riskLimitBean : riskLimit) {
//                                    //保存下单配置-风险限额实体数据
//                                    riskLimitMap.put(futuresCreateOrderConfig.getSymbolId() + riskLimitBean.getSide(), riskLimitBean);
//                                }
//                                getUI().updateLevers();
//                            }
                        }
                    }

                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
