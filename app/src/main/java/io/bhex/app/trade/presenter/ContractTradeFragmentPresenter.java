/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: ContractTradeFragmentPresenter.java
 *   @Date: 19-6-12 下午8:32
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.trade.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseFragmentPresenter;

public class ContractTradeFragmentPresenter extends BaseFragmentPresenter<ContractTradeFragmentPresenter.ContractTradeUI> {
    public interface ContractTradeUI extends AppUI {

    }

}
