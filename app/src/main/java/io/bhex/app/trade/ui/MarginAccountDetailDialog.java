/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MarginAdjustDialog.java
 *   @Date: 19-7-29 下午7:33
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.trade.ui;

import android.app.Activity;
import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Build;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import java.util.List;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import io.bhex.app.R;
import io.bhex.app.trade.adapter.MarginCoinAdapter;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.view.DashboardView;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.data_manager.MarginDataManager;
import io.bhex.sdk.data_manager.RateDataManager;
import io.bhex.sdk.socket.NetWorkObserver;
import io.bhex.sdk.trade.margin.MarginApi;
import io.bhex.sdk.trade.margin.bean.MarginAllPositionResponse;
import io.bhex.sdk.trade.margin.bean.MarginFullAccountLoanPositionResponse;
import io.bhex.sdk.trade.margin.bean.MarginRiskConfigRespone;
import io.bhex.sdk.trade.margin.bean.MarginSafetyResponse;

public class MarginAccountDetailDialog {

    private Dialog bottomDialog;
    private Activity mContext;
    private View mContentView;
    private RecyclerView recyclerView;
    private TextView mFullDebtValueTv;
    private TextView mFullAssetValueTv;
    private TextView mRiskExplainTv;
    private TextView mRiskValueTv;
    private TextView mRiskTv;
    MarginCoinAdapter adapter;

    private MarginSafetyResponse marginSafetyResponse;
    private MarginRiskConfigRespone.DataBean riskConfigData;
    private final MarginAccountDetailDialog.OnDialogObserver mDialogObserver;
    private final MarginAccountDetailDialog.OnLoadingObserver mObserver;
    private DashboardView dashboardRisk;

    public interface OnLoadingObserver {
        void showLoading();

        void hideLoading();
    }

    public interface OnDialogObserver {

        void onTransfer();

        void onDismiss();

        void onLoan();

        void onRepay();

        void onInstruction();

    }

    public MarginAccountDetailDialog(Activity context, MarginAccountDetailDialog.OnDialogObserver dialogObserver, MarginAccountDetailDialog.OnLoadingObserver onLoadingObserver) {
        mContext = context;
        mDialogObserver = dialogObserver;
        mObserver = onLoadingObserver;
    }

    public void ShowDialog() {
        bottomDialog = new Dialog(mContext, R.style.TopDialog);
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.dialog_margin_account_detail, null);
        bottomDialog.setContentView(mContentView);
        initView();

        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) mContentView.getLayoutParams();
        params.width = mContext.getResources().getDisplayMetrics().widthPixels;
        int height = PixelUtils.dp2px(580);
        if (mContext.getResources().getDisplayMetrics().heightPixels *0.8 < height) {
            height = (int) (mContext.getResources().getDisplayMetrics().heightPixels *0.8);
        }
        params.height = height;
        mContentView.setLayoutParams(params);
        bottomDialog.setCanceledOnTouchOutside(true);
        bottomDialog.getWindow().setGravity(Gravity.TOP);
        bottomDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                if (observer!=null) {
                    MarginApi.unSubMarginSafety(observer);
                }
            }
        });
        bottomDialog.show();
        getSafty();
        getMarginRiskConfig();
        getCrossLoanPosition();
        getAllPosition();
    }


    private void initView() {
        initSystemBarTint();

        try {

            mRiskTv = mContentView.findViewById(R.id.tv_risk);
            mRiskValueTv = mContentView.findViewById(R.id.tv_risk_value);
            mRiskExplainTv = mContentView.findViewById(R.id.tv_risk_explain);
            dashboardRisk = mContentView.findViewById(R.id.dashboard_risk);

            mFullAssetValueTv = mContentView.findViewById(R.id.tv_full_asset_value);
            mFullDebtValueTv = mContentView.findViewById(R.id.tv_full_debt_value);

            recyclerView = mContentView.findViewById(R.id.recyclerView);

            mContentView.findViewById(R.id.tv_transfer).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (bottomDialog != null && bottomDialog.isShowing()) {
                        bottomDialog.dismiss();
                    }
                    if (mDialogObserver != null) {
                        mDialogObserver.onTransfer();
                    }
                }
            });
            mContentView.findViewById(R.id.tv_debt).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (bottomDialog != null && bottomDialog.isShowing()) {
                        bottomDialog.dismiss();
                    }
                    if (mDialogObserver != null) {
                        mDialogObserver.onLoan();
                    }
                }
            });
            mContentView.findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (bottomDialog != null && bottomDialog.isShowing()) {
                        bottomDialog.dismiss();
                    }
                }
            });

            mContentView.findViewById(R.id.tv_repay).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (bottomDialog != null && bottomDialog.isShowing()) {
                        bottomDialog.dismiss();
                    }
                    if (mDialogObserver != null) {
                        mDialogObserver.onRepay();
                    }
                }
            });

        } catch (Exception e) {
        }
    }

    public void showRecordList(List<MarginFullAccountLoanPositionResponse.ArrayBean> datas) {
        if (datas == null)
            return;
        String unit = AppData.Config.MARGIN_ASSET_UNIT;
        String debtAsset = "0";
        for (MarginFullAccountLoanPositionResponse.ArrayBean dataBean : datas) {
            debtAsset = NumberUtils.add2(debtAsset, dataBean.getUnpaidBtcValue());
            debtAsset = NumberUtils.add2(debtAsset, dataBean.getLoanBtcValue());
        }
        debtAsset = NumberUtils.roundFormatDown(debtAsset, AppData.Config.DIGIT_DEFAULT_VALUE);
        mFullDebtValueTv.setText(mContext.getString(R.string.string_margin_asset_format,debtAsset));
        if (adapter == null) {

            adapter = new MarginCoinAdapter(datas);
            adapter.isFirstOnly(false);
            adapter.setEnableLoadMore(false);
            View header = LayoutInflater.from(mContext).inflate(R.layout.header_margin_coin_info_layout, null);
            adapter.setHeaderView(header);

            recyclerView.setLayoutManager(new LinearLayoutManager(mContext));
            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(datas);
        }
    }

    /**
     * 设置状态栏颜色
     */
    protected void initSystemBarTint() {
        Window window = bottomDialog.getWindow();

        // 沉浸式状态栏
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            //5.0以上使用原生方法
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(mContext.getResources().getColor(R.color.white));
        }
    }

    public void updateAccountSafety(MarginSafetyResponse response) {
        marginSafetyResponse = response;
        updateSafetyUI();
    }

    public void updateRiskConfig(MarginRiskConfigRespone.DataBean response) {
        riskConfigData = response;
        if (riskConfigData!=null) {
            mRiskExplainTv.setText(mContext.getResources().getString(R.string.string_close_line_explain, NumberUtils.mul(riskConfigData.getStopLine(),"100")+"%"));
        }

        updateSafetyUI();
    }

    private void updateSafetyUI() {
        if (marginSafetyResponse != null) {
            if (NumberUtils.sub(marginSafetyResponse.getSafety(),"100")>0) {
                mRiskValueTv.setText(">10000%");
            } else {
                mRiskValueTv.setText(mContext.getString(R.string.string_safety_value, NumberUtils.roundFormatDown(NumberUtils.mul(marginSafetyResponse.getSafety(), "100"),4) + "%"));
            }
            if (riskConfigData != null) {
                if (NumberUtils.sub(marginSafetyResponse.getSafety(), riskConfigData.getWarnLine()) > 0) {
                    double rate =NumberUtils.div(NumberUtils.sub(riskConfigData.getWithdrawLine(),riskConfigData.getAppendLine()),NumberUtils.sub(riskConfigData.getWithdrawLine(),marginSafetyResponse.getSafety()),AppData.Config.DIGIT_DEFAULT_VALUE);
                    dashboardRisk.setVelocity(0+(int)(60*rate));
                    mRiskTv.setText(mContext.getResources().getString(R.string.margin_no_risk));
                    mRiskValueTv.setTextColor(ContextCompat.getColor(mContext, CommonUtil.isBlackMode() ? R.color.blue_night : R.color.blue));
                } else if (NumberUtils.sub(marginSafetyResponse.getSafety(), riskConfigData.getAppendLine()) > 0) {
                    double rate =NumberUtils.div(NumberUtils.sub(riskConfigData.getAppendLine(),riskConfigData.getWarnLine()),NumberUtils.sub(riskConfigData.getAppendLine(),marginSafetyResponse.getSafety()),AppData.Config.DIGIT_DEFAULT_VALUE);
                    dashboardRisk.setVelocity(60+(int)(60*rate));
                    mRiskTv.setText(mContext.getResources().getString(R.string.margin_warning_risk));
                    mRiskValueTv.setTextColor(ContextCompat.getColor(mContext, CommonUtil.isBlackMode() ? R.color.margin_warning_risk_color : R.color.margin_warning_risk_color));
                } else if (NumberUtils.sub(marginSafetyResponse.getSafety(), AppData.DOUBLE_ZERO) > 0) {
                    double rate =NumberUtils.div(NumberUtils.sub(riskConfigData.getWarnLine(),riskConfigData.getStopLine()),NumberUtils.sub(riskConfigData.getWarnLine(),marginSafetyResponse.getSafety()),AppData.Config.DIGIT_DEFAULT_VALUE);
                    dashboardRisk.setVelocity(120+(int)(60*rate));
                    mRiskTv.setText(mContext.getResources().getString(R.string.margin_high_risk));
                    mRiskValueTv.setTextColor(ContextCompat.getColor(mContext, CommonUtil.isBlackMode() ? R.color.margin_high_risk_color : R.color.margin_high_risk_color));
                } else {
                    dashboardRisk.setVelocity(0);
                    mRiskTv.setText(mContext.getResources().getString(R.string.string_placeholder));
                    mRiskValueTv.setText(mContext.getString(R.string.string_placeholder));

                }

            }
        }
    }

    private void getAllPosition() {
        MarginApi.RequestMarginAllPosition(new SimpleResponseListener<MarginAllPositionResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
//                if (mObserver != null)
//                    mObserver.showLoading();
            }

            @Override
            public void onFinish() {
                super.onFinish();
//                if (mObserver != null)
//                    mObserver.hideLoading();
            }

            @Override
            public void onSuccess(MarginAllPositionResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    updateAccountAllPosition(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    private void updateAccountAllPosition(MarginAllPositionResponse response) {
        if (response==null)
            return;
        String unit = AppData.Config.MARGIN_ASSET_UNIT;
        String totalAsset = NumberUtils.roundFormatDown(RateDataManager.getTokenToBTC(unit,response.getTotal()), AppData.Config.DIGIT_DEFAULT_VALUE);

        mFullAssetValueTv.setText(mContext.getString(R.string.string_margin_asset_format,totalAsset));

    }

    NetWorkObserver<MarginSafetyResponse> observer=new NetWorkObserver<MarginSafetyResponse>() {
        @Override
        public void onShowUI(MarginSafetyResponse response) {
            if (!bottomDialog.isShowing())
                return;
            if (response != null) {
                updateAccountSafety(response);
            }
        }

        @Override
        public void onError(String response) {

        }
    };
    /**
     * 获取安全度
     */
    private void getSafty() {

        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        }

        MarginApi.SubMarginSafety(observer);
        MarginApi.RequestAccountSafety(new SimpleResponseListener<MarginSafetyResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
//                if (mObserver != null)
//                    mObserver.showLoading();
            }

            @Override
            public void onFinish() {
                super.onFinish();
//                if (mObserver != null)
//                    mObserver.hideLoading();
            }

            @Override
            public void onSuccess(MarginSafetyResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    updateAccountSafety(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    protected void getMarginRiskConfig() {
        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        }

        MarginDataManager.GetInstance().getRiskConfig(new SimpleResponseListener<MarginRiskConfigRespone>() {
            @Override
            public void onSuccess(MarginRiskConfigRespone response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    if (response!=null && response!=null && response.getArray().size()>0)
                    updateRiskConfig(response.getArray().get(0));
                }
            }
        });
    }

    private void getCrossLoanPosition() {
        MarginApi.RequestCrossLoanPosition(new SimpleResponseListener<MarginFullAccountLoanPositionResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
//                if (mObserver != null)
//                    mObserver.showLoading();
            }

            @Override
            public void onFinish() {
                super.onFinish();
//                if (mObserver != null)
//                    mObserver.hideLoading();
            }

            @Override
            public void onSuccess(MarginFullAccountLoanPositionResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    showRecordList(response.getArray());
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
