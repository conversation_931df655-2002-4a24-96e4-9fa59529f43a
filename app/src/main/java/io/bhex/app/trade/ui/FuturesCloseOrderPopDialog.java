/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FuturesCloseOrderPopDialog.java
 *   @Date: 19-7-26 下午2:42
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.trade.ui;

import android.app.Dialog;
import android.content.Context;
import android.text.InputFilter;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.enums.PRICE_TYPE;
import io.bhex.app.skin.view.SkinTabLayout;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.app.view.PointLengthFilter;
import io.bhex.app.view.ShadowDrawable;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.enums.ORDER_ENTRUST_TYPE;
import io.bhex.sdk.enums.ORDER_SIDE;
import io.bhex.sdk.quote.QuoteApi;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.TickerBean;
import io.bhex.sdk.quote.bean.TickerListBean;
import io.bhex.sdk.trade.futures.FuturesApi;
import io.bhex.sdk.trade.futures.bean.FuturesOrderResponse;
import io.bhex.sdk.trade.futures.bean.FuturesPositionOrder;
import io.bhex.sdk.trade.futures.bean.OrderCreateParams;

public class FuturesCloseOrderPopDialog {
    private final MarginAdjustDialog.OnDialogObserver mDialogObserver;
    private Context mContext;
    private View mContentView;
    private Dialog bottomDialog;
    private String lastPrice;
    private CoinPairBean coinPairBean;

    private int digitBase;
    private int digitAmount;
    private FuturesPositionOrder mHoldOrderBean;
    private OnLoadingObserver mObserver;

    public interface OnLoadingObserver {
        void showLoading();

        void hideLoading();
    }

    public FuturesCloseOrderPopDialog(Context context, FuturesPositionOrder itemModel, OnLoadingObserver observer, MarginAdjustDialog.OnDialogObserver dialogObserver) {
        mContext = context;
        mHoldOrderBean = itemModel;
        mObserver = observer;
        mDialogObserver = dialogObserver;
    }

    public void ShowDialog() {
        bottomDialog = new Dialog(mContext, R.style.BottomDialog);
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.futures_dialog_content_circle, null);
        bottomDialog.setContentView(mContentView);
        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) mContentView.getLayoutParams();
        params.width = mContext.getResources().getDisplayMetrics().widthPixels;
        mContentView.setLayoutParams(params);
        initView();
        bottomDialog.setCanceledOnTouchOutside(true);
        bottomDialog.getWindow().setGravity(Gravity.BOTTOM);
        bottomDialog.getWindow().setWindowAnimations(R.style.BottomDialog_Animation);
        bottomDialog.show();
    }

    private void initView() {
        try {

            if (mHoldOrderBean != null && mContentView != null) {
                String futuresPriceUnit = KlineUtils.getFuturesPriceUnit(mHoldOrderBean.getSymbolId());
                //isLong 仓位方向: 1=多仓，0=空仓
                String positionSide = KlineUtils.getFuturesOrderPositionTxtByIsLong(mContext, mHoldOrderBean.getIsLong());
                String lever = "·" + mHoldOrderBean.getLeverage() + "X";
                int color = KlineUtils.getFuturesOrderSideColorByIsLong(mContext, mHoldOrderBean.getIsLong());
                int colorOfOpposit = KlineUtils.getFuturesOrderOppositeSideColorByIsLong(mContext, mHoldOrderBean.getIsLong());
                ((TextView) mContentView.findViewById(R.id.order_buy_type)).setText(positionSide + lever);
                ((TextView) mContentView.findViewById(R.id.order_buy_type)).setTextColor(color);
                ((TextView) mContentView.findViewById(R.id.order_coin_name)).setText(mHoldOrderBean.getSymbolName());
                mContentView.findViewById(R.id.order_close).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        bottomDialog.dismiss();
                    }
                });

                ViewPager viewPager = mContentView.findViewById(R.id.createOrderVp);
                SkinTabLayout tab = mContentView.findViewById(R.id.tabText);
                tab.setTabTextColors(mContext.getResources().getColor(CommonUtil.isBlackMode() ? R.color.dark_night : R.color.dark),mContext.getResources().getColor(R.color.blue));


                String exchangeId = "";
                coinPairBean = AppConfigManager.GetInstance().getSymbolInfoById(mHoldOrderBean.getSymbolId());
                if (coinPairBean == null) {
                    ToastUtils.showShort(mContext.getResources().getString(R.string.string_data_exception));
                    if (bottomDialog.isShowing()) {
                        bottomDialog.dismiss();
                    }
                    return;
                }
                exchangeId = coinPairBean.getExchangeId();

                PointLengthFilter pricePointFilter = new PointLengthFilter();
                PointLengthFilter amountPointFilter = new PointLengthFilter();
                digitBase = NumberUtils.calNumerCount(mContext, coinPairBean.getBasePrecision());
                digitAmount = NumberUtils.calNumerCount(mContext, coinPairBean.getQuotePrecision());

                pricePointFilter.setDecimalLength(digitAmount);
                amountPointFilter.setDecimalLength(digitBase);

                List<View> mViewList = new ArrayList<>();

                /** 限价 *************/
                View limitedView = LayoutInflater.from(mContext).inflate(R.layout.item_create_order_layout, null);
                setShadow(limitedView.findViewById(R.id.edit_price_rela));
                setShadow(limitedView.findViewById(R.id.edit_amount_rela));


                ((TextView) limitedView.findViewById(R.id.edit_price_unit)).setText(futuresPriceUnit);

                final TextView limitedAmountTV = limitedView.findViewById(R.id.edit_amount);
                limitedAmountTV.setText(String.valueOf(Math.abs(Double.valueOf(mHoldOrderBean.getAvailable()))));
                limitedAmountTV.setFilters(new InputFilter[]{amountPointFilter});

                final TextView limitedPriceTV = limitedView.findViewById(R.id.edit_price);
                limitedPriceTV.setFilters(new InputFilter[]{pricePointFilter});

                setCanCloseTxt(limitedView, mHoldOrderBean.getAvailable());
                ((TextView) limitedView.findViewById(R.id.btn_create_order)).setBackgroundColor(colorOfOpposit);
                ((TextView) limitedView.findViewById(R.id.btn_create_order)).setText(R.string.string_option_limited_close);
                limitedView.findViewById(R.id.btn_create_order).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        String price = limitedPriceTV.getText().toString().trim();

                        if (TextUtils.isEmpty(price)) {
                            ToastUtils.showShort(mContext, mContext.getString(R.string.string_prompt_input_price));
                            return;
                        }
                        if (Double.valueOf(price) <= 0) {
                            ToastUtils.showShort(mContext, mContext.getString(R.string.string_prompt_input_price));
                            return;
                        }

                        String amount = limitedAmountTV.getText().toString().trim();
                        if (TextUtils.isEmpty(amount)) {
                            ToastUtils.showShort(mContext, mContext.getString(R.string.string_prompt_input_amount));
                            return;
                        }
                        if (Double.valueOf(amount) <= 0) {
                            ToastUtils.showShort(mContext, mContext.getString(R.string.string_prompt_input_amount));
                            return;
                        }
                        if (Double.valueOf(amount) > Math.abs(Double.valueOf(mHoldOrderBean.getAvailable()))) {
                            ToastUtils.showShort(mContext, mContext.getString(R.string.string_option_over_amount));
                            return;
                        }

                        if (coinPairBean != null) {
                            //最小交易数量
                            String minTradeQuantity = coinPairBean.getMinTradeQuantity();
                            //最小交易额
                            String minTradeAmount = coinPairBean.getMinTradeAmount();

                            String minPricePrecision = coinPairBean.getMinPricePrecision();
                            //最小交易价格
                            if (!TextUtils.isEmpty(minPricePrecision)) {
                                if (NumberUtils.sub(price, minPricePrecision) < 0) {
                                    ToastUtils.showShort(mContext, mContext.getString(R.string.string_min_trade_price, minPricePrecision) + coinPairBean.getQuoteTokenName());
                                    return;
                                }
                            }

                            //最小交易数量
                            if (!TextUtils.isEmpty(minTradeQuantity)) {
                                if (NumberUtils.sub(amount, minTradeQuantity) < 0) {
                                    ToastUtils.showShort(mContext, mContext.getString(R.string.string_min_trade_quantity, minTradeQuantity) + mContext.getString(R.string.string_option_unit));
                                    return;
                                }
                            }

                            //最小交易额
//                            if (!TextUtils.isEmpty(minTradeAmount)) {
////                                xxxxxxx  算法不对
//                                if (NumberUtils.sub(String.valueOf(NumberUtils.mul(price, amount)), minTradeAmount) < 0) {
//                                    ToastUtils.showShort(mContext, mContext.getString(R.string.string_min_trade_amount, minTradeAmount) + coinPairBean.getQuoteTokenName());
//                                    return;
//                                }
//                            }

                        } else {
                            return;
                        }
                        String clientOrderId = System.currentTimeMillis() + "";
                        boolean isBuyMode;
                        String side;
                        if (mHoldOrderBean.getIsLong().equals("1")) {
                            isBuyMode = false;
                            side = ORDER_SIDE.SELL_CLOSE.getOrderSide();
                        } else {
                            isBuyMode = true;
                            side = ORDER_SIDE.BUY_CLOSE.getOrderSide();

                        }
                        createOrder(coinPairBean.getExchangeId(), clientOrderId, coinPairBean.getSymbolId(), side, ORDER_ENTRUST_TYPE.LIMIT.getEntrustType(), price, PRICE_TYPE.INPUT.getPriceType(), "", amount, "");
                    }
                });
                mViewList.add(limitedView);

                //请求最新价
                QuoteApi.RequestTicker(exchangeId, mHoldOrderBean.getSymbolId(), new SimpleResponseListener<TickerListBean>() {
                    @Override
                    public void onSuccess(TickerListBean response) {

                        List<TickerBean> datas = response.getData();
                        if (datas != null) {
                            for (TickerBean data : datas) {
                                String s = data.getS();
                                if (!TextUtils.isEmpty(s)) {
                                    if (s.equals(mHoldOrderBean.getSymbolId())) {
                                        lastPrice = data.getC();
                                        limitedPriceTV.setText(lastPrice);
                                    }
                                }
                            }
                        }
                    }
                });


                /** 市价 ***********/
                View marketView = LayoutInflater.from(mContext).inflate(R.layout.item_create_order_layout, null);
                setCanCloseTxt(marketView, mHoldOrderBean.getAvailable());
                ((TextView) marketView.findViewById(R.id.edit_price_unit)).setText(mHoldOrderBean.getUnit());
                marketView.findViewById(R.id.edit_price_rela).setVisibility(View.GONE);
                marketView.findViewById(R.id.priceMarket).setVisibility(View.VISIBLE);
                setShadow(marketView.findViewById(R.id.priceMarket));
                setShadow(marketView.findViewById(R.id.edit_amount_rela));
                final TextView marketAmountTV = marketView.findViewById(R.id.edit_amount);
                marketAmountTV.setText(String.valueOf(Math.abs(Double.valueOf(mHoldOrderBean.getAvailable()))));
                marketAmountTV.setFilters(new InputFilter[]{amountPointFilter});
                ((TextView) marketView.findViewById(R.id.btn_create_order)).setBackgroundColor(colorOfOpposit);
                ((TextView) marketView.findViewById(R.id.btn_create_order)).setText(R.string.string_option_market_close);
                marketView.findViewById(R.id.btn_create_order).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {


                        String amount = marketAmountTV.getText().toString().trim();
                        if (TextUtils.isEmpty(amount)) {
                            ToastUtils.showShort(mContext, mContext.getString(R.string.string_prompt_input_amount));
                            return;
                        }
                        if (Double.valueOf(amount) <= 0) {
                            ToastUtils.showShort(mContext, mContext.getString(R.string.string_prompt_input_amount));
                            return;
                        }
                        if (Double.valueOf(amount) > Math.abs(Double.valueOf(mHoldOrderBean.getAvailable()))) {
                            ToastUtils.showShort(mContext, mContext.getString(R.string.string_option_over_amount));
                            return;
                        }

                        if (coinPairBean != null) {
                            //最小交易数量
                            String minTradeQuantity = coinPairBean.getMinTradeQuantity();
                            //最小交易数量
                            if (!TextUtils.isEmpty(minTradeQuantity)) {
                                if (NumberUtils.sub(amount, minTradeQuantity) < 0) {
                                    ToastUtils.showShort(mContext, mContext.getString(R.string.string_min_trade_quantity, minTradeQuantity) + mContext.getString(R.string.string_option_unit));
                                    return;
                                }
                            }

                        } else {
                            return;
                        }
                        String clientOrderId = System.currentTimeMillis() + "";
                        boolean isBuyMode;
                        String side;
                        if (mHoldOrderBean.getIsLong().equals("1")) {
                            isBuyMode = false;
                            side = ORDER_SIDE.SELL_CLOSE.getOrderSide();
                        } else {
                            isBuyMode = true;
                            side = ORDER_SIDE.BUY_CLOSE.getOrderSide();

                        }
                        createOrder(coinPairBean.getExchangeId(), clientOrderId, coinPairBean.getSymbolId(), side, ORDER_ENTRUST_TYPE.LIMIT.getEntrustType(), "", PRICE_TYPE.MARKET_PRICE.getPriceType(), "", amount, "");
                    }
                });

                mViewList.add(marketView);

                /** 对手价 ***********/
                View rivalView = LayoutInflater.from(mContext).inflate(R.layout.item_create_order_layout, null);
                setCanCloseTxt(rivalView, mHoldOrderBean.getAvailable());
                ((TextView) rivalView.findViewById(R.id.edit_price_unit)).setText(mHoldOrderBean.getUnit());
                rivalView.findViewById(R.id.edit_price_rela).setVisibility(View.GONE);
                rivalView.findViewById(R.id.priceMarket).setVisibility(View.VISIBLE);
                TextView priceMarketTv2 = rivalView.findViewById(R.id.priceMarket);
                priceMarketTv2.setText(mContext.getString(R.string.string_rival_price));
                setShadow(priceMarketTv2);
                setShadow(rivalView.findViewById(R.id.edit_amount_rela));
                final TextView rivalAmountTV = rivalView.findViewById(R.id.edit_amount);
                rivalAmountTV.setText(String.valueOf(Math.abs(Double.valueOf(mHoldOrderBean.getAvailable()))));
                rivalAmountTV.setFilters(new InputFilter[]{amountPointFilter});
                ((TextView) rivalView.findViewById(R.id.btn_create_order)).setBackgroundColor(colorOfOpposit);
                ((TextView) rivalView.findViewById(R.id.btn_create_order)).setText(mContext.getString(R.string.string_rival_price) + mContext.getString(R.string.string_contract_close));
                rivalView.findViewById(R.id.btn_create_order).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {


                        String amount = rivalAmountTV.getText().toString().trim();
                        if (TextUtils.isEmpty(amount)) {
                            ToastUtils.showShort(mContext, mContext.getString(R.string.string_prompt_input_amount));
                            return;
                        }
                        if (Double.valueOf(amount) <= 0) {
                            ToastUtils.showShort(mContext, mContext.getString(R.string.string_prompt_input_amount));
                            return;
                        }
                        if (Double.valueOf(amount) > Math.abs(Double.valueOf(mHoldOrderBean.getAvailable()))) {
                            ToastUtils.showShort(mContext, mContext.getString(R.string.string_option_over_amount));
                            return;
                        }

                        if (coinPairBean != null) {
                            //最小交易数量
                            String minTradeQuantity = coinPairBean.getMinTradeQuantity();
                            //最小交易数量
                            if (!TextUtils.isEmpty(minTradeQuantity)) {
                                if (NumberUtils.sub(amount, minTradeQuantity) < 0) {
                                    ToastUtils.showShort(mContext, mContext.getString(R.string.string_min_trade_quantity, minTradeQuantity) + mContext.getString(R.string.string_option_unit));
                                    return;
                                }
                            }

                        } else {
                            return;
                        }
                        String clientOrderId = System.currentTimeMillis() + "";
                        boolean isBuyMode;
                        String side;
                        if (mHoldOrderBean.getIsLong().equals("1")) {
                            isBuyMode = false;
                            side = ORDER_SIDE.SELL_CLOSE.getOrderSide();
                        } else {
                            isBuyMode = true;
                            side = ORDER_SIDE.BUY_CLOSE.getOrderSide();

                        }
                        createOrder(coinPairBean.getExchangeId(), clientOrderId, coinPairBean.getSymbolId(), side, ORDER_ENTRUST_TYPE.LIMIT.getEntrustType(), "", PRICE_TYPE.OPPONENT.getPriceType(), "", amount, "");
                    }
                });

                mViewList.add(rivalView);

                /** 排队价 ***********/
                View queueView = LayoutInflater.from(mContext).inflate(R.layout.item_create_order_layout, null);
                setCanCloseTxt(queueView, mHoldOrderBean.getAvailable());
                ((TextView) queueView.findViewById(R.id.edit_price_unit)).setText(mHoldOrderBean.getUnit());
                queueView.findViewById(R.id.edit_price_rela).setVisibility(View.GONE);
                queueView.findViewById(R.id.priceMarket).setVisibility(View.VISIBLE);
                TextView priceMarketTv3 = queueView.findViewById(R.id.priceMarket);
                priceMarketTv3.setText(mContext.getString(R.string.string_queuing_price));
                setShadow(priceMarketTv3);
                setShadow(queueView.findViewById(R.id.edit_amount_rela));
                final TextView queueAmountTV = queueView.findViewById(R.id.edit_amount);
                queueAmountTV.setText(String.valueOf(Math.abs(Double.valueOf(mHoldOrderBean.getAvailable()))));
                queueAmountTV.setFilters(new InputFilter[]{amountPointFilter});
                ((TextView) queueView.findViewById(R.id.btn_create_order)).setBackgroundColor(colorOfOpposit);
                ((TextView) queueView.findViewById(R.id.btn_create_order)).setText(mContext.getString(R.string.string_queuing_price) + mContext.getString(R.string.string_contract_close));
                queueView.findViewById(R.id.btn_create_order).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {


                        String amount = queueAmountTV.getText().toString().trim();
                        if (TextUtils.isEmpty(amount)) {
                            ToastUtils.showShort(mContext, mContext.getString(R.string.string_prompt_input_amount));
                            return;
                        }
                        if (Double.valueOf(amount) <= 0) {
                            ToastUtils.showShort(mContext, mContext.getString(R.string.string_prompt_input_amount));
                            return;
                        }
                        if (Double.valueOf(amount) > Math.abs(Double.valueOf(mHoldOrderBean.getAvailable()))) {
                            ToastUtils.showShort(mContext, mContext.getString(R.string.string_option_over_amount));
                            return;
                        }

                        if (coinPairBean != null) {
                            //最小交易数量
                            String minTradeQuantity = coinPairBean.getMinTradeQuantity();
                            //最小交易数量
                            if (!TextUtils.isEmpty(minTradeQuantity)) {
                                if (NumberUtils.sub(amount, minTradeQuantity) < 0) {
                                    ToastUtils.showShort(mContext, mContext.getString(R.string.string_min_trade_quantity, minTradeQuantity) + mContext.getString(R.string.string_option_unit));
                                    return;
                                }
                            }

                        } else {
                            return;
                        }
                        String clientOrderId = System.currentTimeMillis() + "";
                        boolean isBuyMode;
                        String side;
                        if (mHoldOrderBean.getIsLong().equals("1")) {
                            isBuyMode = false;
                            side = ORDER_SIDE.SELL_CLOSE.getOrderSide();
                        } else {
                            isBuyMode = true;
                            side = ORDER_SIDE.BUY_CLOSE.getOrderSide();

                        }
                        createOrder(coinPairBean.getExchangeId(), clientOrderId, coinPairBean.getSymbolId(), side, ORDER_ENTRUST_TYPE.LIMIT.getEntrustType(), "", PRICE_TYPE.QUEUE.getPriceType(), "", amount, "");
                    }
                });

                mViewList.add(queueView);

                /** 超价 ***********/
                View overView = LayoutInflater.from(mContext).inflate(R.layout.item_create_order_layout, null);
                setCanCloseTxt(overView, mHoldOrderBean.getAvailable());
                ((TextView) overView.findViewById(R.id.edit_price_unit)).setText(mHoldOrderBean.getUnit());
                overView.findViewById(R.id.edit_price_rela).setVisibility(View.GONE);
                overView.findViewById(R.id.priceMarket).setVisibility(View.VISIBLE);
                TextView priceMarketTv4 = overView.findViewById(R.id.priceMarket);
                priceMarketTv4.setText(mContext.getString(R.string.string_over_price));
                setShadow(priceMarketTv4);
                setShadow(overView.findViewById(R.id.edit_amount_rela));
                final TextView overAmountTV = overView.findViewById(R.id.edit_amount);
                overAmountTV.setText(String.valueOf(Math.abs(Double.valueOf(mHoldOrderBean.getAvailable()))));
                overAmountTV.setFilters(new InputFilter[]{amountPointFilter});
                ((TextView) overView.findViewById(R.id.btn_create_order)).setBackgroundColor(colorOfOpposit);
                ((TextView) overView.findViewById(R.id.btn_create_order)).setText(mContext.getString(R.string.string_over_price) + mContext.getString(R.string.string_contract_close));
                overView.findViewById(R.id.btn_create_order).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {


                        String amount = overAmountTV.getText().toString().trim();
                        if (TextUtils.isEmpty(amount)) {
                            ToastUtils.showShort(mContext, mContext.getString(R.string.string_prompt_input_amount));
                            return;
                        }
                        if (Double.valueOf(amount) <= 0) {
                            ToastUtils.showShort(mContext, mContext.getString(R.string.string_prompt_input_amount));
                            return;
                        }
                        if (Double.valueOf(amount) > Math.abs(Double.valueOf(mHoldOrderBean.getAvailable()))) {
                            ToastUtils.showShort(mContext, mContext.getString(R.string.string_option_over_amount));
                            return;
                        }

                        if (coinPairBean != null) {
                            //最小交易数量
                            String minTradeQuantity = coinPairBean.getMinTradeQuantity();
                            //最小交易数量
                            if (!TextUtils.isEmpty(minTradeQuantity)) {
                                if (NumberUtils.sub(amount, minTradeQuantity) < 0) {
                                    ToastUtils.showShort(mContext, mContext.getString(R.string.string_min_trade_quantity, minTradeQuantity) + mContext.getString(R.string.string_option_unit));
                                    return;
                                }
                            }

                        } else {
                            return;
                        }
                        String clientOrderId = System.currentTimeMillis() + "";
                        boolean isBuyMode;
                        String side;
                        if (mHoldOrderBean.getIsLong().equals("1")) {
                            isBuyMode = false;
                            side = ORDER_SIDE.SELL_CLOSE.getOrderSide();
                        } else {
                            isBuyMode = true;
                            side = ORDER_SIDE.BUY_CLOSE.getOrderSide();

                        }
                        createOrder(coinPairBean.getExchangeId(), clientOrderId, coinPairBean.getSymbolId(), side, ORDER_ENTRUST_TYPE.LIMIT.getEntrustType(), "", PRICE_TYPE.OVER.getPriceType(), "", amount, "");
                    }
                });

                mViewList.add(overView);

                MultiplePagerAdapter symbolsAdapter = new MultiplePagerAdapter(mViewList);
                viewPager.setAdapter(symbolsAdapter);
                tab.setupWithViewPager(viewPager);
                tab.getTabAt(0).setText(mContext.getString(R.string.string_limited_price));
                tab.getTabAt(1).setText(mContext.getString(R.string.string_market_price));
                tab.getTabAt(2).setText(mContext.getString(R.string.string_rival_price));
                tab.getTabAt(3).setText(mContext.getString(R.string.string_queuing_price));
                tab.getTabAt(4).setText(mContext.getString(R.string.string_over_price));
                tab.setTabMode(TabLayout.MODE_SCROLLABLE);
                tab.setTabGravity(TabLayout.GRAVITY_CENTER);
                CommonUtil.setUpIndicatorWidthByReflex2(tab, 10, 10);


                viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
                    @Override
                    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

                    }

                    @Override
                    public void onPageSelected(int position) {

                    }

                    @Override
                    public void onPageScrollStateChanged(int state) {

                    }
                });
            }
        } catch (Exception e) {
        }

    }

    private void setCanCloseTxt(View parentView, String available) {
        TextView canCloseQutity = parentView.findViewById(R.id.can_close_quantity);
        canCloseQutity.setText(mContext.getResources().getString(R.string.string_can_close) + ": " + available + mContext.getResources().getString(R.string.string_futures_unit));
    }

    public class MultiplePagerAdapter extends PagerAdapter {

        private List<View> mViewList = new ArrayList<>();

        public MultiplePagerAdapter(List<View> views) {
            mViewList = views;
        }

        @Override
        public int getCount() {
            return mViewList.size();
        }

        @Override
        public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
            return view == object;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            View view = (View) object;
            container.removeView(view);
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            View symbolItem = null;
            if (position < mViewList.size())
                symbolItem = mViewList.get(position);

            container.addView(symbolItem);
            return symbolItem;// 返回填充的View对象
        }
    }


    public void createOrder(String exchangeId, String clientOrderId, String symbolId, String side, String type, String price, String priceType, String triggerPrice, String quantity, String leverage) {

        OrderCreateParams orderCreateParams = new OrderCreateParams();
        orderCreateParams.exchangeId = exchangeId;
        orderCreateParams.clientOrderId = clientOrderId;
        orderCreateParams.symbolId = symbolId;
        orderCreateParams.side = side;
        orderCreateParams.type = type;
        orderCreateParams.price = price;
        orderCreateParams.priceType = priceType;
        orderCreateParams.triggerPrice = triggerPrice;
        orderCreateParams.quantity = quantity;
        orderCreateParams.leverage = leverage;


        FuturesApi.orderCreate(orderCreateParams, new SimpleResponseListener<FuturesOrderResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                if (mObserver != null)
                    mObserver.showLoading();
            }

            @Override
            public void onFinish() {
                super.onFinish();
                if (mObserver != null)
                    mObserver.hideLoading();
            }

            @Override
            public void onSuccess(FuturesOrderResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    ToastUtils.showShort(mContext, mContext.getString(R.string.string_create_order_success));
                    if (bottomDialog != null && bottomDialog.isShowing()) {
                        bottomDialog.dismiss();
                    }
                    if (mDialogObserver != null) {
                        mDialogObserver.onReqHttpSuccess();
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(mContext, mContext.getString(R.string.string_create_order_failed));
            }
        });
    }

    private void setShadow(View view) {
        ShadowDrawable.setShadowDrawable(view,
//                getResources().getColor(R.color.white),
                PixelUtils.dp2px(2),
                SkinColorUtil.getDark10(mContext),
                PixelUtils.dp2px(2),
                0,
                PixelUtils.dp2px(1));
    }
}
