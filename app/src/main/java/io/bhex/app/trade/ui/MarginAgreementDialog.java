package io.bhex.app.trade.ui;


import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.CheckedTextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bhex.kline.widget.util.PixelUtils;

import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;
import io.bhex.app.R;
import io.bhex.baselib.core.CApplication;

/**
 *
 */
public class MarginAgreementDialog extends DialogFragment {


    private CheckedTextView check_agreement;
    private Button btnSure;

    public MarginAgreementDialog() {
        // Required empty public constructor
    }

    OnClickListenter onClickListenter;

    public View mRootView;


    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if(mRootView==null){
            mRootView = inflater.inflate(getLayout(), container, false);
        }
        return mRootView;
    }

    public int getLayout() {
        return R.layout.fragment_margin_tips;
    }

    private boolean isCheck;

    @Override
    public void onStart() {
        super.onStart();

        Window window = getDialog().getWindow();
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.getAttributes().windowAnimations = R.style.BottomDialog_Animation;



        DisplayMetrics dm = new DisplayMetrics();
        getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);

        WindowManager.LayoutParams params = window.getAttributes();
        params.gravity = Gravity.BOTTOM;

        params.width = dm.widthPixels;
        params.height = PixelUtils.dp2px(CApplication.getInstance(),531);

        window.setAttributes(params);

    }
    static MarginAgreementDialog fragment;
    public static void showDialog(FragmentManager fm, String tag, OnClickListenter onClickListenter, Boolean isCheck){
        if (fragment==null) {
            fragment = new MarginAgreementDialog();
            fragment.setOnClickListenter(onClickListenter);
            fragment.isCheck = isCheck;
            fragment.show(fm, tag);
        }
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        check_agreement = mRootView.findViewById(R.id.check_agreement);
        btnSure = mRootView.findViewById(R.id.btn_confirm);
        check_agreement.setChecked(isCheck);
        addEvent();
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        fragment =null;
    }
    /**
     * 添加事件
     */
    private void addEvent() {
        check_agreement.setOnClickListener(v -> {
            check_agreement.setChecked(!check_agreement.isChecked());
            btnSure.setEnabled(check_agreement.isChecked());
        });

        btnSure.setOnClickListener(v -> {
            dismiss();
            if(onClickListenter!=null){
                onClickListenter.onCheckClickListener(null,check_agreement.isChecked());
            }
        });
        mRootView.findViewById(R.id.btn_cancel).setOnClickListener(v -> {
            dismiss();
            if(onClickListenter!=null){
                onClickListenter.onCancelClickListener();
            }
        });
    }


    public OnClickListenter getGlobalOnClickListenter() {
        return onClickListenter;
    }

    public void setOnClickListenter(OnClickListenter onClickListenter) {
        this.onClickListenter = onClickListenter;
    }

    public interface OnClickListenter{
        void onCheckClickListener(View view, boolean isCheck);
        void onCancelClickListener();
    }

}
