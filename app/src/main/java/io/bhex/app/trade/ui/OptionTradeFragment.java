/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CoinTradeFragment.java
 *   @Date: 1/8/19 8:46 PM
 *   @Author: chenjun
 *   @Description:期权交易
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.trade.ui;

import android.content.DialogInterface;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.google.android.material.tabs.TabLayout;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

import io.bhex.app.R;
import io.bhex.app.account.ui.CurrentOptionEntrustOrderFragment;
import io.bhex.app.account.ui.CurrentOptionHoldOrderFragment;
import io.bhex.app.account.ui.OptionHistoryOrderFragment;
import io.bhex.app.market.ui.OptionCoinDialogFragment;
import io.bhex.app.trade.presenter.OptionTradeFragmentPresenter;
import io.bhex.app.utils.AnimalUtils;
import io.bhex.app.utils.CoinUtils;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.app.view.alertview.AlertView;
import io.bhex.app.view.alertview.OnItemClickListener;
import io.bhex.sdk.account.EventLogin;
import io.bhex.sdk.account.LoginResultCallback;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.data_manager.MMKVManager;
import io.bhex.sdk.data_manager.RateDataManager;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.enums.COIN_TYPE;
import io.bhex.sdk.quote.QuoteApi;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.EtfPriceBean;
import io.bhex.sdk.quote.bean.OptionSymbolStatusBean;
import io.bhex.sdk.quote.bean.TickerBean;
import io.bhex.sdk.trade.OptionApi;
import io.bhex.sdk.trade.bean.IndicesBean;
import io.bhex.sdk.trade.bean.OptionHoldOrderResponse;
import io.bhex.sdk.trade.bean.OrderBean;

public class OptionTradeFragment extends BaseTradeFragment<OptionTradeFragmentPresenter, OptionTradeFragmentPresenter.OptionTradeFragmentUI> implements OptionTradeFragmentPresenter.OptionTradeFragmentUI {
    private static final String TAG = "OptionTradeFragment";
    private View mOptionTitlelayout;
    private Timer timer;
    private TimerTask task;
    private TimerTask taskSettle;
    private static final int nPeriod = 1 * 1000;
    private TextView option_exercise_point;
    private TextView option_exercise_point_title;
    private TextView option_delivery_time;
    private Handler mHandler;
    private static final int TIMER_MESSAGE = 0X1;
    private static final int TIMER_SETTLE_MESSAGE = 0X2;
    private LinearLayout get_option_amount_ll;
    private LinearLayout get_earnest_money_ll;
    private LinearLayout get_option_money_ll;
    private TextView get_option_legal_money;
    private TextView get_option_money_title;
    private TextView get_earnest_money;
    private TextView get_earnest_money_legal_money;
    private TextView get_option_amount;
    private TextView get_option_money;
    private TextView balance_available_title;
    private ArrayList<Pair<String, Fragment>> items;
    private OptionSymbolStatusBean.OptionSymbolStatus mOptionSymbolStatus;
    private OptionHoldOrderResponse.OptionHoldOrderBean mOptionHoldOrderBean;


    private CurrentOptionEntrustOrderFragment.OptionOpenOrdersAdapter openOrdersAdapter;
    private OptionHistoryOrderFragment.OptionHistoryOrderAdapter historyEntrustAdapter;
    private CurrentOptionHoldOrderFragment.OptionHoldOrdersAdapter optionHoldOrdersAdapter;

    private List<OrderBean> currentOrderDatas = new ArrayList<>();
    private List<OrderBean> historyOrderDatas = new ArrayList<>();
    private List<OptionHoldOrderResponse.OptionHoldOrderBean> mHoldOptionOrders = new ArrayList<>();
    private boolean isShowCumulativeVolume = false;
    private IndicesBean currentIndicesBean;


    @Override
    protected OptionTradeFragmentPresenter.OptionTradeFragmentUI getUI() {
        return this;
    }

    @Override
    protected OptionTradeFragmentPresenter createPresenter() {
        return new OptionTradeFragmentPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_trade_layout, null, false);
    }

    @Override
    protected void initInstanceView() {
        if (headerView != null)
            mOptionTitlelayout = headerView.findViewById(R.id.options_trade_title);
        if (mOptionTitlelayout != null)
            mOptionTitlelayout.setVisibility(View.VISIBLE);
        topBar.setTitleOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                KeyBoardUtil.closeKeybord(editAmount,getActivity());
                FragmentManager childFragmentManager = getFragmentManager();
                OptionCoinDialogFragment dialogFragment = new OptionCoinDialogFragment(ContractTradeFragment.TAB_OPTION, coinPairBean, new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialog) {
                        AnimalUtils.rotateyAnimRun(topBar.getTitleIcon(), 180.0f, 0.0f);
                        getPresenter().getTicker();
                    }
                });
                dialogFragment.show(childFragmentManager, "dialog");
                QuoteApi.UnSubTickers();
                AnimalUtils.rotateyAnimRun(topBar.getTitleIcon(), 0.0f, 180.0f);

            }
        });

        isShowCumulativeVolume = MMKVManager.getInstance().loadBookQuantityShowMode(COIN_TYPE.COIN_TYPE_OPTION.getCoinType());

        option_exercise_point = headerView.findViewById(R.id.option_exercise_point);
        option_exercise_point_title = headerView.findViewById(R.id.option_exercise_point_title);
        option_delivery_time = headerView.findViewById(R.id.option_delivery_time);
        headerView.findViewById(R.id.trade_money_ll).setVisibility(View.GONE);
        headerView.findViewById(R.id.option_money_ll).setVisibility(View.VISIBLE);

        get_option_amount_ll = headerView.findViewById(R.id.get_option_amount_ll);
        get_earnest_money_ll = headerView.findViewById(R.id.get_earnest_money_ll);
        get_earnest_money = headerView.findViewById(R.id.get_earnest_money);
        get_earnest_money_legal_money = headerView.findViewById(R.id.get_earnest_money_legal_money);

        get_option_money_ll = headerView.findViewById(R.id.get_option_money_ll);
        get_option_amount = headerView.findViewById(R.id.get_option_amount);
        get_option_legal_money = headerView.findViewById(R.id.get_option_legal_money);
        get_option_money_title = headerView.findViewById(R.id.get_option_money_title);
        get_option_money = headerView.findViewById(R.id.get_option_money);
        balance_available_title = headerView.findViewById(R.id.balance_available_title);
        initTabs();

        openOrdersAdapter = new CurrentOptionEntrustOrderFragment.OptionOpenOrdersAdapter(getActivity(), currentOrderDatas);
        openOrdersAdapter.isFirstOnly(false);
        //openOrdersAdapter.addHeaderView(headerView);


        LayoutInflater layoutInflater = LayoutInflater.from(getActivity());
        View emptyView = layoutInflater.inflate(R.layout.empty_layout, refreshLayout, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
//        layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
        emptyView.setLayoutParams(layoutParams);
        openOrdersAdapter.setHeaderFooterEmpty(true, true);
        openOrdersAdapter.setEmptyView(emptyView);
        openOrdersAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                if (view.getId() == R.id.revoke_order) {
                    OrderBean itemModel = (OrderBean) adapter.getData().get(position);
                    getPresenter().cancelOrder(itemModel.getOrderId());
                }
            }
        });
        recyclerView.setAdapter(openOrdersAdapter);


        historyEntrustAdapter = new OptionHistoryOrderFragment.OptionHistoryOrderAdapter(getActivity(), historyOrderDatas);
        historyEntrustAdapter.isFirstOnly(false);
        //historyEntrustAdapter.addHeaderView(headerView);
        View emptyView2 = layoutInflater.inflate(R.layout.empty_layout, refreshLayout, false);
        ViewGroup.LayoutParams layoutParams2 = emptyView2.getLayoutParams();
        layoutParams2.height = PixelUtils.dp2px(200);
        emptyView2.setLayoutParams(layoutParams2);
        historyEntrustAdapter.setHeaderFooterEmpty(true, true);
        historyEntrustAdapter.setEmptyView(emptyView2);
        historyEntrustAdapter.setOnLoadMoreListener(this, recyclerView);
        historyEntrustAdapter.setEnableLoadMore(true);

        optionHoldOrdersAdapter = new CurrentOptionHoldOrderFragment.OptionHoldOrdersAdapter(getActivity(), mHoldOptionOrders);
        optionHoldOrdersAdapter.isFirstOnly(false);
        //historyEntrustAdapter.addHeaderView(headerView);
        View emptyView3 = layoutInflater.inflate(R.layout.empty_layout, refreshLayout, false);
        ViewGroup.LayoutParams layoutParams3 = emptyView3.getLayoutParams();
        layoutParams3.height = PixelUtils.dp2px(200);
        emptyView3.setLayoutParams(layoutParams3);
        optionHoldOrdersAdapter.setHeaderFooterEmpty(true, true);
        optionHoldOrdersAdapter.setEmptyView(emptyView3);


        balanceAvailableAboutTx.setVisibility(View.GONE);
        btnTransfer.setVisibility(View.VISIBLE);
    }


    private void initTabs() {
        items = new ArrayList<>();
        items.add(new Pair<String, Fragment>(getString(R.string.string_current_entrust), null));
        items.add(new Pair<String, Fragment>(getString(R.string.string_current_hold), null));
        items.add(new Pair<String, Fragment>(getString(R.string.string_history_entrust), null));

        OrderAdapter adapter = new OrderAdapter(getChildFragmentManager());
        ViewPager viewPager = headerView.findViewById(R.id.clViewPager);
        viewPager.setAdapter(adapter);
        tabLayout.setupWithViewPager(viewPager);
//        tab.setTabTextColors(getResources().getColor(R.color.color_white),getResources().getColor(R.color.color_black));
        tabLayout.setTabMode(TabLayout.MODE_SCROLLABLE);
        tabLayout.setTabGravity(TabLayout.GRAVITY_CENTER);
        /*orderTab.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                int position = tab.getPosition();
                if(position == 0) {recyclerView.setAdapter(openOrdersAdapter);
                }
                else if(position == 1) {
                    recyclerView.setAdapter(optionHoldOrdersAdapter);
                }
                else if(position == 2) {
                    recyclerView.setAdapter(historyEntrustAdapter);
                }

                headerView.requestFocus();
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {

            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });*/
        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                if (position == 0) {
                    /*optionHoldOrdersAdapter.removeAllHeaderView();
                    historyEntrustAdapter.removeAllHeaderView();
                    openOrdersAdapter.addHeaderView(headerView);
                    */
                    recyclerView.setAdapter(openOrdersAdapter);
                    getPresenter().getCurrentEntrustOrders();
                    showOrderOperateViews(true);
                } else if (position == 1) {
                    /*openOrdersAdapter.removeAllHeaderView();
                    historyEntrustAdapter.removeAllHeaderView();
                    optionHoldOrdersAdapter.addHeaderView(headerView);
                    */
                    recyclerView.setAdapter(optionHoldOrdersAdapter);
                    getPresenter().getOptionHoldOrders();
                    showOrderOperateViews(false);
                } else if (position == 2) {
                    /*openOrdersAdapter.removeAllHeaderView();
                    optionHoldOrdersAdapter.removeAllHeaderView();
                    historyEntrustAdapter.addHeaderView(headerView);
                    */
                    recyclerView.setAdapter(historyEntrustAdapter);
                    getPresenter().getHistoryEntrustOrders(false);
                    showOrderOperateViews(false);
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        CommonUtil.setUpIndicatorWidthByReflex(tabLayout, 15, 15);
    }

    private class OrderAdapter extends FragmentPagerAdapter {

        public OrderAdapter(FragmentManager fm) {
            super(fm);
        }

        @Override
        public Fragment getItem(int position) {
            return items.get(position).second;
        }

        @Override
        public int getCount() {
            return items.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return items.get(position).first;
        }


    }

    @Override
    public void showIndices(EtfPriceBean etfPriceInfo, IndicesBean indicesBean) {
        super.showIndices(etfPriceInfo, indicesBean);
        currentIndicesBean = indicesBean;
        if (coinPairBean != null && coinPairBean.baseTokenOption != null && indicesBean!=null) {
            String indexData = NumberUtils.roundFormatDown(indicesBean.getIndex(), AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(coinPairBean.getSymbolId() + quoteToken));
            option_exercise_point.setText(indexData);
        }
    }

    /**
     * 更新期权指数信息
     */
    private void updateOptionIndexInfo() {
        if (coinPairBean != null && coinPairBean.baseTokenOption != null) {
            if (mOptionSymbolStatus != null && mOptionSymbolStatus.settleStatus != null && (mOptionSymbolStatus.settleStatus.equalsIgnoreCase("SETTLE_DOING") || mOptionSymbolStatus.settleStatus.equalsIgnoreCase("SETTLE_DONE"))) {

                option_exercise_point_title.setText(getString(R.string.string_option_delivery_price));
                if (mOptionSymbolStatus.settleDetail != null && !TextUtils.isEmpty(mOptionSymbolStatus.settleDetail.settlementPrice)) {
                    String price = NumberUtils.roundFormatDown(mOptionSymbolStatus.settleDetail.settlementPrice, digitPrice);
                    option_exercise_point.setText(price);
                } else
                    option_exercise_point.setText("--");
            } else {
                option_exercise_point_title.setText(getString(R.string.string_exercise_point));
                if (currentIndicesBean != null) {
                    String index = currentIndicesBean.getIndex();
                    String indexData = NumberUtils.roundFormatDown(index, AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(coinPairBean.getSymbolId() + quoteToken));
                    option_exercise_point.setText(indexData);
                }
            }
        } else{
            option_exercise_point.setText("--");
        }
    }

    private void startTimer() {
        timer = new Timer();
        task = new TimerTask() {
            @Override
            public void run() {
                mHandler.sendEmptyMessage(TIMER_MESSAGE);
            }
        };
        timer.schedule(task, 50, nPeriod);


        taskSettle = new TimerTask() {
            @Override
            public void run() {
                long time = Long.valueOf(coinPairBean.baseTokenOption.settlementDate) - System.currentTimeMillis();
                if (mOptionSymbolStatus != null && mOptionSymbolStatus.settleStatus != null && mOptionSymbolStatus.settleStatus.equalsIgnoreCase("SETTLE_DONE")) {

                } else if (time <= 0) {
                    mHandler.sendEmptyMessage(TIMER_SETTLE_MESSAGE);
                }
            }
        };
        timer.schedule(taskSettle, 50, nPeriod * 6);
    }

    private void stopTimer() {

        if (task != null) {
            task.cancel();
        }
        if (taskSettle != null) {
            taskSettle.cancel();
        }

        if (timer != null) {
            timer.purge();
            timer.cancel();
        }
    }

    private void stopSettleTimer() {
        if (taskSettle != null) {
            taskSettle.cancel();
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (coinPairBean == null){
            CoinPairBean tradeCoin = CoinUtils.getOptionTradeCoin();
            if (tradeCoin != null) {
                coinPairBean = tradeCoin;
            } else {
                coinPairBean = AppConfigManager.GetInstance().getDefaultOptionTradeCoinPair();
            }
        }
        mHandler = new Handler() {
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                switch (msg.what) {
                    case TIMER_MESSAGE:
                        try {
                            updateOptionIndexInfo();

                            if (coinPairBean.baseTokenOption != null) {
                                long time = Long.valueOf(coinPairBean.baseTokenOption.settlementDate) - System.currentTimeMillis();
                                if (time <= 0) {
                                    if (mOptionSymbolStatus != null && mOptionSymbolStatus.settleStatus != null && mOptionSymbolStatus.settleStatus.equalsIgnoreCase("SETTLE_DONE"))
                                        option_delivery_time.setText(getString(R.string.string_option_over_delivery));
                                    else
                                        option_delivery_time.setText(getString(R.string.string_option_over_deliverying));
                                } else {
                                    int day = Math.round(time / 1000 / 60 / 60 / 24);
                                    // 时
                                    int hour = Math.round(time / 1000 / 60 / 60 % 24);
                                    // 分
                                    int minute = Math.round(time / 1000 / 60 % 60);
                                    // 秒
                                    int second = Math.round(time / 1000 % 60);

                                    if (day > 0)
                                        option_delivery_time.setText(day + getString(R.string.string_option_d));
                                    else
                                        option_delivery_time.setText(String.format(getString(R.string.string_option_h_m_s), hour, minute, second));
                                }
                            }

                        } catch (Exception e) {

                        }

                        break;
                    case TIMER_SETTLE_MESSAGE:
                        getSettleStatus();
                        break;
                }
            }
        };

        EventBus.getDefault().register(this);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public synchronized void onMessageEvent(CoinPairBean coinPairBeanParam) {
        if (coinPairBeanParam == null || coinPairBeanParam.baseTokenOption == null || coinPairBeanParam.baseTokenFutures != null)
            return;
        coinPairBean = coinPairBeanParam;
        String oldSymbol = symbol;
        boolean needSwitchTradeTab = coinPairBeanParam.isNeedSwitchTradeTab();
//        ToastUtils.showShort("Message "+needSwitchTradeTab);
        if (!needSwitchTradeTab) {
            if (!isFirst) {
                return;
            }
        }
        isFirst = false;

        if (isBuyMode != coinPairBeanParam.isBuyMode()) {
            if (coinPairBeanParam.isBuyMode()) {
                AnimalUtils.transAnimRun(buySellTabBg, buySellTabBg.getWidth(), 0);
            } else {
                AnimalUtils.transAnimRun(buySellTabBg, 0, buySellTabBg.getWidth());
            }
        }
        loadDefaultConfig(coinPairBeanParam);
        if (!oldSymbol.equals(coinPairBeanParam.getSymbolId())) {
            switchBuySellTab(isBuyMode);
            updateUnit();
            getPresenter().resetAllData(coinPairBeanParam);
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public synchronized void onMessageEvent(EventLogin eventLogin) {
        switchBuySellTab(isBuyMode);
    }

    @Override
    public void onResume() {
        super.onResume();

        if (!UserInfo.isLogin()) {
            if (!UserInfo.isLogin()) {
                if (currentOrderDatas.size() > 0) {
                    currentOrderDatas.clear();
                    if (openOrdersAdapter != null) {
                        openOrdersAdapter.setNewData(currentOrderDatas);
                    }
                }
                if (historyOrderDatas.size() > 0) {
                    historyOrderDatas.clear();
                    if (historyEntrustAdapter != null) {
                        historyEntrustAdapter.setNewData(historyOrderDatas);
                    }
                }
                if (mHoldOptionOrders.size() > 0) {
                    mHoldOptionOrders.clear();
                    if (optionHoldOrdersAdapter != null) {
                        optionHoldOrdersAdapter.setNewData(mHoldOptionOrders);
                    }
                }

            }

        }

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        btnTransfer.setOnClickListener(this);
        bookTitleAmount.setOnClickListener(this);

    }

    @Override
    public void onPause() {
        super.onPause();

    }

    @Override
    protected void onVisibleChanged(boolean visible) {
        super.onVisibleChanged(visible);
        if (isVisible()) {
            if (coinPairBean != null) {
                String symbolId = coinPairBean.getSymbolId();
                CoinPairBean newSymbol = AppConfigManager.GetInstance().getSymbolInfoById(symbolId);
                if (newSymbol != null) {
                    coinPairBean = newSymbol;
                    if (!TextUtils.isEmpty(newSymbol.getSymbolName())) {
                        topBar.setTitle(newSymbol.getSymbolName());
                    }
                }
            }
            isShowCumulativeVolume = MMKVManager.getInstance().loadBookQuantityShowMode(COIN_TYPE.COIN_TYPE_OPTION.getCoinType());
            setBookTitleAmount();

            if (!UserInfo.isLogin()) {
                balanceAvailableTx.setText(getString(R.string.string_placeholder));
                currentOrderDatas.clear();
                if (openOrdersAdapter != null) {
                    openOrdersAdapter.notifyDataSetChanged();
                }
                historyOrderDatas.clear();
                if (historyEntrustAdapter != null) {
                    historyEntrustAdapter.notifyDataSetChanged();
                }
                mHoldOptionOrders.clear();
                if (optionHoldOrdersAdapter != null) {
                    optionHoldOrdersAdapter.setNewData(mHoldOptionOrders);
                }
                return;
            }
        }

    }

    @Override
    public void onClick(View v) {
        super.onClick(v);
        switch (v.getId()) {
           /* case R.id.order_actionsheet:
                showOrderActionSheet();
                break;*/
            case R.id.btnTransfer:
                UserInfo.LoginAndGoin(getActivity(), new LoginResultCallback() {
                    @Override
                    public void onLoginSucceed() {
                        super.onLoginSucceed();
                        if (!TextUtils.isEmpty(quoteToken)) {
                            IntentUtils.goAssetTransfer(getActivity(),quoteToken);
                        }
                    }
                });
                break;
            case R.id.title_amount:

                showSelectBookQuantityTypeAlert();
                break;
        }
    }

    private void showSelectBookQuantityTypeAlert() {
        String[] quantityTypeArray = new String[]{getString(R.string.string_cumulative_quantity_format, baseTokenName),getString(R.string.string_amount_format, baseTokenName)};
        AlertView alertView = new AlertView(null, null, getString(R.string.string_cancel), new String[] {isShowCumulativeVolume?getString(R.string.string_cumulative_quantity_format, baseTokenName):getString(R.string.string_amount_format, baseTokenName)},quantityTypeArray, getActivity(), AlertView.Style.ActionSheet, new OnItemClickListener() {
            @Override
            public void onItemClick(Object o, int position) {
                if (position == 0) {
                    isShowCumulativeVolume = true;
                    setBookTitleAmount();
                    getPresenter().changeBookQuantityShowMode(isShowCumulativeVolume);
                    MMKVManager.getInstance().saveBookQuantityShowMode(isShowCumulativeVolume, COIN_TYPE.COIN_TYPE_OPTION.getCoinType());
                }else if(position == 1){
                    isShowCumulativeVolume = false;
                    setBookTitleAmount();
                    getPresenter().changeBookQuantityShowMode(isShowCumulativeVolume);
                    MMKVManager.getInstance().saveBookQuantityShowMode(isShowCumulativeVolume,COIN_TYPE.COIN_TYPE_OPTION.getCoinType());
                }
            }
        });
        alertView.show();
    }

    private void setBookTitleAmount() {
        bookTitleAmount.setText(getString(isShowCumulativeVolume ? R.string.string_cumulative_quantity_format : R.string.string_amount_format, baseTokenName));
    }

    @Override
    public boolean isShowCumulativeVolume() {
        return isShowCumulativeVolume;
    }

    @Override
    protected void loadDefaultConfig(CoinPairBean coinPairBean) {
        super.loadDefaultConfig(coinPairBean);

        baseTokenName = getString(R.string.string_option_unit);
        mOptionSymbolStatus = null;
        mOptionHoldOrderBean = null;

        if (coinPairBean.baseTokenOption != null && !TextUtils.isEmpty(coinPairBean.getSymbolName()) && !TextUtils.isEmpty(coinPairBean.baseTokenOption.isCall)) {
            String call = "";
            topBar.setLeftImgVisible(View.GONE);
            if (KlineUtils.isOptionCall(coinPairBean.baseTokenOption.isCall)) {
                call = getString(R.string.string_option_call);
                topBar.setLeftTextAndBackGround(call, R.style.Mini_Green,SkinColorUtil.getGreen(getActivity()), SkinColorUtil.getGreenRectBg(getActivity()));
            } else {
                call = getString(R.string.string_option_put);
                topBar.setLeftTextAndBackGround(call, R.style.Mini_Red,SkinColorUtil.getGreen(getActivity()), SkinColorUtil.getRedRectBg(getActivity()));
            }
            topBar.setTitle(coinPairBean.getSymbolName());
            topBar.setTitleAppearance(R.style.BodyL_Dark_Bold);
            //topBar.setTitleLength(180);
        }
        editAmountUnit.setText(baseTokenName);
        ((TextView) headerView.findViewById(R.id.option_exercise_point_title)).setText(getString(R.string.string_exercise_point) + "(" + quoteTokenName + ")");
        ((TextView) headerView.findViewById(R.id.option_exercise_price_title)).setText(getString(R.string.string_exercise_price) + "(" + quoteTokenName + ")");

        if (coinPairBean.baseTokenOption != null) {
            ((TextView) headerView.findViewById(R.id.option_exercise_price)).setText(coinPairBean.baseTokenOption.strikePrice);
        }
        stopTimer();
        startTimer();
    }

    @Override
    protected void updateTradeAmountOfMoney() {

        String amount = editAmount.getText().toString();
        String price = priceLimitedView.getPrice();
        if (isBuyMode) {
            get_option_amount.setText(amount + getString(R.string.string_option_unit));
        } else {
            if (TextUtils.isEmpty(price) || TextUtils.isEmpty(amount)) {
                get_earnest_money.setText(getString(R.string.string_placeholder));
                get_earnest_money_legal_money.setText(getString(R.string.string_placeholder));
            } else {
                try {
                    String baseValue = baseTokenAsset;
                    if (TextUtils.isEmpty(baseValue))
                        baseValue = "0";

                    double result = NumberUtils.sub(amount, baseValue);
                    if (result < 0d) {
                        result = 0d;
                    }

                    String vaule = String.valueOf(NumberUtils.mul(String.valueOf(result), coinPairBean.baseTokenOption.maxPayOff));
                    get_earnest_money.setText(NumberUtils.roundFormatDown(vaule, digitPrice) + quoteTokenName);
                    String legalMoney = RateDataManager.CurRatePrice(quoteToken, vaule);
                    legalMoney = RateDataManager.getShowLegalMoney(legalMoney, AppData.DIGIT_LEGAL_MONEY);
                    get_earnest_money_legal_money.setText("≈" + legalMoney);
                } catch (Exception e) {

                }
            }
        }
        if (!isLimitedPrice) {

        } else {
            if (TextUtils.isEmpty(price) || TextUtils.isEmpty(amount)) {
                get_option_money.setText(getString(R.string.string_placeholder));
                get_option_legal_money.setText(getString(R.string.string_placeholder));
            } else {
                double result = 0d;
                if (isBuyMode) {
                    result = NumberUtils.mul(NumberUtils.mul(price, amount), NumberUtils.add("1", getPresenter().OptionFee()));
                } else
                    result = NumberUtils.mul(NumberUtils.mul(price, amount), NumberUtils.sub("1", getPresenter().OptionFee()));

                String value = NumberUtils.roundFormatDown(String.valueOf(result), digitAmount);
                String legalMoney = RateDataManager.CurRatePrice(quoteToken, value);
                legalMoney = RateDataManager.getShowLegalMoney(legalMoney, AppData.DIGIT_LEGAL_MONEY);
                get_option_money.setText(value + quoteTokenName);
                get_option_legal_money.setText("≈" + legalMoney);
            }
        }
//        tradeTotalMoney.setText(getString(R.string.string_amount_of_money_format, value, quoteTokenName, legalMoney));

    }

    @Override
    protected String getQuoteTokenAsset() {
        String quoteAsset = quoteTokenAsset;
        try {
            if (mOptionHoldOrderBean != null && mOptionHoldOrderBean.position != null && mOptionHoldOrderBean.position.startsWith("-")) {
                if (TextUtils.isEmpty(quoteAsset))
                    quoteAsset = "0";
                quoteAsset = String.valueOf(NumberUtils.add(String.valueOf(mOptionHoldOrderBean.margin), quoteAsset));
            }

        } catch (Exception e) {

        }
        return quoteAsset;
    }

    @Override
    protected String getBaseTokenAsset() {
        String baseAsset = baseTokenAsset;
        try {
            if (quoteTokenAsset != null && coinPairBean != null && coinPairBean.baseTokenOption != null && !TextUtils.isEmpty(coinPairBean.baseTokenOption.maxPayOff)) {
                if (TextUtils.isEmpty(baseAsset))
                    baseAsset = "0";
                double earnest = NumberUtils.div(coinPairBean.baseTokenOption.maxPayOff, quoteTokenAsset);
                baseAsset = String.valueOf(NumberUtils.add(String.valueOf(earnest), baseAsset));
            }
        } catch (Exception e) {

        }
        return baseAsset;
    }

    @Override
    protected void switchPriceMode(boolean isLimitedPriceParams) {
        isLimitedPrice = isLimitedPriceParams;
        editAmount.setText("");
        if (isLimitedPrice) {
            priceMode.setText(getString(R.string.string_limited_price));
            priceAbout.setVisibility(View.VISIBLE);
            if (isBuyMode)
                get_option_amount_ll.setVisibility(View.VISIBLE);
            else
                get_option_amount_ll.setVisibility(View.GONE);
            get_option_money_ll.setVisibility(View.VISIBLE);
            get_option_legal_money.setVisibility(View.VISIBLE);
        } else {
            priceMode.setText(getString(R.string.string_market_price));
            priceAbout.setVisibility(View.INVISIBLE);
            get_option_amount_ll.setVisibility(View.GONE);
            get_option_money_ll.setVisibility(View.GONE);
            get_option_legal_money.setVisibility(View.GONE);
        }

        updatePriceModeAssociatedView(isLimitedPrice);
        updateUnit();
    }

    @Override
    protected void updatePriceModeAssociatedView(boolean isLimitedPrice) {
        if (isLimitedPrice) {
            priceLimitedView.setVisibility(View.VISIBLE);
            priceMarketTx.setVisibility(View.GONE);
        } else {
            priceLimitedView.setVisibility(View.GONE);
            priceMarketTx.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected void updateBuySellTabAssociatedView(boolean isBuyMode) {
        //更新下单按钮状态
        super.updateBuySellTabAssociatedView(isBuyMode);
        if (isBuyMode) {
            balance_available_title.setText(getString(R.string.string_use_available));
        } else {
            balance_available_title.setText(getString(R.string.string_use_available_option));
        }
        if (isLimitedPrice) {
            if (isBuyMode) {
                get_option_amount_ll.setVisibility(View.VISIBLE);
                get_earnest_money_ll.setVisibility(View.GONE);
                get_earnest_money_legal_money.setVisibility(View.GONE);
                get_option_money_title.setText(getString(R.string.string_option_money));
            } else {
                get_option_amount_ll.setVisibility(View.GONE);
                get_earnest_money_ll.setVisibility(View.VISIBLE);
                get_earnest_money_legal_money.setVisibility(View.GONE);
                get_option_money_title.setText(getString(R.string.string_option_get_money));
            }
        } else {
            if (isBuyMode) {
                get_option_amount_ll.setVisibility(View.VISIBLE);
                get_earnest_money_ll.setVisibility(View.GONE);
                get_earnest_money_legal_money.setVisibility(View.GONE);
            } else {

                get_option_amount_ll.setVisibility(View.GONE);
                get_earnest_money_ll.setVisibility(View.VISIBLE);
                get_earnest_money_legal_money.setVisibility(View.GONE);
            }
        }
        if (!UserInfo.isLogin()) {

        } else {
            if (coinPairBean != null && coinPairBean.baseTokenOption != null) {
                String optionCoinName = TextUtils.isEmpty(coinPairBean.baseTokenOption.underlyingId) ? "" : coinPairBean.baseTokenOption.underlyingId;
                String isCall = TextUtils.isEmpty(coinPairBean.baseTokenOption.isCall) ? "" : coinPairBean.baseTokenOption.isCall;
                boolean bullish = KlineUtils.isOptionCall(isCall);//bullish 看涨 bearish看跌
                if (isBuyMode) {
                    btnCreateOrder.setText(getString(R.string.string_purchase) + "(" + getString(bullish ? R.string.string_option_purchase : R.string.string_option_sellout) + optionCoinName + ")");
                    balance_available_title.setText(getString(R.string.string_use_available));
                } else {
                    btnCreateOrder.setText(getString(R.string.string_sellout) + "(" + getString(bullish ? R.string.string_option_sellout : R.string.string_option_purchase) + optionCoinName + ")");
                    balance_available_title.setText(getString(R.string.string_use_available_option));
                }
            } else {
                if (isBuyMode) {
                    btnCreateOrder.setText(getString(R.string.string_purchase));
                    balance_available_title.setText(getString(R.string.string_use_available));
                } else {
                    btnCreateOrder.setText(getString(R.string.string_sellout));
                    balance_available_title.setText(getString(R.string.string_use_available_option));
                }
            }
        }


    }

    @Override
    public void createOrderSuccess() {
        super.createOrderSuccess();
        get_option_legal_money.setText(getString(R.string.string_placeholder));
    }

    @Override
    public void showOpenOrders(List<OrderBean> datas) {
        if (!UserInfo.isLogin()) {
            if (!currentOrderDatas.isEmpty()) {
                currentOrderDatas.clear();
                openOrdersAdapter.notifyDataSetChanged();
            }
            return;
        }
        if (datas != null) {
            currentOrderDatas.clear();
            currentOrderDatas.addAll(datas);
            openOrdersAdapter.notifyDataSetChanged();
        }

        loadEntrustOrdersPriceIntoCache(datas);

    }

    private String bidPrice = "";
    private String askPrice = "";
    private void loadEntrustOrdersPriceIntoCache(List<OrderBean> datas) {
        bidPrice = "";
        askPrice = "";
        if (datas != null) {
            for (OrderBean data : datas) {
                String price = data.getPrice();
                price = NumberUtils.stripTrailingZeros(price);
                if (KlineUtils.isBuyOrder(getActivity(),data.getSide())) {
                    bidPrice = bidPrice + price + ";";
                }else{
                    askPrice = askPrice + price + ";";
                }
            }
        }
    }

    public boolean isCurrentBidOrderPrice(String price){
        price = NumberUtils.stripTrailingZeros(price);
        return bidPrice.contains(price+";");
    }

    public boolean isCurrentAskOrderPrice(String price){
        price = NumberUtils.stripTrailingZeros(price);
        return askPrice.contains(price+";");
    }

    @Override
    public void showHistoryOrders(List<OrderBean> datas) {
        if (!UserInfo.isLogin()) {
            if (!historyOrderDatas.isEmpty()) {
                historyOrderDatas.clear();
                historyEntrustAdapter.notifyDataSetChanged();
            }
            return;
        }
        if (datas != null) {
            historyOrderDatas.clear();
            historyOrderDatas.addAll(datas);
            historyEntrustAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void showOptionHoldOrders(List<OptionHoldOrderResponse.OptionHoldOrderBean> datas) {
        if (!UserInfo.isLogin()) {
            if (!mHoldOptionOrders.isEmpty()) {
                mHoldOptionOrders.clear();
                optionHoldOrdersAdapter.notifyDataSetChanged();
            }
            return;
        }
        if (datas != null) {
            mHoldOptionOrders.clear();
            mHoldOptionOrders.addAll(datas);
            optionHoldOrdersAdapter.notifyDataSetChanged();
        }
        if (mHoldOptionOrders != null && mHoldOptionOrders.size() > 0) {
            for (int i = 0; i < mHoldOptionOrders.size(); i++) {
                if (mHoldOptionOrders.get(i) != null && mHoldOptionOrders.get(i).baseTokenId != null) {
                    if (mHoldOptionOrders.get(i).baseTokenId.equalsIgnoreCase(getBaseToken())) {
                        mOptionHoldOrderBean = mHoldOptionOrders.get(i);

                        if (mHoldOptionOrders.get(i).position.startsWith("-"))
                            updateAssettByToken(getBaseToken(), "0");
                        else
                            updateAssettByToken(getBaseToken(), mHoldOptionOrders.get(i).availPosition);
                    }
                }

            }
        }

    }

    @Override
    protected void updateUnit() {
        bookTitleAmount.setText(getString(isShowCumulativeVolume ? R.string.string_cumulative_quantity_format : R.string.string_amount_format, baseTokenName));
        bookTitlePrice.setText(getString(R.string.string_price_ph, quoteTokenName));

        if (isBuyMode) {
            //买入

            isQuoteToken = false;
            editAmount.setHint(getResources().getString(R.string.string_amount));
            editAmountUnit.setText(baseTokenName);
            updateAsset(quoteToken, quoteTokenName);
            updateStepViewRange(false, baseToken, baseTokenName);

        } else {
            //卖出
            if (isLimitedPrice) {
                //限价
                isQuoteToken = false;
                editAmount.setHint(getResources().getString(R.string.string_amount));
                editAmountUnit.setText(baseTokenName);
                updateAsset(baseToken, baseTokenName);
                updateStepViewRange(false, baseToken, baseTokenName);
            } else {
                //市价
                isQuoteToken = true;
                editAmount.setHint(getResources().getString(R.string.string_amount));
                editAmountUnit.setText(baseTokenName);
                updateAsset(baseToken, baseTokenName);
                updateStepViewRange(false, baseToken, baseTokenName);
            }
        }
    }

    @Override
    protected void updateStepViewRange(boolean isQuoteToken, String token, String tokenName) {
        if (isBuyMode) {//买入
            stepViewMaxValue = getQuoteTokenAsset();
            if (isLimitedPrice) {//限价
                String price = priceLimitedView.getPrice();
                if (!TextUtils.isEmpty(stepViewMaxValue) && !TextUtils.isEmpty(price)) {
                    double assetSubFee = NumberUtils.mul(getQuoteTokenAsset(), String.valueOf(NumberUtils.sub("1", getPresenter().OptionFee())));
                    stepViewMaxValue = NumberUtils.div(price, String.valueOf(assetSubFee)) + "";
                }
            } else {
                //市价 默认的是输入金额 所以最大为可用金额资产
                String price = currentTicker.getC();
                if (!TextUtils.isEmpty(stepViewMaxValue) && !TextUtils.isEmpty(price)) {
                    double assetSubFee = NumberUtils.mul(getQuoteTokenAsset(), String.valueOf(NumberUtils.sub("1", getPresenter().OptionFee())));
                    stepViewMaxValue = NumberUtils.div(1.1d, NumberUtils.div(price, String.valueOf(assetSubFee))) + "";
                }
            }
        } else {//卖出
            stepViewMaxValue = getBaseTokenAsset();
        }
        stepViewMinTx.setText("0");
        stepViewMaxTx.setText(getString(R.string.string_stepview_max_txt_format, NumberUtils.roundFormatDown(stepViewMaxValue, isQuoteToken ? digitPrice : digitBase), tokenName));
        updateStepViewValue(editAmount.getText().toString());
    }

    @Override
    protected void createOrder(final boolean isBuyMode) {
        /**交易下单规则粗略校验**************/
        final String price = priceLimitedView.getPrice();
        if (isLimitedPrice) {
            if (TextUtils.isEmpty(price)) {
                ToastUtils.showShort(getActivity(), getString(R.string.string_prompt_input_price));
                return;
            }
            if (Double.valueOf(price) <= 0) {
                ToastUtils.showShort(getActivity(), getString(R.string.string_prompt_input_price));
                return;
            }
        }

        final String amount = editAmount.getText().toString().trim();
        if (TextUtils.isEmpty(amount)) {
            /*if (isBuyMode && !isLimitedPrice) {
                ToastUtils.showShort(getActivity(),getString(R.string.string_prompt_input_amount_of_money));
            } else {*/
            ToastUtils.showShort(getActivity(), getString(R.string.string_prompt_input_amount));
            //}
            return;
        }
        if (Double.valueOf(amount) <= 0) {
            /*if (isBuyMode && !isLimitedPrice) {
                ToastUtils.showShort(getActivity(),getString(R.string.string_prompt_input_amount_of_money));
            } else {*/
            ToastUtils.showShort(getActivity(), getString(R.string.string_prompt_input_amount));
            //}
            return;
        }
        if (coinPairBean != null) {
            //最小交易数量
            String minTradeQuantity = coinPairBean.getMinTradeQuantity();
            //最小交易额
            String minTradeAmount = coinPairBean.getMinTradeAmount();
            if (isLimitedPrice) {//限价
                String minPricePrecision = coinPairBean.getMinPricePrecision();
                //最小交易价格
                if (!TextUtils.isEmpty(minPricePrecision)) {
                    if (NumberUtils.sub(price, minPricePrecision) < 0) {
                        ToastUtils.showShort(getActivity(), getString(R.string.string_min_trade_price, minPricePrecision) + quoteTokenName);
                        return;
                    }
                }

                //最小交易数量
                if (!TextUtils.isEmpty(minTradeQuantity)) {
                    if (NumberUtils.sub(amount, minTradeQuantity) < 0) {
                        ToastUtils.showShort(getActivity(), getString(R.string.string_min_trade_quantity, minTradeQuantity) + baseTokenName);
                        return;
                    }
                }

                //最小交易额
                if (!TextUtils.isEmpty(minTradeAmount)) {
                    if (NumberUtils.sub(String.valueOf(NumberUtils.mul(price, amount)), minTradeAmount) < 0) {
                        ToastUtils.showShort(getActivity(), getString(R.string.string_min_trade_amount, minTradeAmount) + quoteTokenName);
                        return;
                    }
                }
            } else {//市价
               /* if (isBuyMode) {
                    //最小交易额
                    if (!TextUtils.isEmpty(minTradeAmount)) {
                        //amount 为输入金额
                        if (NumberUtils.sub(amount, minTradeAmount) < 0) {
                            ToastUtils.showShort(getActivity(),getString(R.string.string_min_trade_amount, minTradeAmount)+quoteTokenName);
                            return;
                        }
                    }
                } else */
                {
                    //最小交易数量
                    if (!TextUtils.isEmpty(minTradeQuantity)) {
                        if (NumberUtils.sub(amount, minTradeQuantity) < 0) {
                            ToastUtils.showShort(getActivity(), getString(R.string.string_min_trade_quantity, minTradeQuantity) + baseTokenName);
                            return;
                        }
                    }
                }

            }
        } else {
            ToastUtils.showShort(getActivity(), getString(R.string.string_retry_select_trade_symbol));
            return;
        }

        /** 最大交易额/量判断 **********/
        if (isBuyMode) {//买入
            if (isLimitedPrice) {//限价
                //最大交易额
                if (!TextUtils.isEmpty(getQuoteTokenAsset())) {
                    if (NumberUtils.sub(String.valueOf(NumberUtils.mul(price, amount)), getQuoteTokenAsset()) > 0) {
                        ToastUtils.showShort(getActivity(), getString(R.string.string_balance_not_enough));
                        return;
                    }
                } else {
                    ToastUtils.showShort(getActivity(), getString(R.string.string_balance_not_enough_swipy_refresh));
                    return;
                }
            } else {//市价
                //最大交易额
                /*if (!TextUtils.isEmpty(getQuoteTokenAsset())) {
                    //amount 为输入金额
                    if (NumberUtils.sub(String.valueOf(NumberUtils.mul(NumberUtils.mul(currentTicker.getC() , amount) , 1.1d)), getQuoteTokenAsset()) > 0) {
                        ToastUtils.showShort(getActivity(),getString(R.string.string_balance_not_enough));
                        return;
                    }
                } else {
                    ToastUtils.showShort(getActivity(),getString(R.string.string_balance_not_enough_swipy_refresh));
                    return;
                }*/
            }

        } else {//卖出

            //最大交易数量
            if (!TextUtils.isEmpty(getBaseTokenAsset())) {
                if (NumberUtils.sub(amount, getBaseTokenAsset()) > 0) {
                    ToastUtils.showShort(getActivity(), getString(R.string.string_balance_not_enough));
                    return;
                }
            } else {
                ToastUtils.showShort(getActivity(), getString(R.string.string_balance_not_enough_swipy_refresh));
                return;
            }
        }

        String baseAsset = baseTokenAsset;
        if (TextUtils.isEmpty(baseAsset))
            baseAsset = "0";
        double leftAsset = NumberUtils.sub(amount, baseAsset);


        if (OptionCreateOrderTipDialog.getOptionTipSPShow() == true && isBuyMode == false && leftAsset > 0d && coinPairBean.baseTokenOption != null) {
            String assetVaule = String.valueOf(NumberUtils.mul(String.valueOf(leftAsset), coinPairBean.baseTokenOption.maxPayOff));
            assetVaule = NumberUtils.roundFormatDown(assetVaule, digitPrice);
            String tipText = String.format(getString(R.string.string_option_create_tip_value), assetVaule, quoteTokenName);

            final OptionCreateOrderTipDialog optionCreateOrderTipDialog = new OptionCreateOrderTipDialog(getActivity());
            optionCreateOrderTipDialog.setShowText(tipText);
            optionCreateOrderTipDialog.setNegativeButton(getString(R.string.string_sure), new View.OnClickListener() {

                @Override
                public void onClick(View v) {
                    if (optionCreateOrderTipDialog.getTipChecked() == true) {
                        OptionCreateOrderTipDialog.setOptionTipSP(true);
                    }

                    getPresenter().createOrder(isBuyMode, isLimitedPrice, exchangeId, symbol, price, amount);
                    if (optionCreateOrderTipDialog.isShowing()) {
                        optionCreateOrderTipDialog.dismiss();
                    }
                }
            });
            optionCreateOrderTipDialog.setPositiveButton(getActivity().getString(R.string.string_cancel), new View.OnClickListener() {

                @Override
                public void onClick(View v) {
                    // 取消的操作
                    if (optionCreateOrderTipDialog.isShowing()) {
                        optionCreateOrderTipDialog.dismiss();
                    }
                }
            });
            optionCreateOrderTipDialog.show();
        } else {
            //去下单
            getPresenter().createOrder(isBuyMode, isLimitedPrice, exchangeId, symbol, price, amount);
        }

    }


    @Override
    public void onLoadMoreRequested() {
        getPresenter().loadMore();
    }

    @Override
    public void loadMoreComplete() {
        if (historyEntrustAdapter != null) {
            historyEntrustAdapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {
        if (historyEntrustAdapter != null) {
            historyEntrustAdapter.loadMoreFail();
        }
    }

    @Override
    public void loadEnd() {
        if (historyEntrustAdapter != null) {
            historyEntrustAdapter.loadMoreEnd();
        }
    }

    @Override
    protected void updateLatestPrice(TickerBean tickerBean) {
        super.updateLatestPrice(tickerBean);
        if (tickerBean != null && coinPairBean != null && coinPairBean.baseTokenOption != null) {
            double ratio = NumberUtils.div(tickerBean.getC(), coinPairBean.baseTokenOption.maxPayOff);
            TextView rightTextView = topBar.getRightTextView2();
            if (rightTextView != null) {
                //rightTextView.setVisibility(View.VISIBLE);
                //rightTextView.setText(NumberUtils.roundFormatDown(ratio, 2) + "X");
            }
        }
    }

    private void getSettleStatus() {
        if (coinPairBean != null) {
            OptionApi.RequestOptionStatus(coinPairBean.getSymbolId(), new SimpleResponseListener<OptionSymbolStatusBean>() {
                @Override
                public void onSuccess(OptionSymbolStatusBean response) {
                    super.onSuccess(response);
                    if (CodeUtils.isSuccess(response, false)) {
                        List<OptionSymbolStatusBean.OptionSymbolStatus> data = response.array;
                        if (data != null) {
                            for (OptionSymbolStatusBean.OptionSymbolStatus bean : data) {
                                if (bean != null && !TextUtils.isEmpty(coinPairBean.getSymbolId()) && coinPairBean.getSymbolId().equalsIgnoreCase(bean.symbolId)) {
                                    mOptionSymbolStatus = bean;
                                }
                            }
                        }
                    }
                }
            });
        }
    }


}
