/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: HistoryOptionDeliveryPresenter.java
 *   @Date: 1/15/19 10:16 AM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.trade.presenter;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.trade.OptionApi;
import io.bhex.sdk.trade.bean.OptionHistoryResponse;

public class HistoryOptionDeliveryPresenter extends BasePresenter<HistoryOptionDeliveryPresenter.HistoryOptionDeliveryUI> {
    public interface HistoryOptionDeliveryUI extends AppUI {
        void loadMoreComplete();

        void showOptions(List<OptionHistoryResponse.OptionHistoryBean> listDatas);

        void loadMoreFailed();

        void loadEnd();
    }

    private List<OptionHistoryResponse.OptionHistoryBean> listDatas=new ArrayList<>();
    private OptionHistoryResponse currentData;

    public void loadMore() {
        getHistoryEntrustOrders(true);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, HistoryOptionDeliveryPresenter.HistoryOptionDeliveryUI ui) {
        super.onUIReady(activity, ui);

        getHistoryEntrustOrders(false);
    }


    /**
     * 获取当前委托
     *
     * @param isLoadMore
     */
    public void getHistoryEntrustOrders(final boolean isLoadMore) {
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }


        int page = 1;
        if (isLoadMore) {
            if (currentData != null) {
                page = currentData.page + 1;
            }
        }

        OptionApi.RequestOptionHistoryDelivery(page, new SimpleResponseListener<OptionHistoryResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
                getUI().loadMoreComplete();
                getUI().dismissProgressDialog();
            }
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }
            @Override
            public void onSuccess(OptionHistoryResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    currentData = response;
                    List<OptionHistoryResponse.OptionHistoryBean> data = response.data;
                    if (data != null) {
                        if (isLoadMore) {

                            if (data != null) {
                                listDatas.addAll(data);
                            }

                        } else {
                            if (data != null) {
                                listDatas.clear();
                                listDatas = data;
                            }
                        }
                        getUI().showOptions(listDatas);
                        if (data.size()< AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        }else{
                            getUI().loadMoreComplete();
                        }
                    }else{
                        getUI().loadMoreComplete();
                    }

                }else{
                    getUI().loadMoreFailed();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
