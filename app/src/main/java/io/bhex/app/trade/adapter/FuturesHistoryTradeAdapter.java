/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FuturesHistoryTradeAdapter.java
 *   @Date: 19-7-26 下午6:49
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.trade.adapter;

import android.content.Context;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.sdk.trade.futures.bean.DeliveryOrder;

public class FuturesHistoryTradeAdapter extends BaseQuickAdapter<DeliveryOrder, BaseViewHolder> {

    private Context mContext;
    public FuturesHistoryTradeAdapter(Context context, List<DeliveryOrder> data) {
        super(R.layout.item_futures_history_trade_order_layout, data);
        mContext = context;
    }


    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final DeliveryOrder itemModel) {
        if(itemModel == null)
            return;
        String orderSide = KlineUtils.getFuturesOrderSideTxt(mContext, itemModel.getSide());
//        baseViewHolder.setText(R.id.orderSideAndLever, orderSide + "·" + itemModel.getLeverage() + "X");
        baseViewHolder.setText(R.id.order_buy_type, orderSide);
        baseViewHolder.setTextColor(R.id.order_buy_type, KlineUtils.getFuturesOrderSideColor(mContext, itemModel.getSide()));
        baseViewHolder.setText(R.id.order_coin_name, itemModel.getSymbolName());

        baseViewHolder.setText(R.id.order_time, DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getTime()), "HH:mm:ss yyyy/MM/dd"));
        baseViewHolder.setText(R.id.order_price, itemModel.getPrice()+KlineUtils.getFuturesPriceUnit(itemModel.getSymbolId()));
        baseViewHolder.setText(R.id.order_fee, itemModel.getFee()+itemModel.getFeeTokenName());
        baseViewHolder.setText(R.id.order_entrust_amount, itemModel.getQuantity()+mContext.getString(R.string.string_futures_unit));
        boolean isFuturesCloseOrder = KlineUtils.isFuturesCloseOrder(mContext, itemModel.getSide());
        baseViewHolder.setGone(R.id.order_profit_loss_title,isFuturesCloseOrder);
        baseViewHolder.setGone(R.id.order_profit_loss,isFuturesCloseOrder);
        if (isFuturesCloseOrder) {
            baseViewHolder.setText(R.id.order_profit_loss_title,mContext.getResources().getString(R.string.string_profit_loss)+":");
            baseViewHolder.setText(R.id.order_profit_loss, itemModel.getPnl()+" "+KlineUtils.getFuturesProfitLossUnit(itemModel.getSymbolId()));
        }
//        baseViewHolder.setText(R.id.order_deal_amount, itemModel.getExecutedAmount()+itemModel.getQuoteTokenName());
        baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
    }
}
