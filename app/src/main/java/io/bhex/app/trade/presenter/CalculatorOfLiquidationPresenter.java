package io.bhex.app.trade.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseFragmentPresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.trade.bean.CalculateLiquidationPriceResponse;
import io.bhex.sdk.trade.futures.FuturesApi;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-02-21
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class CalculatorOfLiquidationPresenter extends BaseFragmentPresenter<CalculatorOfLiquidationPresenter.CalculatorOfLiquidationUI> {

    public interface CalculatorOfLiquidationUI extends AppUI{

        void showResult(CalculateLiquidationPriceResponse response);
    }

    public void calculateLiquidationPrice(boolean isLong, CoinPairBean currentCoinPair, String lever, String openPrice, String openQuantity, String addMargin) {
        FuturesApi.calculateLiquidationPrice(isLong ? 1 : 0 ,currentCoinPair.getSymbolId(),lever,openPrice,openQuantity,addMargin,new SimpleResponseListener<CalculateLiquidationPriceResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(CalculateLiquidationPriceResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showResult(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

}
