/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OpenOrdersAdapter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.trade.adapter;

import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.app.R;
import io.bhex.sdk.trade.bean.OrderBean;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;

/**
 * ================================================
 * 描   述：当前委托
 * ================================================
 */

public class OpenOrdersAdapter  extends BaseQuickAdapter<OrderBean, BaseViewHolder> {

    private boolean mIsShowName=true;

    public OpenOrdersAdapter(List<OrderBean> data) {
        super(R.layout.item_current_entrust_order__layout, data);
    }

    public void showCoinPairName(boolean isShowName){
        mIsShowName = isShowName;
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final OrderBean itemModel) {
        baseViewHolder.setVisible(R.id.order_coin_name,mIsShowName);
//        String title = KlineUtils.getBuyOrSellTxt(mContext, itemModel.getSide()) + itemModel.getBaseTokenName() + " / " + itemModel.getQuoteTokenName();
        String title = KlineUtils.getBuyOrSellTxt(mContext, itemModel.getSide());
        baseViewHolder.setText(R.id.order_buy_type, title);
        baseViewHolder.setTextColor(R.id.order_buy_type,KlineUtils.getBuyOrSellColor(mContext,itemModel.getSide()));
        baseViewHolder.setText(R.id.order_coin_name,itemModel.getBaseTokenName() + " / " + itemModel.getQuoteTokenName());
        baseViewHolder.setText(R.id.order_time, DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getTime()), "HH:mm:ss yyyy/MM/dd"));
        baseViewHolder.setText(R.id.order_price, KlineUtils.getPrice(mContext, itemModel));
        baseViewHolder.setText(R.id.order_entrust_amount_title, KlineUtils.getEntrustTitle(mContext, itemModel)+":");
        baseViewHolder.setText(R.id.order_entrust_amount, KlineUtils.getOrderEntrustAndUnit(itemModel));
        int baseDigit = AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(itemModel.getSymbolId() + itemModel.getBaseTokenId());
        baseViewHolder.setText(R.id.order_deal_amount, NumberUtils.roundFormatDown(itemModel.getExecutedQty(),baseDigit)+" "+itemModel.getBaseTokenName());
        baseViewHolder.addOnClickListener(R.id.revoke_order);
        baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
    }
}