package io.bhex.app.trade.ui;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseFragment;
import io.bhex.app.trade.presenter.CalculatorOfLiquidationPresenter;
import io.bhex.app.utils.AnimalUtils;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.view.ShadowDrawable;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.app.view.alertview.AlertView;
import io.bhex.app.view.alertview.OnItemClickListener;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.FuturensBaseToken;
import io.bhex.sdk.trade.bean.CalculateLiquidationPriceResponse;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-02-21
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class CalculatorOfLiquidationFragment extends BaseFragment<CalculatorOfLiquidationPresenter, CalculatorOfLiquidationPresenter.CalculatorOfLiquidationUI> implements CalculatorOfLiquidationPresenter.CalculatorOfLiquidationUI, View.OnClickListener {
    private boolean isLong;
    private View tab;
    private View buyTabRela;
    private View sellTabRela;
    private View buySellTabBg;
    private TextView buyTabTx;
    private TextView sellTabTx;
    private List<CoinPairBean> selectSymbolList = new ArrayList<CoinPairBean>();
    private List<String> selectSymbolNameList = new ArrayList<String>();
    private TextView contractName;
    private EditText leverEt;
    private EditText openPriceEt;
    private EditText openQuantityEt;
    private EditText addMarginEt;
    private String quantityUnit;
    private CoinPairBean currentCoinPair;
    private String displayTokenId;
    private CoinPairBean symbol;

    @Override
    protected CalculatorOfLiquidationPresenter.CalculatorOfLiquidationUI getUI() {
        return this;
    }

    @Override
    protected CalculatorOfLiquidationPresenter createPresenter() {
        return new CalculatorOfLiquidationPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_calculator_of_liquidation_layout,null,false);
    }

    @Override
    protected void initViews() {
        super.initViews();
        Bundle arguments = getArguments();
        if (arguments != null) {
            symbol = (CoinPairBean) arguments.getSerializable("symbol");
        }
        setShadow(R.id.select_contract_rela);
        setShadow(R.id.tab);
        setShadow(R.id.rela_lever);
        setShadow(R.id.rela_open_price);
        setShadow(R.id.rela_open_quantity);
        setShadow(R.id.rela_add_margin);
        setShadow(R.id.rela_result);
        tab = viewFinder.find(R.id.tab);
        buyTabRela = viewFinder.find(R.id.tab_bid_rela);
        sellTabRela = viewFinder.find(R.id.tab_ask_rela);
        buySellTabBg = viewFinder.find(R.id.tab_bg);
        buyTabTx = viewFinder.find(R.id.tab_bid);
        sellTabTx = viewFinder.find(R.id.tab_ask);
        contractName = viewFinder.find(R.id.contract_name);

        switchBuySellTab(true); //默认多仓

        leverEt = viewFinder.editText(R.id.lever);
        openPriceEt = viewFinder.editText(R.id.openPrice);
        openQuantityEt = viewFinder.editText(R.id.openQuantity);
        addMarginEt = viewFinder.editText(R.id.addMargin);

        quantityUnit = getString(R.string.string_futures_unit);

        initContractSymbols();
        setDefaultSelectSymbol();
    }

    private void initContractSymbols() {
        selectSymbolList.clear();
        selectSymbolNameList.clear();
        HashMap<String, CoinPairBean> contractSymbolMap = AppConfigManager.GetInstance().getContractSymbolMap();
        if (contractSymbolMap != null) {
            for (String key : contractSymbolMap.keySet()) {
                CoinPairBean coinPairBean = contractSymbolMap.get(key);
                selectSymbolList.add(coinPairBean);
                selectSymbolNameList.add(coinPairBean.getSymbolName());
            }
        }
    }

    private void setDefaultSelectSymbol() {
        if (symbol != null) {
            setSelectContract(symbol);
            return;
        }
        if (selectSymbolList != null && selectSymbolList.size()>0) {
            CoinPairBean coinPairBean = selectSymbolList.get(0);
            setSelectContract(coinPairBean);
        }
    }

    private void setSelectContract(CoinPairBean coinPairBean) {
        if (coinPairBean != null) {
            if (currentCoinPair != null) {
                if (!currentCoinPair.getSymbolId().equals(coinPairBean.getSymbolId())) {
                    clearViewData();
                }
            }
            currentCoinPair = coinPairBean;
            String quoteTokenName = coinPairBean.getQuoteTokenName();
            FuturensBaseToken baseTokenFutures = coinPairBean.baseTokenFutures;
            if (baseTokenFutures != null) {
                displayTokenId = baseTokenFutures.getDisplayTokenId();

            }
            contractName.setText(coinPairBean.getSymbolName());

            viewFinder.textView(R.id.openPriceUnit).setText(displayTokenId);
            viewFinder.textView(R.id.openQuantityUnit).setText(quantityUnit);
            viewFinder.textView(R.id.addMarginUnit).setText(quoteTokenName);

            viewFinder.textView(R.id.liquidationPriceUnit).setText(displayTokenId);
        }
    }


    private void clearViewData() {
        leverEt.setText("");
        openPriceEt.setText("");
        openQuantityEt.setText("");
        addMarginEt.setText("");

        viewFinder.textView(R.id.liquidationPrice).setText(getString(R.string.string_placeholder));
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.select_contract_rela).setOnClickListener(this);
        viewFinder.find(R.id.tab_bid_rela).setOnClickListener(this);
        viewFinder.find(R.id.tab_ask_rela).setOnClickListener(this);
        viewFinder.find(R.id.btn_calculator).setOnClickListener(this);
    }


    private void setShadow(int viewId) {
        ShadowDrawable.setShadow(viewFinder.find(viewId));
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.select_contract_rela:
                showSelectToken();
                break;
            case R.id.tab_bid_rela:
                switchBuySellTab(true);
                AnimalUtils.transAnimRun(buySellTabBg, buySellTabBg.getWidth(), 0);

                break;
            case R.id.tab_ask_rela:
                switchBuySellTab(false);
                AnimalUtils.transAnimRun(buySellTabBg, 0, buySellTabBg.getWidth());

                break;
            case R.id.btn_calculator:
                calculateLiquidationPrice();
                break;
        }
    }
    /**
     * 计算强平价
     */
    private void calculateLiquidationPrice() {

        String lever = leverEt.getText().toString().trim();
        String openPrice = openPriceEt.getText().toString().trim();
        String openQuantity = openQuantityEt.getText().toString().trim();
        String addMargin = addMarginEt.getText().toString().trim();
        if (currentCoinPair == null) {
            return;
        }
        if (TextUtils.isEmpty(lever)) {
            ToastUtils.showShort(getString(R.string.string_input_lever));
            return;
        }
        if (TextUtils.isEmpty(openPrice)) {
            ToastUtils.showShort(getString(R.string.string_input_open_price));
            return;
        }
        if (TextUtils.isEmpty(openQuantity)) {
            ToastUtils.showShort(getString(R.string.string_input_open_quantity));
            return;
        }


        getPresenter().calculateLiquidationPrice(isLong,currentCoinPair,lever,openPrice,openQuantity,addMargin);
    }

    @Override
    public void showResult(CalculateLiquidationPriceResponse response) {
        if (response != null) {
            viewFinder.textView(R.id.liquidationPrice).setText(response.getLiquidationPrice());
        }

    }

    /**
     * 切换买卖tab
     *
     * @param isLongPosition
     */
    protected void switchBuySellTab(boolean isLongPosition) {
        isLong = isLongPosition;
        if (isLong) {
            buyTabTx.setTextColor(getResources().getColor(R.color.blue));
            sellTabTx.setTextColor(getResources().getColor(CommonUtil.isBlackMode() ? R.color.dark_night : R.color.dark));
            ShadowDrawable.setShadow(tab);
            buySellTabBg.setBackgroundResource(R.drawable.bg_corner_rect_blue);
        } else {
            buyTabTx.setTextColor(getResources().getColor(CommonUtil.isBlackMode() ? R.color.dark_night : R.color.dark));
            sellTabTx.setTextColor(getResources().getColor(R.color.blue));
            ShadowDrawable.setShadow(tab);
            buySellTabBg.setBackgroundResource(R.drawable.bg_corner_rect_blue);
        }
    }

    /**
     * token选择
     */
    private void showSelectToken() {

        AlertView selectContractAlert = new AlertView(getString(R.string.string_select_token), null, getString(R.string.string_cancel), currentCoinPair==null?null:new String[]{currentCoinPair.getSymbolName()}, selectSymbolNameList.toArray(new String[selectSymbolNameList.size()]), getActivity(), AlertView.Style.ActionSheet, new OnItemClickListener() {
            @Override
            public void onItemClick(Object o, int position) {
                if (position == -1) {
                    return;
                }
                if (position < selectSymbolNameList.size()) {
                    CoinPairBean selectContract = selectSymbolList.get(position);
                    setSelectContract(selectContract);
                }
            }
        });
        selectContractAlert.show();
    }
}
