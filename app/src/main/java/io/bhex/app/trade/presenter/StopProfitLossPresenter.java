package io.bhex.app.trade.presenter;

import android.content.Intent;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.trade.futures.FuturesApi;
import io.bhex.sdk.trade.futures.bean.FutureStopProfitLossInfo;
import io.bhex.sdk.trade.futures.bean.FuturesPositionOrder;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-01-03
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class StopProfitLossPresenter extends BasePresenter<StopProfitLossPresenter.StopProfitLossUI> {
    private FuturesPositionOrder holdOrder;
    private FutureStopProfitLossInfo currentStopProfitLossInfo;

    public interface StopProfitLossUI extends AppUI{

        void showStopProfitLossInfo(FutureStopProfitLossInfo response);

        void showHoldInfo(FuturesPositionOrder holdOrder);

        void closeStopProfitLoss(boolean isCloseSuccess);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, StopProfitLossUI ui) {
        super.onUIReady(activity, ui);
        Intent intent = getActivity().getIntent();
        if (intent != null) {
            holdOrder = (FuturesPositionOrder) intent.getSerializableExtra("hold");
            if (holdOrder != null) {
                getUI().showHoldInfo(holdOrder);
                getStopProfitLossOrderInfo(holdOrder);
            }
        }
    }

    /**
     * 获取持仓单：止盈止损信息
     * @param holdOrder
     */
    private void getStopProfitLossOrderInfo(FuturesPositionOrder holdOrder) {
        FuturesApi.getStopProfitLossInfo(holdOrder.getSymbolId(),holdOrder.getIsLong(),new SimpleResponseListener<FutureStopProfitLossInfo>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(FutureStopProfitLossInfo response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    currentStopProfitLossInfo = response;
                    getUI().showStopProfitLossInfo(response);
                }else{

                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }


    /**
     * 设置止盈止损价格
     * @param profitPrice
     * @param STOP_PROFIT_TRIGGER_PRICE_TYPE
     * @param STOP_PROFIT_CLOSE_POSITION_TYPE
     * @param lossPrice
     * @param STOP_LOSS_TRIGGER_PRICE_TYPE
     * @param STOP_LOSS_CLOSE_POSITION_TYPE
     */
    public void sumbitStopProfitLossPrice(String profitPrice, int STOP_PROFIT_TRIGGER_PRICE_TYPE, int STOP_PROFIT_CLOSE_POSITION_TYPE, String lossPrice, int STOP_LOSS_TRIGGER_PRICE_TYPE, int STOP_LOSS_CLOSE_POSITION_TYPE) {
        if (holdOrder != null) {
            FuturesApi.setStopProfitLossPrice(holdOrder.getSymbolId(),holdOrder.getExchangeId(),holdOrder.getIsLong(),profitPrice,STOP_PROFIT_TRIGGER_PRICE_TYPE,STOP_PROFIT_CLOSE_POSITION_TYPE,lossPrice,STOP_LOSS_TRIGGER_PRICE_TYPE,STOP_LOSS_CLOSE_POSITION_TYPE,new SimpleResponseListener<FutureStopProfitLossInfo>(){
                @Override
                public void onBefore() {
                    super.onBefore();
                    getUI().showProgressDialog("","");
                }

                @Override
                public void onFinish() {
                    super.onFinish();
                    getUI().dismissProgressDialog();
                }

                @Override
                public void onSuccess(FutureStopProfitLossInfo response) {
                    super.onSuccess(response);
                    if (CodeUtils.isSuccess(response,true)) {
                        ToastUtils.showLong(getString(R.string.string_set_success));
                        getActivity().finish();
                    }
                }

                @Override
                public void onError(Throwable error) {
                    super.onError(error);
                    ToastUtils.showLong(getString(R.string.string_set_failed));
                }
            });
        }
    }

    /**
     * 关闭止盈止损
     */
    public void closeStopProfitLoss() {
        if (holdOrder != null) {
//            if (currentStopProfitLossInfo != null) {
//                if (!currentStopProfitLossInfo.isStopProfit() && !currentStopProfitLossInfo.isStopLoss()) {
//                    getUI().closeStopProfitLoss(true);
//                    return;
//                }
//            }else{
//                getUI().closeStopProfitLoss(true);
//                return;
//            }
            FuturesApi.cancelStopProfitLossPrice(holdOrder.getSymbolId(),holdOrder.getIsLong(),3,new SimpleResponseListener<FutureStopProfitLossInfo>(){
                @Override
                public void onBefore() {
                    super.onBefore();
                    getUI().showProgressDialog("","");
                }

                @Override
                public void onFinish() {
                    super.onFinish();
                    getUI().dismissProgressDialog();
                }

                @Override
                public void onSuccess(FutureStopProfitLossInfo response) {
                    super.onSuccess(response);
                    if (CodeUtils.isSuccess(response,true)) {
                        ToastUtils.showLong(getString(R.string.string_cancel_success));
                        getUI().closeStopProfitLoss(true);
                    }else{
                        getUI().closeStopProfitLoss(false);
                    }
                }

                @Override
                public void onError(Throwable error) {
                    super.onError(error);
                    ToastUtils.showLong(getString(R.string.string_cancel_failed));
                    getUI().closeStopProfitLoss(false);
                }
            });
        }
    }

}
