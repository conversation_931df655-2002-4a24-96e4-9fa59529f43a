package io.bhex.app.trade.ui;

import android.app.Dialog;
import android.content.Context;
import android.text.InputFilter;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.TextView;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.app.view.PointLengthFilter;
import io.bhex.app.view.ShadowDrawable;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.core.SPEx;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.trade.futures.FuturesApi;
import io.bhex.sdk.trade.futures.bean.FuturesOrderResponse;
import io.bhex.sdk.trade.futures.bean.FuturesPositionOrder;

/**
 * 闪电平仓确认框
 */
public class FuturesQuickCloseOrderPopDialog {
    private final MarginAdjustDialog.OnDialogObserver mDialogObserver;
    private Context mContext;
    private View mContentView;
    private Dialog bottomDialog;
    private String lastPrice;
    private CoinPairBean coinPairBean;

    private int digitBase;
    private int digitAmount;
    private FuturesPositionOrder mHoldOrderBean;
    private OnLoadingObserver mObserver;

    public interface OnLoadingObserver {
        void showLoading();

        void hideLoading();
    }

    public FuturesQuickCloseOrderPopDialog(Context context, FuturesPositionOrder itemModel, OnLoadingObserver observer, MarginAdjustDialog.OnDialogObserver dialogObserver) {
        mContext = context;
        mHoldOrderBean = itemModel;
        mObserver = observer;
        mDialogObserver = dialogObserver;
    }

    public void ShowDialog() {
        bottomDialog = new Dialog(mContext, R.style.BottomDialog);
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.dialog_futures_quick_close_confirm, null);
        bottomDialog.setContentView(mContentView);
        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) mContentView.getLayoutParams();
        params.width = mContext.getResources().getDisplayMetrics().widthPixels;
        mContentView.setLayoutParams(params);
        initView();
        bottomDialog.setCanceledOnTouchOutside(true);
        bottomDialog.getWindow().setGravity(Gravity.BOTTOM);
        bottomDialog.getWindow().setWindowAnimations(R.style.BottomDialog_Animation);
        bottomDialog.show();
    }

    private void initView() {
        try {

            if (mHoldOrderBean != null && mContentView != null) {
                //isLong 仓位方向: 1=多仓，0=空仓
                String positionSide = KlineUtils.getFuturesOrderPositionTxtByIsLong(mContext, mHoldOrderBean.getIsLong());
                String lever = "·" + mHoldOrderBean.getLeverage() + "X";
                int color = KlineUtils.getFuturesOrderSideColorByIsLong(mContext, mHoldOrderBean.getIsLong());
                int colorOfOpposit = KlineUtils.getFuturesOrderOppositeSideColorByIsLong(mContext, mHoldOrderBean.getIsLong());
                ((TextView) mContentView.findViewById(R.id.order_buy_type)).setText(positionSide + lever);
                ((TextView) mContentView.findViewById(R.id.order_buy_type)).setTextColor(color);
                ((TextView) mContentView.findViewById(R.id.order_coin_name)).setText(mHoldOrderBean.getSymbolName());
                mContentView.findViewById(R.id.order_close).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        bottomDialog.dismiss();
                    }
                });

                coinPairBean = AppConfigManager.GetInstance().getSymbolInfoById(mHoldOrderBean.getSymbolId());
                if (coinPairBean == null) {
                    ToastUtils.showShort(mContext.getResources().getString(R.string.string_data_exception));
                    if (bottomDialog.isShowing()) {
                        bottomDialog.dismiss();
                    }
                    return;
                }

                PointLengthFilter pricePointFilter = new PointLengthFilter();
                PointLengthFilter amountPointFilter = new PointLengthFilter();
                digitBase = NumberUtils.calNumerCount(mContext, coinPairBean.getBasePrecision());
                digitAmount = NumberUtils.calNumerCount(mContext, coinPairBean.getQuotePrecision());

                pricePointFilter.setDecimalLength(digitAmount);
                amountPointFilter.setDecimalLength(digitBase);

                setShadow(mContentView.findViewById(R.id.edit_amount_rela));
                final TextView marketAmountTV = mContentView.findViewById(R.id.edit_amount);

                // 闪电平仓会将未平的扯单，变可平
                marketAmountTV.setText(String.valueOf(Math.abs(Double.valueOf(mHoldOrderBean.getTotal()))));
                marketAmountTV.setFilters(new InputFilter[]{amountPointFilter});
                ((TextView) mContentView.findViewById(R.id.btn_close_order)).setBackgroundColor(colorOfOpposit);
                mContentView.findViewById(R.id.btn_close_order).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        SPEx.set(AppData.SPKEY.QUICK_CLOSE_CONFIRM,!((CheckBox)mContentView.findViewById(R.id.need_confirm_checkbox)).isChecked());
                        FuturesApi.closeFuturesPromptly(mHoldOrderBean.getExchangeId(), mHoldOrderBean.getSymbolId(), mHoldOrderBean.getIsLong(), new SimpleResponseListener<FuturesOrderResponse>() {
                            @Override
                            public void onBefore() {
                                super.onBefore();
                                if (mContext instanceof BaseActivity)
                                    ((BaseActivity) mContext).showProgressDialog("", "");
                            }

                            @Override
                            public void onFinish() {
                                super.onFinish();
                                if (mContext instanceof BaseActivity)
                                    ((BaseActivity) mContext).dismissProgressDialog();
                            }

                            @Override
                            public void onSuccess(FuturesOrderResponse response) {
                                super.onSuccess(response);
                                if (CodeUtils.isSuccess(response, true)) {
                                    ToastUtils.showShort(mContext, mContext.getString(R.string.string_create_order_success));

                                    if (bottomDialog != null && bottomDialog.isShowing()) {
                                        bottomDialog.dismiss();
                                    }
                                    if (mDialogObserver != null) {
                                        mDialogObserver.onReqHttpSuccess();
                                    }
                                }
                            }

                            @Override
                            public void onError(Throwable error) {
                                super.onError(error);
                                ToastUtils.showShort(mContext, mContext.getString(R.string.string_create_order_failed));
                            }
                        });

                    }
                });
            }
        } catch (Exception e) {
        }

    }

    private void setShadow(View view) {
        ShadowDrawable.setShadowDrawable(view,
//                getResources().getColor(R.color.white),
                PixelUtils.dp2px(2),
                SkinColorUtil.getDark10(mContext),
                PixelUtils.dp2px(2),
                0,
                PixelUtils.dp2px(1));
    }
}
