/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: TradeFragment.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.trade.ui;

import android.content.DialogInterface;
import android.os.Bundle;
import android.text.InputFilter;
import android.text.TextUtils;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.flyco.tablayout.SegmentTabLayout;
import com.flyco.tablayout.listener.OnTabSelectListener;
import com.google.android.material.tabs.TabLayout;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;
import io.bhex.app.R;
import io.bhex.app.account.ui.HistoryEntrustOrderFragment;
import io.bhex.app.main.ui.MainActivity;
import io.bhex.app.market.ui.CoinDialogFragment;
import io.bhex.app.trade.adapter.HistoryPlanOrdersAdapter;
import io.bhex.app.trade.adapter.OpenOrdersAdapter;
import io.bhex.app.trade.adapter.PlanOrdersAdapter;
import io.bhex.app.trade.presenter.TradeFragmentPresenter;
import io.bhex.app.trade.utils.TradeUtil;
import io.bhex.app.utils.AnimalUtils;
import io.bhex.app.utils.CoinUtils;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.DialogUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.view.ShadowDrawable;
import io.bhex.app.view.alertview.AlertView;
import io.bhex.app.view.alertview.OnItemClickListener;
import io.bhex.app.view.menu.MenuItem;
import io.bhex.app.view.menu.MenuView;
import io.bhex.app.view.pop.FitPopupWindow;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.EventLogin;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.data_manager.MMKVManager;
import io.bhex.sdk.data_manager.RateDataManager;
import io.bhex.sdk.enums.ACCOUNT_TYPE;
import io.bhex.sdk.enums.COIN_TYPE;
import io.bhex.sdk.enums.ORDER_TYPE;
import io.bhex.sdk.quote.QuoteApi;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.trade.bean.OrderBean;
import io.bhex.sdk.trade.bean.PlanOrderBean;

/**
 * ================================================
 * 描   述：币币交易
 * ================================================
 */

public class TradeFragment extends BaseTradeFragment<TradeFragmentPresenter, TradeFragmentPresenter.TradeFragmentUI> implements TradeFragmentPresenter.TradeFragmentUI {

    private static final String TAG = "TradeFragment";
    private TextView tradeTotalMoney;
    private TextView tradeTotalMoneyTitle;
    private ArrayList<Pair<String, Fragment>> items;


    private OpenOrdersAdapter openOrdersAdapter;
    private HistoryEntrustOrderFragment.HistoryEntrustAdapter historyEntrustAdapter;
    private PlanOrdersAdapter planOrdersAdapter;
    private HistoryPlanOrdersAdapter historyPlanOrderAdapter;

    private List<OrderBean> currentOrderDatas = new ArrayList<>();
    private List<OrderBean> historyOrderDatas = new ArrayList<>();
    private List<PlanOrderBean> planOrderDatas = new ArrayList<>();
    private List<PlanOrderBean> historyPlanOrderDatas = new ArrayList<>();
    private boolean isShowCumulativeVolume = false;
    private String forPrice;
    private TextView tradeTotalTitle;

    protected View triggerPriceRela;
    protected EditText triggerPrice;
    protected TextView triggerPriceUnit;
    protected View placeOrderModeRela;
    protected TextView placeOrderMode;
    protected View placeOrderModeArrow;
    private SegmentTabLayout secondTab;

    //默认显示当前委托tab
    private int currentShowOrdersTab = ORDER_TYPE.ORDER_TYPE_GENERAL_ENTRUSTMENT.getOrderType();//默认显示当前委托tab
    private int currentHistoryOrdersTab = ORDER_TYPE.ORDER_TYPE_HISTOREY_GENERAL_ENTRUSTMENT.getOrderType();


    @Override
    protected TradeFragmentPresenter.TradeFragmentUI getUI() {
        return this;
    }

    @Override
    protected TradeFragmentPresenter createPresenter() {
        return new TradeFragmentPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_coin_trade_layout, null, false);
    }

    @Override
    protected void initInstanceView() {
        topBar.setTitleOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                FragmentManager childFragmentManager = getFragmentManager();
                CoinDialogFragment coinDialogFragment = new CoinDialogFragment(coinPairBean, new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialog) {
                        AnimalUtils.rotateyAnimRun(topBar.getTitleIcon(), 180.0f, 0.0f);
                        getPresenter().getTicker();
                    }
                });

                coinDialogFragment.show(childFragmentManager, "dialog");
                QuoteApi.UnSubTickers();
                AnimalUtils.rotateyAnimRun(topBar.getTitleIcon(), 0.0f, 180.0f);

            }
        });
        //下单模式
        placeOrderModeRela = headerView.findViewById(R.id.placeOrderModeRela);
        placeOrderModeRela.setVisibility(View.VISIBLE);
        placeOrderMode = headerView.findViewById(R.id.placeOrderMode);
        placeOrderModeArrow = headerView.findViewById(R.id.placeOrderModeArrow);
        // 触发价
        triggerPriceRela = headerView.findViewById(R.id.trigger_price_rela);
        triggerPrice = headerView.findViewById(R.id.trigger_price);
        triggerPriceUnit = headerView.findViewById(R.id.trigger_price_unit);


        headerView.findViewById(R.id.trade_money_ll).setVisibility(View.VISIBLE);
        headerView.findViewById(R.id.option_money_ll).setVisibility(View.GONE);
        tradeTotalTitle = headerView.findViewById(R.id.trade_total_amount_title);
        tradeTotalMoneyTitle = headerView.findViewById(R.id.trade_total_money_title);
        tradeTotalMoney = headerView.findViewById(R.id.trade_total_money);

        openOrdersAdapter = new OpenOrdersAdapter(currentOrderDatas);
        //openOrdersAdapter.showCoinPairName(false);
        openOrdersAdapter.isFirstOnly(false);
        //openOrdersAdapter.addHeaderView(headerView);

        isShowCumulativeVolume = MMKVManager.getInstance().loadBookQuantityShowMode(COIN_TYPE.COIN_TYPE_BB.getCoinType());


        LayoutInflater layoutInflater = LayoutInflater.from(getActivity());
        View emptyView = layoutInflater.inflate(R.layout.empty_layout, refreshLayout, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
//        layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
        emptyView.setLayoutParams(layoutParams);
        openOrdersAdapter.setHeaderFooterEmpty(true, true);
        openOrdersAdapter.setEmptyView(emptyView);

        openOrdersAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                if (view.getId() == R.id.revoke_order && adapter.getData() != null && position < adapter.getData().size()) {
                    OrderBean itemModel = (OrderBean) adapter.getData().get(position);
                    getPresenter().cancelOrder(itemModel.getOrderId());
                }
            }
        });
        recyclerView.setAdapter(openOrdersAdapter);
        historyEntrustAdapter = new HistoryEntrustOrderFragment.HistoryEntrustAdapter(getActivity(), historyOrderDatas);
        historyEntrustAdapter.isFirstOnly(false);
        //historyEntrustAdapter.addHeaderView(headerView);

        View emptyView2 = layoutInflater.inflate(R.layout.empty_layout, refreshLayout, false);
        ViewGroup.LayoutParams layoutParams2 = emptyView2.getLayoutParams();
        layoutParams2.height = PixelUtils.dp2px(200);
        emptyView2.setLayoutParams(layoutParams2);
        historyEntrustAdapter.setHeaderFooterEmpty(true, true);
        historyEntrustAdapter.setEmptyView(emptyView2);
        historyEntrustAdapter.setOnLoadMoreListener(this, recyclerView);
        historyEntrustAdapter.setEnableLoadMore(true);

        planOrdersAdapter = new PlanOrdersAdapter(planOrderDatas);
        planOrdersAdapter.isFirstOnly(false);
        //historyEntrustAdapter.addHeaderView(headerView);

        View emptyView3 = layoutInflater.inflate(R.layout.empty_layout, refreshLayout, false);
        ViewGroup.LayoutParams layoutParams3 = emptyView3.getLayoutParams();
        layoutParams3.height = PixelUtils.dp2px(200);
        emptyView3.setLayoutParams(layoutParams3);
        planOrdersAdapter.setHeaderFooterEmpty(true, true);
        planOrdersAdapter.setEmptyView(emptyView3);
        planOrdersAdapter.setEnableLoadMore(false);
        planOrdersAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                if (view.getId() == R.id.revoke_order && adapter.getData() != null && position < adapter.getData().size()) {
                    PlanOrderBean itemModel = (PlanOrderBean) adapter.getData().get(position);
                    getPresenter().cancelPlanOrder(itemModel.getOrderId());
                }
            }
        });

        historyPlanOrderAdapter = new HistoryPlanOrdersAdapter(historyPlanOrderDatas, ACCOUNT_TYPE.ASSET_WALLET.getType());
        historyPlanOrderAdapter.isFirstOnly(false);
        //historyEntrustAdapter.addHeaderView(headerView);

        View emptyView4 = layoutInflater.inflate(R.layout.empty_layout, refreshLayout, false);
        ViewGroup.LayoutParams layoutParams4 = emptyView4.getLayoutParams();
        layoutParams4.height = PixelUtils.dp2px(200);
        emptyView4.setLayoutParams(layoutParams4);
        historyPlanOrderAdapter.setHeaderFooterEmpty(true, true);
        historyPlanOrderAdapter.setEmptyView(emptyView4);
        historyPlanOrderAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                getPresenter().getHistoryPlanOrders(true);
            }
        }, recyclerView);
        historyPlanOrderAdapter.setEnableLoadMore(true);

        balanceAvailableAboutTx.setVisibility(View.VISIBLE);
        btnTransfer.setVisibility(View.GONE);
        ShadowDrawable.setShadow(triggerPriceRela);
        triggerPrice.setFilters(new InputFilter[]{pricePointFilter});
        placeOrderModeControl(PLACE_ORDER_MODE_ORDINARY);
        secondTab = viewFinder.find(R.id.secondTab);
        if (coinPairBean==null||!coinPairBean.isAllowPlan()) {
            placeOrderMode.setText(getString(R.string.string_ordinary_order));
            placeOrderModeControl(PLACE_ORDER_MODE_ORDINARY);
        }
        initTabs();
    }

    private void initTabs() {
        items = new ArrayList<>();
        items.add(new Pair<String, Fragment>(getString(R.string.string_ordinary_order), null));
        items.add(new Pair<String, Fragment>(getString(R.string.string_trigger_order), null));
        items.add(new Pair<String, Fragment>(getString(R.string.string_history_entrust), null));
        secondTab.setTabData(new String[]{getString(R.string.string_ordinary_order),getString(R.string.string_trigger_order)});

        OrderAdapter adapter = new OrderAdapter(getChildFragmentManager());
        ViewPager viewPager = headerView.findViewById(R.id.clViewPager);
        viewPager.setAdapter(adapter);
        tabLayout.setupWithViewPager(viewPager);
//        tab.setTabTextColors(getResources().getColor(R.color.color_white),getResources().getColor(R.color.color_black));
        tabLayout.setTabMode(TabLayout.MODE_SCROLLABLE);
        tabLayout.setTabGravity(TabLayout.GRAVITY_CENTER);

        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                if (position == 0) {
                    currentShowOrdersTab=ORDER_TYPE.ORDER_TYPE_GENERAL_ENTRUSTMENT.getOrderType();
                    viewFinder.find(R.id.secondTabLinear).setVisibility(View.GONE);
                    /*historyEntrustAdapter.removeAllHeaderView();
                    openOrdersAdapter.addHeaderView(headerView);
                    */
                    recyclerView.setAdapter(openOrdersAdapter);
                    getPresenter().switchOrderList(currentShowOrdersTab);
                    showOrderOperateViews(true);
                } else if (position == 1) {
                    currentShowOrdersTab=ORDER_TYPE.ORDER_TYPE_PLANNING_ENTRUSTMENT.getOrderType();
                    viewFinder.find(R.id.secondTabLinear).setVisibility(View.GONE);
                    recyclerView.setAdapter(planOrdersAdapter);
                    getPresenter().switchOrderList(currentShowOrdersTab);
                    getPresenter().getPlanCurrentOrders();
                    showOrderOperateViews(true);
                } else if (position == 2) {
                    viewFinder.find(R.id.secondTabLinear).setVisibility(View.VISIBLE);
                    showOrderOperateViews(false);
                    if (currentHistoryOrdersTab == ORDER_TYPE.ORDER_TYPE_HISTOREY_GENERAL_ENTRUSTMENT.getOrderType()) {
                        currentShowOrdersTab=ORDER_TYPE.ORDER_TYPE_HISTOREY_GENERAL_ENTRUSTMENT.getOrderType();
                        recyclerView.setAdapter(historyEntrustAdapter);
                        getPresenter().switchOrderList(currentShowOrdersTab);
                        getPresenter().getHistoryEntrustOrders(false);
                    } else {
                        currentShowOrdersTab=ORDER_TYPE.ORDER_TYPE_HISTOREY_PLANNING_ENTRUSTMENT.getOrderType();
                        recyclerView.setAdapter(historyPlanOrderAdapter);
                        getPresenter().switchOrderList(currentShowOrdersTab);
                        getPresenter().getHistoryPlanOrders(false);
                    }
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        CommonUtil.setUpIndicatorWidthByReflex(tabLayout, 15, 15);
        secondTab.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                if (position ==0) {
                    currentHistoryOrdersTab = ORDER_TYPE.ORDER_TYPE_HISTOREY_GENERAL_ENTRUSTMENT.getOrderType();
                    currentShowOrdersTab=ORDER_TYPE.ORDER_TYPE_HISTOREY_GENERAL_ENTRUSTMENT.getOrderType();
                    recyclerView.setAdapter(historyEntrustAdapter);
                    getPresenter().switchOrderList(currentHistoryOrdersTab);
                    //普通委托
                    getPresenter().getHistoryEntrustOrders(false);
                }else if(position ==1){
                    currentHistoryOrdersTab = ORDER_TYPE.ORDER_TYPE_HISTOREY_PLANNING_ENTRUSTMENT.getOrderType();
                    currentShowOrdersTab=ORDER_TYPE.ORDER_TYPE_HISTOREY_PLANNING_ENTRUSTMENT.getOrderType();
                    recyclerView.setAdapter(historyPlanOrderAdapter);
                    getPresenter().switchOrderList(currentHistoryOrdersTab);
                    //计划委托
                    getPresenter().getHistoryPlanOrders(false);
                }
            }

            @Override
            public void onTabReselect(int position) {

            }
        });
    }

    private class OrderAdapter extends FragmentPagerAdapter {

        public OrderAdapter(FragmentManager fm) {
            super(fm);
        }

        @Override
        public Fragment getItem(int position) {
            return items.get(position).second;
        }

        @Override
        public int getCount() {
            return items.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return items.get(position).first;
        }


    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (coinPairBean == null) {
            CoinPairBean bbTradeCoin = CoinUtils.getBBTradeCoin();
            if (bbTradeCoin != null) {
                coinPairBean = bbTradeCoin;
            }else{
                coinPairBean = AppConfigManager.GetInstance().getDefaultTradeCoinPair();
            }
        }

        EventBus.getDefault().register(this);
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public synchronized void onMessageEvent(CoinPairBean postCoinPairBean) {
        if (postCoinPairBean == null)
            return;
        if (KlineUtils.isSymbolOfBB(postCoinPairBean.getCoinType())) {
            coinPairBean = postCoinPairBean;
            String oldSymbol = symbol;
            boolean needSwitchTradeTab = postCoinPairBean.isNeedSwitchTradeTab();
//        ToastUtils.showShort("Message "+needSwitchTradeTab);
            if (!needSwitchTradeTab) {
                if (!isFirst) {
                    return;
                }
            }
            isFirst = false;
            if (isBuyMode != postCoinPairBean.isBuyMode()) {
                if (postCoinPairBean.isBuyMode()) {
                    AnimalUtils.transAnimRun(buySellTabBg, buySellTabBg.getWidth(), 0);
                } else {
                    AnimalUtils.transAnimRun(buySellTabBg, 0, buySellTabBg.getWidth());
                }
            }
            loadDefaultConfig(postCoinPairBean);
            if (!postCoinPairBean.isAllowPlan()) {
                placeOrderMode.setText(getString(R.string.string_ordinary_order));
                placeOrderModeControl(PLACE_ORDER_MODE_ORDINARY);
            }
            if (!oldSymbol.equals(postCoinPairBean.getSymbolId())) {
                switchBuySellTab(isBuyMode);
                updateUnit();
                getPresenter().resetAllData(postCoinPairBean);
            }
        }

    }
    @Override
    public void loadDefaultConfig(CoinPairBean postCoinPairBean) {
        super.loadDefaultConfig(postCoinPairBean);
        triggerPrice.setText("");
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public synchronized void onMessageEvent(EventLogin eventLogin) {
        switchBuySellTab(isBuyMode);
    }


    @Override
    public void onResume() {
        super.onResume();
        ShadowDrawable.setShadow(triggerPriceRela);

        if (!UserInfo.isLogin()) {
            if (currentOrderDatas.size() > 0) {
                currentOrderDatas.clear();
                if (openOrdersAdapter != null) {
                    openOrdersAdapter.setNewData(currentOrderDatas);
                }
            }
            if (planOrderDatas.size() > 0) {
                planOrderDatas.clear();
                if (planOrdersAdapter != null) {
                    planOrdersAdapter.setNewData(planOrderDatas);
                }
            }
            if (historyOrderDatas.size() > 0) {
                historyOrderDatas.clear();
                if (historyEntrustAdapter != null) {
                    historyEntrustAdapter.setNewData(historyOrderDatas);
                }
            }

            if (historyPlanOrderDatas.size() > 0) {
                historyPlanOrderDatas.clear();
                if (historyPlanOrderAdapter != null) {
                    historyPlanOrderAdapter.setNewData(historyPlanOrderDatas);
                }
            }
        }
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        bookTitleAmount.setOnClickListener(this);
        headerView.findViewById(R.id.placeOrderModeRela).setOnClickListener(this);
    }

    @Override
    public void onPause() {
        super.onPause();

    }

    @Override
    protected void onVisibleChanged(boolean visible) {
        super.onVisibleChanged(visible);
        if (isVisible()) {
            isShowCumulativeVolume = MMKVManager.getInstance().loadBookQuantityShowMode(COIN_TYPE.COIN_TYPE_BB.getCoinType());
            setBookTitleAmount();

            if (!UserInfo.isLogin()) {
                balanceAvailableTx.setText(getString(R.string.string_placeholder));
                currentOrderDatas.clear();
                if (openOrdersAdapter != null) {
                    openOrdersAdapter.notifyDataSetChanged();
                }
                historyOrderDatas.clear();
                if (historyEntrustAdapter != null) {
                    historyEntrustAdapter.notifyDataSetChanged();
                }
                planOrderDatas.clear();
                if (planOrdersAdapter != null) {
                    planOrdersAdapter.notifyDataSetChanged();
                }

                if (historyPlanOrderDatas.size() > 0) {
                    historyPlanOrderDatas.clear();
                    if (planOrdersAdapter != null) {
                        planOrdersAdapter.notifyDataSetChanged();
                    }
                }
                return;
            }
        }

    }


    @Override
    public void onClick(View v) {
        super.onClick(v);
        switch (v.getId()) {
            case R.id.title_amount:
                //ToastUtils.showShort("==showSelectBookQuantityTypeAlert==");
                showSelectBookQuantityTypeAlert();
                //showSelectBookQuantityTypeMenu();
                break;

            case R.id.placeOrderModeRela:
                //下单模式选择 普通委托/计划委托
                showPlaceOrderModeSelect();
                break;
        }
    }

    private int mPlaceOrderMode;

    /**
     * 下单委托模式UI控制
     *
     * @param placeOrderMode
     */
    protected void placeOrderModeControl(int placeOrderMode) {
        mPlaceOrderMode = placeOrderMode;
        if (placeOrderMode == PLACE_ORDER_MODE_ORDINARY) {
            //普通委托
            triggerPriceRela.setVisibility(View.GONE);
        } else if (placeOrderMode == PLACE_ORDER_MODE_PLANNING) {
            //计划委托
            triggerPriceRela.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 下单模式选择 普通委托/计划委托
     */
    private void showPlaceOrderModeSelect() {
        closeKeyBoard(editAmount);
        ArrayList<String> placeOrderModeList = new ArrayList<>();
        placeOrderModeList.add(getString(R.string.string_ordinary_order));
        if (coinPairBean!=null &&coinPairBean.isAllowPlan()) {
            placeOrderModeList.add(getString(R.string.string_trigger_order));
        }
        List<String> selectArr = new ArrayList<>();
        selectArr.add(mPlaceOrderMode == PLACE_ORDER_MODE_PLANNING?getString(R.string.string_trigger_order):getString(R.string.string_ordinary_order));

        AlertView.showSheet(getActivity(), selectArr,placeOrderModeList, null, null, getString(R.string.string_cancel), new AlertView.DialogListener() {
            @Override
            public void onShow(final AlertView alert) {
                ((MainActivity) getActivity()).registerBackKeyListener(new MainActivity.KeyBackListener() {
                    @Override
                    public void onKeyBack() {
                        alert.dismiss();
                    }
                });
            }

            @Override
            public void onDissmiss(AlertView alert) {
                ((MainActivity) getActivity()).removeBackKeyListener();
            }

            @Override
            public void onItemClick(int position, String item) {
                if (position == -1) {
                    return;
                }
                placeOrderMode.setText(item);
                placeOrderModeControl(position);

            }
        });
    }

    private void showSelectBookQuantityTypeMenu() {
        MenuView mTopRightMenu = new MenuView(getActivity());
        List<MenuItem> menuItems = new ArrayList<>();
        menuItems.add(new MenuItem(getString(R.string.string_cumulative_quantity_format, baseTokenName)));
        menuItems.add(new MenuItem(getString(R.string.string_amount_format, baseTokenName)));
        mTopRightMenu
                .setHeight(RecyclerView.LayoutParams.WRAP_CONTENT)
                .setWidth(RecyclerView.LayoutParams.WRAP_CONTENT)
                .showIcon(false)
                .dimBackground(true)
                .needAnimationStyle(true)
                .setAnimationStyle(R.style.TRM_ANIM_STYLE)
                .addMenuList(menuItems)
                .setOnMenuItemClickListener(position -> {
                    if (position == 0) {
                        isShowCumulativeVolume = true;
                        setBookTitleAmount();
                        getPresenter().changeBookQuantityShowMode(isShowCumulativeVolume);
                        MMKVManager.getInstance().saveBookQuantityShowMode(isShowCumulativeVolume, COIN_TYPE.COIN_TYPE_BB.getCoinType());
                    } else if (position == 1) {
                        isShowCumulativeVolume = false;
                        setBookTitleAmount();
                        getPresenter().changeBookQuantityShowMode(isShowCumulativeVolume);
                        MMKVManager.getInstance().saveBookQuantityShowMode(isShowCumulativeVolume, COIN_TYPE.COIN_TYPE_BB.getCoinType());
                    }
                })
                .showAsDropDown(viewFinder.find(R.id.title_amount), 0 - PixelUtils.dp2px(64), 0);
    }

    private void showSelectBookQuantityTypeAlert() {
        String[] quantityTypeArray = new String[]{getString(R.string.string_cumulative_quantity_format, baseTokenName),getString(R.string.string_amount_format, baseTokenName)};
        AlertView alertView = new AlertView(null, null, getString(R.string.string_cancel), new String[] {isShowCumulativeVolume?getString(R.string.string_cumulative_quantity_format, baseTokenName):getString(R.string.string_amount_format, baseTokenName)},quantityTypeArray, getActivity(), AlertView.Style.ActionSheet, new OnItemClickListener() {
            @Override
            public void onItemClick(Object o, int position) {
                if (position == 0) {
                    isShowCumulativeVolume = true;
                    setBookTitleAmount();
                    getPresenter().changeBookQuantityShowMode(isShowCumulativeVolume);
                    MMKVManager.getInstance().saveBookQuantityShowMode(isShowCumulativeVolume, COIN_TYPE.COIN_TYPE_BB.getCoinType());
                } else if (position == 1) {
                    isShowCumulativeVolume = false;
                    setBookTitleAmount();
                    getPresenter().changeBookQuantityShowMode(isShowCumulativeVolume);
                    MMKVManager.getInstance().saveBookQuantityShowMode(isShowCumulativeVolume, COIN_TYPE.COIN_TYPE_BB.getCoinType());
                }
            }
        });
        alertView.show();
    }

    private void setBookTitleAmount() {
        bookTitleAmount.setText(getString(isShowCumulativeVolume ? R.string.string_cumulative_quantity_format : R.string.string_amount_format, baseTokenName));
    }


    private void showSelectBookQuantityType() {
        View selectQuantityTypeView = LayoutInflater.from(getActivity()).inflate(R.layout.pop_select_book_quantity_type, null);
        FitPopupWindow selectQuantityTypePopupWindow = new FitPopupWindow(getActivity(), WindowManager.LayoutParams.WRAP_CONTENT, WindowManager.LayoutParams.WRAP_CONTENT);


        TextView cumulateQuantity = selectQuantityTypeView.findViewById(R.id.cumulateQuantity);
        TextView quantity = selectQuantityTypeView.findViewById(R.id.quantity);

        cumulateQuantity.setText(getString(R.string.string_cumulative_quantity_format, baseTokenName));
        quantity.setText(getString(R.string.string_amount_format, baseTokenName));

        cumulateQuantity.setBackgroundColor(getResources().getColor(isShowCumulativeVolume ? R.color.color_bg_2 : R.color.color_bg_1));
        quantity.setBackgroundColor(getResources().getColor(isShowCumulativeVolume ? R.color.color_bg_1 : R.color.color_bg_2));

        cumulateQuantity.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                isShowCumulativeVolume = true;
                bookTitleAmount.setText(getString(isShowCumulativeVolume ? R.string.string_cumulative_quantity_format : R.string.string_amount_format, baseTokenName));
                selectQuantityTypePopupWindow.dismiss();
            }
        });

        quantity.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                isShowCumulativeVolume = false;
                bookTitleAmount.setText(getString(isShowCumulativeVolume ? R.string.string_cumulative_quantity_format : R.string.string_amount_format, baseTokenName));
                selectQuantityTypePopupWindow.dismiss();
            }
        });

        selectQuantityTypePopupWindow.setView(selectQuantityTypeView, bookTitleAmount);
        selectQuantityTypePopupWindow.show();
    }

    @Override
    public boolean isShowCumulativeVolume() {
        return isShowCumulativeVolume;
    }

    @Override
    protected void updateTradeAmountOfMoney() {
        if (!isLimitedPrice) {
            return;
        }
        String amount = editAmount.getText().toString();
//        String price = editPrice.getText().toString().trim();
        String price = priceLimitedView.getPrice();
        if (TextUtils.isEmpty(price) || TextUtils.isEmpty(amount)) {
            tradeTotalMoneyTitle.setText(getString(R.string.string_placeholder));
            tradeTotalMoney.setText(getString(R.string.string_placeholder));
            return;
        }
        double result = NumberUtils.mul(price, amount);
        String value = NumberUtils.roundFormatDown(String.valueOf(result), digitAmount);
        String legalMoney = RateDataManager.CurRatePrice(quoteToken, value);
        legalMoney = RateDataManager.getShowLegalMoney(legalMoney, AppData.DIGIT_LEGAL_MONEY);
        tradeTotalMoneyTitle.setText(value + quoteTokenName);
        tradeTotalMoney.setText("≈" + legalMoney);
//        tradeTotalMoney.setText(getString(R.string.string_amount_of_money_format, value, quoteTokenName, legalMoney));

    }

    @Override
    protected void switchPriceMode(boolean isLimitedPriceParams) {
        isLimitedPrice = isLimitedPriceParams;
        editAmount.setText("");
        if (isLimitedPrice) {
            priceMode.setText(getString(R.string.string_limited_price));
            priceAbout.setVisibility(View.VISIBLE);
            tradeTotalMoney.setVisibility(View.VISIBLE);
            tradeTotalMoneyTitle.setVisibility(View.VISIBLE);
            tradeTotalTitle.setVisibility(View.VISIBLE);
        } else {
            priceMode.setText(getString(R.string.string_market_price));
            priceAbout.setVisibility(View.INVISIBLE);
            tradeTotalMoney.setVisibility(View.INVISIBLE);
            tradeTotalMoneyTitle.setVisibility(View.INVISIBLE);
            tradeTotalTitle.setVisibility(View.INVISIBLE);
        }

        updatePriceModeAssociatedView(isLimitedPrice);
        updateUnit();
    }

    @Override
    public void showOpenOrders(List<OrderBean> datas) {
        if (currentShowOrdersTab != ORDER_TYPE.ORDER_TYPE_GENERAL_ENTRUSTMENT.getOrderType()) {
            return;
        }
        if (!UserInfo.isLogin()) {
            if (!currentOrderDatas.isEmpty()) {
                currentOrderDatas.clear();
                openOrdersAdapter.notifyDataSetChanged();
            }
            return;
        }
        if (datas != null) {
            currentOrderDatas.clear();
            currentOrderDatas.addAll(datas);
            openOrdersAdapter.notifyDataSetChanged();
        }

        loadEntrustOrdersPriceIntoCache(datas);
    }

    private String bidPrice = "";
    private String askPrice = "";
    private void loadEntrustOrdersPriceIntoCache(List<OrderBean> datas) {
        bidPrice = "";
        askPrice = "";
        if (datas != null) {
            for (OrderBean data : datas) {
                String price = data.getPrice();
                price = NumberUtils.stripTrailingZeros(price);
                if (KlineUtils.isBuyOrder(getActivity(), data.getSide())) {
                    bidPrice = bidPrice + price + ";";
                } else {
                    askPrice = askPrice + price + ";";
                }
            }
        }
    }

    public boolean isCurrentBidOrderPrice(String price) {
        price = NumberUtils.stripTrailingZeros(price);
        return bidPrice.contains(price + ";");
    }

    public boolean isCurrentAskOrderPrice(String price) {
        price = NumberUtils.stripTrailingZeros(price);
        return askPrice.contains(price + ";");
    }

    @Override
    public void showHistoryOrders(List<OrderBean> datas) {
        if (currentShowOrdersTab != ORDER_TYPE.ORDER_TYPE_HISTOREY_GENERAL_ENTRUSTMENT.getOrderType()) {
            return;
        }
        if (!UserInfo.isLogin()) {
            if (!historyOrderDatas.isEmpty()) {
                historyOrderDatas.clear();
                historyEntrustAdapter.notifyDataSetChanged();
            }
            return;
        }
        if (datas != null) {
            historyOrderDatas.clear();
            historyOrderDatas.addAll(datas);
            historyEntrustAdapter.notifyDataSetChanged();
        }
    }
    @Override
    public void showHistoryPlanOrders(List<PlanOrderBean> datas) {
        if (currentShowOrdersTab != ORDER_TYPE.ORDER_TYPE_HISTOREY_PLANNING_ENTRUSTMENT.getOrderType()) {
            return;
        }
        if (!UserInfo.isLogin()) {
            if (!historyPlanOrderDatas.isEmpty()) {
                historyPlanOrderDatas.clear();
                historyPlanOrderAdapter.notifyDataSetChanged();
            }
            return;
        }
        if (datas != null) {
            historyPlanOrderDatas.clear();
            historyPlanOrderDatas.addAll(datas);
            historyPlanOrderAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public int getCurrentTabType() {
        return currentShowOrdersTab;
    }

    @Override
    public void loadMorePlanOrderComplete() {
        if (historyPlanOrderAdapter != null) {
            historyPlanOrderAdapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMorePlanOrderFailed() {
        if (historyPlanOrderAdapter != null) {
            historyPlanOrderAdapter.loadMoreFail();
        }
    }

    @Override
    public void loadPlanOrderEnd() {
        if (historyPlanOrderAdapter != null) {
            historyPlanOrderAdapter.loadMoreEnd();
        }
    }

    @Override
    public void showPlanOrders(List<PlanOrderBean> datas) {
        if (currentShowOrdersTab != ORDER_TYPE.ORDER_TYPE_PLANNING_ENTRUSTMENT.getOrderType()) {
            return;
        }
        if (!UserInfo.isLogin()) {
            if (!planOrderDatas.isEmpty()) {
                planOrderDatas.clear();
                planOrdersAdapter.notifyDataSetChanged();
            }
            return;
        }
        if (datas != null) {
            planOrderDatas.clear();
            planOrderDatas.addAll(datas);
            planOrdersAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void onLoadMoreRequested() {
        getPresenter().loadMore();
    }

    @Override
    public void loadMoreComplete() {
        if (historyEntrustAdapter != null) {
            historyEntrustAdapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {
        if (historyEntrustAdapter != null) {
            historyEntrustAdapter.loadMoreFail();
        }
    }

    @Override
    public void loadEnd() {
        if (historyEntrustAdapter != null) {
            historyEntrustAdapter.loadMoreEnd();
        }
    }

    @Override
    protected void updatePriceModeAssociatedView(boolean isLimitedPrice) {
        if (isLimitedPrice) {
            priceLimitedView.setVisibility(View.VISIBLE);
            priceMarketTx.setVisibility(View.GONE);
        } else {
            priceLimitedView.setVisibility(View.GONE);
            priceMarketTx.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected void updateUnit() {
        bookTitleAmount.setText(getString(isShowCumulativeVolume ? R.string.string_cumulative_quantity_format : R.string.string_amount_format, baseTokenName));
        bookTitlePrice.setText(getString(R.string.string_price_ph, quoteTokenName));

        triggerPriceUnit.setText(quoteTokenName);

        if (isBuyMode) {
            //买入
            if (isLimitedPrice) {
                //限价
                isQuoteToken = false;
                editAmount.setHint(getResources().getString(R.string.string_amount));
                editAmountUnit.setText(baseTokenName);
                updateAsset(quoteToken, quoteTokenName);
                updateStepViewRange(false, baseToken, baseTokenName);
            } else {
                //市价
                isQuoteToken = true;
                editAmount.setHint(getResources().getString(R.string.string_amount_of_money));
                editAmountUnit.setText(quoteTokenName);
                updateAsset(quoteToken, quoteTokenName);
                updateStepViewRange(true, quoteToken, quoteTokenName);
            }
        } else {
            //卖出
            if (isLimitedPrice) {
                //限价
                isQuoteToken = false;
                editAmount.setHint(getResources().getString(R.string.string_amount));
                editAmountUnit.setText(baseTokenName);
                updateAsset(baseToken, baseTokenName);
                updateStepViewRange(false, baseToken, baseTokenName);
            } else {
                //市价
                isQuoteToken = true;
                editAmount.setHint(getResources().getString(R.string.string_amount));
                editAmountUnit.setText(baseTokenName);
                updateAsset(baseToken, baseTokenName);
                updateStepViewRange(false, baseToken, baseTokenName);
            }
        }
    }

    @Override
    protected void updateStepViewRange(boolean isQuoteToken, String token, String tokenName) {
        if (isBuyMode) {//买入
            stepViewMaxValue = getQuoteTokenAsset();
            if (isLimitedPrice) {//限价
//                String price = editPrice.getText().toString().trim();
                String price = priceLimitedView.getPrice();
                if (!TextUtils.isEmpty(stepViewMaxValue) && !TextUtils.isEmpty(price)) {
//                    stepViewMaxValue = NumberUtils.div(price, getQuoteTokenAsset(), digitPrice) + "";
//                    stepViewMaxValue = NumberUtils.div(price, getQuoteTokenAsset(), AppData.DIGIT_MAX_NUM) + "";
                    stepViewMaxValue = NumberUtils.roundFormatDown(NumberUtils.div(price, getQuoteTokenAsset()), AppData.DIGIT_MAX_NUM);
                }
            } else {
                //市价 默认的是输入金额 所以最大为可用金额资产
            }
        } else {//卖出
            stepViewMaxValue = getBaseTokenAsset();
        }
        stepViewMinTx.setText("0");
        stepViewMaxTx.setText(getString(R.string.string_stepview_max_txt_format, NumberUtils.roundFormatDown(stepViewMaxValue, isQuoteToken ? digitPrice : digitBase), tokenName));
        updateStepViewValue(editAmount.getText().toString());
    }

    @Override
    protected void createOrder(boolean isBuyMode) {

        String triggerPriceStr = triggerPrice.getText().toString().trim();
        if (mPlaceOrderMode == PLACE_ORDER_MODE_PLANNING) {
            //计划委托
            if (TextUtils.isEmpty(triggerPriceStr)) {
                ToastUtils.showShort(getActivity(), getString(R.string.string_input_trigger_price));
                return;
            }
        } else {
            triggerPriceStr = "";
        }
        final String finalTriggerPrice = triggerPriceStr;

        /**交易下单规则粗略校验**************/
//        String price = editPrice.getText().toString().trim();
        String price = priceLimitedView.getPrice();
        if (isLimitedPrice) {
            if (TextUtils.isEmpty(price)) {
                ToastUtils.showShort(getActivity(), getString(R.string.string_prompt_input_price));
                return;
            }
            if (Double.valueOf(price) <= 0) {
                ToastUtils.showShort(getActivity(), getString(R.string.string_prompt_input_price));
                return;
            }
        }

        String amount = editAmount.getText().toString().trim();
        if (TextUtils.isEmpty(amount)) {
            if (isBuyMode && !isLimitedPrice) {
                ToastUtils.showShort(getActivity(), getString(R.string.string_prompt_input_amount_of_money));
            } else {
                ToastUtils.showShort(getActivity(), getString(R.string.string_prompt_input_amount));
            }
            return;
        }
        if (Double.valueOf(amount) <= 0) {
            if (isBuyMode && !isLimitedPrice) {
                ToastUtils.showShort(getActivity(), getString(R.string.string_prompt_input_amount_of_money));
            } else {
                ToastUtils.showShort(getActivity(), getString(R.string.string_prompt_input_amount));
            }
            return;
        }
        if (coinPairBean != null) {
            //最小交易数量
            String minTradeQuantity = coinPairBean.getMinTradeQuantity();
            //最小交易额
            String minTradeAmount = coinPairBean.getMinTradeAmount();
            if (isLimitedPrice) {//限价
                String minPricePrecision = coinPairBean.getMinPricePrecision();
                //最小交易价格
                if (!TextUtils.isEmpty(minPricePrecision)) {
                    if (NumberUtils.sub(price, minPricePrecision) < 0) {
                        ToastUtils.showShort(getActivity(), getString(R.string.string_min_trade_price, minPricePrecision) + quoteTokenName);
                        return;
                    }
                }

                //最小交易数量
                if (!TextUtils.isEmpty(minTradeQuantity)) {
                    if (NumberUtils.sub(amount, minTradeQuantity) < 0) {
                        ToastUtils.showShort(getActivity(), getString(R.string.string_min_trade_quantity, minTradeQuantity) + baseTokenName);
                        return;
                    }
                }

//                //最小交易额
//                if (!TextUtils.isEmpty(minTradeAmount)) {
//                    if (NumberUtils.sub(String.valueOf(NumberUtils.mul(price, amount)), minTradeAmount) < 0) {
//                        ToastUtils.showShort(getActivity(),getString(R.string.string_min_trade_amount, minTradeAmount)+quoteTokenName);
//                        return;
//                    }
//                }
            } else {//市价
                if (isBuyMode) {
                    //最小交易额
                    if (!TextUtils.isEmpty(minTradeAmount)) {
                        //amount 为输入金额
                        if (NumberUtils.sub(amount, minTradeAmount) < 0) {
                            ToastUtils.showShort(getActivity(), getString(R.string.string_min_trade_amount, minTradeAmount) + quoteTokenName);
                            return;
                        }
                    }
                } else {
                    //最小交易数量
                    if (!TextUtils.isEmpty(minTradeQuantity)) {
                        if (NumberUtils.sub(amount, minTradeQuantity) < 0) {
                            ToastUtils.showShort(getActivity(), getString(R.string.string_min_trade_quantity, minTradeQuantity) + baseTokenName);
                            return;
                        }
                    }
                }

            }
        } else {
            ToastUtils.showShort(getActivity(), getString(R.string.string_retry_select_trade_symbol));
            return;
        }

        if (mPlaceOrderMode != PLACE_ORDER_MODE_PLANNING) {
            /** 最大交易额/量判断 **********/
            if (isBuyMode) {//买入
                if (isLimitedPrice) {//限价
                    //最大交易额
                    if (!TextUtils.isEmpty(getQuoteTokenAsset())) {
                        if (NumberUtils.sub(String.valueOf(NumberUtils.mul(price, amount)), getQuoteTokenAsset()) > 0) {
                            ToastUtils.showShort(getActivity(), getString(R.string.string_balance_not_enough));
                            return;
                        }
                    } else {
                        ToastUtils.showShort(getActivity(), getString(R.string.string_balance_not_enough_swipy_refresh));
                        return;
                    }
                } else {//市价
                    //最大交易额
                    if (!TextUtils.isEmpty(getQuoteTokenAsset())) {
                        //amount 为输入金额
                        if (NumberUtils.sub(amount, getQuoteTokenAsset()) > 0) {
                            ToastUtils.showShort(getActivity(), getString(R.string.string_balance_not_enough));
                            return;
                        }
                    } else {
                        ToastUtils.showShort(getActivity(), getString(R.string.string_balance_not_enough_swipy_refresh));
                        return;
                    }
                }

            } else {//卖出

                //最大交易数量
                if (!TextUtils.isEmpty(getBaseTokenAsset())) {
                    if (NumberUtils.sub(amount, getBaseTokenAsset()) > 0) {
                        ToastUtils.showShort(getActivity(), getString(R.string.string_balance_not_enough));
                        return;
                    }
                } else {
                    ToastUtils.showShort(getActivity(), getString(R.string.string_balance_not_enough_swipy_refresh));
                    return;
                }
            }
        }

        final String finalTriggerPriceStr = triggerPriceStr;
        //买1卖1价差10%提示
        if(checkBookIsException()){
            DialogUtils.showDialog(getActivity(), getString(R.string.string_reminder), getString(R.string.string_book_price_difference_too_large), getString(R.string.string_sure), getString(R.string.string_cancel), false, new DialogUtils.OnButtonEventListener() {
                @Override
                public void onConfirm() {
                    nextStepCreateOrder(isBuyMode, isLimitedPrice, exchangeId, symbol, finalTriggerPriceStr,price, amount);
                }

                @Override
                public void onCancel() {

                }
            });
            return;
        }else{
            nextStepCreateOrder(isBuyMode, isLimitedPrice, exchangeId, symbol,triggerPriceStr, price, amount);
        }

    }

    private void nextStepCreateOrder(boolean isBuyMode, boolean isLimitedPrice, String exchangeId, String symbol,String triggerPriceStr, String price, String amount) {
        if (mPlaceOrderMode == PLACE_ORDER_MODE_PLANNING) {
            if (isLimitedPrice) {
                //判断下单价格是否偏离过多，防止严重亏损
                if (!TradeUtil.checkCreatePlanOrderPrice(isBuyMode, price, triggerPriceStr)) {
                    DialogUtils.showDialog(getActivity(), "", getString(isBuyMode ? R.string.string_buy_price_too_higher_tips : R.string.string_sell_price_lower_tips), getString(R.string.string_sure), getString(R.string.string_cancel), false, new DialogUtils.OnButtonEventListener() {
                        @Override
                        public void onConfirm() {
                            //去下单
                            getPresenter().createPlanOrder(isBuyMode, isLimitedPrice, exchangeId, symbol, price, amount, triggerPriceStr);
                        }

                        @Override
                        public void onCancel() {

                        }
                    });
                    return;
                }
            }
            //计划委托
            getPresenter().createPlanOrder(isBuyMode, isLimitedPrice, exchangeId, symbol, price, amount, triggerPriceStr);
        } else {
            if (isLimitedPrice) {
                //判断下单价格是否偏离过多，防止严重亏损
                if (!TradeUtil.checkCreateOrderPrice(isBuyMode, price, currentTicker)) {
                    DialogUtils.showDialog(getActivity(), "", getString(isBuyMode ? R.string.string_buy_price_too_higher_tips : R.string.string_sell_price_lower_tips), getString(R.string.string_sure), getString(R.string.string_cancel), false, new DialogUtils.OnButtonEventListener() {
                        @Override
                        public void onConfirm() {
                            //去下单
                            getPresenter().createOrder(isBuyMode, isLimitedPrice, exchangeId, symbol, price, amount);
                        }

                        @Override
                        public void onCancel() {

                        }
                    });
                    return;
                }
            }
            getPresenter().createOrder(isBuyMode, isLimitedPrice, exchangeId, symbol, price, amount);
        }
    }
}
