package io.bhex.app.trade.ui;

import android.content.Intent;
import android.os.Bundle;

import androidx.core.util.Pair;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;

import java.io.Serializable;
import java.util.ArrayList;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.market.adapter.FragmentAdapter;
import io.bhex.app.skin.view.SkinTabLayout;
import io.bhex.app.trade.presenter.ContractCalculatorPresenter;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.view.TopBar;
import io.bhex.sdk.quote.bean.CoinPairBean;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-02-21
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class ContractCalculatorActivity extends BaseActivity<ContractCalculatorPresenter, ContractCalculatorPresenter.ContractCalculatorUI> implements ContractCalculatorPresenter.ContractCalculatorUI {
    private TopBar topBar;
    private ViewPager viewPager;
    private SkinTabLayout tab;
    private ArrayList<Pair<String, Fragment>> items;
    private FragmentAdapter fragmentAdapter;
    private CalculatorOfProfitFragment calculatorOfProfitFragment;
    private CalculatorOfLiquidationFragment calculatorOfLiquidationFragment;
    private CoinPairBean symbol;

    @Override
    protected int getContentView() {
        return R.layout.activity_contract_calculator_layout;
    }

    @Override
    protected ContractCalculatorPresenter createPresenter() {
        return new ContractCalculatorPresenter();
    }

    @Override
    protected ContractCalculatorPresenter.ContractCalculatorUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        this.setTopbarNight(topBar);

        viewPager = viewFinder.find(R.id.viewPager);
        tab = viewFinder.find(R.id.tab);
        tab.setTabTextColors(this.getResources().getColor(CommonUtil.isBlackMode() ? R.color.dark_night : R.color.dark),this.getResources().getColor(R.color.blue));
        Intent intent = getIntent();
        if (intent != null) {
           symbol = (CoinPairBean) intent.getSerializableExtra("symbol");
        }
        initFragmentTab();
    }

    private void initFragmentTab() {

        items = new ArrayList<>();
        Bundle bundle = new Bundle();
        bundle.putSerializable("symbol",symbol);
        calculatorOfProfitFragment = new CalculatorOfProfitFragment();
        calculatorOfProfitFragment.setArguments(bundle);
        calculatorOfLiquidationFragment = new CalculatorOfLiquidationFragment();
        calculatorOfLiquidationFragment.setArguments(bundle);

        items.add(new Pair<String, Fragment>(getString(R.string.string_calculate_profit_rate), calculatorOfProfitFragment));
        items.add(new Pair<String, Fragment>(getString(R.string.string_calculate_liquidation_price), calculatorOfLiquidationFragment));
        if (fragmentAdapter == null) {

            fragmentAdapter = new FragmentAdapter(getSupportFragmentManager(),items);
            viewPager.setAdapter(fragmentAdapter);
            tab.setupWithViewPager(viewPager);
//        tab.setTabTextColors(getResources().getColor(R.color.color_white),getResources().getColor(R.color.color_black));
            tab.setTabMode(TabLayout.MODE_FIXED);
            tab.setTabGravity(TabLayout.GRAVITY_FILL);

            viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
                @Override
                public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

                }

                @Override
                public void onPageSelected(int position) {

                }

                @Override
                public void onPageScrollStateChanged(int state) {

                }
            });
        } else {
            fragmentAdapter.notifyDataSetChanged();
        }
        CommonUtil.setUpIndicatorWidthByReflex(tab, 15, 15);
    }
}
