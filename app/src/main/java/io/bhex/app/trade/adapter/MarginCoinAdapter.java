package io.bhex.app.trade.adapter;

import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.DateUtils;
import io.bhex.sdk.trade.bean.AssetRecordResponse;
import io.bhex.sdk.trade.margin.bean.MarginFullAccountLoanPositionResponse;

public class MarginCoinAdapter extends BaseQuickAdapter<MarginFullAccountLoanPositionResponse.ArrayBean, BaseViewHolder> {


    public MarginCoinAdapter(List<MarginFullAccountLoanPositionResponse.ArrayBean> data) {
        super(R.layout.item_margin_coin_info_layout, data);
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final MarginFullAccountLoanPositionResponse.ArrayBean itemModel) {
        baseViewHolder.setText(R.id.tv_token,itemModel.getTokenId());
        baseViewHolder.setText(R.id.tv_remain_to_repay_value,itemModel.getLoanTotal());
        baseViewHolder.setText(R.id.tv_remain_interest_value,itemModel.getInterestUnpaid());
    }

}
