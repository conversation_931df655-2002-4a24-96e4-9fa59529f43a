/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: IntentUtils.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.text.TextUtils;

import com.zopim.android.sdk.api.ZopimChat;
import com.zopim.android.sdk.model.VisitorInfo;
import com.zopim.android.sdk.prechat.ZopimChatActivity;

import androidx.core.app.ActivityCompat;
import io.bhex.app.R;
import io.bhex.app.account.ui.AccountActivity;
import io.bhex.app.account.ui.AddCoinAddressActivity;
import io.bhex.app.account.ui.AddressListActivity;
import io.bhex.app.account.ui.AllFuturesOrdersActivity;
import io.bhex.app.account.ui.AllMarginOrdersActivity;
import io.bhex.app.account.ui.AllOptionOrdersActivity;
import io.bhex.app.account.ui.AllOrdersActivity;
import io.bhex.app.account.ui.AnnounceActivity;
import io.bhex.app.account.ui.AntiPhishingCodeActivity;
import io.bhex.app.account.ui.AssetDetailActivity;
import io.bhex.app.account.ui.AssetRecordActivity;
import io.bhex.app.account.ui.AssetTransferActivity;
import io.bhex.app.account.ui.AssetWithdrawDetailActivity;
import io.bhex.app.account.ui.AuthStatusActivity;
import io.bhex.app.account.ui.AuthenticateActivity;
import io.bhex.app.account.ui.AuthenticateSubmitActivity;
import io.bhex.app.account.ui.BindEmailActivity;
import io.bhex.app.account.ui.BindGAActivity;
import io.bhex.app.account.ui.BindInfoActivity;
import io.bhex.app.account.ui.BindMobileActivity;
import io.bhex.app.account.ui.ChangeEmailActivity;
import io.bhex.app.account.ui.ChangeGAActivity;
import io.bhex.app.account.ui.ChangeMobileActivity;
import io.bhex.app.account.ui.CoinAddressActivity;
import io.bhex.app.account.ui.ContractOrderDetailActivity;
import io.bhex.app.account.ui.CreateSubAccountActivity;
import io.bhex.app.account.ui.FinancePasswdActivity;
import io.bhex.app.account.ui.FindPwd2FAActivity;
import io.bhex.app.account.ui.ForgetPasswdActivity;
import io.bhex.app.account.ui.FuturesAssetDetailActivity;
import io.bhex.app.account.ui.GABindHelpActivity;
import io.bhex.app.account.ui.GADownloadHelpActivity;
import io.bhex.app.account.ui.InvitationRewardActivity;
import io.bhex.app.account.ui.KycLevelVideoActivity;
import io.bhex.app.account.ui.KycLevelsActivity;
import io.bhex.app.account.ui.KycTwoComfirmInfoActivity;
import io.bhex.app.account.ui.LoginActivity;
import io.bhex.app.account.ui.MarginAssetDetailActivity;
import io.bhex.app.account.ui.MarginOrderDetailActivity;
import io.bhex.app.account.ui.MobileCodeListActivity;
import io.bhex.app.account.ui.ModifyPasswdActivity;
import io.bhex.app.account.ui.MyInvitationActivity;
import io.bhex.app.account.ui.NetworkLineActivity;
import io.bhex.app.account.ui.NewAllOrdersActivity;
import io.bhex.app.account.ui.OptionAssetDetailActivity;
import io.bhex.app.account.ui.OptionOrderDetailActivity;
import io.bhex.app.account.ui.OrderDetailActivity;
import io.bhex.app.account.ui.PlanOrderDetailActivity;
import io.bhex.app.account.ui.RechargeCoinActivity;
import io.bhex.app.account.ui.ResetPasswdActivity;
import io.bhex.app.account.ui.RiskyAssetsDetailActivity;
import io.bhex.app.account.ui.ScanLoginActivity;
import io.bhex.app.account.ui.SecurityActivity;
import io.bhex.app.account.ui.SetLoginPasswdActivity;
import io.bhex.app.account.ui.SettingActivity;
import io.bhex.app.account.ui.SharePosterActivity;
import io.bhex.app.account.ui.SignUpActivity;
import io.bhex.app.account.ui.SubAccountActivity;
import io.bhex.app.account.ui.TwoVerificaionActivity;
import io.bhex.app.account.ui.UserDetailActivity;
import io.bhex.app.account.ui.WithDrawResultActivity;
import io.bhex.app.account.ui.WithDrawSecurityActivity;
import io.bhex.app.account.ui.WithdrawCoinActivity;
import io.bhex.app.app.ActivityManager;
import io.bhex.app.finance.ui.AllStakingOrdersActivity;
import io.bhex.app.finance.ui.CoinPlusOrdersActivity;
import io.bhex.app.finance.ui.FinanceDetailActivity;
import io.bhex.app.finance.ui.FinanceListActivity;
import io.bhex.app.finance.ui.FinancePurchaseActivity;
import io.bhex.app.finance.ui.FinanceRecodeDetailActivity;
import io.bhex.app.finance.ui.FinanceRedemptionActivity;
import io.bhex.app.finance.ui.StakingOrderDetailActivity;
import io.bhex.app.finance.ui.StakingProductDetailActivity;
import io.bhex.app.finance.ui.StakingPurchaseActivity;
import io.bhex.app.finance.ui.StakingPurchaseResultActivity;
import io.bhex.app.gesture.lock.GestureLockActivity;
import io.bhex.app.kline.ui.KlineActivity;
import io.bhex.app.kline.ui.KlineExtActivity;
import io.bhex.app.main.ui.ExitDialogActivity;
import io.bhex.app.main.ui.MainActivity;
import io.bhex.app.margin.ui.LoanRecordDetailActivity;
import io.bhex.app.margin.ui.MarginActivity;
import io.bhex.app.margin.ui.MarginLoanHistoryActivity;
import io.bhex.app.margin.ui.MarginRepayActivity;
import io.bhex.app.margin.ui.MarginRepayDetailActivity;
import io.bhex.app.margin.ui.MarginRepayHistoryActivity;
import io.bhex.app.margin.ui.MarginToRepayActivity;
import io.bhex.app.market.ui.CoinPairListActivity;
import io.bhex.app.market.ui.EditOptionalActivity;
import io.bhex.app.market.ui.SearchActivity;
import io.bhex.app.market.ui.TokenListActivity;
import io.bhex.app.otc.ui.OtcAddReceiptChannelActivity;
import io.bhex.app.otc.ui.OtcAdsActivity;
import io.bhex.app.otc.ui.OtcAppealActivity;
import io.bhex.app.otc.ui.OtcBuyActivity;
import io.bhex.app.otc.ui.OtcDetailActivity;
import io.bhex.app.otc.ui.OtcHistoryProofActivity;
import io.bhex.app.otc.ui.OtcMsgActivity;
import io.bhex.app.otc.ui.OtcNickNameSetActivity;
import io.bhex.app.otc.ui.OtcOrdersActivity;
import io.bhex.app.otc.ui.OtcPublishAdsActivity;
import io.bhex.app.otc.ui.OtcSellActivity;
import io.bhex.app.otc.ui.OtcSettingsActivity;
import io.bhex.app.otc.ui.OtcUploadProofActivity;
import io.bhex.app.otc.ui.ReceiptChannelActivity;
import io.bhex.app.point.ui.BuyPointCardActivity;
import io.bhex.app.point.ui.PointCardActivity;
import io.bhex.app.point.ui.PointCardBuySuccessActivity;
import io.bhex.app.point.ui.PointCardFlowActivity;
import io.bhex.app.point.ui.PointCardListActivity;
import io.bhex.app.point.ui.PointCardRuleActivity;
import io.bhex.app.qrcode.ScanExActivity;
import io.bhex.app.safe.fingerprint.FingerActivity;
import io.bhex.app.share.ui.ContractShareProfitActivity;
import io.bhex.app.share.ui.ShareProfitActivity;
import io.bhex.app.trade.ui.ContractCalculatorActivity;
import io.bhex.app.trade.ui.HistoryOptionDeliveryActivity;
import io.bhex.app.trade.ui.StopProfitLossActivity;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.constant.Fields;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.LoginApi;
import io.bhex.sdk.account.LoginResultCallback;
import io.bhex.sdk.account.LoginResultCarrier;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.FindPwdCheckResponse;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.account.bean.kyc.KycLevelBean;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.finance.bean.FinanceBean;
import io.bhex.sdk.finance.bean.ProductsBean;
import io.bhex.sdk.finance.bean.StakingOrderListResponse;
import io.bhex.sdk.invite.bean.InviteResponse;
import io.bhex.sdk.otc.bean.OtcAdsListResponse;
import io.bhex.sdk.otc.bean.OtcConfigResponse;
import io.bhex.sdk.otc.bean.OtcListResponse;
import io.bhex.sdk.otc.bean.OtcOrderInfoResponse;
import io.bhex.sdk.otc.bean.OtcPayChannelListResponse;
import io.bhex.sdk.otc.bean.OtcPaymentChannelBean;
import io.bhex.sdk.point.bean.AppPointCardListResponse;
import io.bhex.sdk.point.bean.BuyPointResponse;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.QuoteTokensBean;
import io.bhex.sdk.trade.bean.AssetListResponse;
import io.bhex.sdk.trade.bean.FeeBeanResponse;
import io.bhex.sdk.trade.bean.FuturesAssetListResponse;
import io.bhex.sdk.trade.bean.OptionAssetListResponse;
import io.bhex.sdk.trade.bean.OptionDeliveryRecordResponse;
import io.bhex.sdk.trade.bean.OptionHoldOrderResponse;
import io.bhex.sdk.trade.bean.OrderBean;
import io.bhex.sdk.trade.bean.PlanOrderBean;
import io.bhex.sdk.trade.bean.RecordBeanResponse;
import io.bhex.sdk.trade.futures.bean.FuturesOrderResponse;
import io.bhex.sdk.trade.futures.bean.FuturesPositionOrder;
import io.bhex.sdk.trade.margin.bean.MarginAccountAssetResponse;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordResponse;
import io.bhex.sdk.trade.margin.bean.QueryRepayRecordResponse;

public class IntentUtils {

    /**
     * 去主页面
     *
     * @param context
     */
    public static void goMain(Context context) {
        Intent intent = new Intent();
        if (!(context instanceof Activity)) {
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        intent.setClass(context, MainActivity.class);
        context.startActivity(intent);
    }



    public static void goLogin(Context context, LoginResultCarrier callback) {
        boolean fingerOpen = UserManager.getInstance().isFingerSetOpenStatus();
        if (!TextUtils.isEmpty(UserManager.getInstance().getUserId()) && fingerOpen) {

            IntentUtils.openFinger(context, callback);
        } else {
            Intent intent = new Intent();
            intent.putExtra(AppData.INTENT.LOGIN_CALLBACK, callback);
            if (!(context instanceof Activity)) {
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            }
            intent.setClass(context, LoginActivity.class);
            context.startActivity(intent);
        }
    }
    /**
     * 跳转到Kline
     *
     * @param context
     */
    public static void goKline(Context context, CoinPairBean coinPair) {
        Intent intent = new Intent();
        intent.putExtra(AppData.INTENT.SYMBOLS, coinPair);
//        intent.putExtra(AppData.INTENT.SYMBOLS, coinPair.getSymbolId());
        //intent.setClass(context, KlineActivity.class);
        intent.setClass(context, KlineExtActivity.class);
        context.startActivity(intent);
    }

    /**
     * 跳转到Kline DepthView
     *
     * @param context
     */
    public static void goKlineSeeDepth(Context context, CoinPairBean coinPair) {
        Intent intent = new Intent();
        intent.putExtra(AppData.INTENT.SYMBOLS, coinPair);
        intent.putExtra(AppData.INTENT.DEPTHVIEW, true);
        intent.setClass(context, KlineActivity.class);
        context.startActivity(intent);
    }

    /**
     * 注册
     *
     */
    public static void goRegister(Activity activity, int requestCode, LoginResultCarrier callback) {
        Intent intent = new Intent();
        intent.putExtra(AppData.INTENT.LOGIN_CALLBACK, callback);
        intent.setClass(activity, SignUpActivity.class);
        activity.startActivityForResult(intent, requestCode);
    }
    public static void goRegister(Activity activity, LoginResultCarrier callback) {
        Intent intent = new Intent();
        intent.putExtra(AppData.INTENT.LOGIN_CALLBACK, callback);
        intent.setClass(activity, SignUpActivity.class);
        activity.startActivity(intent);
    }
    /**
     * 选择币对
     *
     * @param context
     * @param coinPair
     */
    public static void goSelectCoinPair(Context context, String from, String exchangeId, String coinPair) {
        Intent intent = new Intent();
        intent.putExtra(Fields.FIELD_EXCHANGE_ID2, exchangeId);
        intent.putExtra(Fields.FIELD_SYMBOL, coinPair);
        intent.putExtra(AppData.INTENT.FROM, from);
        intent.setClass(context, CoinPairListActivity.class);
        context.startActivity(intent);
    }

    /**
     * 搜索
     *
     * @param context
     */
    public static void goSearch(Context context,String key) {
        Intent intent = new Intent();
        intent.putExtra(AppData.INTENT.SEARCH_KEY, key);
        intent.setClass(context, SearchActivity.class);
        context.startActivity(intent);
    }

    /**
     * 搜索
     *
     * @param context
     */
    public static void goSearch(Context context) {
        goSearch(context,"");
    }
    /**
     * 我的
     *
     * @param context
     */
    public static void goAccount(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, AccountActivity.class);
        context.startActivity(intent);
    }

    /**
     * 设置
     *
     * @param context
     */
    public static void goSettings(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, SettingActivity.class);
        context.startActivity(intent);
    }

    /**
     * 手机区号列表
     *  @param context
     * @param requestCode
     * @param reqParamKey
     */
    public static void goMobileCodeList(Activity context, int requestCode, String reqParamKey) {
        Intent intent = new Intent();
        intent.putExtra("reqParamType",reqParamKey);
        intent.setClass(context, MobileCodeListActivity.class);
        context.startActivityForResult(intent, requestCode);
    }

    /**
     * 全部订单
     *
     * @param context
     */
    public static void goAllOrders(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, AllOrdersActivity.class);
        context.startActivity(intent);
    }

    /**
     * 订单详情
     *
     * @param context
     * @param order
     */
    public static void goOrderDetail(Context context, OrderBean order) {
        Intent intent = new Intent();
        intent.putExtra(AppData.INTENT.KEY_ORDER, order);
        intent.setClass(context, OrderDetailActivity.class);
        context.startActivity(intent);
    }
    public static void goPlanOrderDetail(Context context, PlanOrderBean order,int accountType) {
        Intent intent = new Intent();
        intent.putExtra(AppData.INTENT.KEY_ORDER, order);
        intent.putExtra("accountType", accountType);
        intent.setClass(context, PlanOrderDetailActivity.class);
        context.startActivity(intent);
    }

    /**
     * 订单详情
     *
     * @param context
     * @param order
     */
    public static void goMarginOrderDetail(Context context, OrderBean order) {
        Intent intent = new Intent();
        intent.putExtra(AppData.INTENT.KEY_ORDER, order);
        intent.setClass(context, MarginOrderDetailActivity.class);
        context.startActivity(intent);
    }

    /**
     * 订单详情
     *
     * @param context
     * @param order
     */
    public static void goContractOrderDetail(Context context, FuturesOrderResponse order) {
        Intent intent = new Intent();
        intent.putExtra(AppData.INTENT.KEY_ORDER, order);
        intent.setClass(context, ContractOrderDetailActivity.class);
        context.startActivity(intent);
    }

    /**
     * 订单详情
     *
     * @param context
     * @param order
     */
    public static void goOptionOrderDetail(Context context, OrderBean order) {
        Intent intent = new Intent();
        intent.putExtra(AppData.INTENT.KEY_ORDER, order);
        intent.setClass(context, OptionOrderDetailActivity.class);
        context.startActivity(intent);
    }

    /**
     * 订单详情
     *
     * @param context
     * @param order
     */
    public static void goStakingOrderDetail(Context context, StakingOrderListResponse.ArrayBean order) {
        Intent intent = new Intent();
        intent.putExtra(AppData.INTENT.KEY_ORDER, order);
        intent.setClass(context, StakingOrderDetailActivity.class);
        context.startActivity(intent);
    }

    /**
     * 我的资产
     *
     * @param context
     */
    public static void goMyAsset(Context context) {
//        Intent intent = new Intent();
//        intent.setClass(context, MyAssetFragment.class);
//        context.startActivity(intent);
        if (MainActivity.getInstance() != null) {
            if(!(ActivityManager.getInstance().getCurrentActivity() instanceof MainActivity)) {
                goMain(context);
            }
            MainActivity.getInstance().goAsset();
        }
    }

    public static void goMyAssetTokenDeposit(String token, Context context) {
        goMyAssetTokenDeposit(token,"",context);

    }

    public static void goMyAssetTokenDeposit(String token,String chainType, Context context) {
        //AssetListResponse.BalanceBean getTokenAsset = TradeDataManager.GetInstance().getTokenAsset(token);

        //if(getTokenAsset != null) {

        QuoteTokensBean.TokenItem tokenItem = AppConfigManager.GetInstance().getToken(token);
        if(tokenItem != null){
            AssetListResponse.BalanceBean BalanceBean = new AssetListResponse.BalanceBean();
            BalanceBean.setTokenId(tokenItem.getTokenId());
            BalanceBean.setTokenName(tokenItem.getTokenName());
            BalanceBean.setTokenFullName(tokenItem.getTokenFullName());
            BalanceBean.setIconUrl(tokenItem.getIconUrl());
            BalanceBean.setAllowDeposit(tokenItem.isAllowDeposit());
            BalanceBean.setAllowWithdraw(tokenItem.isAllowWithdraw());
            BalanceBean.setNeedAddressTag(tokenItem.isNeedAddressTag());
            if (BalanceBean.isAllowDeposit()) {
                IntentUtils.goRechargeCoin(context, BalanceBean,chainType);
            } else {
                ToastUtils.showShort(context, tokenItem.getTokenName()+" "+context.getString(R.string.string_suspeng_deposit));
            }
        }
        else
            goMyAsset(context);
        //IntentUtils.goAssetDetail(context, getTokenAsset);
        //}
        //else
        //    goMyAsset(context);


    }
    public static void goMyAssetTokenWithdraw(String token, Context context) {
        goMyAssetTokenWithdraw(token,"",context);
    }

    public static void goMyAssetTokenWithdraw(String token,String chainType, Context context) {
        //AssetListResponse.BalanceBean getTokenAsset = TradeDataManager.GetInstance().getTokenAsset(token);

        //if(getTokenAsset != null) {

        QuoteTokensBean.TokenItem tokenItem = AppConfigManager.GetInstance().getToken(token);
        if(tokenItem != null){
            AssetListResponse.BalanceBean balanceBean = new AssetListResponse.BalanceBean();
            balanceBean.setTokenId(tokenItem.getTokenId());
            balanceBean.setTokenName(tokenItem.getTokenName());
            balanceBean.setTokenFullName(tokenItem.getTokenFullName());
            balanceBean.setIconUrl(tokenItem.getIconUrl());
            balanceBean.setAllowDeposit(tokenItem.isAllowDeposit());
            balanceBean.setAllowWithdraw(tokenItem.isAllowWithdraw());
            balanceBean.setNeedAddressTag(tokenItem.isNeedAddressTag());
            if (balanceBean.isAllowWithdraw()) {
                UserInfoBean userInfo = UserManager.getInstance().getUserInfo();
                if (userInfo != null) {
                    boolean bindTradePwd = userInfo.isBindTradePwd();
                    boolean bindGA = userInfo.isBindGA();
                    String mobile = userInfo.getMobile();
                    String email = userInfo.getEmail();
                    boolean isBindMobile = !TextUtils.isEmpty(mobile);
                    boolean isBindEmail = !TextUtils.isEmpty(email);

                    /**判断是否设置了资金密码 是否2FA(至少两个绑定) ***********/
                    if (bindTradePwd&& ((isBindEmail&&isBindMobile)||(bindGA&&isBindEmail)||(bindGA&&isBindMobile))){
                        IntentUtils.goWithDrawCoin(context,chainType, balanceBean);
                    }else{
                        IntentUtils.goCompleteWithDrawSecurityInfo(context,balanceBean.getTokenId());
                    }
                }
            } else {
                ToastUtils.showShort(context, tokenItem.getTokenName()+" "+context.getString(R.string.string_suspeng_withdraw));
            }
        }
        else
            goMyAsset(context);
        //IntentUtils.goAssetDetail(context, getTokenAsset);
        //}
        //else
        //    goMyAsset(context);


    }
    /**
     * 资产详情
     *
     * @param context
     * @param itemModel
     */
    public static void goAssetDetail(Context context, AssetListResponse.BalanceBean itemModel) {
        Intent intent = new Intent();
        intent.putExtra(AppData.INTENT.KEY_ASSET, itemModel);
        intent.setClass(context, AssetDetailActivity.class);
        context.startActivity(intent);
    }

    /**
     * 期权资产详情
     *
     * @param context
     * @param itemModel
     */
    public static void goOptionAssetDetail(Context context, OptionAssetListResponse.OptionAssetBean itemModel) {
        Intent intent = new Intent();
        intent.putExtra(AppData.INTENT.KEY_ASSET, itemModel);
        intent.setClass(context, OptionAssetDetailActivity.class);
        context.startActivity(intent);
    }


    /**
     * 期货资产详情
     *
     * @param context
     * @param itemModel
     */
    public static void goFuturesAssetDetail(Context context, FuturesAssetListResponse.FuturesAssetBean itemModel) {
        Intent intent = new Intent();
        intent.putExtra(AppData.INTENT.KEY_ASSET, itemModel);
        intent.setClass(context, FuturesAssetDetailActivity.class);
        context.startActivity(intent);
    }

    /**
     * 杠杆资产详情
     *
     * @param context
     * @param itemModel
     */
    public static void goAssetMarginDetail(Context context, MarginAccountAssetResponse.DataBean itemModel) {
        Intent intent = new Intent();
        intent.putExtra(AppData.INTENT.KEY_MARGIN_ASSET, itemModel);
        intent.setClass(context, MarginAssetDetailActivity.class);
        context.startActivity(intent);
    }

    /**
     * 充币地址
     *
     * @param context
     * @param assetItemBean
     */
    public static void goRechargeCoin(final Context context, final AssetListResponse.BalanceBean assetItemBean) {
        goRechargeCoin(context,assetItemBean,"");
    }


    public static void goRechargeCoin(final Context context, final AssetListResponse.BalanceBean assetItemBean,final String chainType) {
        checkUserIsBindPasswd(context, new BindPasswdListener() {
            @Override
            public void onBefore() {

            }

            @Override
            public void onFinish() {

            }

            @Override
            public void onError(Throwable error) {

            }

            @Override
            public void onResult(boolean bindPassword) {
                if (bindPassword) {
                    //绑定了登录密码，才可以去充币
                    Intent intent = new Intent();
                    intent.putExtra(AppData.INTENT.KEY_ASSET, assetItemBean);
                    intent.putExtra(AppData.INTENT.KEY_CHAINTYPE, chainType);
                    intent.setClass(context, RechargeCoinActivity.class);
                    context.startActivity(intent);
                }
            }
        });


    }

    /**
     * 提币
     *
     * @param context
     * @param assetItemBean
     */
    public static void goWithDrawCoin(Context context,String chainType, AssetListResponse.BalanceBean assetItemBean) {
        UserInfoBean userInfo = UserManager.getInstance().getUserInfo();
        if (userInfo != null) {
            boolean bindTradePwd = userInfo.isBindTradePwd();
            boolean bindGA = userInfo.isBindGA();
            String mobile = userInfo.getMobile();
            String email = userInfo.getEmail();
            boolean isBindMobile = !TextUtils.isEmpty(mobile);
            boolean isBindEmail = !TextUtils.isEmpty(email);

            /**判断是否设置了资金密码 是否2FA(至少两个绑定) ***********/
            if (bindTradePwd&& ((isBindEmail&&isBindMobile)||(bindGA&&isBindEmail)||(bindGA&&isBindMobile))){
                Intent intent = new Intent();
                intent.putExtra(AppData.INTENT.KEY_ASSET, assetItemBean);
                intent.putExtra(AppData.INTENT.KEY_CHAINTYPE, chainType);
                intent.setClass(context, WithdrawCoinActivity.class);
                context.startActivity(intent);
            }else{
                IntentUtils.goCompleteWithDrawSecurityInfo(context,assetItemBean.getTokenId());
            }
        }

    }

    /**
     * 提币记录
     * @param context
     */
    public static void goAssetRecord(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, AssetRecordActivity.class);
        context.startActivity(intent);
    }

    /**
     *  去行情列表
     * @param context
     */
    public static void goMarket(Context context) {
        if (MainActivity.getInstance() != null){
            if(!(ActivityManager.getInstance().getCurrentActivity() instanceof MainActivity)) {
                goMain(context);
            }
            MainActivity.getInstance().goMarket();
        }
    }

    /**
     *  去交易
     * @param context
     */
    public static void goTrade(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, MainActivity.class);
        intent.putExtra(AppData.INTENT.MAIN_TAB, 2);
        intent.putExtra(AppData.INTENT.HOME_TRADE_TAB, 0);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    /**
     *  去永续合约交易
     * @param context
     */
    public static void goPerpetualContractTrade(Context context) {
        /*Intent intent = new Intent();
        intent.setClass(context, MainActivity.class);
        intent.putExtra(AppData.INTENT.MAIN_TAB, 2);
        intent.putExtra(AppData.INTENT.HOME_TRADE_TAB, 1);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);*/

        DebugLog.e("IntentUtils","begin goPerpetualContractTrade");
        if (MainActivity.getInstance() != null){
            DebugLog.e("IntentUtils","goPerpetualContractTrade");
            if(!(ActivityManager.getInstance().getCurrentActivity() instanceof MainActivity)) {
                goMain(context);
                DebugLog.e("IntentUtils","goMain");
            }
            MainActivity.getInstance().goFuturesTrade();
            DebugLog.e("IntentUtils","MainActivity.getInstance().goFuturesTrade()");
        }
    }

    /**
     *  去期权交易
     * @param context
     */
    public static void goOptionTrade(Context context) {
        /*Intent intent = new Intent();
        intent.setClass(context, MainActivity.class);
        intent.putExtra(AppData.INTENT.MAIN_TAB, 2);
        intent.putExtra(AppData.INTENT.HOME_TRADE_TAB, 1);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);*/

        if (MainActivity.getInstance() != null){
            if(!(ActivityManager.getInstance().getCurrentActivity() instanceof MainActivity)) {
                goMain(context);
            }
            MainActivity.getInstance().goOptionTrade();
        }
    }

    /**
     *  去交易
     * @param context
     */
    public static void goOTCTrade(Context context) {
        /*Intent intent = new Intent();
        intent.setClass(context, MainActivity.class);
        intent.putExtra(AppData.INTENT.MAIN_TAB, 2);
        intent.putExtra(AppData.INTENT.HOME_TRADE_TAB, 2);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);*/

        if (MainActivity.getInstance() != null){
            if(!(ActivityManager.getInstance().getCurrentActivity() instanceof MainActivity)) {
                goMain(context);
            }
            MainActivity.getInstance().goOTCTrade();
        }
    }

    /**
     *  去交易
     * @param context
     */
    public static void goMarginTrade(Context context) {
        /*Intent intent = new Intent();
        intent.setClass(context, MainActivity.class);
        intent.putExtra(AppData.INTENT.MAIN_TAB, 2);
        intent.putExtra(AppData.INTENT.HOME_TRADE_TAB, 2);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);*/
        if (MainActivity.getInstance() != null){
            if(!(ActivityManager.getInstance().getCurrentActivity() instanceof MainActivity)) {
                goMain(context);
            }
            MainActivity.getInstance().goMarginTrade();
        }
    }

    /**
     *  去BB交易
     * @param context
     */
    public static void goBBTrade(Context context) {
        /*Intent intent = new Intent();
        intent.setClass(context, MainActivity.class);
        intent.putExtra(AppData.INTENT.MAIN_TAB, 2);
        intent.putExtra(AppData.INTENT.HOME_TRADE_TAB, 2);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);*/

        if (MainActivity.getInstance() != null) {
            if(!(ActivityManager.getInstance().getCurrentActivity() instanceof MainActivity)) {
                goMain(context);
            }
            MainActivity.getInstance().goBBTrade();
        }
    }

    /**
     * 安全中心
     * @param context
     */
    public static void goSecurityCenter(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, SecurityActivity.class);
        context.startActivity(intent);
    }

    /**
     * 实名认证
     * @param context
     */
    public static void goIdentityAuth(final Context context) {
        Intent intent = new Intent();
        intent.setClass(context, KycLevelsActivity.class);
        context.startActivity(intent);
    }

    /**
     * 实名认证：一级
     * @param context
     */
    public static void goAuthLV1(final Context context) {
        Intent intent = new Intent();
        intent.setClass(context, AuthenticateActivity.class);
        context.startActivity(intent);
    }

    /**
     * 实名认证：二级
     * @param context
     * @param twoLevel
     */
    public static void goAuthLV2ComfirmInfo(final Context context, KycLevelBean twoLevel) {
        Intent intent = new Intent();
        intent.setClass(context, KycTwoComfirmInfoActivity.class);
        intent.putExtra("kycLevelBean",twoLevel);
        context.startActivity(intent);
    }

    /**
     * 二级认证
     * @param context
     * @param kycLevelBean
     * @param requestCode
     */
    public static void goAuthLV2(Activity context, KycLevelBean kycLevelBean, int requestCode) {
        Intent intent = new Intent();
        intent.setClass(context, AuthenticateSubmitActivity.class);
        intent.putExtra("kycLevelBean",kycLevelBean);
        context.startActivityForResult(intent,requestCode);
    }

    /**
     * 实名认证：三级
     * @param context
     * @param threeLevel
     */
    public static void goAuthLV3(final Context context, KycLevelBean threeLevel) {
        Intent intent = new Intent();
        intent.setClass(context, KycLevelVideoActivity.class);
        intent.putExtra("levelBean",threeLevel);
        context.startActivity(intent);
    }

    /**
     * 认证信息
     * @param context
     * @param levelBean
     * @param verifyStatus
     * @param verifyStatusTips
     */
    public static void goAuthStatus(Context context, KycLevelBean levelBean, int verifyStatus, String verifyStatusTips) {
        Intent intent = new Intent();
        intent.setClass(context, AuthStatusActivity.class);
        intent.putExtra("levelBean", levelBean);
        intent.putExtra("verifyStatus", verifyStatus);
        intent.putExtra("verifyStatusTips", verifyStatusTips);
        context.startActivity(intent);
    }

    /**
     * 提币地址
     * @param context
     */
    public static void goCoinAddress(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, CoinAddressActivity.class);
        context.startActivity(intent);
    }

    /**
     * 打开手势验证
     * @param context
     */
    public static void openGestureVerify(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, GestureLockActivity.class);
        context.startActivity(intent);
    }

    /**
     * 打开手势验证
     * @param context
     */
    public static void openFinger(Context context, LoginResultCarrier callback) {
        Intent intent = new Intent();
        intent.setClass(context, FingerActivity.class);
        intent.putExtra(AppData.INTENT.LOGIN_CALLBACK, callback);
        intent.putExtra(AppData.INTENT.LOGIN_CALLER, "");
        if (!(context instanceof Activity)) {
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        context.startActivity(intent);
    }
    /**
     * 打开手势验证
     * @param activity
     */
    public static void openFinger(Activity activity, int requestCode, String caller) {
        Intent intent = new Intent();
        intent.setClass(activity, FingerActivity.class);
        intent.putExtra(AppData.INTENT.LOGIN_CALLER, caller);
        activity.startActivityForResult(intent, requestCode);
    }

    /**
     * 修改登录密码
     * @param context
     */
    public static void goUpdatePasswd(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, ModifyPasswdActivity.class);
        context.startActivity(intent);
    }

    /**
     * 资金密码
     * @param context
     * @param isBindTradePwd
     */
    public static void goFinancePasswd(Context context, boolean isBindTradePwd) {
        Intent intent = new Intent();
        intent.putExtra("isBindTradePwd", isBindTradePwd);
        intent.setClass(context, FinancePasswdActivity.class);
        context.startActivity(intent);
    }

    /**
     * 忘记密码-找回设置密码
     * @param context
     * @param isEmail
     * @param account
     * @param nationalCode
     * @param orderId
     */
    public static void goFindPasswd(Context context, boolean isEmail, String account, String nationalCode, String orderId) {
        Intent intent = new Intent();
        intent.setClass(context, ResetPasswdActivity.class);
        intent.putExtra("isEmail", isEmail);
        intent.putExtra("account", account);
        intent.putExtra("nationalCode", nationalCode);
        intent.putExtra("orderId", orderId);
        context.startActivity(intent);
    }

    /**
     * 设置密码
     * @param context
     */
    public static void goSetPwd(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, SetLoginPasswdActivity.class);
        context.startActivity(intent);
    }

    /**
     * 忘记密码
     * @param context
     * @param isEmail
     */
    public static void goForgetPwd(Context context, boolean isEmail) {
        Intent intent = new Intent();
        intent.setClass(context, ForgetPasswdActivity.class);
        intent.putExtra("isEmail", isEmail);
        context.startActivity(intent);
    }


    /**
     * 二次验证
     * @param activity
     * @param from  来自哪个页面  登录：fromlogin
     * @param requestId  来自登录的requestId
     * @param codeType 验证码渠道
     * @param bindGA
     * @param bindMobile
     * @param bindEmail
     * @param isVerifyEmail  当前要验证状态
     */
    public static void goTwoVerify(Activity activity, int intentRequestCode, String from, String requestId, String codeType, boolean bindGA, boolean bindMobile, boolean bindEmail, boolean isVerifyEmail) {

        Intent intent = new Intent();
        intent.setClass(activity, TwoVerificaionActivity.class);
        intent.putExtra("from", from);
        intent.putExtra("requestId", requestId);
        intent.putExtra("type", codeType);
        intent.putExtra("bindGA", bindGA);
        intent.putExtra("bindMobile", bindMobile);
        intent.putExtra("bindEmail", bindEmail);
        intent.putExtra("isVerifyEmail", isVerifyEmail);
        activity.startActivityForResult(intent, intentRequestCode);
    }

    public static void goFindPwdTwoVerify(Activity activity,int intentRequestCode, boolean isEmail, String account, String nationalCode, FindPwdCheckResponse findPwdCheckResponse) {

        Intent intent = new Intent();
        intent.setClass(activity, FindPwd2FAActivity.class);
        intent.putExtra("isEmail", isEmail);
        intent.putExtra("account", account);
        intent.putExtra("nationalCode", nationalCode);
        intent.putExtra("findPwdCheckResponse", findPwdCheckResponse);
        activity.startActivityForResult(intent, intentRequestCode);
    }

    /**
     * 地址列表管理
     * @param context
     * @param itemModel
     */
    public static void goCoinAddressList(Context context, QuoteTokensBean.TokenItem itemModel) {
        if (itemModel != null) {
            Intent intent = new Intent();
            intent.setClass(context, AddressListActivity.class);
            intent.putExtra("from", "manage");
            intent.putExtra("tokenId", itemModel.getTokenId());
            intent.putExtra("tokenName", itemModel.getTokenName());
            intent.putExtra("icon", itemModel.getIconUrl());
            intent.putExtra("tokenFullName", itemModel.getTokenFullName());
            intent.putExtra("isEOS", itemModel.isNeedAddressTag());
            context.startActivity(intent);
        }
    }

    /**
     * 地址列表管理
     * @param activity
     * @param tokenId
     */
    public static void goCoinAddressList(Activity activity,String chainType, String tokenId, String tokenName, String iconUrl, String tokenFullName, int requestCode, boolean isNeedTag) {
        if (!TextUtils.isEmpty(tokenId)) {
            Intent intent = new Intent();
            intent.setClass(activity, AddressListActivity.class);
            intent.putExtra("tokenId", tokenId);
            intent.putExtra("tokenName", tokenName);
            intent.putExtra("chainType", chainType);
            intent.putExtra("icon", iconUrl);
            intent.putExtra("tokenFullName", tokenFullName);
            intent.putExtra("isEOS", isNeedTag);
            if (requestCode == -5) {
                intent.putExtra("from", "manage");
                activity.startActivity(intent);
            } else {
                intent.putExtra("from", "withdraw");
                activity.startActivityForResult(intent, requestCode);
            }

        }
    }

    /**
     * 添加币地址
     * @param context
     * @param chainType
     * @param tokenId
     * @param tokenName
     * @param tokenFullName
     * @param iconUrl
     */
    public static void goAddCoinAddress(Context context, String chainType, String tokenId, String tokenName, String tokenFullName, String iconUrl, boolean isEOS) {
        Intent intent = new Intent();
        intent.setClass(context, AddCoinAddressActivity.class);
        intent.putExtra("tokenId", tokenId);
        intent.putExtra("chainType", chainType);
        intent.putExtra("tokenName", tokenName);
        intent.putExtra("icon", iconUrl);
        intent.putExtra("tokenFullName", tokenFullName);
        intent.putExtra("isEOS", isEOS);
        context.startActivity(intent);
    }

    /**
     * 扫码
     * @param activity
     * @param requestCodeOfScan
     */
    public static void goScan(Activity activity, int requestCodeOfScan) {
        Intent intent = new Intent();
        intent.setClass(activity, ScanExActivity.class);
        activity.startActivityForResult(intent, requestCodeOfScan);
    }

    /**
     * 用户信息
     * @param context
     */
    public static void goUserInfoPage(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, UserDetailActivity.class);
        context.startActivity(intent);
    }

    /**
     * 公告
     * @param context
     */
    public static void goAnnouncements(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, AnnounceActivity.class);
        context.startActivity(intent);
    }

    /**
     * GA help
     * @param context
     */
    public static void goBindGAHelp(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, GADownloadHelpActivity.class);
        context.startActivity(intent);
    }

    /**
     * GA
     * @param context
     */
    public static void goBindGAHelp2(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, GABindHelpActivity.class);
        context.startActivity(intent);
    }

    /**
     * GA
     * @param context
     */
    public static void goBindGA(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, BindGAActivity.class);
        context.startActivity(intent);
    }

    /**
     * 绑定手机
     * @param context
     */
    public static void goBindMobile(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, BindMobileActivity.class);
        context.startActivity(intent);
    }

    /**
     * 绑定邮箱
     * @param context
     */
    public static void goBindEmail(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, BindEmailActivity.class);
        context.startActivity(intent);
    }

    /**
     * 换绑GA
     * @param context
     */
    public static void goChangeGA(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, ChangeGAActivity.class);
        context.startActivity(intent);
    }

    /**
     * 换绑手机
     * @param context
     */
    public static void goChangeMobile(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, ChangeMobileActivity.class);
        context.startActivity(intent);
    }

    /**
     * 换绑邮箱
     * @param context
     */
    public static void goChangeEmail(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, ChangeEmailActivity.class);
        context.startActivity(intent);
    }

    /**
     * 反钓鱼码
     * @param context
     * @param hasSetAntiPhishingCode 是否设置过防钓鱼码
     */
    public static void goSetAntiPhishingCode(Context context, boolean hasSetAntiPhishingCode) {
        Intent intent = new Intent();
        intent.putExtra("hasSetAntiPhishingCode",hasSetAntiPhishingCode);
        intent.setClass(context, AntiPhishingCodeActivity.class);
        context.startActivity(intent);
    }

    /**
     * 我的点卡
     * @param context
     */
    public static void goPointCard(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, PointCardActivity.class);
        context.startActivity(intent);
    }

    /**
     * 我的邀请
     * @param context
     */
    public static void goMyInvitation(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, MyInvitationActivity.class);
        context.startActivity(intent);
    }

    /**
     * 返佣明细
     * @param context
     * @param isInviteList
     */
    public static void goRewardDetail(Context context, boolean isInviteList) {
        Intent intent = new Intent();
        intent.putExtra("isInviteList",isInviteList);
        intent.setClass(context, InvitationRewardActivity.class);
        context.startActivity(intent);
    }

    /**
     * 专属海报
     * @param context
     */
    public static void goInvitePoster(Context context, InviteResponse shareInfo) {
        Intent intent = new Intent();
        intent.setClass(context, SharePosterActivity.class);
        intent.putExtra("shareInfo", shareInfo);
        context.startActivity(intent);
    }

    /**
     * 购买点卡列表
     * @param context
     */
    public static void goPointCardBuyList(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, PointCardListActivity.class);
        context.startActivity(intent);
    }

    /**
     * 购买点卡
     * @param context
     * @param pointPack
     */
    public static void goPointCardBuy(Context context, AppPointCardListResponse.PointCardTypeBean pointPack) {
        Intent intent = new Intent();
        intent.putExtra("pointPack", pointPack);
        intent.setClass(context, BuyPointCardActivity.class);
        context.startActivity(intent);
    }

    /**
     * 点卡购买记录
     * @param context
     */
    public static void goPointCardBuyFlow(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, PointCardFlowActivity.class);
        context.startActivity(intent);
    }

    /**
     * 点卡购买成功
     * @param context
     * @param response
     */
    public static void goPointCardBuySuccess(Context context, BuyPointResponse response) {
        Intent intent = new Intent();
        intent.setClass(context, PointCardBuySuccessActivity.class);
        intent.putExtra("pointResult", response);
        context.startActivity(intent);
    }

    /**
     * 点卡规则
     * @param context
     */
    public static void goPointCardRule(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, PointCardRuleActivity.class);
        context.startActivity(intent);
    }

    /**
     * 提币详情
     * @param context
     */
    public static void goAssetWithdrawDetail(Context context, int type, RecordBeanResponse.RecordItem data) {
        Intent intent = new Intent();
        intent.setClass(context, AssetWithdrawDetailActivity.class);
        intent.putExtra("type", type);
        intent.putExtra("RecordItem", data);
        context.startActivity(intent);
    }

    /**
     * 全部订单
     * @param context
     */
    public static void goNewAllOrders(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, NewAllOrdersActivity.class);
        context.startActivity(intent);
    }

    /**
     * 期货订单
     * @param context
     */
    public static void goAllFuturesOrders(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, AllFuturesOrdersActivity.class);
        context.startActivity(intent);
    }

    /**
     * 期权订单
     * @param context
     */
    public static void goAllOptionOrders(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, AllOptionOrdersActivity.class);
        context.startActivity(intent);
    }

    /**
     * 期权历史交割
     * @param context
     */
    public static void goHistoryOptionDelivery(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, HistoryOptionDeliveryActivity.class);
        context.startActivity(intent);
    }

    public static void goOtcDetail(Activity activity, OtcListResponse.OtcItemBean otcItemBean, OtcConfigResponse currentConfigData) {
        Intent intent = new Intent();
        intent.putExtra("item", otcItemBean);
        intent.putExtra("config", currentConfigData);
        intent.setClass(activity, OtcDetailActivity.class);
        activity.startActivity(intent);
    }

    /**
     * 订单支付确认页
     * @param context
     * @param isBuy
     * @param orderId
     */
    public static void goOtcBuySellPay(Context context, boolean isBuy, String orderId) {
        Intent intent = new Intent();
        intent.putExtra("orderId", orderId);
        intent.setClass(context, isBuy ? OtcBuyActivity.class : OtcSellActivity.class);
        context.startActivity(intent);
    }

    /**
     * 资产划转
     * @param context
     * @param tokenId
     */
    public static void goAssetTransfer(Context context, String tokenId) {
        Intent intent = new Intent();
        intent.putExtra("tokenId",tokenId);
        intent.setClass(context, AssetTransferActivity.class);
        context.startActivity(intent);
    }
    public static void goAssetTransfer(Context context, String tokenId,int targetAccountType) {
        goAssetTransfer(context,tokenId,-1,targetAccountType);
    }
    /**
     * 资产划转
     * @param context
     * @param tokenId
     */
    public static void goAssetTransfer(Context context, String tokenId,int sourceAccountType,int targetAccountType) {
        Intent intent = new Intent();
        intent.putExtra("tokenId",tokenId);
        intent.putExtra("sourceAccountType",sourceAccountType);
        intent.putExtra("targetAccountType",targetAccountType);
        intent.setClass(context, AssetTransferActivity.class);
        context.startActivity(intent);
    }


    /**
     * 子账户
     * @param context
     */
    public static void goSubAccount(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, SubAccountActivity.class);
        context.startActivity(intent);
    }


    /**
     * 创建子账户
     * @param context
     */
    public static void goCreateSubAccount(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, CreateSubAccountActivity.class);
        context.startActivity(intent);
    }


    /**
     * 订单申诉
     * @param context
     * @param orderId
     */
    public static void goOtcOrderAppeal(Context context, String orderId) {
        Intent intent = new Intent();
        intent.putExtra("orderId", orderId);
        intent.setClass(context, OtcAppealActivity.class);
        context.startActivity(intent);
    }

    /**
     * 设置昵称
     * @param context
     */
    public static void goSetOtcNickName(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, OtcNickNameSetActivity.class);
        context.startActivity(intent);
    }

    /**
     * Otc订单
     * @param context
     */
    public static void goOtcOrders(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, OtcOrdersActivity.class);
        context.startActivity(intent);
    }

    /**
     * otc设置
     * @param context
     */
    public static void goOtcSettings(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, OtcSettingsActivity.class);
        context.startActivity(intent);
    }

    /**
     * otc广告管理
     * @param context
     */
    public static void goOtcAds(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, OtcAdsActivity.class);
        context.startActivity(intent);
    }

    /**
     * otc发布广告
     * @param context
     */
    public static void goOtcPublishAds(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, OtcPublishAdsActivity.class);
        context.startActivity(intent);
    }

    /**
     * otc发布广告
     * @param context
     */
    public static void goOtcPublishAds(Context context, OtcAdsListResponse.AdBean adBean) {
        Intent intent = new Intent();
        intent.putExtra("ad",adBean);
        intent.setClass(context, OtcPublishAdsActivity.class);
        context.startActivity(intent);
    }

    /**
     * otc 消息
     * @param context
     * @param currentOrderInfo
     */
    public static void goOtcMessage(Context context, OtcOrderInfoResponse currentOrderInfo) {
        Intent intent = new Intent();
        intent.putExtra("order", currentOrderInfo);
        intent.setClass(context, OtcMsgActivity.class);
        context.startActivity(intent);
    }

    /**
     * 上传证明
     * @param context
     * @param mOrderInfo
     */
    public static void goOtcUploadProof(Context context, OtcOrderInfoResponse mOrderInfo) {
        Intent intent = new Intent();
        intent.putExtra("order", mOrderInfo);
        intent.setClass(context, OtcUploadProofActivity.class);
        context.startActivity(intent);
    }

    /**
     * otc订单历史证明
     * @param context
     * @param orderInfo
     */
    public static void goOtcHistoryProof(Context context, OtcOrderInfoResponse orderInfo) {
        Intent intent = new Intent();
        intent.putExtra("order", orderInfo);
        intent.setClass(context, OtcHistoryProofActivity.class);
        context.startActivity(intent);
    }

    /**
     * 设置收款渠道
     * @param context
     */
    public static void goPayChannelList(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, ReceiptChannelActivity.class);
        context.startActivity(intent);
    }

    /**
     * 添加修改收款渠道
     * @param context
     * @param receiptChannelList
     * @param item
     */
    public static void goAddReceiptChannel(Context context, OtcPayChannelListResponse receiptChannelList, OtcPaymentChannelBean item) {
        Intent intent = new Intent();
        intent.putExtra("receiptList", receiptChannelList);
        intent.putExtra("receipt", item);
        intent.setClass(context, OtcAddReceiptChannelActivity.class);
        context.startActivity(intent);
    }

    /**
     * 拨打电话（直接拨打电话）
     * @param phoneNum 电话号码
     */
    public static void callPhone(Context context, String phoneNum) {
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
            ToastUtils.showShort("No call privilege");
            return;
        }
        Intent intent = new Intent(Intent.ACTION_CALL);
        Uri data = Uri.parse("tel:" + phoneNum);
        intent.setData(data);
        context.startActivity(intent);
    }

    /**
     * 拨打电话（跳转到拨号界面，用户手动点击拨打）
     *
     * @param phoneNum 电话号码
     */
    public static void callDialPhone(Context context,String phoneNum) {
        Intent intent = new Intent(Intent.ACTION_DIAL);
        Uri data = Uri.parse("tel:" + phoneNum);
        intent.setData(data);
        context.startActivity(intent);
    }

    /**
     * 理财产品列表
     * @param context
     */
    public static void goFinanceList(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, FinanceListActivity.class);
        context.startActivity(intent);
    }
    /**
     * 理财详情
     * @param context
     * @param financeBean
     */
    public static void goFinanceDetail(Context context, FinanceBean financeBean) {
        Intent intent = new Intent();
        intent.putExtra("detail", financeBean);
        intent.setClass(context, FinanceDetailActivity.class);
        context.startActivity(intent);
    }

    /**
     * 定期和持仓理财
     * @param context
     * @param financeBean
     */
    public static void goStakingProductDetail(Context context, ProductsBean financeBean) {
        Intent intent = new Intent();
        intent.putExtra("detail", financeBean);
        intent.setClass(context, StakingProductDetailActivity.class);
        context.startActivity(intent);
    }
    /**
     * 理财申购
     * @param context
     * @param productId
     */
    public static void goFinancePurchase(Context context, String productId) {
        Intent intent = new Intent();
        intent.putExtra("productId", productId);
        intent.setClass(context, FinancePurchaseActivity.class);
        context.startActivity(intent);
    }
    /**
     * 定期理财申购
     * @param context
     * @param productId
     */
    public static void goStakingPurchase(Context context, String productId) {
        Intent intent = new Intent();
        intent.putExtra("productId", productId);
        intent.setClass(context, StakingPurchaseActivity.class);
        context.startActivity(intent);
    }
    /**
     * 理财赎回
     * @param context
     * @param productId
     */
    public static void goFinanceRedemption(Context context, String productId) {
        Intent intent = new Intent();
        intent.putExtra("productId", productId);
        intent.setClass(context, FinanceRedemptionActivity.class);
        context.startActivity(intent);
    }

    /**
     * CoinPlus订单
     * @param context
     */
    public static void goCoinPlusOrders(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, CoinPlusOrdersActivity.class);
        context.startActivity(intent);
    }

    /**
     * 杠杆订单
     * @param context
     */
    public static void goMarginOrders(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, AllMarginOrdersActivity.class);
        context.startActivity(intent);
    }

    /**
     * 理财结果页面
     * @param context
     * @param isFromOrderPage 来自订单页
     * @param recordId
     */
    public static void goFinanceResult(Context context,boolean isFromOrderPage, String sevenYearRate,String recordId) {
        Intent intent = new Intent();
        intent.putExtra("isFromOrderPage", isFromOrderPage);
        intent.putExtra("recordId", recordId);
        intent.putExtra("sevenYearRate", sevenYearRate);
        intent.setClass(context, FinanceRecodeDetailActivity.class);
        context.startActivity(intent);
    }

    public static void goCoinPlusAsset(Context context) {

        if (MainActivity.getInstance() != null){
            if(!(ActivityManager.getInstance().getCurrentActivity() instanceof MainActivity)) {
                goMain(context);
            }
            MainActivity.getInstance().goCoinPlusAsset();
        }
    }

    public static void goOptionAsset(Context context) {
        if (MainActivity.getInstance() != null){
            if(!(ActivityManager.getInstance().getCurrentActivity() instanceof MainActivity)) {
                goMain(context);
            }
            MainActivity.getInstance().goOptionAsset();
        }
    }

    public static void goFuturesAsset(Context context) {

        if (MainActivity.getInstance() != null){
            if(!(ActivityManager.getInstance().getCurrentActivity() instanceof MainActivity)) {
                goMain(context);
            }
            MainActivity.getInstance().goFuturesAsset();
        }
    }

    public static void goMarginAsset(Context context) {
        if (MainActivity.getInstance() != null){
            if(!(ActivityManager.getInstance().getCurrentActivity() instanceof MainActivity)) {
                goMain(context);
            }
            MainActivity.getInstance().goMarginAsset();
        }
    }

    /**
     * Token列表
     * @param context
     * @param isDeposit
     */
    public static void goTokenList(Context context,boolean isDeposit) {
        Intent intent = new Intent();
        intent.putExtra("isDeposit",isDeposit);
        intent.setClass(context, TokenListActivity.class);
        context.startActivity(intent);
    }

    /**
     * 绑定信息
     * @param context
     * @param bindType
     * @param bindInfo
     */
    public static void goBindInfo(Context context,String bindType, String bindInfo) {
        Intent intent = new Intent();
        intent.putExtra("bindType",bindType);
        intent.putExtra("bindInfo",bindInfo);
        intent.setClass(context, BindInfoActivity.class);
        context.startActivity(intent);
    }

    /**
     * 提币安全信息设置
     * @param context
     */
    public static void goCompleteWithDrawSecurityInfo(Context context,String tokenId) {
        Intent intent = new Intent();
        intent.putExtra("tokenId",tokenId);
        intent.setClass(context, WithDrawSecurityActivity.class);
        context.startActivity(intent);
    }

    /**
     * Zendesk Chat
     * @param context
     */
    public static void goZendeskChat(Context context) {
        VisitorInfo visitorData;
        UserInfoBean userInfo = UserManager.getInstance().getUserInfo();
        if (userInfo != null) {
            visitorData = new VisitorInfo.Builder()
                    .name(context.getResources().getString(R.string.app_name)+" And Uid: "+UserManager.getInstance().getUserId())
                    .email(userInfo.getEmail())
                    .phoneNumber(userInfo.getMobile())
                    .build();
        }else{
            visitorData = new VisitorInfo.Builder()
                    .name(context.getResources().getString(R.string.app_name)+" And Uid: "+UserManager.getInstance().getUserId())
                    .email("visitor@"+context.getResources().getString(R.string.app_name))
                    .phoneNumber("no phone")
                    .build();
        }
        ZopimChat.setVisitorInfo(visitorData);
        context.startActivity(new Intent(context, ZopimChatActivity.class));
    }

    public static void goShareProfit(Context context, OptionHoldOrderResponse.OptionHoldOrderBean itemModel, OptionDeliveryRecordResponse.OptionDeliveryRecordBean historyDelivery) {
        Intent intent = new Intent();
        intent.putExtra("hold",itemModel);
        intent.putExtra("historyDelivery",historyDelivery);
        intent.setClass(context, ShareProfitActivity.class);
        context.startActivity(intent);
    }

    /**
     * 合约分享
     * @param context
     * @param itemModel
     */
    public static void goContractShareProfit(Context context, FuturesPositionOrder itemModel) {
        Intent intent = new Intent();
        intent.putExtra("hold",itemModel);
        intent.setClass(context, ContractShareProfitActivity.class);
        context.startActivity(intent);
    }

    /**
     * 提币结果页面
     * @param context
     * @param assetItemBean
     * @param withDrawAmount
     * @param arrivalAmount
     * @param address
     * @param tag
     * @param fee
     * @param feeUnit
     */
    public static void goWithDrawResult(Context context, AssetListResponse.BalanceBean assetItemBean, String withDrawAmount, String arrivalAmount, String address, String tag, String fee, String feeUnit) {
        Intent intent = new Intent();
        intent.putExtra("assetItem",assetItemBean);
        intent.putExtra("withDrawAmount",withDrawAmount);
        intent.putExtra("arrivalAmount",arrivalAmount);
        intent.putExtra("address",address);
        intent.putExtra("tag",tag);
        intent.putExtra("fee",fee);
        intent.putExtra("feeUnit",feeUnit);
        intent.setClass(context, WithDrawResultActivity.class);
        context.startActivity(intent);
    }

    /**
     * 退出Dialog Activity
     */
    public static void goExitAppDialog(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, ExitDialogActivity.class);
        context.startActivity(intent);
    }

    /**
     * 止盈止损单
     */
    public static void goStopProfitLossActivity(Context context, FuturesPositionOrder itemModel) {
        Intent intent = new Intent();
        intent.putExtra("hold",itemModel);
        intent.setClass(context, StopProfitLossActivity.class);
        context.startActivity(intent);
    }

    /**
     * 风险资产明细
     * @param context
     * @param feeBean
     */
    public static void goRiskyAssets(Context context, FeeBeanResponse feeBean) {
        Intent intent = new Intent();
        intent.putExtra("feeBean",feeBean);
        intent.setClass(context, RiskyAssetsDetailActivity.class);
        context.startActivity(intent);
    }

    /**
     * 合约计算器
     * @param context
     * @param coinPairBean
     */
    public static void goContractCalculator(Context context, CoinPairBean coinPairBean) {
        Intent intent = new Intent();
        intent.putExtra("symbol",coinPairBean);
        intent.setClass(context, ContractCalculatorActivity.class);
        context.startActivity(intent);
    }

    /**
     * 编辑自选
     * @param context
     */
    public static void goEditOptional(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, EditOptionalActivity.class);
        context.startActivity(intent);
    }

    /**
     * 检查用户是否设置了登录密码
     * @param listener
     */
    public static void checkUserIsBindPasswd(final Context context, final BindPasswdListener listener) {
        if (UserManager.getInstance().getUserInfo()!=null && UserManager.getInstance().getUserInfo().isBindPassword()) {
            if (listener != null) {
                listener.onResult(true);
            }
        }else{
            LoginApi.GetUserInfo(new SimpleResponseListener<UserInfoBean>(){
                @Override
                public void onBefore() {
                    super.onBefore();
                    if (listener != null) {
                        listener.onBefore();
                    }
                }

                @Override
                public void onFinish() {
                    super.onFinish();
                    if (listener != null) {
                        listener.onFinish();
                    }
                }

                @Override
                public void onSuccess(UserInfoBean response) {
                    super.onSuccess(response);
                    if (listener != null) {
                        if (CodeUtils.isSuccess(response,true)) {
                            UserManager.getInstance().saveUserInfo(response);
                            boolean bindPassword = response.isBindPassword();
                            listener.onResult(bindPassword);
                            if (!bindPassword) {
                                showGoSetPasswdDialog(context);
                            }
                        }
                    }
                }

                @Override
                public void onError(Throwable error) {
                    super.onError(error);
                    if (listener != null) {
                        listener.onError(error);
                    }
                }
            });
        }
    }

    /**
     * 设置登录密码
     * @param context
     */
    public static void showGoSetPasswdDialog(final Context context) {
        DialogUtils.showDialog(ActivityManager.getInstance().getCurrentActivity(), "", context.getResources().getString(R.string.string_go_set_login_passwd_tips), context.getResources().getString(R.string.string_set), context.getResources().getString(R.string.string_not_need), true, new DialogUtils.OnButtonEventListener() {
            @Override
            public void onConfirm() {
                IntentUtils.goSetPwd(context);
            }

            @Override
            public void onCancel() {

            }
        });
    }


    /**
     * 去融币
     */
    public static void goMargin(Activity context,String token) {
        UserManager.getInstance().LoginAndGoin(context, new LoginResultCallback() {
            @Override
            public void onLoginSucceed() {
                super.onLoginSucceed();
                Intent intent = new Intent();
                intent.setClass(context, MarginActivity.class);
                intent.putExtra(AppData.INTENT.MARGIN_LOAN_TOKEN,token);
                context.startActivity(intent);
            }
        });
    }

    public static void goMarginLoanHistory(Activity context,String token) {
        Intent intent = new Intent();
        intent.setClass(context, MarginLoanHistoryActivity.class);
        intent.putExtra(AppData.INTENT.MARGIN_LOAN_TOKEN,token);
        context.startActivity(intent);
    }

    public static void goMarginRecordDetail(Context context,QueryLoanRecordResponse.DataBean dataBean) {
        Intent intent = new Intent();
        intent.setClass(context, LoanRecordDetailActivity.class);
        intent.putExtra("loanRecord",dataBean);
        context.startActivity(intent);

    }

    public static void goMarginToRepay(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, MarginToRepayActivity.class);
        context.startActivity(intent);

    }

    public static void goMarginRepay(Context context,  QueryLoanRecordResponse.DataBean item) {
        Intent intent = new Intent();
        intent.setClass(context, MarginRepayActivity.class);
        intent.putExtra("loanRecord",item);
        context.startActivity(intent);

    }

    public static void goMarginRepayHistory(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, MarginRepayHistoryActivity.class);
        context.startActivity(intent);
    }

    public static void goMarginRepayRecordDetail(Context context, QueryRepayRecordResponse.DataBean repayRecord) {
        Intent intent = new Intent();
        intent.putExtra("repayRecord",repayRecord);
        intent.setClass(context, MarginRepayDetailActivity.class);
        context.startActivity(intent);

    }

    public static void goStakingPurchaseResult(Context context, String productId, String transferId) {
        Intent intent = new Intent();
        intent.putExtra("productId",productId);
        intent.putExtra("transferId",transferId);
        intent.setClass(context, StakingPurchaseResultActivity.class);
        context.startActivity(intent);
    }

    public static void goStakingOrders(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, AllStakingOrdersActivity.class);
        context.startActivity(intent);

    }

    /**
     * 切换线路
     * @param context
     */
    public static void goSwitchLines(Context context) {
        Intent intent = new Intent();
        intent.setClass(context, NetworkLineActivity.class);
        context.startActivity(intent);
    }

    public static void goScanAuthComfirm(Context context, String result) {
        Intent intent = new Intent();
        intent.putExtra("loginQRCode",result);
        intent.setClass(context, ScanLoginActivity.class);
        context.startActivity(intent);
    }

    public interface BindPasswdListener{

        void onBefore();

        void onFinish();

        void onError(Throwable error);

        void onResult(boolean bindPassword);
    }

    public interface ApiRequestListener{

        void onBefore();

        void onFinish();

        void onError(Throwable error);

    }
    /**
     * 页面跳转
     * @param context
     * @param cls
     */
    public static void startActivity(Context context, Class cls) {
        Intent intent = new Intent(context, cls);
        context.startActivity(intent);
    }
}
