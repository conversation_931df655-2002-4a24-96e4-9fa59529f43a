/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: DialogUtils.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.utils;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import java.util.HashMap;
import java.util.List;

import io.bhex.app.BuildConfig;
import io.bhex.app.R;
import io.bhex.app.ScreenShot.ShotScreenUtils;
import io.bhex.app.view.InputView;
import io.bhex.app.view.PasswordEditText;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.core.SPEx;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.otc.bean.OtcConfigResponse;
import io.bhex.sdk.otc.bean.OtcOrderInfoResponse;
import io.bhex.sdk.trade.futures.bean.FuturesOrderResponse;

public class DialogUtils {

    /**
     * 显示对话框(有两个button, button显示的文字可以自由设置)
     *
     * @param context        上下文索引
     * @param btnConfirmText button显示的文字
     * @param canceled       按返回键是否关闭对话框 true 关闭 false 不关闭
     * @param btnCancelText  button显示的文字 buttonEventListener 用户点击button时的监听器, 此参数可以传null
     */
    public static void showKYCThreeConditons(Context context,String title, String btnConfirmText, String btnCancelText, boolean canceled, final OnButtonEventListener buttonEventListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(canceled);
        dialog.show();
        LinearLayout layout = (LinearLayout) ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.dialog_kyc_three_condition_layout, null);
        TextView titleTx = layout.findViewById(R.id.title);
        if (TextUtils.isEmpty(title)) {
            titleTx.setVisibility(View.GONE);
        }else{
            titleTx.setVisibility(View.VISIBLE);
            titleTx.setText(title);
        }
        Button btCommit = layout.findViewById(R.id.btnConfirm);
        btCommit.setText(btnConfirmText);
        btCommit.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                dialog.dismiss();
                if (buttonEventListener != null) {
                    buttonEventListener.onConfirm();
                }
            }
        });
        Button btCancel = layout.findViewById(R.id.btnCancel);
        if (TextUtils.isEmpty(btnCancelText)) {
            btCancel.setVisibility(View.GONE);
        }else{
            btCancel.setVisibility(View.VISIBLE);
            btCancel.setText(btnCancelText);
        }
        btCancel.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                dialog.dismiss();
                if (buttonEventListener != null) {
                    buttonEventListener.onCancel();
                }
            }
        });
        int screenWidth = PixelUtils.getScreenWidth();
        dialog.getWindow().setLayout((int) (screenWidth - context.getResources().getDimension(R.dimen.dip_30)), WindowManager.LayoutParams.WRAP_CONTENT);
        dialog.getWindow().setContentView(layout);
    }
    /**
     * 显示对话框(有两个button, button显示的文字可以自由设置)
     *
     * @param context        上下文索引
     * @param msg            dialog显示的消息内容的字符串ID
     * @param btnConfirmText button显示的文字
     * @param canceled       按返回键是否关闭对话框 true 关闭 false 不关闭
     * @param btnCancelText  button显示的文字 buttonEventListener 用户点击button时的监听器, 此参数可以传null
     */
    public static void showDialog(Context context,String title, String msg, String btnConfirmText, String btnCancelText, boolean canceled, final OnButtonEventListener buttonEventListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(canceled);
        dialog.show();
        LinearLayout layout = (LinearLayout) ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.dialog_two_button_layout, null);
        TextView titleTx = layout.findViewById(R.id.title);
        if (TextUtils.isEmpty(title)) {
            titleTx.setVisibility(View.GONE);
        }else{
            titleTx.setVisibility(View.VISIBLE);
            titleTx.setText(title);
        }
        TextView msgText = layout.findViewById(R.id.msg);
        msgText.setText(msg);
        Button btCommit = layout.findViewById(R.id.btnConfirm);
        btCommit.setText(btnConfirmText);
        btCommit.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                dialog.dismiss();
                if (buttonEventListener != null) {
                    buttonEventListener.onConfirm();
                }
            }
        });
        Button btCancel = layout.findViewById(R.id.btnCancel);
        if (TextUtils.isEmpty(btnCancelText)) {
            btCancel.setVisibility(View.GONE);
        }else{
            btCancel.setVisibility(View.VISIBLE);
            btCancel.setText(btnCancelText);
        }
        btCancel.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                dialog.dismiss();
                if (buttonEventListener != null) {
                    buttonEventListener.onCancel();
                }
            }
        });
        int screenWidth = PixelUtils.getScreenWidth();
        dialog.getWindow().setLayout((int) (screenWidth * 0.75), WindowManager.LayoutParams.WRAP_CONTENT);
        dialog.getWindow().setContentView(layout);
    }

    /**
     * 显示对话框(有两个button, button显示的文字可以自由设置)
     *
     * @param context        上下文索引
     * @param canceled       按返回键是否关闭对话框 true 关闭 false 不关闭
     */
    public static void showLoanSucceedDialog(Context context,boolean canceled, final OnButtonEventListener buttonEventListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(canceled);
        dialog.show();
        LinearLayout layout = (LinearLayout) ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.dialog_loan_succeed_layout, null);
        TextView msgText = layout.findViewById(R.id.msg);
        Button btCommit = layout.findViewById(R.id.btnConfirm);
        btCommit.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                dialog.dismiss();
                if (buttonEventListener != null) {
                    buttonEventListener.onConfirm();
                }
            }
        });
        Button btCancel = layout.findViewById(R.id.btnCancel);
        btCancel.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                dialog.dismiss();
                if (buttonEventListener != null) {
                    buttonEventListener.onCancel();
                }
            }
        });
        int screenWidth = PixelUtils.getScreenWidth();
        dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        dialog.getWindow().setLayout((int) (screenWidth - PixelUtils.dp2px(48)), WindowManager.LayoutParams.WRAP_CONTENT);
        dialog.getWindow().setContentView(layout);
    }

    /**
     * 显示对话框(有一个button, button显示的文字可以自由设置)
     *
     * @param context        上下文索引
     * @param msg            dialog显示的消息内容的字符串ID
     * @param btnConfirmText button显示的文字
     * @param canceled       按返回键是否关闭对话框 true 关闭 false 不关闭
     */
    public static void showDialogOneBtn(Context context,String title, String msg, String btnConfirmText,boolean canceled, final OnButtonEventListener buttonEventListener) {
        showDialogOneBtn(context,title,msg,Gravity.LEFT,btnConfirmText,canceled,0.85f,buttonEventListener);

    }

    /**
     * 显示对话框(有一个button, button显示的文字可以自由设置)
     *
     * @param context        上下文索引
     * @param msg            dialog显示的消息内容的字符串ID
     * @param btnConfirmText button显示的文字
     * @param canceled       按返回键是否关闭对话框 true 关闭 false 不关闭
     */
    public static void showDialogOneBtn(Context context,String title, String msg,int contentGravity, String btnConfirmText,boolean canceled,float ratio, final OnButtonEventListener buttonEventListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(canceled);
        dialog.show();
        LinearLayout layout = (LinearLayout) ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.dialog_one_button_layout, null);
        TextView titleTx = layout.findViewById(R.id.title);
        if (TextUtils.isEmpty(title)) {
            titleTx.setVisibility(View.GONE);
        }else{
            titleTx.setVisibility(View.VISIBLE);
            titleTx.setText(title);
        }
        TextView msgText = layout.findViewById(R.id.msg);
        msgText.setText(msg);
        msgText.setGravity(contentGravity);
        Button btCommit = layout.findViewById(R.id.btnConfirm);
        btCommit.setText(btnConfirmText);
        btCommit.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                dialog.dismiss();
                if (buttonEventListener != null) {
                    buttonEventListener.onConfirm();
                }
            }
        });

        int screenWidth = PixelUtils.getScreenWidth();
        dialog.getWindow().setLayout((int) (screenWidth * ratio), WindowManager.LayoutParams.WRAP_CONTENT);
        dialog.getWindow().setContentView(layout);
    }

    /**
     * 显示对话框(有一个button, button显示的文字可以自由设置)  新的UI
     *
     * @param context        上下文索引
     * @param msg            dialog显示的消息内容的字符串ID
     * @param btnConfirmText button显示的文字
     * @param canceled       按返回键是否关闭对话框 true 关闭 false 不关闭
     */
    public static void showDialogOneBtn_new(Context context,String title, String msg, String btnConfirmText,boolean canceled, final OnButtonEventListener buttonEventListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(canceled);
        dialog.show();
        LinearLayout layout = (LinearLayout) ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.dialog_one_button_layout_new, null);
        TextView titleTx = layout.findViewById(R.id.title);
        if (TextUtils.isEmpty(title)) {
            titleTx.setVisibility(View.GONE);
        }else{
            titleTx.setVisibility(View.VISIBLE);
            titleTx.setText(title);
        }
        TextView msgText = layout.findViewById(R.id.msg);
        msgText.setText(msg);
        Button btCommit = layout.findViewById(R.id.btnConfirm);
        btCommit.setText(btnConfirmText);
        btCommit.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                dialog.dismiss();
                if (buttonEventListener != null) {
                    buttonEventListener.onConfirm();
                }
            }
        });

        int screenWidth = PixelUtils.getScreenWidth();
        dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        dialog.getWindow().setLayout((int) (screenWidth - PixelUtils.dp2px(48)), WindowManager.LayoutParams.WRAP_CONTENT);
        dialog.getWindow().setContentView(layout);
    }


    /**
     * 显示强平明细
     *
     * @param context        上下文索引
     * @param order
     * @param canceled       按返回键是否关闭对话框 true 关闭 false 不关闭
     */
    public static void showForceCloseOrderDetail(Context context, FuturesOrderResponse order, boolean canceled, final OnButtonEventListener buttonEventListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(canceled);
        dialog.show();
        LinearLayout layout = (LinearLayout) ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.dialog_contract_force_close_order_detail_layout, null);
        TextView time = layout.findViewById(R.id.time);
        TextView name = layout.findViewById(R.id.name);
        TextView liquidationPrice = layout.findViewById(R.id.liquidationPrice);
        TextView bankruptcyPrice = layout.findViewById(R.id.bankruptcyPrice);
        time.setText(DateUtils.getSimpleTimeFormat(Long.valueOf(order.getTime()), "yyyy-MM-dd HH:mm:ss"));
        name.setText(order.getSymbolName());
        liquidationPrice.setText(order.getLiquidationPrice());
//        bankruptcyPrice.setText(order.getPrice());
        Button btCommit = layout.findViewById(R.id.btnConfirm);
        btCommit.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                dialog.dismiss();
                if (buttonEventListener != null) {
                    buttonEventListener.onConfirm();
                }
            }
        });

        int screenWidth = PixelUtils.getScreenWidth();
        dialog.getWindow().setLayout((int) (screenWidth * 0.75), WindowManager.LayoutParams.WRAP_CONTENT);
        dialog.getWindow().setContentView(layout);
    }


    /**
     * 显示对话框(有一个button, button显示的文字可以自由设置)
     *
     * @param context        上下文索引
     * @param msg            dialog显示的消息内容的字符串ID
     * @param btnConfirmText button显示的文字
     * @param canceled       按返回键是否关闭对话框 true 关闭 false 不关闭
     */
    public static void showSystemDialogOneBtn(Context context,String title, String msg, String btnConfirmText,boolean canceled, final OnButtonEventListener buttonEventListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(canceled);
        dialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
        dialog.show();
        LinearLayout layout = (LinearLayout) ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.dialog_one_button_layout, null);
        TextView titleTx = layout.findViewById(R.id.title);
        if (TextUtils.isEmpty(title)) {
            titleTx.setVisibility(View.GONE);
        }else{
            titleTx.setVisibility(View.VISIBLE);
            titleTx.setText(title);
        }
        TextView msgText = layout.findViewById(R.id.msg);
        msgText.setText(msg);
        Button btCommit = layout.findViewById(R.id.btnConfirm);
        btCommit.setText(btnConfirmText);
        btCommit.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                dialog.dismiss();
                if (buttonEventListener != null) {
                    buttonEventListener.onConfirm();
                }
            }
        });

        int screenWidth = PixelUtils.getScreenWidth();
        dialog.getWindow().setLayout((int) (screenWidth - context.getResources().getDimension(R.dimen.dip_30)), WindowManager.LayoutParams.WRAP_CONTENT);
        dialog.getWindow().setContentView(layout);
    }

    /**
     * 显示对话框(有一个button, button显示的文字可以自由设置)
     *
     * @param context        上下文索引
     * @param msg            dialog显示的消息内容的字符串ID
     * @param btnConfirmText button显示的文字
     * @param canceled       按返回键是否关闭对话框 true 关闭 false 不关闭
     */
    public static void showLockCoinTipsDialogOneBtn(Context context,String title, String msg, String btnConfirmText,boolean canceled, final OnButtonEventListener buttonEventListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(canceled);
        dialog.show();
        LinearLayout layout = (LinearLayout) ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.dialog_lock_coin_tips_layout, null);
        TextView titleTx = layout.findViewById(R.id.title);
        if (TextUtils.isEmpty(title)) {
            titleTx.setVisibility(View.GONE);
        }else{
            titleTx.setVisibility(View.VISIBLE);
            titleTx.setText(title);
        }
        TextView msgText = layout.findViewById(R.id.msg);
        msgText.setText(msg);
        TextView btCommit = layout.findViewById(R.id.btnConfirm);
        btCommit.setText(btnConfirmText);
        btCommit.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                dialog.dismiss();
                if (buttonEventListener != null) {
                    buttonEventListener.onConfirm();
                }
            }
        });

        int screenWidth = PixelUtils.getScreenWidth();
        dialog.getWindow().setLayout((int) (screenWidth - context.getResources().getDimension(R.dimen.dip_30)), WindowManager.LayoutParams.WRAP_CONTENT);
        dialog.getWindow().setContentView(layout);
    }


    /**
     * 显示分享对话框(有一个button, button显示的文字可以自由设置)
     *  @param context        上下文索引
     * @param canceled       按返回键是否关闭对话框 true 关闭 false 不关闭
     */
    public static AlertDialog showShareDialog(Context context, String title,int thidIconRes,String thirdTitle,boolean canceled, final OnShareListener shareListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final AlertDialog dialog = builder.create();
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(canceled);
        dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                if (shareListener != null) {
                    shareListener.onCancel();
                }
            }
        });
        dialog.show();
        RelativeLayout layout = (RelativeLayout) ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.dialog_share_layout, null);
        ImageView thidIcon = layout.findViewById(R.id.thidIcon);
        if (thidIconRes!=-1) {
            thidIcon.setBackgroundResource(thidIconRes);
        }
        TextView thidTitleTv = layout.findViewById(R.id.thidTitle);
        if (!TextUtils.isEmpty(thirdTitle)) {
            thidTitleTv.setText(thirdTitle);
        }

        TextView titleTx = layout.findViewById(R.id.title);
        TextView cancel = layout.findViewById(R.id.cancel);
        cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        if (TextUtils.isEmpty(title)) {
//            titleTx.setVisibility(View.GONE);
        }else{
            titleTx.setVisibility(View.VISIBLE);
            titleTx.setText(title);
        }
        //设置支持的分享列表
        TextView moreBtnTitleTv = layout.findViewById(R.id.moreBtnTitle);
        if (TextUtils.isEmpty(BuildConfig.WEIXIN_ID)) {
            layout.findViewById(R.id.shareWx).setVisibility(View.GONE);
            layout.findViewById(R.id.shareWxFriends).setVisibility(View.GONE);
            moreBtnTitleTv.setText(context.getResources().getString(R.string.string_share));
        }else{
            moreBtnTitleTv.setText(context.getResources().getString(R.string.string_more));
        }


        layout.findViewById(R.id.shareWx).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (shareListener != null) {
                    shareListener.onShareWx();
                }
            }
        });
        layout.findViewById(R.id.shareWxFriends).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (shareListener != null) {
                    shareListener.onShareWxCircle();
                }
            }
        });

        layout.findViewById(R.id.shareSaveImg).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (shareListener != null) {
                    shareListener.onSavePic();
                }
            }
        });


        layout.findViewById(R.id.btnMore).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (shareListener != null) {
                    shareListener.onMore();
                }
            }
        });

        int screenWidth = PixelUtils.getScreenWidth();
        dialog.getWindow().setLayout(WindowManager.LayoutParams.MATCH_PARENT,WindowManager.LayoutParams.MATCH_PARENT);
        dialog.getWindow().setContentView(layout);
        return dialog;
    }

    /**
     * 显示分享对话框(有一个button, button显示的文字可以自由设置)
     *  @param context        上下文索引
     * @param canceled       按返回键是否关闭对话框 true 关闭 false 不关闭
     */
    public static AlertDialog showShareDialogOneBtn(Activity context, String title, String imgFilePath, boolean canceled,  final OnShareListener shareListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final AlertDialog dialog = builder.create();
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(canceled);
        dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                if (shareListener != null) {
                    shareListener.onCancel();
                }
            }
        });
        dialog.show();
        int screenWidth = PixelUtils.getScreenWidth();

        RelativeLayout layout = (RelativeLayout) context.getLayoutInflater().inflate(R.layout.dialog_share_qrcode_layout, null);
        //View preView = layout.findViewById(R.id.preView);
        ImageView preViewImg = layout.findViewById(R.id.preViewImg);
        if (!TextUtils.isEmpty(imgFilePath)) {
            preViewImg.setVisibility(View.VISIBLE);
            Bitmap bitmap = ShotScreenUtils.scaleResource(context,imgFilePath);

            //preViewImg.setImageURI(Uri.fromFile(new File(imgFilePath)));
            preViewImg.setImageBitmap(bitmap);
        }else{
            preViewImg.setVisibility(View.GONE);
        }

        TextView titleTx = layout.findViewById(R.id.title);
        TextView cancel = layout.findViewById(R.id.cancel);
        cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        if (TextUtils.isEmpty(title)) {
//            titleTx.setVisibility(View.GONE);
        }else{
            titleTx.setVisibility(View.VISIBLE);
            titleTx.setText(title);
        }

        //设置支持的分享列表
        TextView moreBtnTitleTv = layout.findViewById(R.id.moreBtnTitle);
        if (TextUtils.isEmpty(BuildConfig.WEIXIN_ID)) {
            layout.findViewById(R.id.shareWx).setVisibility(View.GONE);
            layout.findViewById(R.id.shareWxFriends).setVisibility(View.GONE);
            moreBtnTitleTv.setText(context.getResources().getString(R.string.string_share));
        }else{
            moreBtnTitleTv.setText(context.getResources().getString(R.string.string_more));
        }

        layout.findViewById(R.id.shareWx).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (shareListener != null) {
                    shareListener.onShareWx();
                }
            }
        });
        layout.findViewById(R.id.shareWxFriends).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (shareListener != null) {
                    shareListener.onShareWxCircle();
                }
            }
        });

        layout.findViewById(R.id.shareSaveImg).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (shareListener != null) {
                    shareListener.onSavePic();
                }
            }
        });

        layout.findViewById(R.id.btnMore).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (shareListener != null) {
                    shareListener.onMore();
                }
            }
        });

        layout.findViewById(R.id.share_root).setOnClickListener(v->{
            dialog.dismiss();
        });

        dialog.getWindow().setLayout(WindowManager.LayoutParams.MATCH_PARENT,WindowManager.LayoutParams.MATCH_PARENT);
        dialog.getWindow().setContentView(layout);
        return dialog;
    }

    /**
     * 显示对话框(有两个button, button显示的文字可以自由设置)
     *
     * @param context        上下文索引
     * @param msg            dialog显示的消息内容的字符串ID
     * @param btnConfirmText button显示的文字
     * @param canceled       按返回键是否关闭对话框 true 关闭 false 不关闭
     */
    public static void showPasswdDialog(Context context,String title, String msg,String tips, String btnConfirmText,boolean canceled, final OnButtonEventListener buttonEventListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context,R.style.inputDialog);
        final AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(true);
        dialog.setCancelable(canceled);
        dialog.show();
        LinearLayout layout = (LinearLayout) ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.dialog_passwd_layout, null);
        TextView titleTx = layout.findViewById(R.id.title);
        if (TextUtils.isEmpty(title)) {
            titleTx.setVisibility(View.GONE);
        }else{
            titleTx.setVisibility(View.VISIBLE);
            titleTx.setText(title);
        }
        TextView contentTv = layout.findViewById(R.id.content);
        contentTv.setText(msg);
        TextView tipsTv = layout.findViewById(R.id.tips);
        if (!TextUtils.isEmpty(tips)) {
            tipsTv.setText(tips);
        }
        final PasswordEditText passwdEt = layout.findViewById(R.id.passwdEt);
//        passwdEt.postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                passwdEt.setFocus();
//            }
//        },1500);
        ImageView close = layout.findViewById(R.id.close);
        close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (dialog != null) {
                    if (dialog.isShowing()) {
                        dialog.dismiss();
                    }
                }
            }
        });
        TextView btSwitch = layout.findViewById(R.id.button);
        btSwitch.setText(btnConfirmText);
        btSwitch.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                if (buttonEventListener != null) {
                    buttonEventListener.onConfirm();
                }
            }
        });

        int screenWidth = PixelUtils.getScreenWidth();
        dialog.getWindow().setLayout((int) (screenWidth - context.getResources().getDimension(R.dimen.dip_30)), WindowManager.LayoutParams.WRAP_CONTENT);
        dialog.getWindow().setContentView(layout);
    }

    /**
     *
     * @param context
     * @param title
     * @param msg
     * @param btnConfirmText
     * @param canceled
     * @param buttonEventListener
     */
    public static AlertDialog showVerifyDialog(final Context context, String title, String msg, String btnConfirmText, boolean canceled, final OnVerifyEditEventListener buttonEventListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(canceled);
        dialog.show();
        LinearLayout layout = (LinearLayout) ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.dialog_two_verify_layout, null);
        TextView titleTx = layout.findViewById(R.id.title);
        if (TextUtils.isEmpty(title)) {
            titleTx.setVisibility(View.GONE);
        } else {
            titleTx.setVisibility(View.VISIBLE);
            titleTx.setText(title);
        }
        TextView msgText = layout.findViewById(R.id.msg);
        msgText.setText(msg);
        final InputView verifyInput = layout.findViewById(R.id.verify_code_et);
        verifyInput.setInputString("");
        verifyInput.setPaddingRight(PixelUtils.dp2px(80));
        final TextView btnVerify = layout.findViewById(R.id.get_verify_code);
        if (buttonEventListener != null) {
            buttonEventListener.sendVerify(btnVerify,true);
        }
        btnVerify.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (buttonEventListener != null) {
                    buttonEventListener.sendVerify(btnVerify,false);
                }
            }
        });

        final Button btCommit = layout.findViewById(R.id.btnConfirm);
        btCommit.setText(btnConfirmText);
        btCommit.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                if (buttonEventListener != null) {
                    String verifyCode = verifyInput.getInputString();
                    if (!TextUtils.isEmpty(verifyCode)) {
                        buttonEventListener.onConfirm(btCommit,verifyCode,btnVerify);
                    }else{
                        ToastUtils.showShort(context, context.getResources().getString(R.string.input_verify));
                        return;
                    }
                }
//                dialog.dismiss();
            }
        });

        TextView btnCancel = layout.findViewById(R.id.btnCancel);
        btnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (buttonEventListener != null) {
                    buttonEventListener.onCancel();
                }
            }
        });

        int screenWidth = PixelUtils.getScreenWidth();
        dialog.getWindow().setLayout((int) (screenWidth - context.getResources().getDimension(R.dimen.dip_30)), WindowManager.LayoutParams.WRAP_CONTENT);
        dialog.getWindow().setContentView(layout);

        //弹出对话框时需要点击输入框才能弹出软键盘
        dialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
        //加上下面这一行弹出对话框时软键盘随之弹出
        //dialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
        return dialog;
    }

    /**
     *显示资金密码
     * @param context
     * @param title
     * @param msg
     * @param btnConfirmText
     * @param canceled
     * @param buttonEventListener
     */
    public static AlertDialog showTradePasswdDialog(final Context context, String title, String msg, String btnConfirmText, boolean canceled, final OnOtcEventListener buttonEventListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(canceled);
        dialog.show();
        LinearLayout layout = (LinearLayout) ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.dialog_trade_passwd_layout, null);
        TextView titleTx = layout.findViewById(R.id.title);
        if (TextUtils.isEmpty(title)) {
            titleTx.setVisibility(View.GONE);
        } else {
            titleTx.setVisibility(View.VISIBLE);
            titleTx.setText(title);
        }
        TextView msgText = layout.findViewById(R.id.msg);
        if (TextUtils.isEmpty(msg)) {
            msgText.setVisibility(View.GONE);
        } else {
            msgText.setVisibility(View.VISIBLE);
            msgText.setText(msg);
        }
        final InputView input = layout.findViewById(R.id.input_et);
        Button btnConfirm = layout.findViewById(R.id.btnConfirm);
        if (!TextUtils.isEmpty(btnConfirmText)) {
            btnConfirm.setText(btnConfirmText);
        }
        btnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String inputString = input.getInputString();
                if (TextUtils.isEmpty(inputString)) {
                    ToastUtils.showShort(context.getResources().getString(R.string.input_finance_passwd));
                    return;
                }
                dialog.dismiss();
                if (buttonEventListener != null) {
                    buttonEventListener.onConfirm(inputString);
                }
            }
        });

        TextView btnCancel = layout.findViewById(R.id.btnCancel);
        btnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (buttonEventListener != null) {
                    buttonEventListener.onCancel();
                }
            }
        });

        layout.findViewById(R.id.btn_forget_pwd).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                UserInfoBean userInfo = UserManager.getInstance().getUserInfo();
                if (userInfo != null) {
                    IntentUtils.goFinancePasswd(context,userInfo.isBindTradePwd());
                }
            }
        });

        int screenWidth = PixelUtils.getScreenWidth();
        dialog.getWindow().setLayout((int) (screenWidth - context.getResources().getDimension(R.dimen.dip_30)), WindowManager.LayoutParams.WRAP_CONTENT);
        dialog.getWindow().setContentView(layout);

        //弹出对话框时需要点击输入框才能弹出软键盘
        dialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
        //加上下面这一行弹出对话框时软键盘随之弹出
        //dialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
        return dialog;
    }

    /**
     * 登录密码确认框
     * @param context
     * @param title
     * @param msg
     * @param btnConfirmText
     * @param canceled
     * @param buttonEventListener
     */
    public static AlertDialog showLoginPasswdDialog(final Context context, String title, String msg, String btnConfirmText, boolean canceled, final OnLoginListener buttonEventListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(canceled);
        dialog.show();
        LinearLayout layout = (LinearLayout) ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.dialog_login_passwd_layout, null);
        TextView titleTx = layout.findViewById(R.id.title);
        if (TextUtils.isEmpty(title)) {
            titleTx.setVisibility(View.GONE);
        } else {
            titleTx.setVisibility(View.VISIBLE);
            titleTx.setText(title);
        }
        TextView msgText = layout.findViewById(R.id.msg);
        if (TextUtils.isEmpty(msg)) {
            msgText.setVisibility(View.GONE);
        } else {
            msgText.setVisibility(View.VISIBLE);
            msgText.setText(msg);
        }
        final InputView input = layout.findViewById(R.id.input_et);
        Button btnConfirm = layout.findViewById(R.id.btnConfirm);
        if (!TextUtils.isEmpty(btnConfirmText)) {
            btnConfirm.setText(btnConfirmText);
        }
        btnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String inputString = input.getInputString();
                if (TextUtils.isEmpty(inputString)) {
                    ToastUtils.showShort(context.getResources().getString(R.string.input_pwd));
                    return;
                }
//                dialog.dismiss();
                if (buttonEventListener != null) {
                    buttonEventListener.onConfirm(dialog,inputString);
                }
            }
        });

        TextView btnCancel = layout.findViewById(R.id.btnCancel);
        btnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (buttonEventListener != null) {
                    buttonEventListener.onCancel();
                }
            }
        });

        layout.findViewById(R.id.btn_forget_pwd).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (buttonEventListener != null) {
                    buttonEventListener.forgetPasswd();
                }
            }
        });

        int screenWidth = PixelUtils.getScreenWidth();
        dialog.getWindow().setLayout((int) (screenWidth - context.getResources().getDimension(R.dimen.dip_30)), WindowManager.LayoutParams.WRAP_CONTENT);
        dialog.getWindow().setContentView(layout);

        //弹出对话框时需要点击输入框才能弹出软键盘
        dialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
        //加上下面这一行弹出对话框时软键盘随之弹出
        //dialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
        return dialog;
    }

    /**
     *Otc付款确认框
     * @param context
     * @param bankMap
     * @param currentPaymentBean
     * @param title
     * @param msg
     * @param btnConfirmText
     * @param canceled
     * @param buttonEventListener
     */
    public static AlertDialog showOtcPayConfirm(final Context context, HashMap<String, OtcConfigResponse.BankBean> bankMap, OtcOrderInfoResponse orderInfo, OtcOrderInfoResponse.PaymentTermListBean currentPaymentBean, String title, String msg, String btnConfirmText, String btnCancelText, boolean canceled, final OnOtcEventListener buttonEventListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(canceled);
        dialog.show();
        View layout = ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.dialog_otc_confirm_pay_layout, null);
        layout.findViewById(R.id.closeBtn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (buttonEventListener != null) {
                    buttonEventListener.onCancel();
                }
                dialog.dismiss();
            }
        });
        TextView titleTx = layout.findViewById(R.id.title);
        if (TextUtils.isEmpty(title)) {
            titleTx.setVisibility(View.GONE);
        } else {
            titleTx.setVisibility(View.VISIBLE);
            titleTx.setText(title);
        }
        TextView msgText = layout.findViewById(R.id.importTips);
        msgText.setText(msg);
        ((TextView)layout.findViewById(R.id.currency_name)).setText(orderInfo.getCurrencyId());
        ((TextView)layout.findViewById(R.id.pay_amount)).setText(orderInfo.getAmount()+" "+orderInfo.getCurrencyId());
        if (currentPaymentBean.getPaymentType() ==0){
            //银行卡
            String bankId = currentPaymentBean.getBankName();
            OtcConfigResponse.BankBean bankBean = bankMap.get(bankId);
            String bankName;
            if (bankBean != null) {
                bankName = bankBean.getName();
            }else {
                bankName = currentPaymentBean.getBankName();
            }

            String branchName = currentPaymentBean.getBranchName();
            if (!TextUtils.isEmpty(branchName)) {
                ((TextView)layout.findViewById(R.id.pay_way)).setText(bankName+"\n"+currentPaymentBean.getBranchName()+"\n"+currentPaymentBean.getAccountNo());
            }else{
                ((TextView)layout.findViewById(R.id.pay_way)).setText(bankName+"\n"+currentPaymentBean.getAccountNo());
            }
        }else{
            ((TextView)layout.findViewById(R.id.pay_way)).setText(currentPaymentBean.getPayName());
        }

        ((TextView)layout.findViewById(R.id.pay_target)).setText(currentPaymentBean.getRealName());
//        ((TextView)layout.findViewById(R.id.tip_red)).setText(context.getResources().getString(R.string.string_format_otc_buy_pay_sure_tips,orderInfo.getBuyerRealName(),orderInfo.getSellerRealName()));

        //        final InputView tradePasswdInput = layout.findViewById(R.id.tradePasswd);
//        tradePasswdInput.setInputString("");


        final Button btCommit = layout.findViewById(R.id.btn_sure);
        btCommit.setText(btnConfirmText);
        btCommit.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                if (buttonEventListener != null) {
                    buttonEventListener.onConfirm("");
                }
                dialog.dismiss();
            }
        });

        int screenWidth = PixelUtils.getScreenWidth();
        dialog.getWindow().setLayout((int) (screenWidth - context.getResources().getDimension(R.dimen.dip_30)), WindowManager.LayoutParams.WRAP_CONTENT);
        dialog.getWindow().setContentView(layout);

        //弹出对话框时需要点击输入框才能弹出软键盘
        dialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
        //加上下面这一行弹出对话框时软键盘随之弹出
        //dialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
        return dialog;
    }

    /**
     *Otc放币确认框
     * @param context
     * @param title
     * @param msg
     * @param btnConfirmText
     * @param canceled
     * @param buttonEventListener
     */
    public static AlertDialog showOtcGiveBConfirm(final Context context,HashMap<String, OtcConfigResponse.BankBean> bankMap, OtcOrderInfoResponse orderInfo, OtcOrderInfoResponse.PaymentTermListBean currentPaymentBean, String title, String msg, String btnConfirmText, String btnCancelText, boolean canceled, final OnOtcEventListener buttonEventListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(canceled);
        dialog.show();
        View layout = ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.dialog_otc_give_b_layout, null);
        layout.findViewById(R.id.closeBtn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (buttonEventListener != null) {
                    buttonEventListener.onCancel();
                }
                dialog.dismiss();
            }
        });
        TextView titleTx = layout.findViewById(R.id.title);
        if (TextUtils.isEmpty(title)) {
            titleTx.setVisibility(View.GONE);
        } else {
            titleTx.setVisibility(View.VISIBLE);
            titleTx.setText(title);
        }
        TextView msgText = layout.findViewById(R.id.importTips);
        msgText.setText(msg);
        ((TextView)layout.findViewById(R.id.give_b_name)).setText(orderInfo.getTokenName());
        ((TextView)layout.findViewById(R.id.give_b_quantity)).setText(orderInfo.getQuantity()+" "+orderInfo.getTokenName());
        ((TextView)layout.findViewById(R.id.receivables_amount)).setText(orderInfo.getAmount()+" "+orderInfo.getCurrencyId());

        if (currentPaymentBean.getPaymentType() ==0){
            //银行卡
            String bankId = currentPaymentBean.getBankName();
            OtcConfigResponse.BankBean bankBean = bankMap.get(bankId);
            String bankName;
            if (bankBean != null) {
                bankName = bankBean.getName();
            }else {
                bankName = currentPaymentBean.getBankName();
            }

            String branchName = currentPaymentBean.getBranchName();
            if (!TextUtils.isEmpty(branchName)) {
                ((TextView)layout.findViewById(R.id.receivables_way)).setText(bankName+"\n"+currentPaymentBean.getBranchName()+"\n"+currentPaymentBean.getAccountNo());
            }else{
                ((TextView)layout.findViewById(R.id.receivables_way)).setText(bankName+"\n"+currentPaymentBean.getAccountNo());
            }
        }else{
            ((TextView)layout.findViewById(R.id.receivables_way)).setText(currentPaymentBean.getPayName());
        }

        ((TextView)layout.findViewById(R.id.drawee)).setText(orderInfo.getBuyerRealName());
//        ((TextView)layout.findViewById(R.id.tip_red)).setText(context.getResources().getString(R.string.string_format_otc_sell_give_b_sure_tips,orderInfo.getBuyerRealName()));
        final InputView tradePasswdInput = layout.findViewById(R.id.tradePasswd);
        tradePasswdInput.setInputString("");


        final Button btCommit = layout.findViewById(R.id.btn_sure);
        btCommit.setText(btnConfirmText);
        btCommit.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                if (buttonEventListener != null) {
                    String tradePasswd = tradePasswdInput.getInputString();
                    if (!TextUtils.isEmpty(tradePasswd)) {
                        buttonEventListener.onConfirm(tradePasswd);
                    }else{
                        ToastUtils.showShort(context, context.getResources().getString(R.string.input_finance_passwd));
                        return;
                    }
                }
                dialog.dismiss();
            }
        });

        int screenWidth = PixelUtils.getScreenWidth();
        dialog.getWindow().setLayout((int) (screenWidth - context.getResources().getDimension(R.dimen.dip_30)), WindowManager.LayoutParams.WRAP_CONTENT);
        dialog.getWindow().setContentView(layout);

        //弹出对话框时需要点击输入框才能弹出软键盘
        dialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
        //加上下面这一行弹出对话框时软键盘随之弹出
        //dialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
        return dialog;
    }

    /**
     * 隐私协议
     */
    public static void showProtoculPrivacy(Context context,OnButtonPrivacyListener buttonEventListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
        dialog.show();
        View layout = ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.privacy_agreement_dialog_layout, null);
        ScrollView contentSV = layout.findViewById(R.id.contentLinear);
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) contentSV.getLayoutParams();
        layoutParams.height = (int) (PixelUtils.getScreenHeight() * 0.4);
        contentSV.setLayoutParams(layoutParams);
        TextView msgText = layout.findViewById(R.id.msg);
        String appName = context.getResources().getString(R.string.app_name);
        msgText.setText(context.getResources().getString(R.string.string_protocol_tips_content,appName,appName,appName));
        Button btCommit = layout.findViewById(R.id.btnConfirm);
        btCommit.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                if (buttonEventListener != null) {
                    buttonEventListener.onConfirm();
                }
                dialog.dismiss();
            }
        });
        Button btCancel = layout.findViewById(R.id.btnCancel);
        btCancel.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                if (buttonEventListener != null) {
                    buttonEventListener.onExit();
                }
                dialog.dismiss();
            }
        });
        layout.findViewById(R.id.signup_protocol).setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                if (buttonEventListener != null) {
                    buttonEventListener.onClickUserAgree();
                }
            }
        });
        layout.findViewById(R.id.privacy_protocol).setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                if (buttonEventListener != null) {
                    buttonEventListener.onClickPrivacy();
                }
            }
        });
        //int screenWidth = PixelUtils.getScreenWidth()- PixelUtils.dp2px(48);
        int width = PixelUtils.getScreenWidth()- PixelUtils.dp2px(48);
        dialog.getWindow().setLayout(width, WindowManager.LayoutParams.WRAP_CONTENT);
        dialog.getWindow().setContentView(layout);
    }

    public static void showQuickCloseSettingDialog(Context context) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(true);
        dialog.show();
        LinearLayout layout = (LinearLayout) ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.dialog_quick_close_setting_layout, null);


        CheckBox needConfirmChb = layout.findViewById(R.id.setting_switch);
        needConfirmChb.setChecked(SPEx.get(AppData.SPKEY.QUICK_CLOSE_CONFIRM,true));
        Button btCancel = layout.findViewById(R.id.btnCancel);

        btCancel.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                dialog.dismiss();
            }
        });
        Button btnSure = layout.findViewById(R.id.btnConfirm);

        btnSure.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                dialog.dismiss();
                SPEx.set(AppData.SPKEY.QUICK_CLOSE_CONFIRM,needConfirmChb.isChecked());
            }
        });
        int screenWidth = PixelUtils.getScreenWidth();
        dialog.getWindow().setLayout((int) (screenWidth - PixelUtils.dp2px(32)), WindowManager.LayoutParams.WRAP_CONTENT);
        dialog.getWindow().setContentView(layout);
    }


    /**
     * 显示等待对话框
     *
     * @param context        上下文索引
     * @param canceled       按返回键是否关闭对话框 true 关闭 false 不关闭
     */
    public static void showWaitingResultDialog(Context context,boolean canceled, String hint,final OnButtonEventListener buttonEventListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(canceled);
        dialog.show();

        dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                if (buttonEventListener != null) {
                    buttonEventListener.onCancel();
                }
            }
        });
        RelativeLayout layout = (RelativeLayout) ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.dialog_waiting_for_result_layout, null);
        TextView msgText = layout.findViewById(R.id.loading_hint_text);
        msgText.setText(hint);
        int screenWidth = PixelUtils.getScreenWidth();
        dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        dialog.getWindow().setLayout((int) (screenWidth - PixelUtils.dp2px(40)), WindowManager.LayoutParams.WRAP_CONTENT);
        dialog.getWindow().setContentView(layout);
    }


    public interface OnButtonPrivacyListener {

        void onClickPrivacy();

        void onClickUserAgree();

        void onConfirm();

        void onExit();
    }

    public interface OnButtonEventListener {
        void onConfirm();

        void onCancel();
    }

    public interface OnOtcEventListener {
        void onConfirm(String editContent);

        void onCancel();
    }

    public interface OnLoginListener {
        void onConfirm(AlertDialog dialog, String editContent);

        void onCancel();

        void forgetPasswd();
    }

    public interface OnVerifyEditEventListener {
        void onConfirm(Button okBtn,String editContent,TextView getVerifyTx);

        void sendVerify(TextView btnVerify,boolean isAuto);

        void onCancel();
    }

    public interface OnShareListener {
        void onShareWx();
        void onShareWxCircle();
        void onSavePic();
        void onMore();

        void onCancel();
    }
}
