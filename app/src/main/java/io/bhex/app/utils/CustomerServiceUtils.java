/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CustomerServiceUtils.java
 *   @Date: 19-5-29 上午11:18
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.utils;

import android.content.Context;

import com.zopim.android.sdk.api.ZopimChat;

import io.bhex.app.BuildConfig;
import io.bhex.baselib.utils.DevicesUtil;
import zendesk.commonui.UiConfig;
import zendesk.core.AnonymousIdentity;
import zendesk.core.Zendesk;
import zendesk.support.Support;
import zendesk.support.guide.HelpCenterActivity;
import zendesk.support.requestlist.RequestListActivity;

public class CustomerServiceUtils {
    public static void initZendesk(Context context){
        Zendesk.INSTANCE.init(context, BuildConfig.ZENDESK_URL,
                BuildConfig.ZENDESK_APP_ID,
                BuildConfig.ZENDESK_OAUTH_CLIENT_ID);
    }

    public static void setZendeskIdentify(Context context){
//        String helpEmail = SPEx.get(AppData.SPKEY.USER_ACCOUNT_KEY + SPEx.get(AppData.SPKEY.USER_ACCOUNT_MODE_KEY, true), "");

        Zendesk.INSTANCE.setIdentity(
                new AnonymousIdentity.Builder()
                        .withNameIdentifier("DId:"+DevicesUtil.getDeviceID(context))
//                            .withEmailIdentifier("DId:"+DevicesUtil.getDeviceID(context))
                        .withEmailIdentifier("visitor")
                        .build()
        );

        //TODO 屏蔽用户信息，修改信息(sdk用户身份变更)会导致工单历史数据丢失
//        if(UserManager.getInstance().getUserInfo() != null) {
//            UserInfoBean userInfo = UserManager.getInstance().getUserInfo();
//            Zendesk.INSTANCE.setIdentity(
//                    new AnonymousIdentity.Builder()
//                            .withNameIdentifier("DId:"+DevicesUtil.getDeviceID(context))
////                            .withEmailIdentifier("DId:"+DevicesUtil.getDeviceID(context))
////                            .withEmailIdentifier(UserManager.getInstance().getUserId() + "  " + userInfo.getMobile() + "  "+userInfo.getEmail())
//                            .withEmailIdentifier("visitor")
//                            .build()
//            );
//        }
//        else{
//            Zendesk.INSTANCE.setIdentity(
//                    new AnonymousIdentity.Builder()
//                            .withNameIdentifier("DId:"+DevicesUtil.getDeviceID(context))
////                            .withEmailIdentifier("DId:"+DevicesUtil.getDeviceID(context))
//                            .withEmailIdentifier("visitor")
//                            .build()
//            );
//        }

        // b). JWT (Must be initialized with your JWT identifier)
//        Zendesk.INSTANCE.setIdentity(new JwtIdentity("{"+ DevicesUtil.getDeviceID(context) + "}"));
    }

    public static void initZendeskSupport(){
        Support.INSTANCE.init(Zendesk.INSTANCE);
    }

    public static void initZendeskChat(){

        //Zendesk Chat init
        ZopimChat.init(BuildConfig.ZENDESK_CHAT_ACCOUNT_KEY);
    }

    /**
     * 提交工单
     * @param context
     */
    public static void goSubmitOrder(Context context) {
        CustomerServiceUtils.setZendeskIdentify(context);
        UiConfig config = RequestListActivity.builder()
                //.withTicketForm(TICKET_FORM_ID, getCustomFields())
                .config();
        RequestListActivity.builder()
                //.withRequestSubject("subject")
                .show(context, config);
    }

    /**
     * 新手入门
     * @param context
     */
    public static void goGuide(Context context) {
        CustomerServiceUtils.setZendeskIdentify(context);
        UiConfig helpCenterConfig = HelpCenterActivity.builder()
                //.withContactUsButtonVisible(true)
                //.withArticlesForCategoryIds(360000882773L)
                .config();

        HelpCenterActivity.builder()
                //.withLabelNames("Announcements")
                .show(context, helpCenterConfig );
    }
}
