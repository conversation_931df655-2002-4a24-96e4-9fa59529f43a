/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FingerUpdateUtil.java
 *   @Date: 12/1/18 9:04 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.utils;

import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.view.View;

import io.bhex.app.R;
import io.bhex.app.safe.SafeUilts;
import io.bhex.app.safe.bean.FingerSwitcher;
import io.bhex.app.view.SetFingerPrintDialog;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.core.SPEx;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserManager;

import static android.app.Activity.RESULT_OK;

public class FingerTipUtil {

    private  SetFingerPrintDialog builder;
    private  Activity mContext;
    private OnFingerFinish mListener;

    public interface OnFingerFinish{
        void OnFingerFinish();
    }
    public FingerTipUtil( Activity context, OnFingerFinish listener){
        mContext = context;
        mListener = listener;
    }
    public  void showFingerDialog() {
        builder=new SetFingerPrintDialog(mContext);
        builder.setTitle(mContext.getString(R.string.string_set_finger_login));
        builder.setMessage(mContext.getString(R.string.string_finger_tip));
        builder.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                if(mListener != null)
                    mListener.OnFingerFinish();
            }
        });
        builder.setPositiveButton(mContext.getString(R.string.string_sure), new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                if (SafeUilts.isFinger(mContext)) {
                    IntentUtils.openFinger(mContext,AppData.INTENT.REQUEST_CODE_FINGER_OPEN,AppData.INTENT.FINGER_CALLER_SECURITY);

//                        SPEx.set(AppData.SPKEY.FINGER_PWD_KEY,true);
//                        ToastUtils.showShort(getString(R.string.string_fingerprint_open_success));
//                        fingerCb.setButtonDrawable(R.mipmap.icon_switch_button_on);
                }
                else if(builder.isShowing()){
                    builder.dismiss();
                }
            }
        });
        builder.setNegativeButton(mContext.getString(R.string.string_cancel), new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                // 取消的操作
                if(builder.isShowing()){
                    builder.dismiss();
                }
            }
        });

        builder.show();
    }

    public  void onActivityResult(int requestCode, int resultCode, Intent data) {

        if(builder != null && builder.isShowing()) {
            if (requestCode == AppData.INTENT.REQUEST_CODE_FINGER_OPEN && resultCode == RESULT_OK) {
                FingerSwitcher fingerauth = (FingerSwitcher) data.getSerializableExtra("fingerauth");
                if (fingerauth.isAuthSuccess()) {
                    UserManager.getInstance().updateFingerSetOpenStatus(true);
                    ToastUtils.showShort(mContext, mContext.getString(R.string.string_fingerprint_open_success));
                }
            }
            builder.dismiss();
        }
    }
}
