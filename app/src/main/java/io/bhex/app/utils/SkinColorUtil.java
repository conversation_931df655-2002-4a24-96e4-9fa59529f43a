package io.bhex.app.utils;

import android.content.Context;

import androidx.core.content.ContextCompat;

import io.bhex.app.R;
import io.bhex.sdk.config.AppSetting;

public class SkinColorUtil {
    public static int getDefaultDark(Context context) {
        return getColor(context, R.color.color_black);
    }

    public static int getDefaultWhite(Context context) {
        return getColor(context, R.color.color_white);
    }

    public static int getDark(Context context) {
        return SkinColorUtil.getColor(context, CommonUtil.isBlackMode() ? R.color.dark_night : R.color.dark);
    }

    public static int getKlinDark(Context context) {
        return SkinColorUtil.getColor(context, CommonUtil.isBlackMode() ? R.color.kline_dark : R.color.dark);
    }

    public static int getDark10(Context context) {
        return SkinColorUtil.getColor(context, CommonUtil.isBlackMode() ? R.color.dark10_night : R.color.dark10);
    }

    public static int getDark20(Context context) {
        return SkinColorUtil.getColor(context, CommonUtil.isBlackMode() ? R.color.dark20_night : R.color.dark20);
    }

    public static int getDark50(Context context) {
        return SkinColorUtil.getColor(context, CommonUtil.isBlackMode() ? R.color.dark50_night : R.color.dark50);
    }

    public static int getDark80(Context context) {
        return SkinColorUtil.getColor(context, CommonUtil.isBlackMode() ? R.color.dark80_night : R.color.dark80);
    }

    public static int getWhite(Context context) {
        return SkinColorUtil.getColor(context, CommonUtil.isBlackMode() ? R.color.white_night : R.color.white);
    }

    public static int getWhite10(Context context) {
        return SkinColorUtil.getColor(context, CommonUtil.isBlackMode() ? R.color.white10_night : R.color.white10);
    }

    public static int getWhite20(Context context) {
        return SkinColorUtil.getColor(context, CommonUtil.isBlackMode() ? R.color.white20_night : R.color.white20);
    }

    public static int getWhite50(Context context) {
        return SkinColorUtil.getColor(context, CommonUtil.isBlackMode() ? R.color.white50_night : R.color.white50);
    }

    public static int getWhite80(Context context) {
        return SkinColorUtil.getColor(context, CommonUtil.isBlackMode() ? R.color.white80_night : R.color.white80);
    }

    public static int getGrey(Context context) {
        return SkinColorUtil.getColor(context, CommonUtil.isBlackMode() ? R.color.grey_night : R.color.grey);
    }

    public static int getGreen(Context context) {
        if (CommonUtil.isBlackMode()) {
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.green_night : R.color.red_night);
        }else{
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.green : R.color.red);
        }
    }

    public static int getRed(Context context) {
        if (CommonUtil.isBlackMode()) {
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.red_night : R.color.green_night);
        }else{
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.red : R.color.green);
        }
    }

    public static int getGreen10(Context context) {
        if (CommonUtil.isBlackMode()) {
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.green10_night : R.color.red10_night);
        }else{
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.green10 : R.color.red10);
        }
    }

    public static int getRed10(Context context) {
        if (CommonUtil.isBlackMode()) {
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.red10_night : R.color.green10_night);
        }else{
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.red10 : R.color.green10);
        }
    }

    public static int getGreen20(Context context) {
        if (CommonUtil.isBlackMode()) {
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.green20_night : R.color.red20_night);
        }else{
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.green20 : R.color.red20);
        }
    }

    public static int getRed20(Context context) {
        if (CommonUtil.isBlackMode()) {
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.red20_night : R.color.green20_night);
        }else{
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.red20 : R.color.green20);
        }
    }

    public static int getGreen50(Context context) {
        if (CommonUtil.isBlackMode()) {
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.green50_night : R.color.red50_night);
        }else{
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.green50 : R.color.red50);
        }
    }

    public static int getRed50(Context context) {
        if (CommonUtil.isBlackMode()) {
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.red50_night : R.color.green50_night);
        }else{
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.red50 : R.color.green50);
        }
    }

    public static int getGreen80(Context context) {
        if (CommonUtil.isBlackMode()) {
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.green80_night : R.color.red80_night);
        }else{
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.green80 : R.color.red80);
        }
    }

    public static int getRed80(Context context) {
        if (CommonUtil.isBlackMode()) {
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.red80_night : R.color.green80_night);
        }else{
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.red80 : R.color.green80);
        }
    }

    public static int getGreen5(Context context) {
        if (CommonUtil.isBlackMode()) {
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.green5_night : R.color.red5_night);
        }else{
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.green5 : R.color.red5);
        }
    }

    public static int getRed5(Context context) {
        if (CommonUtil.isBlackMode()) {
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.red5_night : R.color.green5_night);
        }else{
            return SkinColorUtil.getColor(context, AppSetting.getInstance().isGreenRose() ? R.color.red5 : R.color.green5);
        }
    }

    public static int getGreenBg(Context context) {
        if (CommonUtil.isBlackMode()) {
            return AppSetting.getInstance().isGreenRose() ? R.drawable.bg_corner_green_night : R.drawable.bg_corner_red_night;
        }else{
            return AppSetting.getInstance().isGreenRose() ? R.drawable.bg_corner_green : R.drawable.bg_corner_red;
        }
    }

    public static int getRedBg(Context context) {
        if (CommonUtil.isBlackMode()) {
            return AppSetting.getInstance().isGreenRose() ? R.drawable.bg_corner_red_night : R.drawable.bg_corner_green_night;
        }else{
            return AppSetting.getInstance().isGreenRose() ? R.drawable.bg_corner_red : R.drawable.bg_corner_green;
        }
    }

    public static int getGreenRectBg(Context context) {
        if (CommonUtil.isBlackMode()) {
            return AppSetting.getInstance().isGreenRose() ? R.drawable.bg_corner_rect_green_night : R.drawable.bg_corner_rect_red_night;
        }else{
            return AppSetting.getInstance().isGreenRose() ? R.drawable.bg_corner_rect_green : R.drawable.bg_corner_rect_red;
        }
    }

    public static int getRedRectBg(Context context) {
        if (CommonUtil.isBlackMode()) {
            return AppSetting.getInstance().isGreenRose() ? R.drawable.bg_corner_rect_red_night : R.drawable.bg_corner_rect_green_night;
        }else{
            return AppSetting.getInstance().isGreenRose() ? R.drawable.bg_corner_rect_red : R.drawable.bg_corner_rect_green;
        }
    }

    public static int getBuyProgressDrawable(Context context) {
        return AppSetting.getInstance().isGreenRose() ? R.drawable.progress_left_green_style : R.drawable.progress_left_red_style;
    }

    public static int getSellProgressDrawable(Context context) {
        return AppSetting.getInstance().isGreenRose() ? R.drawable.progress_right_red_style : R.drawable.progress_right_green_style;
    }



    public static int getColor(Context context, int color) {
//        int ret = context.getResources().getColor(color);
        int ret = ContextCompat.getColor(context,color);
        return ret;
    }
}
