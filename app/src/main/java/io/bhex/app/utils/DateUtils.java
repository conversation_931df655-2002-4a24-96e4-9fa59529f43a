/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: DateUtils.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.utils;

import android.content.Context;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

import io.bhex.app.R;
import io.bhex.baselib.constant.AppData;

public class DateUtils {

    public static Date str2Date(String timeStr) {

        long time = Long.valueOf(timeStr);
        Date date = new Date((time / 1000 - (time / 1000) % 60) * 1000);
        return date;
    }

    //日期时间格式化
    public static String getNewDateFormat(String content, String format, String to) {
        String newFormat = "";
        try {
            DateFormat fmt = new SimpleDateFormat(format);
            Date date = fmt.parse(content);
            SimpleDateFormat sdf = new SimpleDateFormat(to);
            newFormat = sdf.format(date);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return newFormat;
    }

    public static String getSimpleTimeFormat(long time, String formatStr) {
        String strTemp = "";
        Date dateCreate = new Date(time);
        SimpleDateFormat myFmt2 = new SimpleDateFormat(formatStr);
        myFmt2.setTimeZone(TimeZone.getDefault());
        strTemp = myFmt2.format(dateCreate);
        return strTemp;
    }

    public static String getSimpleTimeFormatS(long time, String formatStr) {
        String strTemp = "";
        Date dateCreate = new Date(time);
        SimpleDateFormat myFmt2 = new SimpleDateFormat(formatStr);
        myFmt2.setTimeZone(TimeZone.getDefault());
        strTemp = myFmt2.format(dateCreate);
        return strTemp;
    }

    public static String getSimpleTimeFormat(String time, String formatStr) {
        String strTemp = "";
        try{
            Date dateCreate = new Date(Long.valueOf(time));
            SimpleDateFormat myFmt2 = new SimpleDateFormat(formatStr);
            myFmt2.setTimeZone(TimeZone.getDefault());
            strTemp = myFmt2.format(dateCreate);
            return strTemp;
        } catch (Exception e) {
            return strTemp;
        }
    }

    public static String getSimpleTimeFormat(String time) {
        String strTemp = "";
        try {
            Date dateCreate = new Date(Long.valueOf(time));
            SimpleDateFormat myFmt2 = new SimpleDateFormat(AppData.Config.TIME_FORMAT2);
            myFmt2.setTimeZone(TimeZone.getDefault());
            strTemp = myFmt2.format(dateCreate);
            return strTemp;
        } catch (Exception e) {
            return strTemp;
        }
    }

    /**
     * 转换今天 昨天
     *
     * @param content
     * @param format
     * @param to
     * @return
     */
    public static String getNewDateFormat2(String content, String format, String to) {
        String newFormat = "";
        try {
            DateFormat fmt = new SimpleDateFormat(format);
            Date date = fmt.parse(content);
            SimpleDateFormat sdf = new SimpleDateFormat(to);
            DateFormat fmt1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            long currentTimeMillis = System.currentTimeMillis();
            Date today = new Date(currentTimeMillis);
//			Date today = fmt1.parse(content);
            newFormat = sdf.format(date);
            if (Math.abs(((today.getTime() - date.getTime()) / (24 * 3600 * 1000))) == 0) {
                newFormat = "今天";
            }
            if (Math.abs(((today.getTime() - date.getTime()) / (24 * 3600 * 1000))) == 1) {
                newFormat = "昨天";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return newFormat;
    }

    public static String getYesterdayFormat(String content, String format, String to, String todayStr) {
        String newFormat = "";
        try {
            DateFormat fmt = new SimpleDateFormat(format);
            Date date = fmt.parse(content);
            DateFormat fmt1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date today = fmt1.parse(todayStr);
            SimpleDateFormat sdf = new SimpleDateFormat(to);
            if (Math.abs(((today.getTime() - date.getTime()) / (24 * 3600 * 1000))) == 1) {
                newFormat = "Y";
            } else {
                newFormat = sdf.format(date);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return newFormat;
    }

    public static String getTodayFormat(String content, String format, String to, String todayStr) {
        String newFormat = "";
        try {
            DateFormat fmt = new SimpleDateFormat(format);
            Date date = fmt.parse(content);
            DateFormat fmt1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date today = fmt1.parse(todayStr);
            SimpleDateFormat sdf = new SimpleDateFormat(to);
            if (Math.abs(((today.getTime() - date.getTime()) / (24 * 3600 * 1000))) == 0) {
                newFormat = "Y";
            } else {
                newFormat = sdf.format(date);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return newFormat;
    }

    /**
     * 计算开始时间
     *
     * @param klineType
     * @return
     */
    public static long calKlineFromTime(String klineType) {
        //默认开始时间向前推一周
        long perDayS = 86400000;
        long from = new BigDecimal(7l).multiply(new BigDecimal(perDayS)).longValue();
        if (klineType.equals("1m")) {
            from = new BigDecimal(2l).multiply(new BigDecimal(perDayS)).longValue();
        } else if (klineType.equals("5m")) {
            from = new BigDecimal(5l).multiply(new BigDecimal(perDayS)).longValue();
        } else if (klineType.equals("15m")) {
            from = new BigDecimal(10l).multiply(new BigDecimal(perDayS)).longValue();
        } else if (klineType.equals("30m")) {
            //一天48根数据 48*6 大约将近300根
            from = new BigDecimal(20l).multiply(new BigDecimal(perDayS)).longValue();
        } else if (klineType.equals("1h")) {
            from = new BigDecimal(40l).multiply(new BigDecimal(perDayS)).longValue();
        } else if (klineType.equals("1d")) {
            from = new BigDecimal(1000l).multiply(new BigDecimal(perDayS)).longValue();
        } else if (klineType.equals("1w")) {
            from = new BigDecimal(1000l).multiply(new BigDecimal(perDayS)).longValue();
        } else if (klineType.equals("1")) {
            from = new BigDecimal(1100l).multiply(new BigDecimal(perDayS)).longValue();
        }
        return from;
    }

    public static long calKlineFromTimeExt(String klineType) {
        //默认开始时间向前推一周
        long perDayS = 60*1000;
        long from = new BigDecimal(1l).multiply(new BigDecimal(perDayS)).longValue();

        if(klineType.equals("1m")){
            from = new BigDecimal(1l).multiply(new BigDecimal(perDayS)).longValue();
        }else if(klineType.equals("5m")){
            from = new BigDecimal(5l).multiply(new BigDecimal(perDayS)).longValue();
        }else if(klineType.equals("30m")){
            from = new BigDecimal(30l).multiply(new BigDecimal(perDayS)).longValue();
        }else if(klineType.equals("1h")){
            from = new BigDecimal(60l).multiply(new BigDecimal(perDayS)).longValue();
        } else if(klineType.equals("2h")){
            from = new BigDecimal(120l).multiply(new BigDecimal(perDayS)).longValue();
        }else if(klineType.equals("4h")){
            from = new BigDecimal(4*60l).multiply(new BigDecimal(perDayS)).longValue();
        } else if(klineType.equals("6h")){
            from = new BigDecimal(6*60l).multiply(new BigDecimal(perDayS)).longValue();
        }else if(klineType.equals("12h")){
            from = new BigDecimal(12*60l).multiply(new BigDecimal(perDayS)).longValue();
        }else if(klineType.equals("1d")){
            from = new BigDecimal(24*60l).multiply(new BigDecimal(perDayS)).longValue();
        }else if(klineType.equals("1w")){
            from = new BigDecimal(7*24*60l).multiply(new BigDecimal(perDayS)).longValue();
        }else if(klineType.equals("1M")){
            from = new BigDecimal(30*24*60l).multiply(new BigDecimal(perDayS)).longValue();
        }
        return from;
    }

    /**
     * 计算剩余时间 格式：分 秒
     *
     * @param context
     * @param lastSeconds 毫秒
     * @return
     */
    public static String getRemainingTime(Context context, long lastSeconds) {
//        Long remainingTime = Long.valueOf(lastSeconds);
        lastSeconds = lastSeconds / 1000;
        int s = (int) (lastSeconds % 60);
        int m = (int) ((lastSeconds - s) / 60);
        return context.getResources().getString(R.string.string_format_time_ms, m, s < 10 ? "0" + s : "" + s);
    }

    /**
     * 计算天差（相差天数）
     *
     * @param endTime
     * @param startTime
     * @return
     * @throws ParseException
     */
    public static int calDaysByCalendar(String endTime, String startTime) {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd");
            Date endDate = new Date(Long.valueOf(endTime));
            Date startDate = new Date(Long.valueOf(startTime));

            Calendar c1 = Calendar.getInstance();
            c1.setTime(endDate);
            Calendar c2 = Calendar.getInstance();
            c2.setTime(startDate);

            int endDay = c1.get(Calendar.DAY_OF_YEAR);
            int startDay = c2.get(Calendar.DAY_OF_YEAR);
            return endDay - startDay;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }

    }

    /**
     * 增加天数
     *
     * @param time
     * @return
     */
    public static String addDays(String time,int days,String format){
        try {
            long resultTime = Long.valueOf(time)+days*24*60*60*1000;
            SimpleDateFormat formatter = new SimpleDateFormat(format);
            Date date = new Date(resultTime);
            String timeF = formatter.format(date);
            return timeF;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 增加天数
     *
     * @param time
     * @return
     */
    public static String calDaysByCalendar(long time) {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd");
            Date date = new Date(time);
            String timeF = formatter.format(date);
            return timeF;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public static String getDateAndWeek(String time,int days){
        long resultTime = Long.valueOf(time)+days*24*60*60*1000;
        String date = calDaysByCalendar(resultTime);
        String week = getWeekSimple(resultTime);
        return date+" "+week;
    }


    //根据日期取得星期几
    public static String getWeek(Date date){
        String[] weeks = {"星期日","星期一","星期二","星期三","星期四","星期五","星期六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int week_index = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if(week_index<0){
            week_index = 0;
        }
        return weeks[week_index];
    }

    //根据日期取得星期几
    public static String getWeekSimple(long time){
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            Date  date = new Date(time);
            SimpleDateFormat sdf = new SimpleDateFormat("EEEE");
            String week = sdf.format(date);
            return week;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }


    public static String getCurrentHHMMSS(){
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            Date  date = new Date();

            String currentStr = formatter.format(date);
            return currentStr;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}
