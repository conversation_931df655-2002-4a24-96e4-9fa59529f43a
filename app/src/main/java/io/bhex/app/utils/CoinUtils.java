package io.bhex.app.utils;

import java.util.Locale;

import io.bhex.app.account.utils.LocalManageUtil;
import io.bhex.app.app.BHexApplication;
import io.bhex.baselib.core.SPEx;
import io.bhex.baselib.network.Utils.Convert;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.FuturensBaseToken;
import io.bhex.sdk.trade.CurrentOtcSelection;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-02-05
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class CoinUtils {
    /**
     * 保存杠杆交易币种
     * @param coinPairBean
     */
    public static void saveMarginTradeCoin(CoinPairBean coinPairBean) {
        String coinJson = Convert.toJson(coinPairBean);
        SPEx.set("marginTradeCoin", coinJson);
    }

    /**
     * 保存币币交易币种
     * @param coinPairBean
     */
    public static void saveBBTradeCoin(CoinPairBean coinPairBean) {
        String coinJson = Convert.toJson(coinPairBean);
        SPEx.set("bbTradeCoin", coinJson);
    }

    /**
     * 获取币币交易币种
     * @return
     */
    public static CoinPairBean getBBTradeCoin() {
        String tradeCoin = SPEx.get("bbTradeCoin", "");
        CoinPairBean coin = Convert.fromJson(tradeCoin, CoinPairBean.class);
        return coin;
    }
    /**
     * 获取杠杆交易币种
     * @return
     */
    public static CoinPairBean getMarginTradeCoin() {
        String tradeCoin = SPEx.get("marginTradeCoin", "");
        CoinPairBean coin = Convert.fromJson(tradeCoin, CoinPairBean.class);
        return coin;
    }
    /**
     * 保存合约交易币种
     * @param coinPairBean
     */
    public static void saveContractTradeCoin(CoinPairBean coinPairBean) {
        String coinJson = Convert.toJson(coinPairBean);
        DebugLog.d("ABBB====>","coin_json=="+coinJson);
        Locale locale = LocalManageUtil.getSetLanguageLocale(BHexApplication.getInstance());
        if(locale!=null && locale.getLanguage().startsWith("zh")){
            SPEx.set("contractTradeCoin_zh", coinJson);
        }else {
            SPEx.set("contractTradeCoin_en", coinJson);
        }

    }

    /**
     * 获取合约交易币种
     * @return
     */
    public static CoinPairBean getContractTradeCoin() {
        Locale locale = LocalManageUtil.getSetLanguageLocale(BHexApplication.getInstance());
        String tradeCoin;
        if(locale!=null && locale.getLanguage().startsWith("zh")){
            tradeCoin = SPEx.get("contractTradeCoin_zh", "");
        }else{
            tradeCoin = SPEx.get("contractTradeCoin_en", "");
        }

        CoinPairBean coin = Convert.fromJson(tradeCoin, CoinPairBean.class);
        return coin;
    }

    /**
     * 保存期权交易币种
     * @param coinPairBean
     */
    public static void saveOptionTradeCoin(CoinPairBean coinPairBean) {
        String optionJson = Convert.toJson(coinPairBean);
        Locale locale = LocalManageUtil.getSetLanguageLocale(BHexApplication.getInstance());
        if(locale!=null && locale.getLanguage().startsWith("zh")){
            SPEx.set("optionTradeCoin_zh", optionJson);
        }else{
            SPEx.set("optionTradeCoin_en", optionJson);
        }
    }

    /**
     * 获取期权交易币种
     * @return
     */
    public static CoinPairBean getOptionTradeCoin() {
        //判断语言
        Locale locale = LocalManageUtil.getSetLanguageLocale(BHexApplication.getInstance());
        String optionTradeCoin = "";
        if(locale!=null && locale.getLanguage().startsWith("zh")){
            optionTradeCoin = SPEx.get("optionTradeCoin_zh", "");
        }else{
            optionTradeCoin = SPEx.get("optionTradeCoin_en", "");
        }
        CoinPairBean optionCoin = Convert.fromJson(optionTradeCoin, CoinPairBean.class);
        return optionCoin;
    }

    /**
     * 获取 币币 期权 合约 币对名称
     * @param coinPairBean
     * @return
     */
    public static String getSymbolName(CoinPairBean coinPairBean) {
        if (KlineUtils.isSymbolOfBB(coinPairBean.getCoinType())) {
            //币币 name
            return coinPairBean.getBaseTokenName() + "/" + coinPairBean.getQuoteTokenName();
        }else{
            //期权 合约name
            return coinPairBean.getSymbolName();
        }
    }

    /**
     * 获取 币币 期权 合约 作价单位tokenId
     * @param coinPairBean
     * @return
     */
    public static String getQuotePriceUnit(CoinPairBean coinPairBean) {
        if (KlineUtils.isSymbolOfBB(coinPairBean.getCoinType())||KlineUtils.isSymbolOfOption(coinPairBean.getCoinType())) {
            //币币 期权 作价单位tokenId
            return coinPairBean.getQuoteTokenId();
        }else{
            //合约 作价单位tokenId
            FuturensBaseToken futurensBaseToken = coinPairBean.baseTokenFutures;
            if (futurensBaseToken != null) {
                return futurensBaseToken.getDisplayTokenId();
            }else{
                return "";
            }
        }
    }

    static CurrentOtcSelection mOtcToken;
    public static void setOTCCoin(CurrentOtcSelection otcToken) {
        mOtcToken = otcToken;
    }

    public static void setOTCCoinToken(String token) {
        if (mOtcToken==null) {
            mOtcToken = new CurrentOtcSelection();
        }
        mOtcToken.token = token;
    }
    public static void setOTCIsBuy(boolean isBuy) {
        if (mOtcToken==null) {
            mOtcToken = new CurrentOtcSelection();
        }
        mOtcToken.isBuy = isBuy;
    }
    public static CurrentOtcSelection getOTCCoin() {
        return mOtcToken;
    }
}
