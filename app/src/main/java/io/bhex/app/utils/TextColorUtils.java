/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: TextColorUtils.java
 *   @Date: 19-1-22 下午2:54
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.utils;

import android.content.Context;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.ForegroundColorSpan;
import android.widget.TextView;

import io.bhex.app.R;

public class TextColorUtils {
    /**
     * 替换文案排版  占位符要用 %s
     * @param tv
     * @param formatContent
     * @param replaceContent 此处占位符要用 %s
     * @param textColor
     */
    public static void setTextViewColor(TextView tv, String formatContent, String replaceContent, int textColor) {
        Context context = tv.getContext();
        String content = formatContent.replace("%s",replaceContent);
        SpannableStringBuilder builder = new SpannableStringBuilder(content);
        ForegroundColorSpan orangeColor = new ForegroundColorSpan(context.getResources().getColor(textColor));
        int start = formatContent.indexOf("%s");
        int end = start + replaceContent.length();
        builder.setSpan(orangeColor, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        tv.setText(builder);
    }
}
