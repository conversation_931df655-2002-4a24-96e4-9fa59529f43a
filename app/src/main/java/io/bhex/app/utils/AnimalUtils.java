/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AnimalUtils.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.utils;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.view.View;

/**
 * ================================================
 * 描   述：动画
 * ================================================
 */

public class AnimalUtils {

    private static final String ANIM_PROPERTY_ROTATE_NAME = "rotation";
    private static final String ANIM_PROPERTY_SCALE_NAME = "scaleX";
    private static final String ANIM_PROPERTY_ALPHA_NAME = "alpha";
    private static final String ANIM_PROPERTY_TRANS_NAME = "translationX";


    public static void rotateyAnimRun(final View view, float start, float end) {
        ObjectAnimator anim = ObjectAnimator
                .ofFloat(view, ANIM_PROPERTY_ROTATE_NAME, start, end)
                .setDuration(200);
        anim.start();
        anim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float cVal = (Float) animation.getAnimatedValue();
//                view.setAlpha(cVal);
//                view.setScaleX(cVal);
//                view.setScaleY(cVal);

                view.setRotation(cVal);

            }
        });
    }

    public static void scaleAnimRun(final View view, float start, float end) {
        ObjectAnimator anim = ObjectAnimator
                .ofFloat(view, ANIM_PROPERTY_SCALE_NAME, start, end)
                .setDuration(200);
        anim.start();
        anim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float cVal = (Float) animation.getAnimatedValue();
//                view.setAlpha(cVal);
                view.setScaleX(cVal);
//                view.setScaleY(cVal);


            }
        });
    }

    public static void scaleXYAnimRun(final View view, float start, float end) {
        ObjectAnimator anim = ObjectAnimator
                .ofFloat(view, ANIM_PROPERTY_SCALE_NAME, start, end)
                .setDuration(500);
        anim.start();
        anim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float cVal = (Float) animation.getAnimatedValue();
//                view.setAlpha(cVal);
                view.setScaleX(cVal);
                view.setScaleY(cVal);
            }
        });
    }

    public static void alphaAnimRun(final View view, float start, float end) {
        ObjectAnimator anim = ObjectAnimator
                .ofFloat(view, ANIM_PROPERTY_ALPHA_NAME, start, end)
                .setDuration(500);
        anim.start();
        anim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float cVal = (Float) animation.getAnimatedValue();
                view.setAlpha(cVal);
//                view.setScaleX(cVal);
//                view.setScaleY(cVal);


            }
        });
    }


    public static void transAnimRun(final View view,float startX,float endX) {
        ObjectAnimator anim = ObjectAnimator
                .ofFloat(view, ANIM_PROPERTY_TRANS_NAME,startX,endX)
                .setDuration(200);
        anim.start();
        anim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float cVal = (Float) animation.getAnimatedValue();
                view.setTranslationX(cVal);

            }
        });
    }

    public static void transAnimRun(final View view,float startX,float endX,float startY,float endY) {
        ObjectAnimator anim = ObjectAnimator
                .ofFloat(view, ANIM_PROPERTY_TRANS_NAME,startY,endY)
                .setDuration(1000);
        anim.start();
        anim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float cVal = (Float) animation.getAnimatedValue();
//                view.setTranslationX(cVal);
                view.setTranslationY(cVal);

            }
        });
    }

}
