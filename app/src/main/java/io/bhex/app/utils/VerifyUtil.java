/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: VerifyUtil.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.utils;

import android.content.Context;
import android.text.TextUtils;

import java.util.ArrayList;

import io.bhex.app.R;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.app.view.alertview.AlertView;
import io.bhex.app.view.alertview.OnItemClickListener;
import io.bhex.sdk.account.LoginApi;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;

/**
 * ================================================
 * 描   述：验证判断工具
 * ================================================
 */

public class VerifyUtil {

    public interface VerifyListener {
        void on2FAVerify(boolean isVerify2FA);
    }

    public static void is2FA(final Context context, final VerifyListener listener) {
        LoginApi.GetUserInfo(new SimpleResponseListener<UserInfoBean>() {
            @Override
            public void onSuccess(UserInfoBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    UserManager.getInstance().saveUserInfo(response);

                    boolean bindGA = response.isBindGA();
                    final boolean bindEmail = !TextUtils.isEmpty(response.getEmail());
                    boolean bindMobile = !TextUtils.isEmpty(response.getMobile());
                    ArrayList<String> faList = new ArrayList<String>();
                    if (!bindGA && !bindEmail) {
                        faList.add(context.getResources().getString(R.string.string_auth_ga));
                        faList.add(context.getResources().getString(R.string.string_auth_email));

                    } else if (!bindGA && !bindMobile) {
                        faList.add(context.getResources().getString(R.string.string_auth_ga));
                        faList.add(context.getResources().getString(R.string.string_auth_msm));
                    }

                    String[] arrayOf2FA = faList.toArray(new String[faList.size()]);
                    if (arrayOf2FA != null && arrayOf2FA.length > 0) {
                        AlertView faAction = new AlertView(context.getResources().getString(R.string.string_2fa_alert_title), null, context.getResources().getString(R.string.string_cancel), null, arrayOf2FA, context, AlertView.Style.ActionSheet, new OnItemClickListener() {
                            @Override
                            public void onItemClick(Object o, int position) {
                                if (position == -1) {
                                    return;
                                }
                                if (position == 0) {
                                    IntentUtils.goBindGAHelp(context);
                                } else {
                                    if (!bindEmail) {
                                        IntentUtils.goBindEmail(context);
                                    } else {
                                        IntentUtils.goBindMobile(context);
                                    }
                                }
                            }
                        });
                        faAction.show();
                        if (listener != null) {
                            listener.on2FAVerify(false);
                        }
                    } else {
                        if (listener != null) {
                            listener.on2FAVerify(true);
                        }
                    }
                }
            }
        });
    }

    public static boolean is2FA(final Context context, UserInfoBean userInfo) {

        boolean bindGA = userInfo.isBindGA();
        final boolean bindEmail = !TextUtils.isEmpty(userInfo.getEmail());
        boolean bindMobile = !TextUtils.isEmpty(userInfo.getMobile());
        ArrayList<String> faList = new ArrayList<String>();
        if (!bindGA && !bindEmail) {
            return false;

        } else return bindGA || bindMobile;
    }


    public static void is2FA(final Context context, UserInfoBean userInfo, final VerifyListener listener) {

        boolean bindGA = userInfo.isBindGA();
        final boolean bindEmail = !TextUtils.isEmpty(userInfo.getEmail());
        boolean bindMobile = !TextUtils.isEmpty(userInfo.getMobile());
        ArrayList<String> faList = new ArrayList<String>();
        if (!bindGA && !bindEmail) {
            faList.add(context.getResources().getString(R.string.string_auth_ga));
            faList.add(context.getResources().getString(R.string.string_auth_email));

        } else if (!bindGA && !bindMobile) {
            faList.add(context.getResources().getString(R.string.string_auth_ga));
            faList.add(context.getResources().getString(R.string.string_auth_msm));
        }

        String[] arrayOf2FA = faList.toArray(new String[faList.size()]);
        if (arrayOf2FA != null && arrayOf2FA.length > 0) {
            AlertView faAction = new AlertView(context.getResources().getString(R.string.string_2fa_alert_title), null, context.getResources().getString(R.string.string_cancel), null, arrayOf2FA, context, AlertView.Style.ActionSheet, new OnItemClickListener() {
                @Override
                public void onItemClick(Object o, int position) {
                    if (position == -1) {
                        return;
                    }
                    if (position == 0) {
                        IntentUtils.goBindGAHelp(context);
                    } else {
                        if (!bindEmail) {
                            IntentUtils.goBindEmail(context);
                        } else {
                            IntentUtils.goBindMobile(context);
                        }
                    }
                }
            });
            faAction.show();
            if (listener != null) {
                listener.on2FAVerify(false);
            }
        } else {
            if (listener != null) {
                listener.on2FAVerify(true);
            }
        }
    }
}
