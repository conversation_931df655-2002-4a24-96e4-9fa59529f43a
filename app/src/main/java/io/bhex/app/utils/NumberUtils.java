/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: NumberUtils.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.utils;

import android.content.Context;
import android.text.TextUtils;

import java.math.BigDecimal;

import io.bhex.app.R;
import io.bhex.baselib.utils.DebugLog;



public class NumberUtils {
    static public String roundFormat(double num, int len, int roundMode) {
        String strTemp = "";

        try {
            BigDecimal b = new BigDecimal(String.valueOf(num));
            BigDecimal f1 = b.setScale(len, roundMode);
            strTemp = f1.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            strTemp = "";
        }

        return strTemp;
    }

    static public String roundFormat(double num, int len) {
        return roundNum(num, len).toPlainString();
    }

    private static BigDecimal roundNum(double num, int len) {
        BigDecimal b = new BigDecimal(String.valueOf(num));
        BigDecimal f1 = b.setScale(len, BigDecimal.ROUND_HALF_UP);
        return f1;
    }

    /**
     * 带有加减号的
     *
     * @param num
     * @param len
     * @return
     */
    public static String roundFormatS(double num, int len) {
        float value = roundNum(num, len).floatValue();
        if (Float.compare(value, 0) == 0)
            return "0";
        else
            return String.format("%1$+." + len + "f", value);
    }

    /**
     * 向上取整
     *
     * @param num
     * @param len
     * @return
     */
    static public String roundFormatUpMode(double num, int len) {
        String strTemp = "";
        try {
            BigDecimal b = new BigDecimal(String.valueOf(num));
            BigDecimal f1 = b.setScale(len, BigDecimal.ROUND_HALF_UP);
            strTemp = f1.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            strTemp = "";
        }

        return strTemp;
    }

    static public String roundFormat(float num, int len) {
        String strTemp = "";
        try {
            BigDecimal b = new BigDecimal(String.valueOf(num));
            BigDecimal f1 = b.setScale(len, BigDecimal.ROUND_HALF_UP);
            strTemp = f1.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            strTemp = "";
        }
        return strTemp;
    }

    static public String roundFormat2(double num, int len) {
        String temp = "";
        if (num < 10000) {
            temp = roundFormat(num, len) + "";
        } else if (num >= 10000 && num < 100000000) {
            temp = roundFormat(num / 10000, len) + "万";
        } else {
            temp = roundFormat(num / 100000000, len) + "亿";
        }
        return temp;
    }

    static public String roundFormat(String num, int len) {
        String strTemp = "";

        try {
            if (num == null || num.equals("")) {
                return strTemp;
            }

            BigDecimal b = new BigDecimal(num);
            BigDecimal f1 = b.setScale(len, BigDecimal.ROUND_HALF_UP);
            strTemp = f1.toPlainString();
        } catch (Exception ex) {
            ex.printStackTrace();
            strTemp = "";
        }

        return strTemp;
    }

    static public String roundFormatUp(String num, int len) {
        String strTemp = "";

        try {
            if (num == null || num.equals("")) {
                return strTemp;
            }
            BigDecimal b = new BigDecimal(num);
            BigDecimal f1 = b.setScale(len, BigDecimal.ROUND_UP);
            strTemp = f1.toPlainString();
        } catch (Exception ex) {
            ex.printStackTrace();
            strTemp = "";
        }

        return strTemp;
    }

    static public String roundFormatUp(double num, int len) {
        String strTemp = "";

        try {
            strTemp = roundFormatUp(String.valueOf(num),len);
        } catch (Exception ex) {
            ex.printStackTrace();
            strTemp = "";
        }

        return strTemp;
    }

    static public String roundFormatDown(String num, int len) {
        String strTemp = "";

        try {
            if (num == null || num.equals("")) {
                return strTemp;
            }
            BigDecimal b = new BigDecimal(num);
            BigDecimal f1 = b.setScale(len, BigDecimal.ROUND_DOWN);
            strTemp = f1.toPlainString();
        } catch (Exception ex) {
            ex.printStackTrace();
            strTemp = "";
        }

        return strTemp;
    }

    static public String roundFormatDown(Double num, int len) {
        String strTemp = "";

        try {
            strTemp = roundFormatDown(String.valueOf(num),len);
        } catch (Exception ex) {
            ex.printStackTrace();
            strTemp = "";
        }

        return strTemp;
    }

    static public String roundFormatDown(Float num, int len) {
        String strTemp = "";

        try {
            strTemp = roundFormatDown(String.valueOf(num),len);
        } catch (Exception ex) {
            ex.printStackTrace();
            strTemp = "";
        }

        return strTemp;
    }


    static public String roundFormatUp(String num, float digits, int len) {
        String strTemp = "";
        try {
            if (num == null || num.equals("")) {
                return strTemp;
            }
            BigDecimal b = new BigDecimal(num).divide(new BigDecimal(String.valueOf(digits)));
            BigDecimal f1 = b.setScale(0, BigDecimal.ROUND_DOWN);
            BigDecimal num2 = f1.multiply(new BigDecimal(String.valueOf(digits)));

//            if (digits<1){
//                double div = div(1, digits);
//            }else{
//                String strDigits = String.valueOf(digits);
//                String strDigits = new BigDecimal(String.valueOf(digits)).toPlainString();
//                int startIndex;
//                boolean contains = strDigits.contains(".");
//                if (contains) {
//                    startIndex = strDigits.indexOf(".");
//                    len = strDigits.substring(startIndex + 1, strDigits.length()).length();
//                } else {
//                    len = 0;
//                }


            strTemp = num2.setScale(len, BigDecimal.ROUND_DOWN).toString();
//            strTemp = roundFormatUp(String.valueOf(num2), len);


        } catch (Exception ex) {
            ex.printStackTrace();
            strTemp = "";
        }

        return strTemp;
    }

    static public String roundFormat(BigDecimal b, int len) {
        String strTemp = "";

        try {
            BigDecimal f1 = b.setScale(len, BigDecimal.ROUND_DOWN);
            strTemp = f1.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            strTemp = "";
        }

        return strTemp;
    }

    // 百分比
    public static String getPercentFormat(double d) {
        String date = new java.text.DecimalFormat("#0.00").format(d);
        return date + "%";
    }


    /**
     * 由于Java的简单类型不能够精确的对浮点数进行运算，这个工具类提供精
     * 确的浮点数运算，包括加减乘除和四舍五入。
     */
    private static final int DEF_DIV_SCALE = 10; //这个类不能实例化

    /**
     * 提供精确的加法运算。
     *
     * @param v1 被加数
     * @param v2 加数
     * @return 两个参数的和
     */
    public static double add(double v1, double v2) {
        try {
            BigDecimal b1 = new BigDecimal(Double.toString(v1));
            BigDecimal b2 = new BigDecimal(Double.toString(v2));
            return b1.add(b2).doubleValue();

        }catch (Exception e){
            return 0d;
        }
    }

    /**
     * 提供精确的减法运算。
     *
     * @param v1 被减数
     * @param v2 减数
     * @return 两个参数的差
     */
    public static double sub(double v1, double v2) {
        try {
            BigDecimal b1 = new BigDecimal(Double.toString(v1));
            BigDecimal b2 = new BigDecimal(Double.toString(v2));
            return b1.subtract(b2).doubleValue();
        }catch (Exception e){
            return 0d;
        }
    }

    /**
     * 提供精确的加法运算。
     *
     * @param v1 被加数
     * @param v2 加数
     * @return 两个参数的和
     */
    public static double add(String v1, String v2) {
        try {
            BigDecimal b1 = new BigDecimal(v1);
            BigDecimal b2 = new BigDecimal(v2);
            return b1.add(b2).doubleValue();
        }catch (Exception e){
            return 0d;
        }
    }

    /**
     * 提供精确的加法运算。
     *
     * @param v1 被加数
     * @param v2 加数
     * @return 两个参数的和
     */
    public static String add2(String v1, String v2) {
        try {
            BigDecimal b1 = new BigDecimal(v1);
            BigDecimal b2 = new BigDecimal(v2);
            return b1.add(b2).toPlainString();
        }catch (Exception e){
            return "";
        }
    }

    /**
     * 提供精确的减法运算。
     *
     * @param v1 被减数
     * @param v2 减数
     * @return 两个参数的差
     */
    public static double sub(String v1, String v2) {
        try {
            BigDecimal b1 = new BigDecimal(v1);
            BigDecimal b2 = new BigDecimal(v2);
            return b1.subtract(b2).doubleValue();
        }catch (Exception e){
            return 0d;
        }
    }

    /**
     * 提供精确的乘法运算。
     *
     * @param v1 被乘数
     * @param v2 乘数
     * @return 两个参数的积
     */
    public static double mul(double v1, double v2) {
        try {
            BigDecimal b1 = new BigDecimal(Double.toString(v1));
            BigDecimal b2 = new BigDecimal(Double.toString(v2));
            return b1.multiply(b2).doubleValue();
        }catch (Exception e){
            return 0d;
        }
    }

    /**
     * 提供精确的乘法运算。
     *
     * @param v1 被乘数
     * @param v2 乘数
     * @return 两个参数的积
     */
    public static double mul(String v1, String v2) {
        try {
            if (TextUtils.isEmpty(v1) || TextUtils.isEmpty(v2)) {
                return 0;
            }
            BigDecimal b1 = new BigDecimal(v1);
            BigDecimal b2 = new BigDecimal(v2);
            return b1.multiply(b2).doubleValue();
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 提供（相对）精确的除法运算，当发生除不尽的情况时，精确到
     * 小数点以后10位，以后的数字四舍五入。
     *
     * @param v1 除数
     * @param v2 被除数
     * @return 两个参数的商 v2/v1
     */
    public static double div(double v1, double v2) {
        return div(v1, v2, DEF_DIV_SCALE);
    }

    /**
     * 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指
     * 定精度，以后的数字四舍五入。
     *
     * @param v1    除数
     * @param v2    被除数
     * @param scale 表示表示需要精确到小数点以后几位。
     * @return 两个参数的商 v2/v1
     */
    public static double div(double v1, double v2, int scale) {
        try {
            if (scale < 0) {
                throw new IllegalArgumentException(
                        "The scale must be a positive integer or zero");
            }
            BigDecimal b1 = new BigDecimal(Double.toString(v1));
            BigDecimal b2 = new BigDecimal(Double.toString(v2));
            return b2.divide(b1, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
        }catch (Exception e){
            return 0d;
        }
    }

    /**
     * 提供（相对）精确的除法运算，当发生除不尽的情况时，精确到
     * 小数点以后10位，以后的数字四舍五入。
     *
     * @param v1 除数
     * @param v2 被除数
     * @return 两个参数的商 v2/v1
     */
    public static double div(String v1, String v2) {
        try {
            if (TextUtils.isEmpty(v1) || TextUtils.isEmpty(v2)) {
                return 0;
            }
            return div(v1, v2, DEF_DIV_SCALE);
        } catch (Exception e) {
            return 0;
        }
    }

    public static String divStr(String v1, String v2) {
        try {
            if (TextUtils.isEmpty(v1) || TextUtils.isEmpty(v2)) {
                return "";
            }
            BigDecimal b1 = new BigDecimal(v1);
            BigDecimal b2 = new BigDecimal(v2);

            return b2.divide(b1,18,BigDecimal.ROUND_HALF_UP).toPlainString();
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指
     * 定精度，以后的数字四舍五入。
     *
     * @param v1    除数
     * @param v2    被除数
     * @param scale 表示表示需要精确到小数点以后几位。
     * @return 两个参数的商 v2/v1
     */
    public static double div(String v1, String v2, int scale) {
        try {
            if (TextUtils.isEmpty(v1) || TextUtils.isEmpty(v2)) {
                return 0;
            }
            if (scale < 0) {
                throw new IllegalArgumentException(
                        "The scale must be a positive integer or zero");
            }
            BigDecimal b1 = new BigDecimal(v1);
            BigDecimal b2 = new BigDecimal(v2);
            return b2.divide(b1, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
        } catch (Exception e) {
            return 0;
        }

    }

    /**
     * 提供精确的小数位四舍五入处理。
     *
     * @param v     需要四舍五入的数字
     * @param scale 小数点后保留几位
     * @return 四舍五入后的结果
     */
    public static double round(double v, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        BigDecimal b = new BigDecimal(Double.toString(v));
        BigDecimal one = new BigDecimal("1");
        return b.divide(one, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }


    /**
     * 转换0.01 、0.001等格式文案为小数位数
     *
     * @param digit
     */
    public static int calNumerCount(Context context, String digit) {
        try{
            if (TextUtils.isEmpty(digit) || !digit.contains("1")) {
                return 0;
            } else {
                int dianIndex = digit.contains(".") ? digit.indexOf(".") : digit.length();
                int yiIndex = digit.indexOf("1");
                int diffValue = dianIndex - yiIndex;
                if (diffValue > 0) {
                    String result = String.valueOf(1);
                    for (int value = diffValue; value > 0; value--) {
                        result = result + "0";
                    }
                    return 0;
                } else if (diffValue < 0) {
                    return Math.abs(diffValue);
                } else {
                    return 0;
                }

            }
        }catch (Exception e){
            return 0;
        }

    }

    /**
     * 转行0.01为文案
     *
     * @param s
     */
    public static String calNumerName(Context context, String s) {
        if (TextUtils.isEmpty(s) || !s.contains("1")) {
            return context.getResources().getString(R.string.string_number_zero, "0");
        } else {
            int dianIndex = s.contains(".") ? s.indexOf(".") : s.length();
            int yiIndex = s.indexOf("1");
            int diffValue = dianIndex - yiIndex;
            if (diffValue > 0) {
                String result = String.valueOf(1);
                for (int value = diffValue; value > 0; value--) {
                    result = result + "0";
                }
                return result;
            } else if (diffValue < 0) {
                return context.getResources().getString(R.string.string_number_zero, "" + Math.abs(diffValue));
            } else {
                return context.getResources().getString(R.string.string_number_zero, "0");
            }

        }

    }

    /**
     * 字符串截取补长度 四舍五入
     * @param key
     * @param digitCount
     * @return
     */
    public static String roundingString(String key, int digitCount) {
        if (TextUtils.isEmpty(key)) {
            return key;
        }

        if (key.contains(".")) {
            int diff = key.length() - key.indexOf(".")-1;
            if (diff<digitCount){
                for (int i = 0; i < digitCount-diff; i++) {
                    key=key+"0";
                }
                return key;
            }else{
                if (digitCount>0) {
                    key = key.substring(0,key.indexOf(".")+1+digitCount);
                }else{
                    key = key.substring(0,key.indexOf("."));
                }
                return key;
            }
        }else{
            if (digitCount>0) {
                key = key+".";
            }
            for (int i = 0; i < digitCount; i++) {
                key = key+"0";
            }
            return key;
        }
    }

    /**
     * 字符串截取补长度 去尾
     * @param key
     * @param digitCount
     * @return
     */
    public static String roundDownString(String key, int digitCount) {
        if (TextUtils.isEmpty(key)) {
            return key;
        }

        try {
            BigDecimal num = new BigDecimal(key).setScale(digitCount, BigDecimal.ROUND_DOWN);
            return num.toPlainString();

        }catch (Exception e){
            return key;
        }

    }

    /**
     * 字符串截取补长度 补前
     * @param key
     * @param digitCount
     * @return
     */
    public static String roundUpString(String key, int digitCount) {

        if (TextUtils.isEmpty(key)) {
            return key;
        }


        try {
            BigDecimal num = new BigDecimal(key).setScale(digitCount, BigDecimal.ROUND_UP);
            return num.toPlainString();

        }catch (Exception e){
            return key;
        }
    }

    /**
     * 使用java正则表达式去掉多余的.与0
     * @param s
     * @return
     */
    public static String subZeroAndDot(String s){
        if(s.indexOf(".") > 0){
            s = s.replaceAll("0+?$", "");//去掉多余的0
            s = s.replaceAll("[.]$", "");//如最后一位是.则去掉
        }
        return s;
    }

    /**
     * 去除末尾多余的0
     * @param num
     * @return
     */
    public static String stripTrailingZeros(String num){
        num = new BigDecimal(num).stripTrailingZeros().toPlainString();
        return num;
    }
}
