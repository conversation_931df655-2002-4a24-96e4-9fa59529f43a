/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BasicFunctionsUtil.java
 *   @Date: 19-5-6 下午3:36
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.utils;

import io.bhex.app.BuildConfig;
import io.bhex.baselib.network.Utils.Convert;
import io.bhex.sdk.config.bean.BasicFunctionsConfig;

public class BasicFunctionsUtil {

    /**
     * 获取基础功能配置
     * @return
     */
    public static BasicFunctionsConfig getBasicFunctionsConfig() {
        BasicFunctionsConfig basicFunctionsConfig = Convert.fromJson(BuildConfig.BASIC_FUNCTIONS, BasicFunctionsConfig.class);
        if (basicFunctionsConfig != null) {
            return basicFunctionsConfig;
        }else{
            return new BasicFunctionsConfig();
        }
    }

}
