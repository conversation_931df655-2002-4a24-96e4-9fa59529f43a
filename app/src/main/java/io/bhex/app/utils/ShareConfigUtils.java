/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: ShareConfigUtils.java
 *   @Date: 19-7-8 下午4:43
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.utils;

import android.text.TextUtils;

import io.bhex.baselib.network.Utils.Convert;
import io.bhex.baselib.utils.SP;
import io.bhex.sdk.config.bean.ShareConfigBean;

public class ShareConfigUtils {

    /**
     * 保存分享配置信息
     * @param response
     */
    public static void saveShareConfigData(String response) {
        SP.set("shareConfig",response);
    }

    /**
     * 获取配置信息
     * @return
     */
    public static ShareConfigBean getShareConfig(){
        String shareConfig = SP.get("shareConfig", "");
        if (!TextUtils.isEmpty(shareConfig)) {
            ShareConfigBean shareConfigBean = Convert.fromJson(shareConfig, ShareConfigBean.class);
            return shareConfigBean;
        }
        return null;
    }
}
