/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: VersionUpdateUtil.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.utils;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Environment;
import android.text.TextUtils;
import android.view.View;

import java.io.File;

import io.bhex.app.R;
import io.bhex.app.download.ApkDownLoadService;
import io.bhex.app.download.DownloadInfo;
import io.bhex.sdk.account.bean.UpdateResponse;
import io.bhex.sdk.utils.UtilsApi;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.app.view.VersionUpdateDialog;
import io.bhex.baselib.utils.ToastUtils;

public class VersionUpdateUtil {


    private static void showForceUpdateDialog(final Context context, final String url,String descp,final boolean forceUpdate) {
        final VersionUpdateDialog builder=new VersionUpdateDialog(context);
        builder.setTitle(context.getString(R.string.string_version_find_new));
        builder.setMessage(descp);
        builder.setCancelable(false);
        builder.setCanceledOnTouchOutside(false);
        builder.setPositiveButton(context.getString(R.string.string_version_update), new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                // 更新的操作
                if(builder.isShowing()){
                    builder.dismiss();
                }
                String channel = CommonUtil.getChannel(context);
                if (!TextUtils.isEmpty(channel)) {
                    //TODO google play渠道配置，请设置渠道名称包含 googleplay 字符串
                    if (channel.toLowerCase().contains("googleplay")) {
                        context.startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(url)));
                        return;
                    }
                }
				downloadLatestVersion(context,url);
                //context.startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(url)));
            }
        });
        builder.setNegativeButton("", new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                // 取消的操作
                if(builder.isShowing()){
                    builder.dismiss();
                }
            }
        });

        builder.setNegativeButtonEnable(!forceUpdate);
        builder.show();
    }


    /**
     * 下载更新APP的方法
     */
    public static void checkVersionUpdate(final Context context, final boolean showToast){
        UtilsApi.RequestCheckVersionUpdate(context,new SimpleResponseListener<UpdateResponse>() {

            @Override
            public void onSuccess(UpdateResponse data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data,false)) {
                    String url = data.downloadUrl;
                    String descp = data.newFeatures;
                    boolean forceUpdate = data.needForceUpdate;
                    if(showToast){
                        if(data.needUpdate == true && !TextUtils.isEmpty(url))
                            showForceUpdateDialog(context,url,descp,forceUpdate);
                    }else{
                        if(data.needUpdate == true && !TextUtils.isEmpty(url))
                            showForceUpdateDialog(context,url,descp,forceUpdate);
                        else
                            ToastUtils.showLong(context, context.getString(R.string.string_version_new));
                    }
                }
            }

        });

    }
    /**
     * 最新版本apk的下载
     * @param url 下载地址
     */
    public static void downloadLatestVersion(Context context,String url){
        /*int flag = checkSdkStateAndDownloadManagerState();
        //下载服务可用
        if(flag==SDCARD_DOWNLOADSERVICE_OK){
            DownLoadUtil downloadutil=new DownLoadUtil(mContext);
            downloadutil.setNotificationTitle("Bhex");
            downloadutil.setNotificationDescription("正在下载...");
            try {
                downloadutil.downLoad(url);
            } catch (Exception e) {
            }
        }else if(flag==NO_SDCARD){
            ToastFactory.showToast(mContext, "您的SD卡不可用");
        }else if(flag==NO_DOWNLOADSERVICE){
            ToastFactory.showToast(mContext, "您的下载管理器已经关闭，请先开启再下载更新");
        }*/

        Intent intent = new Intent(context, ApkDownLoadService.class);
        DownloadInfo downloadInfo = new DownloadInfo(url,null);
        File file = new File(context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS),downloadInfo.getApkFileName());
        if(file.exists()){
            file.delete();
        }
        downloadInfo.apkLocalPath = file.getAbsolutePath();
        intent.putExtra("taskInfo", downloadInfo);
        context.startService(intent);
        //ToastUtils.showToast(getResources().getString(**********));
        ToastUtils.showLong(context.getResources().getString(R.string.app_loading_now));
    }
    /**
     * 检查sd卡或者下载管理器的服务状态
     * @return 默认返回-1
     * 0：代表有sd卡并且下载管理可用
     * 1：sd卡不可用
     * 2：下载服务不可用
     */
    /*public int checkSdkStateAndDownloadManagerState(){
        int applicationEnabledSetting = mContext.getPackageManager().getApplicationEnabledSetting("com.android.providers.downloads");
        if(Environment.getExternalStorageState().equals(
                Environment.MEDIA_MOUNTED) && (applicationEnabledSetting==PackageManager.COMPONENT_ENABLED_STATE_DEFAULT
                || applicationEnabledSetting==PackageManager.COMPONENT_ENABLED_STATE_ENABLED))
            return SDCARD_DOWNLOADSERVICE_OK;
        if(!Environment.getExternalStorageState().equals(
                Environment.MEDIA_MOUNTED))
            return NO_SDCARD;
        if(!(applicationEnabledSetting==PackageManager.COMPONENT_ENABLED_STATE_DEFAULT
                || applicationEnabledSetting==PackageManager.COMPONENT_ENABLED_STATE_ENABLED))
            return NO_DOWNLOADSERVICE;
        return DEFAULT;
    }*/
}