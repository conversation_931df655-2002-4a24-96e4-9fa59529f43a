package io.bhex.app.download;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2020-7-3 10:30:57
 * 下载
 */
public class DownloadInfo implements Serializable {

    public String downloadUrl;
    public String apkVersion;
    public String apkLocalPath;

    public DownloadInfo(String downloadUrl, String apkVersion) {
        this.downloadUrl = downloadUrl;
        this.apkVersion = apkVersion;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public String getApkVersion() {
        return apkVersion;
    }

    public void setApkVersion(String apkVersion) {
        this.apkVersion = apkVersion;
    }

    public String getApkLocalPath() {
        return apkLocalPath;
    }

    public void setApkLocalPath(String apkLocalPath) {
        this.apkLocalPath = apkLocalPath;
    }

    public String getApkFileName(){
        StringBuffer sb = new StringBuffer("");
        //sb.append("v").append(this.apkVersion).append("-hbtc.apk");
        sb.append("v").append("-hbtc.apk");
        return sb.toString();
    }
}
