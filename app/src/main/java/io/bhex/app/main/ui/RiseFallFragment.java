/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: RiseFallFragment.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.main.ui;

import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseFragment;
import io.bhex.app.main.bean.RiseFallBean;
import io.bhex.app.main.presenter.RiseFallFragmentPresenter;

/**
 * ================================================
 * 描   述：涨跌幅
 * ================================================
 */

public class RiseFallFragment extends BaseFragment<RiseFallFragmentPresenter,RiseFallFragmentPresenter.RiseFallUI> implements RiseFallFragmentPresenter.RiseFallUI, View.OnClickListener, BaseQuickAdapter.RequestLoadMoreListener, SwipeRefreshLayout.OnRefreshListener {


    private RecyclerView recyclerView;
    private SwipeRefreshLayout swipeRefresh;
    private RangeAdapter adapter;

    @Override
    protected RiseFallFragmentPresenter.RiseFallUI getUI() {
        return this;
    }

    @Override
    protected RiseFallFragmentPresenter createPresenter() {
        return new RiseFallFragmentPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_risefall_layout,null,false);
    }

    @Override
    protected void initViews() {
        super.initViews();
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        swipeRefresh.setEnabled(false);
        recyclerView = viewFinder.find(R.id.recyclerView);

       initData();

    }

    private void initData() {
        List<RiseFallBean> items = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            RiseFallBean item = new RiseFallBean("ETH/BTC","1000","0.000123","1.25","+1.2%");
            items.add(item);
        }

        adapter = new RangeAdapter(items);
        adapter.openLoadAnimation(BaseQuickAdapter.SCALEIN);
        adapter.isFirstOnly(false);
        adapter.setOnLoadMoreListener(this);

        swipeRefresh.setColorSchemeColors(Color.RED, Color.BLUE, Color.GREEN);
        swipeRefresh.setOnRefreshListener(this);
        swipeRefresh.setEnabled(false);

        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        recyclerView.setItemAnimator(new DefaultItemAnimator());
        recyclerView.addItemDecoration(new DividerItemDecoration(getContext(), LinearLayoutManager.VERTICAL));

        recyclerView.setAdapter(adapter);
    }

    @Override
    protected void addEvent() {
        super.addEvent();

    }

    @Override
    public void onClick(View v) {

    }

    @Override
    public void onLoadMoreRequested() {
        swipeRefresh.postDelayed(new Runnable() {
            @Override
            public void run() {
//                adapter.loadComplete();
                adapter.loadMoreComplete();
            }
        }, 500);
    }

    @Override
    public void onRefresh() {
        setRefreshing(false);
    }


    public void setRefreshing(final boolean refreshing) {
        swipeRefresh.post(new Runnable() {
            @Override
            public void run() {
                swipeRefresh.setRefreshing(refreshing);
            }
        });
    }

    private class RangeAdapter extends BaseQuickAdapter<RiseFallBean,BaseViewHolder> {

        RangeAdapter(List<RiseFallBean> data) {
            super(R.layout.item_rise_fall_list_layout, data);
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final RiseFallBean itemModel) {
            baseViewHolder.setText(R.id.item_coinpair, itemModel.coinPair);
            baseViewHolder.setText(R.id.item_amount, itemModel.amount);
            baseViewHolder.setText(R.id.item_price1, itemModel.price1);
            baseViewHolder.setText(R.id.item_price2, itemModel.price2);
            baseViewHolder.setText(R.id.item_range_ratio, itemModel.riseFallRatio);
            baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
//                    ToastUtils.showShort(itemModel.coinPair+": "+itemModel.price1);
//                    IntentUtils.goKline(getActivity(), itemModel.getExchangeId(), itemModel.getSymbolId(), "BTCUSDT");

                }
            });
        }
    }
}
