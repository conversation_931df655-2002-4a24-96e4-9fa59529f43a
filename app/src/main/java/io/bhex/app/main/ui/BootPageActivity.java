/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BootPageActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.main.ui;

import android.view.View;
import android.widget.LinearLayout;

import androidx.viewpager.widget.ViewPager;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.main.adapter.BootPageAdapter;
import io.bhex.app.main.presenter.BootPagePresenter;
import io.bhex.app.utils.IntentUtils;

/**
 * ================================================
 * 描   述：App 引导页
 * ================================================
 */

public class BootPageActivity extends BaseActivity<BootPagePresenter,BootPagePresenter.BootPageUI> implements BootPagePresenter.BootPageUI, View.OnClickListener {
    private ViewPager myViewPager;
    private LinearLayout circyleLayout;


    @Override
    protected int getContentView() {
        return R.layout.activity_boot_page_layout;
    }

    @Override
    protected BootPagePresenter createPresenter() {
        return new BootPagePresenter();
    }

    @Override
    protected BootPagePresenter.BootPageUI getUI() {
        return this;
    }

    @Override
    protected boolean translucentStatusBar() {
        return true;
    }

    @Override
    protected void initView() {
        super.initView();
        myViewPager = viewFinder.find(R.id.viewPager);
        circyleLayout = viewFinder.find(R.id.circyle_layout);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.signup).setOnClickListener(this);
        viewFinder.find(R.id.login).setOnClickListener(this);
        viewFinder.find(R.id.login_btn).setOnClickListener(this);
        viewFinder.find(R.id.btn_start).setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.signup:
                getPresenter().toSignUp();
                break;
            case R.id.login:
                getPresenter().toLogin();
                break;
            case R.id.login_btn:
                getPresenter().toLogin();
                break;
            case R.id.btn_start:
                int totalCount = myViewPager.getAdapter().getCount();
                int currentPosition = myViewPager.getCurrentItem();
                if (totalCount-1==currentPosition) {
                    IntentUtils.goMain(this);
                    //最后一页
                    finish();
                }else{
                    //前几页,跳到下一页
                    myViewPager.setCurrentItem(currentPosition+1);

                }
                break;
        }
    }

    @Override
    public void setAdapter(final BootPageAdapter adapter) {
        myViewPager.setAdapter(adapter);
        //设置ViewPage的选中事件
        myViewPager.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            public void onPageSelected(int position) {
                getPresenter().selectDot(position);
                if (adapter.getCount()==position+1) {
//                    viewFinder.find(R.id.btn_start).setVisibility(View.VISIBLE);

                    viewFinder.find(R.id.btn_start).setBackgroundResource(R.mipmap.boot_start_btn);
                    viewFinder.textView(R.id.login_btn).setVisibility(View.VISIBLE);
                }else{
//                    viewFinder.find(R.id.btn_start).setVisibility(View.GONE);

                    viewFinder.find(R.id.btn_start).setBackgroundResource(R.mipmap.boot_next_btn);
                    viewFinder.textView(R.id.login_btn).setVisibility(View.INVISIBLE);
                }
            }

            @Override
            public void onPageScrolled(int arg0, float arg1, int arg2) {
            }

            @Override
            public void onPageScrollStateChanged(int arg0) {
            }
        });
    }

    @Override
    public LinearLayout getCircleLayout() {
        return circyleLayout;
    }

    @Override
    protected boolean isStatusColorDefault() {
        return false;
    }
}
