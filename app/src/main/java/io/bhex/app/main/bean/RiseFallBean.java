/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: RiseFallBean.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.main.bean;

import io.bhex.baselib.network.response.BaseResponse;

/**
 * ================================================
 * 描   述：涨跌幅实体
 * ================================================
 */

public class RiseFallBean extends BaseResponse {
    public String coinPair;//币对
    public String amount;//成交量
    public String price1;//价1
    public String price2;//价2
    public String riseFallRatio;//涨跌幅度

    public RiseFallBean() {
    }

    public RiseFallBean(String coinPair,String amount,String price1,String price2,String riseFallRatio) {
        this.coinPair = coinPair;
        this.amount = amount;
        this.price1 = price1;
        this.price2 = price2;
        this.riseFallRatio = riseFallRatio;
    }
}
