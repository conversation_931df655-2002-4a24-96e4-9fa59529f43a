/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BootPagePresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.main.presenter;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import java.util.ArrayList;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.main.adapter.BootPageAdapter;
import io.bhex.app.utils.IntentUtils;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;



public class BootPagePresenter extends BasePresenter<BootPagePresenter.BootPageUI> {
    private int[] imageIds;
    private int[] circles;
    private ArrayList<View> views;

    @Override
    public void onUIReady(BaseCoreActivity activity, BootPageUI ui) {
        super.onUIReady(activity, ui);


        views=new ArrayList<>();
        circles = new int[]{R.mipmap.page_oval, R.mipmap.page_oval_s};
        imageIds = new int[]{R.mipmap.page_one, R.mipmap.page_two, R.mipmap.page_three, R.mipmap.page_four};
        for (int i = 1; i < getUI().getCircleLayout().getChildCount(); i++) {
            ((ImageView) getUI().getCircleLayout().getChildAt(i)).setImageResource(circles[0]);
        }
        ((ImageView) getUI().getCircleLayout().getChildAt(0)).setImageResource(circles[1]);
        BootPageAdapter adapter = new BootPageAdapter(views,imageIds);
        for(int i=0;i<imageIds.length;i++){
            ImageView mImageView=new ImageView(getActivity());
            mImageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
            views.add(mImageView);
        }
        getUI().setAdapter(adapter);
    }

    public void toSignUp() {
        IntentUtils.goRegister(getActivity(),null);
    }

    public void toLogin() {
        IntentUtils.goLogin(getActivity(),null);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    public void selectDot(int position) {
        int circle;
        for (int i = 0; i < getUI().getCircleLayout().getChildCount(); i++) {
            if (i == position) {
                circle = circles[1];
            } else {
                circle = circles[0];
            }
            ((ImageView) getUI().getCircleLayout().getChildAt(i)).setImageResource(circle);
        }
    }

    public interface BootPageUI extends AppUI{
        void setAdapter(BootPageAdapter adapter);

        LinearLayout getCircleLayout();
    }
}
