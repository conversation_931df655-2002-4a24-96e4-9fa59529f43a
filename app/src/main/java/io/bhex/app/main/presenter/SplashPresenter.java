/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: SplashPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.main.presenter;

import android.content.Intent;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.main.ui.BootPageActivity;
import io.bhex.app.main.ui.MainActivity;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.DialogUtils;
import io.bhex.app.web.ui.WebActivity;
import io.bhex.baselib.core.SPEx;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.HttpUtils;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.config.ConfigApi;
import io.bhex.sdk.config.bean.IndexConfigBean;
import io.bhex.sdk.data_manager.MMKVManager;


public class SplashPresenter extends BasePresenter<SplashPresenter.SplashUI> {
    public boolean bBlocked = false;
    private IndexConfigBean currentProtocul;

    public Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if(bBlocked == false)
                startApp();
        }
    };


    @Override
    public void onResume() {
        super.onResume();
        getProtocolUrls();
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, SplashUI ui) {
        super.onUIReady(activity, ui);
        mHandler.sendEmptyMessageDelayed(0, 1500);
        getProtocolUrls();
    }

    public interface SplashUI extends AppUI{

    }


    public void startApp() {
        boolean isAgreePrivacy = checkIsAgreePrivacy();
        if (!isAgreePrivacy) {
            //未同意隐私协议，弹出隐私协议确认
            showProtocol();
            return;
        }

        boolean firstlaunch = SPEx.get("firstlaunch", true);

//        firstlaunch = false;
        if (firstlaunch&&CommonUtil.isBhex(getActivity())) {
            SPEx.set("firstlaunch",false);
            Intent intent = new Intent(getActivity(), BootPageActivity.class);
            getActivity().startActivity(intent);
        }else{
            Intent intent = new Intent(getActivity(), MainActivity.class);
            getActivity().startActivity(intent);
        }
        getActivity().finish();
    }

    /**
     * 显示隐私协议
     */
    private void showProtocol() {
        if (getUI() == null) {
            return;
        }
        DialogUtils.showProtoculPrivacy(getActivity(), new DialogUtils.OnButtonPrivacyListener() {
            @Override
            public void onClickPrivacy() {
                if (currentProtocul != null) {
                    String privacyAgreement = currentProtocul.getPrivacyAgreement();
                    if (!TextUtils.isEmpty(privacyAgreement)) {
                        WebActivity.runActivity(getActivity(), getString(R.string.string_privacy_protocol), privacyAgreement);
                    }
                }
            }

            @Override
            public void onClickUserAgree() {
                if (currentProtocul != null) {
                    String userAgreement = currentProtocul.getUserAgreement();
                    if (!TextUtils.isEmpty(userAgreement)) {
                        WebActivity.runActivity(getActivity(), getString(R.string.string_signup_protocol), userAgreement);
                    }
                }
            }

            @Override
            public void onConfirm() {
                MMKVManager.getInstance().mmkv().encode("isAgreePrivacy",true);
                startApp();
            }

            @Override
            public void onExit() {
                exit();
            }
        });
    }

    //检查是否同意了隐私协议
    private boolean checkIsAgreePrivacy() {
        boolean isAgreePrivacy = MMKVManager.getInstance().mmkv().decodeBool("isAgreePrivacy",false);
        return isAgreePrivacy;
    }

    public void exit() {
        System.exit(0);
    }

    /**
     * 获取协议地址
     */
    private void getProtocolUrls() {
        if (checkIsAgreePrivacy()) {
            return;
        }
        ConfigApi.getIndexConfig(new SimpleResponseListener<IndexConfigBean>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(IndexConfigBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    currentProtocul = response;
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
