package io.bhex.app.main.manager;

import android.content.Context;
import android.util.Log;


import io.bhex.app.R;
import io.bhex.app.view.CustomFreshHeader;
import io.bhex.baselib.utils.DebugLog;

/**
 * created by gong<PERSON><PERSON>
 * on 2020/3/1
 */
public class RefreshLayoutManager {
    public static void init(){

    }

    public static void initLocalize(Context context){
        DebugLog.d("RefreshLayoutManager","REFRESH_HEADER_PULLING==");
        CustomFreshHeader.REFRESH_HEADER_PULLING = context.getString(R.string.bh_srl_header_pulling);
        DebugLog.d("RefreshLayoutManager","REFRESH_HEADER_PULLING=="+CustomFreshHeader.REFRESH_HEADER_PULLING);
        CustomFreshHeader.REFRESH_HEADER_REFRESHING = context.getString(R.string.bh_srl_header_refreshing);
        CustomFreshHeader.REFRESH_HEADER_LOADING = context.getString(R.string.bh_srl_header_loading);
        CustomFreshHeader.REFRESH_HEADER_RELEASE = context.getString(R.string.bh_srl_header_release);
        CustomFreshHeader.REFRESH_HEADER_FINISH = context.getString(R.string.bh_srl_header_finish);
        CustomFreshHeader.REFRESH_HEADER_FAILED = context.getString(R.string.bh_srl_header_failed);
        CustomFreshHeader.REFRESH_HEADER_UPDATE = context.getString(R.string.bh_srl_header_update);
        CustomFreshHeader.REFRESH_HEADER_SECONDARY = context.getString(R.string.bh_srl_header_secondary);
    }
}
