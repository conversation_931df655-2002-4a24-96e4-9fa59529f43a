/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AdsAdapter.java
 *   @Date: 19-4-25 下午6:09
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.main.adapter;

import android.text.TextUtils;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.ArrayList;

import io.bhex.app.R;
import io.bhex.app.main.bean.HomeAdsBean;
import io.bhex.baselib.images.CImageLoader;
import io.bhex.sdk.config.bean.IndexModuleItem;

/**
 * ================================================
 * 描   述：搜索币种
 * ================================================
 */

public class AdsAdapter extends BaseQuickAdapter<IndexModuleItem, BaseViewHolder> {

    public AdsAdapter(ArrayList<IndexModuleItem> data) {
        super(R.layout.item_ads_layout, data);
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final IndexModuleItem itemModel) {
        baseViewHolder.setText(R.id.name, itemModel.getModuleName());
        ImageView imageView = baseViewHolder.getView(R.id.icon);
        String iconUrl = itemModel.getDefaultIcon();
        if (!TextUtils.isEmpty(iconUrl)) {
            CImageLoader.getInstance().load(imageView,iconUrl);
        }
    }
}
