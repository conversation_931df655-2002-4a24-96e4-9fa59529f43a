package io.bhex.app.main.ui;

import android.view.KeyEvent;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.main.presenter.ExitDialogPresenter;
import io.bhex.app.utils.DialogUtils;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-11-20
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class ExitDialogActivity extends BaseActivity<ExitDialogPresenter, ExitDialogPresenter.ExitDialogUI> implements ExitDialogPresenter.ExitDialogUI {

    @Override
    protected int getContentView() {
        return R.layout.activity_exit_app_layout;
    }

    @Override
    protected ExitDialogPresenter createPresenter() {
        return new ExitDialogPresenter();
    }

    @Override
    protected ExitDialogPresenter.ExitDialogUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        DialogUtils.showDialogOneBtn(this, "", this.getResources().getString(R.string.string_pinning_tips), this.getResources().getString(R.string.string_logout), false, new DialogUtils.OnButtonEventListener() {
            @Override
            public void onConfirm() {
                System.exit(0);
            }

            @Override
            public void onCancel() {

            }
        });
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        return super.onKeyDown(keyCode, event);
    }
}
