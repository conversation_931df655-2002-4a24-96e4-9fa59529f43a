package io.bhex.app.main.bean;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.enums.ACCESS_TYPE;
import io.bhex.app.main.ui.MainActivity;
import io.bhex.app.utils.BasicFunctionsUtil;
import io.bhex.app.utils.CustomerServiceUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.web.ui.WebActivity;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.Urls;
import io.bhex.sdk.account.LoginResultCallback;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.config.bean.BasicFunctionsConfig;
import io.bhex.sdk.config.bean.IndexModuleItem;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.enums.ACCOUNT_TYPE;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.trade.CurrentOtcSelection;
import io.bhex.sdk.utils.bean.BannerResponse;
import zendesk.commonui.UiConfig;
import zendesk.support.guide.HelpCenterActivity;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-09-19
 * 邮   箱：
 * 描   述：首页九宫格-快捷入口数据组装
 * ================================================
 */

public class ShortcutEntry {

    public static HomeAdsResponse createShortcutEntryData(Context context) {
        BasicFunctionsConfig basicFunctionsConfig = BasicFunctionsUtil.getBasicFunctionsConfig();
        HomeAdsResponse homeAdsResponse = new HomeAdsResponse();
        List<HomeAdsBean> list = new ArrayList<HomeAdsBean>();
        for (int i = 0; i < 8; i++) {

            switch (i) {
                case 0:
                    if (!basicFunctionsConfig.isOtc()) {
                        HomeAdsBean homeAdsBean = new HomeAdsBean();
                        homeAdsBean.setName(context.getString(R.string.string_access_otc));
                        homeAdsBean.setAccessType(ACCESS_TYPE.TYPE_OTC.getmStatus());
                        homeAdsBean.setIconRes(R.mipmap.icon_ads_otc);
                        list.add(homeAdsBean);
                    }
                    break;
                case 1:
                    if (!basicFunctionsConfig.isExchange()) {
                        HomeAdsBean homeAdsBean = new HomeAdsBean();
                        homeAdsBean.setName(context.getString(R.string.string_access_bb));
                        homeAdsBean.setAccessType(ACCESS_TYPE.TYPE_BB.getmStatus());
                        homeAdsBean.setIconRes(R.mipmap.icon_ads_bb);
                        list.add(homeAdsBean);
                    }
                    break;
                case 2:
                    if (!basicFunctionsConfig.isFuture()) {
                        HomeAdsBean homeAdsBean = new HomeAdsBean();
                        homeAdsBean.setName(context.getString(R.string.string_perpetual_contract));
                        homeAdsBean.setAccessType(ACCESS_TYPE.TYPE_PERPETUAL_CONTRACT.getmStatus());
                        homeAdsBean.setIconRes(R.mipmap.icon_ads_option_new);
                        list.add(homeAdsBean);
                    }
                    break;
                case 3:
                    if (!basicFunctionsConfig.isBonus()) {
                        HomeAdsBean homeAdsBean = new HomeAdsBean();
                        homeAdsBean.setName(context.getString(R.string.string_access_finance));
                        homeAdsBean.setAccessType(ACCESS_TYPE.TYPE_FINANCE.getmStatus());
                        homeAdsBean.setIconRes(R.mipmap.icon_ads_finance_new);
                        list.add(homeAdsBean);
                    }
                    break;
                case 4:
                    if (!basicFunctionsConfig.isNovice()) {
                        HomeAdsBean homeAdsBean4 = new HomeAdsBean();
                        homeAdsBean4.setName(context.getString(R.string.string_access_novice));
                        homeAdsBean4.setAccessType(ACCESS_TYPE.TYPE_NOVICE.getmStatus());
                        homeAdsBean4.setIconRes(R.mipmap.icon_ads_novice);
                        list.add(homeAdsBean4);
                    }
                    break;
                case 5:
                    if (!basicFunctionsConfig.isCustomer_service()) {
                        HomeAdsBean homeAdsBean5 = new HomeAdsBean();
                        homeAdsBean5.setName(context.getString(R.string.string_access_customer));
                        homeAdsBean5.setAccessType(ACCESS_TYPE.TYPE_customer.getmStatus());
                        homeAdsBean5.setIconRes(R.mipmap.icon_ads_customer);
                        list.add(homeAdsBean5);
                    }
                    break;
                case 6:
                    if (!basicFunctionsConfig.isInvite()) {
                        HomeAdsBean homeAdsBean6 = new HomeAdsBean();
                        homeAdsBean6.setName(context.getString(R.string.string_access_invite));
                        homeAdsBean6.setAccessType(ACCESS_TYPE.TYPE_INVITE.getmStatus());
                        homeAdsBean6.setIconRes(R.mipmap.icon_ads_invite);
                        list.add(homeAdsBean6);
                    }
                    break;
                case 7:
                    if (!basicFunctionsConfig.isGuild()) {
                        HomeAdsBean homeAdsBean = new HomeAdsBean();
                        homeAdsBean.setName(context.getString(R.string.string_access_guild));
                        homeAdsBean.setAccessType(ACCESS_TYPE.TYPE_GUILD.getmStatus());
                        homeAdsBean.setIconRes(R.mipmap.icon_ads_guild);
                        list.add(homeAdsBean);

                    }
                    break;
                case 8:
//                    H5页面
//                    if (!basicFunctionsConfig.isH5_link()) {
//                        HomeAdsBean homeAdsBean8 = new HomeAdsBean();
//                        homeAdsBean8.setName(getString(R.string.string_access_h5));
//                        homeAdsBean8.setAccessType(ACCESS_TYPE.TYPE_H5.getmStatus());
//                        homeAdsBean8.setIconRes(R.mipmap.icon_ads_invite);
//                        list.add(homeAdsBean8);
//
//                    }
                    break;
            }

        }
        homeAdsResponse.setArray(list);
        return homeAdsResponse;
    }

    /**
     * 动态设置首页九宫格-快接入口 grid每行个数显示规则
     *
     * @param gridLayoutManagerAds
     * @param data
     */
    public static void setGridDynamicSpanCountRule(GridLayoutManager gridLayoutManagerAds, List<IndexModuleItem> data) {
        if (data == null) {
            return;
        }
        int size = data.size();
        if (size % 5 == 0) {
            gridLayoutManagerAds.setSpanCount(5);
        } else {
            gridLayoutManagerAds.setSpanCount(4);
        }
//        每行3列或者4列展示规则
//        if (size>4) {
//            if (size<=6) {
//                gridLayoutManagerAds.setSpanCount(3);
//            }else{
//                if (size%3==0) {
//                    if (size%4!=1) {
//                        gridLayoutManagerAds.setSpanCount(4);
//                    }else{
//                        gridLayoutManagerAds.setSpanCount(3);
//                    }
//                }else{
//                    gridLayoutManagerAds.setSpanCount(4);
//                }
//            }
//        }else{
//            gridLayoutManagerAds.setSpanCount(size);
//        }
    }


    /**
     * 首页九宫格-快接入口 跳转逻辑处理
     *
     * @param activity
     * @param data
     * @param position
     */
    public static void handleGoWhere(FragmentActivity activity, List<IndexModuleItem> data, int position) {
        try {
            if (data != null) {
                IndexModuleItem homeAdsBean = data.get(position);
                if (homeAdsBean != null) {
                    HashMap<String, String> paramsMap = new HashMap<>();
                    String jumpType = homeAdsBean.getJumpType();
                    String jumpUrl = homeAdsBean.getJumpUrl();
                    if (TextUtils.isEmpty(jumpType) || TextUtils.isEmpty(jumpUrl)) {
                        return;
                    }
                    if (jumpType.equals("H5")) {
                        WebActivity.runActivity(activity, "", jumpUrl);
                    } else if (jumpType.equals("NATIVE")) {
                        String[] paramArray = jumpUrl.split("&");
                        if (paramArray != null && paramArray.length > 0) {
                            for (String param : paramArray) {
                                String[] kv = param.split("=");
                                if (kv != null && kv.length > 0) {
                                    String k = "";
                                    String v = "";
                                    k = kv[0].toUpperCase();
                                    if (kv.length > 1) {
                                        v = kv[1];
                                    }
                                    paramsMap.put(k, v);
                                }
                            }
                        }
                        if (paramsMap != null && paramsMap.size() > 0) {
                            String page = paramsMap.get("PAGE");
                            if (TextUtils.isEmpty(page)) {
                                return;
                            }

                            handleNavigatePage(activity, paramsMap, page);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * banner图跳转逻辑
     *
     * @param bannersBean
     */
    public static void handleBannerGoWhere(FragmentActivity activity, BannerResponse.BannerBean bannersBean) {
        try {
            if (bannersBean != null) {
                HashMap<String, String> paramsMap = new HashMap<>();
                String jumpUrl = bannersBean.getDirectUrl();
                if (TextUtils.isEmpty(jumpUrl)) {
                    return;
                }
                if (jumpUrl.toLowerCase().startsWith("page")) {
                    String[] paramArray = jumpUrl.split("&");
                    if (paramArray != null && paramArray.length > 0) {
                        for (String param : paramArray) {
                            String[] kv = param.split("=");
                            if (kv != null && kv.length > 0) {
                                String k = "";
                                String v = "";
                                k = kv[0].toUpperCase();
                                if (kv.length > 1) {
                                    v = kv[1];
                                }
                                paramsMap.put(k, v);
                            }
                        }
                    }
                    if (paramsMap != null && paramsMap.size() > 0) {
                        String page = paramsMap.get("PAGE");
                        if (TextUtils.isEmpty(page)) {
                            return;
                        }

                        handleNavigatePage(activity, paramsMap, page);
                    }
                } else if (bannersBean.isIsDirect()) {
                    WebActivity.runActivity(activity, "", jumpUrl);
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void handleNavigatePage(FragmentActivity activity, HashMap<String, String> paramsMap, String page) {
        if (page.equalsIgnoreCase("TABHOME")) { //首页
            IntentUtils.goMain(activity);
        } else if (page.equalsIgnoreCase("TABQUOTE")) { //行情
            IntentUtils.goMarket(activity);
        } else if (page.equalsIgnoreCase("TABTRADE")) { //交易
            IntentUtils.goBBTrade(activity);
        } else if (page.equalsIgnoreCase("TABFUTURES")) {   //合约
            IntentUtils.goPerpetualContractTrade(activity);
        } else if (page.equalsIgnoreCase("TABASSETS")) {    //资产
            UserInfo.LoginAndGoin(activity, new LoginResultCallback() {
                @Override
                public void onLoginSucceed() {
                    String tabName = paramsMap.get("TAB");
                    if (TextUtils.isEmpty(tabName)||tabName.equalsIgnoreCase("COIN")) {
                        IntentUtils.goMyAsset(activity);
                    } else if (tabName.equalsIgnoreCase("FUTURES")) {
                        IntentUtils.goFuturesAsset(activity);
                    } else if (tabName.equalsIgnoreCase("OPTION")) {
                        IntentUtils.goOptionAsset(activity);
                    } else if (tabName.equalsIgnoreCase("MARGIN")) {
                        IntentUtils.goMarginAsset(activity);
                    } else if (tabName.equalsIgnoreCase("COINPLUS")) {
                        IntentUtils.goCoinPlusAsset(activity);
                    }
                }
            });
        } else if (page.equalsIgnoreCase("ME")) {   //我的
            IntentUtils.goAccount(activity);
        } else if (page.equalsIgnoreCase("BDD")) {  //币多多(理财)
            IntentUtils.goFinanceList(activity);
        } else if (page.equalsIgnoreCase("LOGIN")) {    //登录
            if (!UserManager.getInstance().isLogin()) {
                IntentUtils.goLogin(activity, null);
            }
        } else if (page.equalsIgnoreCase("SIGNUP")) {   //注册
            if (!UserManager.getInstance().isLogin()) {
                IntentUtils.goRegister(activity, null);
            }
        } else if (page.equalsIgnoreCase("SETTING")) {  //设置
            IntentUtils.goSettings(activity);
        } else if (page.equalsIgnoreCase("SAFE")) {     //安全

            UserInfo.LoginAndGoin(activity, new LoginResultCallback() {
                @Override
                public void onLoginSucceed() {
                    IntentUtils.goSecurityCenter(activity);
                }
            });
        } else if (page.equalsIgnoreCase("kyc")) {      //KYC
            UserInfo.LoginAndGoin(activity, new LoginResultCallback() {
                @Override
                public void onLoginSucceed() {
                    IntentUtils.goIdentityAuth(activity);
                }
            });
        } else if (page.equalsIgnoreCase("HELP")) {     //帮助中心
            CustomerServiceUtils.goGuide(activity);
        } else if (page.equalsIgnoreCase("TICKETS")) {      //工单
            CustomerServiceUtils.goSubmitOrder(activity);
        } else if (page.equalsIgnoreCase("ONLINESERVICE")) {    //在线客服
            IntentUtils.goZendeskChat(activity);
        } else if (page.equalsIgnoreCase("INVITE")) {       //我的邀请
            UserInfo.LoginAndGoin(activity, new LoginResultCallback() {
                @Override
                public void onLoginSucceed() {
                    IntentUtils.goMyInvitation(activity);
                }
            });
        } else if (page.equalsIgnoreCase("ANNOUNCEMENT")) {     //公告
            IntentUtils.goAnnouncements(activity);
        } else if (page.equalsIgnoreCase("GUILD")) {    //工会
            WebActivity.runActivity(activity, activity.getString(R.string.string_guild), Urls.H5_URL_GUILD);
        } else if (page.equalsIgnoreCase("DEPOSIT")) {      //充值
            UserInfo.LoginAndGoin(activity, new LoginResultCallback() {
                @Override
                public void onLoginSucceed() {
                    String tokenId = paramsMap.get("TOKEN");
                    String chainType = paramsMap.get("CHAIN_TYPE");
                    IntentUtils.goMyAssetTokenDeposit(tokenId, chainType,activity);
                }
            });
        } else if (page.equalsIgnoreCase("WITHDRAW")) {     //提现
            UserInfo.LoginAndGoin(activity, new LoginResultCallback() {
                @Override
                public void onLoginSucceed() {
                    String tokenId = paramsMap.get("TOKEN");
                    String chainType = paramsMap.get("CHAIN_TYPE");
                    IntentUtils.goMyAssetTokenWithdraw(tokenId,chainType, activity);
                }
            });
        } else if (page.equalsIgnoreCase("TRANSFER")) {     //资金划转
            UserInfo.LoginAndGoin(activity, new LoginResultCallback() {
                @Override
                public void onLoginSucceed() {

                    String tokenId = paramsMap.get("TOKEN");
                    String fromTypeStr = paramsMap.get("FROM_TYPE");
                    String targetTypeStr = paramsMap.get("TARGET_TYPE");
                    int fromType =-1;
                    int targetType =-1;
                    if(!TextUtils.isEmpty(fromTypeStr)) {
                        if (fromTypeStr.equalsIgnoreCase("COIN")) {
                            fromType = ACCOUNT_TYPE.ASSET_WALLET.getType();
                        } else if (fromTypeStr.equalsIgnoreCase("FUTURES")) {
                            fromType = ACCOUNT_TYPE.ASSET_FUTURES.getType();
                        } else if (fromTypeStr.equalsIgnoreCase("OPTION")) {
                            fromType = ACCOUNT_TYPE.ASSET_OPTION.getType();
                        } else if (fromTypeStr.equalsIgnoreCase("MARGIN")) {
                            fromType = ACCOUNT_TYPE.ASSET_MARGIN.getType();
                        }
                    }
                    if (!TextUtils.isEmpty(targetTypeStr)) {
                        if (targetTypeStr.equalsIgnoreCase("COIN")) {
                            targetType = ACCOUNT_TYPE.ASSET_WALLET.getType();
                        } else if (targetTypeStr.equalsIgnoreCase("FUTURES")) {
                            targetType = ACCOUNT_TYPE.ASSET_FUTURES.getType();
                        } else if (targetTypeStr.equalsIgnoreCase("OPTION")) {
                            targetType = ACCOUNT_TYPE.ASSET_OPTION.getType();
                        } else if (targetTypeStr.equalsIgnoreCase("MARGIN")) {
                            targetType = ACCOUNT_TYPE.ASSET_MARGIN.getType();
                        }
                    }
                    IntentUtils.goAssetTransfer(activity, tokenId,fromType,targetType);
                }
            });
        } else if (page.equalsIgnoreCase("symboldetail")) {     //K线
            String indexType = paramsMap.get("INDEX_TYPE");
            String symbolId = paramsMap.get("SYMBOL_ID");
            CoinPairBean coinPairBean = AppConfigManager.GetInstance().getSymbolInfoById(symbolId);
            if (!TextUtils.isEmpty(indexType)&&indexType.equalsIgnoreCase("margin")) {
                coinPairBean = AppConfigManager.GetInstance().getMarginSymbolInfoById(symbolId);
            }
            if (coinPairBean != null && !TextUtils.isEmpty(coinPairBean.getSymbolId())) {
                IntentUtils.goKline(activity, coinPairBean);
            } else {
                ToastUtils.showShort(activity.getResources().getString(R.string.string_data_exception));
            }
        } else if (page.equalsIgnoreCase("trade")) {       //交易(symbolId) coin:币币 futures:合约 option:期权 otc:法币
            String indexType = paramsMap.get("INDEX_TYPE");
            String symbolId = paramsMap.get("SYMBOL_ID");
            String tokenId = paramsMap.get("TOKEN");
            String orderDir = paramsMap.get("ORDER_DIR");
            CoinPairBean coinPairBean = AppConfigManager.GetInstance().getSymbolInfoById(symbolId);
            if (TextUtils.isEmpty(indexType)||indexType.equalsIgnoreCase("COIN")) {
                IntentUtils.goBBTrade(activity);
                if (coinPairBean != null && !TextUtils.isEmpty(coinPairBean.getSymbolId())) {
                    coinPairBean.setBuyMode(isBuyOrder(activity,orderDir));
                    coinPairBean.setNeedSwitchTradeTab(true);
                    EventBus.getDefault().postSticky(coinPairBean);
                } else {
                    //可能不配交易对
                }
            } else if (indexType.equalsIgnoreCase("futures")) { //币币交易
                IntentUtils.goPerpetualContractTrade(activity);
                if (coinPairBean != null && !TextUtils.isEmpty(coinPairBean.getSymbolId())) {
                    coinPairBean.setBuyMode(KlineUtils.isFuturesBuyOrder(activity,orderDir));
                    coinPairBean.setOpenStatus(KlineUtils.isFuturesOpenOrder(activity,orderDir));
                    coinPairBean.setNeedSwitchTradeTab(true);
                    DebugLog.e("ShortcutEntry","post coinpair");
                    EventBus.getDefault().postSticky(coinPairBean);
                } else {
                    //可能不配交易对
                }
            } else if (indexType.equalsIgnoreCase("option")) {
                IntentUtils.goOptionTrade(activity);
                if (coinPairBean != null && !TextUtils.isEmpty(coinPairBean.getSymbolId())) {
                    coinPairBean.setBuyMode(isBuyOrder(activity,orderDir));
                    coinPairBean.setNeedSwitchTradeTab(true);
                    EventBus.getDefault().postSticky(coinPairBean);
                } else {
                    //可能不配交易对
                }
            }  else if (indexType.equalsIgnoreCase("margin")) {
                IntentUtils.goMarginTrade(activity);
                // 融币根据symbolId获取
                coinPairBean = AppConfigManager.GetInstance().getMarginSymbolInfoById(symbolId);
                if (coinPairBean != null && !TextUtils.isEmpty(coinPairBean.getSymbolId())) {
                    coinPairBean.setBuyMode(isBuyOrder(activity,orderDir));
                    coinPairBean.setNeedSwitchTradeTab(true);
                    EventBus.getDefault().postSticky(coinPairBean);
                }else{
                    //可能不配交易对
                }
            } else if (indexType.equalsIgnoreCase("otc")) {
                CurrentOtcSelection otcToken =new CurrentOtcSelection();
                otcToken.token = tokenId;
                otcToken.isBuy = isBuyOrder(activity,orderDir);
                EventBus.getDefault().postSticky(otcToken);
                IntentUtils.goOTCTrade(activity);
            }

        } else if (page.equalsIgnoreCase("order")) {
            String indexType = paramsMap.get("ORDER_TYPE");
            UserInfo.LoginAndGoin(activity, new LoginResultCallback() {
                @Override
                public void onLoginSucceed() {
                    if (TextUtils.isEmpty(indexType)||indexType.equalsIgnoreCase("COIN")) {
                        IntentUtils.goAllOrders(activity);
                    }
                    else if (indexType.equalsIgnoreCase("futures")) { //币币交易
                        IntentUtils.goAllFuturesOrders(activity);

                    } else if (indexType.equalsIgnoreCase("option")) {
                        IntentUtils.goAllOptionOrders(activity);

                    }  else if (indexType.equalsIgnoreCase("margin")) {
                        IntentUtils.goMarginOrders(activity);
                    } else if (indexType.equalsIgnoreCase("otc")) {

                        String orderId = paramsMap.get("ORDER_ID");
                        String orderDir = paramsMap.get("ORDER_DIR");
                        if(!TextUtils.isEmpty(orderId)&&!TextUtils.isEmpty(orderDir)) {
                            boolean isBuy = orderDir.equalsIgnoreCase("BUY");
                            IntentUtils.goOtcBuySellPay(activity, isBuy, orderId);
                        }


                    } else if (indexType.equalsIgnoreCase("COINPLUS")) {
                        IntentUtils.goCoinPlusOrders(activity);
                    }
                }
            });


        } else if (page.equalsIgnoreCase("level")) {
            UserInfo.LoginAndGoin(activity, new LoginResultCallback() {
                @Override
                public void onLoginSucceed() {
                    // 感觉是不是直接配地址跳转
                    WebActivity.runActivity(activity, "", Urls.H5_URL_USER_LEVEL);
                }
            });
        } else if (page.equalsIgnoreCase("search_symbol")) {
            String key = paramsMap.get("KEY");
            IntentUtils.goSearch(activity,key);
        }
    }


    public static boolean isBuyOrder(Context context, String side) {
        if (!TextUtils.isEmpty(side)&&side.equalsIgnoreCase("SELL")) {
            return false;
        } else {
            return true;
        }
    }
}
