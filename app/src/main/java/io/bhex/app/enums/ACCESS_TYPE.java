/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: ACCESS_TYPE.java
 *   @Date: 19-4-26 上午11:46
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.enums;

/**
 * 描   述：访问类型
 * ================================================
 */

public enum ACCESS_TYPE {
    TYPE_OTC(0, "string_access_otc"),
    TYPE_BB(1, "string_access_bb"),
    TYPE_OPTION(2, "string_access_option"),
    TYPE_FINANCE(3, "string_access_finance"),
    TYPE_NOVICE(4, "string_access_novice"),
    TYPE_customer(5, "string_access_customer"),
    TYPE_INVITE(6, "string_access_invite"),
    TYPE_GUILD(7, "string_access_guild"),
    TYPE_H5(8, "string_access_h5"),
    TYPE_PERPETUAL_CONTRACT(9, "string_perpetual_contract");

    private String mDesc;
    private int mStatus;

    ACCESS_TYPE(int status, String desc) {
        this.mStatus = status;
        this.mDesc = desc;
    }

    /**
     * 根据状态查询状态描述
     * @param status
     * @return
     */
    public static String getDescByStatus(int status){
        for (ACCESS_TYPE verify_status : values()) {
            if (verify_status.mStatus == status) {
                return verify_status.mDesc;
            }

        }
        //默认返回未认证状态
        return TYPE_H5.getmDesc();
    }


    public String getmDesc() {
        return mDesc;
    }

    public void setmDesc(String mDesc) {
        this.mDesc = mDesc;
    }

    public int getmStatus() {
        return mStatus;
    }

    public void setmStatus(int mStatus) {
        this.mStatus = mStatus;
    }
}
