/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PRICE_TYPE.java
 *   @Date: 19-7-23 下午3:15
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.enums;

public enum PRICE_TYPE {
    /**
     * 价格类型，参数：
     *
     * INPUT=用户输入价
     *
     * OPPONENT=对手价
     *
     * QUEUE=排队价
     *
     * OVER=超价
     *
     * MARKET_PRICE=市价
     */

    //限价-输入价格
    INPUT(0,"INPUT"),
    //市价
    MARKET_PRICE(1,"MARKET_PRICE"),
    //对手价
    OPPONENT(2,"OPPONENT"),
    QUEUE(3,"QUEUE"),
    OVER(4,"OVER");


    private int typeId;
    private String priceType;

    PRICE_TYPE(int id, String mPriceType) {
        typeId = id;
        priceType = mPriceType;
    }

    public int getTypeId() {
        return typeId;
    }

    public void setTypeId(int typeId) {
        this.typeId = typeId;
    }

    public String getPriceType() {
        return priceType;
    }

    public void setPriceType(String priceType) {
        this.priceType = priceType;
    }
}
