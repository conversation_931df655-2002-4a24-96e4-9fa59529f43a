package io.bhex.app.margin.presenter;

import android.util.Log;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.trade.margin.MarginApi;
import io.bhex.sdk.trade.margin.bean.MarginAccountAssetResponse;
import io.bhex.sdk.trade.margin.bean.MarginTokenConfigResponse;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordRequest;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordResponse;

public class MarginRepayPresenter  extends BasePresenter<MarginRepayPresenter.MarginRepayUI> {

    public void queryLoanRecord(String loandId) {

        QueryLoanRecordRequest requestData = new QueryLoanRecordRequest();
        requestData.token_id = "";
        requestData.loan_id = loandId;
        requestData.status = 1;
        requestData.limit = AppData.Config.PAGE_LIMIT;

        MarginApi.RequestLoanHistory(requestData, new SimpleResponseListener<QueryLoanRecordResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(QueryLoanRecordResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<QueryLoanRecordResponse.DataBean> data = response.getArray();
                    if (data!=null &&data.size()>0) {
                        getUI().showCurrentLoanRecord(data.get(0));
                    }

                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
    public void queryMarginRepay(int repayType,String lendOrderId, String repayAmountStr, String accountId) {
        MarginApi.repayLoanOrder(repayType,lendOrderId, repayAmountStr,accountId, new SimpleResponseListener<ResultResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("", "");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    ToastUtils.showShort(getActivity(), getResources().getString(R.string.margin_repay_succeed));
                    getActivity().finish();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(getActivity(), getResources().getString(R.string.server_error));
            }
        });
    }

    public void queryMarginRepayAll(String loanOrderId, String accountId) {
        MarginApi.RequestRepayAll(loanOrderId,accountId, new SimpleResponseListener<ResultResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("", "");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    ToastUtils.showShort(getActivity(), getResources().getString(R.string.margin_repay_succeed));
                    getActivity().finish();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(getActivity(), getResources().getString(R.string.server_error));
            }
        });
    }

    public interface MarginRepayUI extends AppUI {

        void showMarginAvailQty(String free);

        void updateMarginTokenConfig(MarginTokenConfigResponse.MarginToken dataBean);

        void showCurrentLoanRecord(QueryLoanRecordResponse.DataBean dataBean);
    }

    /**
     * 获取融币配置
     *
     */
    public void getMarginTokenConfig(String tokenId) {
        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        }
        MarginApi.getMarginTokenConfig(tokenId,new SimpleResponseListener<MarginTokenConfigResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(MarginTokenConfigResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<MarginTokenConfigResponse.MarginToken> data = response.getArray();
                    if (data != null) {
                        if (data.size() > 0) {
                            getUI().updateMarginTokenConfig(data.get(0));
                        }
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
    /**
     * 查询融币账户资产
     */
    public void queryMarginAccountAsset(String tokenId) {
        MarginApi.RequestTokenIdAsset(tokenId,new SimpleResponseListener<MarginAccountAssetResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(MarginAccountAssetResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<MarginAccountAssetResponse.DataBean> data = response.getArray();
                    if (data != null) {
                        if (data.size() > 0) {
                            MarginAccountAssetResponse.DataBean dataBean = data.get(0);
                            if (dataBean != null) {
                                getUI().showMarginAvailQty(dataBean.getFree());
                            }
                        }
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                Log.d("", "查询融币资产错误");
            }
        });
    }
}

