package io.bhex.app.margin.ui;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioButton;
import android.widget.RadioGroup;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.margin.adapter.MarginRepayRecordAdapter;
import io.bhex.app.margin.bean.MarginTokenFilterBean;
import io.bhex.app.margin.presenter.MarginRepayHistoryPresenter;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.trade.margin.bean.QueryRepayRecordResponse;

public class MarginRepayHistoryActivity  extends BaseActivity<MarginRepayHistoryPresenter,MarginRepayHistoryPresenter.MarginRepayHistoryUI> implements MarginRepayHistoryPresenter.MarginRepayHistoryUI, View.OnClickListener, BaseQuickAdapter.RequestLoadMoreListener, OnRefreshListener {
    private RecyclerView recyclerView;
    private MarginRepayRecordAdapter adapter;
    private TokenFilterAdapter filterTokenAdapter;
    private SmartRefreshLayout swipeRefresh;
    private View emptyView;
    private TopBar topBar;
    private String filterToken;
    private String filterType;
    private RecyclerView mCurrencyRv;
    private List<MarginTokenFilterBean> mTokenList;
    private int mLastCheckedPosition;
    private boolean isShowFilter = false;
    private View filterLayout;
    private RadioGroup RepayTypeRadioGroup;
    private List<QueryRepayRecordResponse.DataBean> mCurrentOrders;
    private ArrayList<String> selectTokenList =new ArrayList<>();

    @Override
    protected int getContentView() {
        return R.layout.activity_margin_repay_history;
    }

    @Override
    protected MarginRepayHistoryPresenter createPresenter() {
        return new MarginRepayHistoryPresenter();
    }

    @Override
    protected MarginRepayHistoryPresenter.MarginRepayHistoryUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        recyclerView = viewFinder.find(R.id.recyclerView);
        filterLayout = viewFinder.find(R.id.filter_layout);
        topBar = viewFinder.find(R.id.topBar);
        topBar.getDivider().setVisibility(View.GONE);
        topBar.setLeftOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        mCurrencyRv = viewFinder.find(R.id.margin_currency_recyclerview);
        mCurrencyRv.setLayoutManager(new GridLayoutManager(this, 3));

        RepayTypeRadioGroup = viewFinder.find(R.id.repay_type_group);
        mTokenList = new ArrayList<>();
        MarginTokenFilterBean currency = new MarginTokenFilterBean();
        currency.setMargin_crncy(getResources().getString(R.string.string_all));
        currency.setChecked(true);
        mTokenList.add(currency);
        filterTokenAdapter = new TokenFilterAdapter(mTokenList);
        mCurrencyRv.setAdapter(filterTokenAdapter);

        LayoutInflater layoutInflater = LayoutInflater.from(MarginRepayHistoryActivity.this);
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
        emptyView.setLayoutParams(layoutParams);
    }



    @Override
    protected void addEvent() {
        super.addEvent();

        viewFinder.find(R.id.filter_layout).setOnClickListener(this);
        viewFinder.find(R.id.btn_reset).setOnClickListener(this);
        viewFinder.find(R.id.btn_complete).setOnClickListener(this);

        RepayTypeRadioGroup.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {

                switch (checkedId) {
                    case R.id.price_mode_all:
                        filterType = "";
                        break;
                    case R.id.customer_repay:
                        filterType = "1";
                        break;
                    case R.id.price_mode_market:
                        filterType = "2";
                        break;
                }

            }
        });
        swipeRefresh.setOnRefreshListener(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_reset:
                ((RadioButton) viewFinder.find(R.id.price_mode_all)).setChecked(true);
                resetFilterConditions();
                break;
            case R.id.btn_complete:
                isShowFilter = false;
                filterLayout.setVisibility(View.GONE);
                ((RadioButton) viewFinder.find(R.id.price_mode_all)).setChecked(true);
                break;
        }
    }
    private void resetFilterConditions() {
        filterToken = "";
        filterType = "";
    }

    @Override
    public void onLoadMoreRequested() {
        getPresenter().loadMore();
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        getPresenter().getHistoryRecords(false);
        refreshLayout.finishRefresh(1000);
    }

    @Override
    public void loadMoreComplete() {
        if (adapter != null) {
            adapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {
        if (adapter != null) {
            adapter.loadMoreFail();
        }
    }

    @Override
    public void loadEnd() {
        if (adapter != null) {
            adapter.loadMoreEnd();
        }
    }

    @Override
    public String getFilterTokenId() {
        return filterToken;
    }


    @Override
    public void showRecords(List<QueryRepayRecordResponse.DataBean> currentOrders) {
        mCurrentOrders = currentOrders;
        if (adapter == null) {
            adapter = new MarginRepayRecordAdapter(MarginRepayHistoryActivity.this, currentOrders);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this,recyclerView);
            adapter.setEmptyView(emptyView);
            adapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                    QueryRepayRecordResponse.DataBean dataBean = mCurrentOrders.get(position);
                    IntentUtils.goMarginRepayRecordDetail(MarginRepayHistoryActivity.this,dataBean);
                }
            });
            recyclerView.setLayoutManager(new LinearLayoutManager(MarginRepayHistoryActivity.this));

            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(currentOrders);
        }
    }
    private class TokenFilterAdapter extends BaseQuickAdapter<MarginTokenFilterBean, BaseViewHolder> {

        public TokenFilterAdapter(@Nullable List<MarginTokenFilterBean> data) {
            super(R.layout.item_filter_margin_token_layout , data);
        }

        @Override
        protected void convert(BaseViewHolder helper, MarginTokenFilterBean item) {
            helper.setText(R.id.currency_name, item.getMargin_crncy());
            if(item.isChecked()) {
                helper.setTextColor(R.id.currency_name, mContext.getResources().getColor(R.color.blue));
                helper.setBackgroundRes(R.id.currency_name, R.mipmap.btn_flter_checked);
            } else {
                helper.setTextColor(R.id.currency_name, mContext.getResources().getColor(R.color.dark50));
                helper.setBackgroundRes(R.id.currency_name, R.mipmap.btn_filter);
            }
        }
    }
}

