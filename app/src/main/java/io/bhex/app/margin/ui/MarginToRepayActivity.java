package io.bhex.app.margin.ui;

import android.content.Intent;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.margin.adapter.MarginCurrentLoanRecordAdapter;
import io.bhex.app.margin.presenter.MarginToRepayPresenter;
import io.bhex.app.utils.IntentUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.app.view.alertview.AlertView;
import io.bhex.app.view.alertview.OnDismissListener;
import io.bhex.app.view.alertview.OnItemClickListener;
import io.bhex.sdk.trade.margin.bean.MarginTokenConfigResponse;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordResponse;

public class





MarginToRepayActivity extends BaseActivity<MarginToRepayPresenter,MarginToRepayPresenter.MarginToRepayUI> implements MarginToRepayPresenter.MarginToRepayUI, View.OnClickListener, OnRefreshListener {

    private RecyclerView recyclerView;
    private MarginCurrentLoanRecordAdapter adapter;
    private List<QueryLoanRecordResponse.DataBean> mCurrentRecords;
    private SmartRefreshLayout swipeRefresh;
    private View emptyView;
    private String currentToken ="";
    private ArrayList<String> selectTokenList;

    @Override
    protected int getContentView() {
        return R.layout.activity_margin_to_repay;
    }

    @Override
    protected MarginToRepayPresenter createPresenter() {
        return new MarginToRepayPresenter();
    }

    @Override
    protected MarginToRepayPresenter.MarginToRepayUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        selectTokenList = new ArrayList<>();
        Intent intent = getIntent();
        if (intent != null) {
            currentToken = intent.getStringExtra(AppData.INTENT.MARGIN_LOAN_TOKEN);
        }
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        recyclerView = viewFinder.find(R.id.recyclerView);

        LayoutInflater layoutInflater = LayoutInflater.from(MarginToRepayActivity.this);
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
        emptyView.setLayoutParams(layoutParams);

        adapter = new MarginCurrentLoanRecordAdapter(MarginToRepayActivity.this, mCurrentRecords);
        adapter.isFirstOnly(false);
        adapter.setEmptyView(emptyView);
        adapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                QueryLoanRecordResponse.DataBean item  = mCurrentRecords.get(position);
                if (item.getStatus()!=1)
                    return;
                IntentUtils.goMarginRepay(MarginToRepayActivity.this,item);

            }
        });
        recyclerView.setLayoutManager(new LinearLayoutManager(MarginToRepayActivity.this));

        recyclerView.setAdapter(adapter);
    }



    @Override
    protected void addEvent() {
        super.addEvent();
        swipeRefresh.setOnRefreshListener(this);
        viewFinder.find(R.id.title_bar_left_img).setOnClickListener(this);
        viewFinder.find(R.id.iv_filter).setOnClickListener(this);
        viewFinder.find(R.id.iv_repay_history).setOnClickListener(this);
    }

    @Override
    protected void onResume() {
        super.onResume();

        getPresenter().getMarginTokenConfig();
        getPresenter().queryToRepayRecord();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.title_bar_left_img:
                this.finish();
                break;
            case R.id.iv_filter:
                List<String> allTokenList =new ArrayList<>();
                allTokenList.add(getString(R.string.string_all));
                allTokenList.addAll(selectTokenList);

                List<String> selectArr = new ArrayList<>();
                selectArr.add(TextUtils.isEmpty(currentToken)?getString(R.string.string_all):currentToken);

                AlertView selectPhotoAlert = new AlertView(null, null, getString(R.string.string_cancel), selectArr, allTokenList, MarginToRepayActivity.this, AlertView.Style.ActionSheet, new OnItemClickListener() {
                    @Override
                    public void onItemClick(Object o, int position) {
                        if (position == -1) {
                            return;
                        }
                        if (position==0) {
                            currentToken = "";
                            getPresenter().queryToRepayRecord();
                        } else if (position < allTokenList.size()) {
                            currentToken = allTokenList.get(position);
                            getPresenter().queryToRepayRecord();
                        }
                    }
                });
                selectPhotoAlert.setOnDismissListener(new OnDismissListener() {
                    @Override
                    public void onDismiss(Object o) {

                    }
                });
                selectPhotoAlert.show();
                break;
            case R.id.iv_repay_history:
                IntentUtils.goMarginRepayHistory(MarginToRepayActivity.this);
                break;
        }
    }
    @Override
    public void updateMarginTokenConfig(List<MarginTokenConfigResponse.MarginToken> datas) {
        if (datas != null) {
            selectTokenList.clear();
            for (MarginTokenConfigResponse.MarginToken token : datas) {
                selectTokenList.add(token.getTokenId().toUpperCase());
            }
        }
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        getPresenter().queryToRepayRecord();
        getPresenter().getMarginTokenConfig();
        refreshLayout.finishRefresh(1000);
    }

    @Override
    public void showCurrentLoanRecords(List<QueryLoanRecordResponse.DataBean> currentRecords) {
        mCurrentRecords = currentRecords;
        adapter.setNewData(mCurrentRecords);
    }

    @Override
    public String getFilterTokenId() {
        return currentToken;
    }
}
