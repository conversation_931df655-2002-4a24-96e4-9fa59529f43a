package io.bhex.app.margin.ui;

import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.margin.adapter.MarginSingleRepayRecordAdapter;
import io.bhex.app.margin.presenter.LoanRecordDetailPresenter;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordResponse;
import io.bhex.sdk.trade.margin.bean.QueryRepayRecordResponse;

public class LoanRecordDetailActivity extends BaseActivity<LoanRecordDetailPresenter,LoanRecordDetailPresenter.LoanRecordDetailUI> implements LoanRecordDetailPresenter.LoanRecordDetailUI, View.OnClickListener, OnRefreshListener {

    private RecyclerView recyclerView;
    private MarginSingleRepayRecordAdapter adapter;
    private SmartRefreshLayout swipeRefresh;
    private View emptyView;
    private String loanId ="";
    private QueryLoanRecordResponse.DataBean mLoanRecord;
    private String currentToken;

    @Override
    protected int getContentView() {
        return R.layout.activity_loan_record_detail;
    }

    @Override
    protected LoanRecordDetailPresenter createPresenter() {
        return new LoanRecordDetailPresenter();
    }

    @Override
    protected LoanRecordDetailPresenter.LoanRecordDetailUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();

        Intent intent = getIntent();
        if (intent != null) {
            mLoanRecord = (QueryLoanRecordResponse.DataBean)intent.getSerializableExtra("loanRecord");
            currentToken = mLoanRecord.getTokenId();
            loanId = mLoanRecord.getLoanOrderId();
            String loanedValue = NumberUtils.roundFormatDown(mLoanRecord.getLoanAmount(), AppData.Config.DIGIT_DEFAULT_VALUE);
            String remainToRepayValue = NumberUtils.roundFormatUp(mLoanRecord.getUnpaidAmount(), AppData.Config.DIGIT_DEFAULT_VALUE);
            double totalRemainValue = NumberUtils.add(mLoanRecord.getInterestUnpaid(), mLoanRecord.getInterestPaid());
            String totalInterestValue = NumberUtils.roundFormatUp(totalRemainValue, AppData.Config.DIGIT_DEFAULT_VALUE);

            viewFinder.textView(R.id.tv_token).setText(getResources().getString(R.string.string_margin_loan_token,mLoanRecord.getTokenId()));
            double value = Double.parseDouble(mLoanRecord.getInterestRate1()) * 24 * 3600;
            String showValue = NumberUtils.mul(NumberUtils.roundFormatUp(value, 6), "100") + "%";
            viewFinder.textView(R.id.tv_daily_interest_value).setText(showValue);

            viewFinder.textView(R.id.tv_loan_amount_value).setText(loanedValue + currentToken);
            viewFinder.textView(R.id.tv_remain_to_repay_value).setText(remainToRepayValue + currentToken);

            viewFinder.textView(R.id.tv_total_interest_value).setText(totalInterestValue + currentToken);
            viewFinder.textView(R.id.tv_loan_date_value).setText(DateUtils.getSimpleTimeFormat(mLoanRecord.getCreatedAt(),AppData.Config.TIME_FORMAT));
        }
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        recyclerView = viewFinder.find(R.id.recyclerView);


        LayoutInflater layoutInflater = LayoutInflater.from(LoanRecordDetailActivity.this);
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
        emptyView.setLayoutParams(layoutParams);
    }



    @Override
    protected void addEvent() {
        super.addEvent();
        swipeRefresh.setOnRefreshListener(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
        getPresenter().queryRepayRecord(loanId);
    }

    @Override
    public void onClick(View v) {

    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        getPresenter().queryRepayRecord(loanId);
        refreshLayout.finishRefresh(1000);

    }

    @Override
    public void showRepayRecords(List<QueryRepayRecordResponse.DataBean> currentOrders) {
        if (adapter == null) {
            adapter = new MarginSingleRepayRecordAdapter(LoanRecordDetailActivity.this, currentOrders);
            adapter.isFirstOnly(false);
            adapter.setEmptyView(emptyView);
            adapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                    QueryRepayRecordResponse.DataBean dataBean = currentOrders.get(position);
                    IntentUtils.goMarginRepayRecordDetail(LoanRecordDetailActivity.this,dataBean);
                }
            });
            recyclerView.setLayoutManager(new LinearLayoutManager(LoanRecordDetailActivity.this));

            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(currentOrders);
        }
    }
}
