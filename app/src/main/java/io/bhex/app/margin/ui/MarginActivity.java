package io.bhex.app.margin.ui;

import android.content.Intent;
import android.text.InputFilter;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import androidx.annotation.NonNull;
import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.margin.presenter.MarginPresenter;
import io.bhex.app.utils.DialogUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.view.PointLengthFilter;
import io.bhex.app.view.ShadowDrawable;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.QuoteTokensBean;
import io.bhex.sdk.trade.margin.MarginApi;
import io.bhex.sdk.trade.margin.bean.MarginFundingCrossResponse;
import io.bhex.sdk.trade.margin.bean.MarginTokenConfigResponse;
import io.bhex.sdk.trade.margin.bean.MarginUserInterestResponse;


/**
 * 融币页面
 *
 * <AUTHOR>
 */
public class MarginActivity extends BaseActivity<MarginPresenter, MarginPresenter.MarginUI> implements MarginPresenter.MarginUI, View.OnClickListener {


    private TopBar topBar;
    private ArrayList<String> selectTokenList;
    private String currentToken;
    private TextView marginAmountTokenUnit;
    private TextView selectTokenname;
    private String availableAsset;
    MarginTokenConfigResponse.MarginToken currentMarginConfig;
    private LinkedHashMap<String, MarginTokenConfigResponse.MarginToken> tokenMap = new LinkedHashMap<>();
    private EditText token_margin_amount_et;
    private MarginFundingCrossResponse.DataBean marginFundingCross;
    private SmartRefreshLayout swipeRefresh;
    private PointLengthFilter pointFilter;

    @Override
    protected int getContentView() {
        return R.layout.activity_margin_laon_layout;
    }

    @Override
    protected MarginPresenter createPresenter() {
        return new MarginPresenter();
    }

    @Override
    protected MarginPresenter.MarginUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        pointFilter = new PointLengthFilter();
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        selectTokenList = new ArrayList<>();
        ShadowDrawable.setShadow(viewFinder.find(R.id.margin_amount_rela));
        topBar = viewFinder.find(R.id.topBar);
        topBar.getDivider().setVisibility(View.GONE);
        topBar.setRightImg(R.mipmap.icon_record);
        Intent intent = getIntent();
        if (intent != null) {
            currentToken = intent.getStringExtra(AppData.INTENT.MARGIN_LOAN_TOKEN);
        }
        topBar.setRightOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                IntentUtils.goMarginLoanHistory(MarginActivity.this, "");
            }
        });
        selectTokenname = viewFinder.find(R.id.item_asset_coin_name);
        marginAmountTokenUnit = viewFinder.find(R.id.token_margin_amount_unit);
        token_margin_amount_et = viewFinder.find(R.id.token_margin_amount_et);
        token_margin_amount_et.setFilters(new InputFilter[]{pointFilter});

    }


    @Override
    protected void addEvent() {
        super.addEvent();

        swipeRefresh.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {

                getPresenter().getMarginTokenConfig();
                refreshLayout.finishRefresh(1000);
            }
        });
        //选择TOKEN
        viewFinder.find(R.id.select_token_rela).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showSelectToken();
            }
        });
        //确认
        viewFinder.find(R.id.btn_sure).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                submitLoan();
            }
        });
        viewFinder.find(R.id.asset_all).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (TextUtils.isEmpty(currentToken) || currentMarginConfig == null) {
                    return;
                }
                if (!TextUtils.isEmpty(availableAsset)) {
                    String showValue = NumberUtils.roundFormatDown(availableAsset, currentMarginConfig!=null?currentMarginConfig.getQuantityPrecision():AppData.Config.DIGIT_DEFAULT_VALUE);
                    token_margin_amount_et.setText(showValue);
                } else {
                    token_margin_amount_et.setText("");
                }
            }
        });
    }

    private void submitLoan() {
        if (TextUtils.isEmpty(currentToken) || currentToken.equalsIgnoreCase(getString(R.string.string_placeholder))) {
            ToastUtils.showShort(getString(R.string.string_select_loan_token));
            return;
        }
        String amount = token_margin_amount_et.getText().toString().trim();
        if (TextUtils.isEmpty(amount)) {
            ToastUtils.showShort(getString(R.string.string_asset_loan_input_amount));
            return;
        }
        if (NumberUtils.sub(amount, "0") <= 0) {
            ToastUtils.showShort(getString(R.string.string_asset_loan_input_amount));
            return;
        }
		if (!TextUtils.isEmpty(availableAsset)) {
			if (NumberUtils.sub(amount, availableAsset) > 0) {
				ToastUtils.showLong(this, getString(R.string.string_margin_qty_not_be_greater_tips, availableAsset , currentToken));
				return;
			}
		} else {
			ToastUtils.showLong(this, getString(R.string.string_margin_qty_not_be_greater_tips, "0" , currentToken));
			return;
		}

        if (!TextUtils.isEmpty(currentMarginConfig.getMaxQuantity())) {
            if (NumberUtils.sub(amount, currentMarginConfig.getMaxQuantity()) > 0) {
                ToastUtils.showLong(this, getString(R.string.string_max_loan_qty_pertime, currentMarginConfig.getMaxQuantity() , currentToken));
                return;
            }
        } else {
            ToastUtils.showLong(this, getString(R.string.string_max_loan_qty_pertime, "0" , currentToken));
            return;
        }
		if (io.bhex.baselib.utils.NumberUtils.sub(amount, currentMarginConfig.getMinQuantity()) < 0) {
			ToastUtils.showLong(this, getString(R.string.string_margin_qty_not_be_less_tips, currentMarginConfig.getMinQuantity(), currentToken));
			return;
		}

		MarginApi.createLoanOrder(currentToken, amount, new SimpleResponseListener<ResultResponse>() {
			@Override
			public void onBefore() {
				super.onBefore();
				getUI().showProgressDialog("", "");
			}

			@Override
			public void onFinish() {
				super.onFinish();
				getUI().dismissProgressDialog();
			}

			@Override
			public void onSuccess(ResultResponse response) {
				super.onSuccess(response);
				if (CodeUtils.isSuccess(response, true)) {
					DialogUtils.showLoanSucceedDialog(MarginActivity.this, true, new DialogUtils.OnButtonEventListener() {
						@Override
						public void onConfirm() {
                            goTrade();
							finish();
						}

						@Override
						public void onCancel() {
                            finish();
						}
					});
				}
			}

			@Override
			public void onError(Throwable error) {
				super.onError(error);
				ToastUtils.showShort(MarginActivity.this, getResources().getString(R.string.server_error));
			}
		});

    }

    private void goTrade() {
        if (!TextUtils.isEmpty(currentToken)) {
            CoinPairBean coinPairBean;
            //默认xxx/USDT币对
            coinPairBean = AppConfigManager.GetInstance().getMarginSymbolInfoById(currentToken+"USDT");
            if (coinPairBean != null && !TextUtils.isEmpty(coinPairBean.getSymbolId())) {
            }else{
                //如果没有xxx/USDT币对,则查询相关tokenId的xxx/ 币对
                coinPairBean = AppConfigManager.GetInstance().getMarginSymbolByToken(currentToken);
            }
            if(coinPairBean != null && !TextUtils.isEmpty(coinPairBean.getSymbolId())) {
                coinPairBean.setNeedSwitchTradeTab(true);
                EventBus.getDefault().postSticky(coinPairBean);
                IntentUtils.goMain(MarginActivity.this);
                finish();
            }else{
                //如果没有 则使用默认币对
                CoinPairBean defaultTradeCoinPair = AppConfigManager.GetInstance().getDefaultMarginCoinPair();
                if(defaultTradeCoinPair != null && !TextUtils.isEmpty(defaultTradeCoinPair.getSymbolId())) {
                    defaultTradeCoinPair.setNeedSwitchTradeTab(true);
                    EventBus.getDefault().postSticky(defaultTradeCoinPair);
                    IntentUtils.goMain(MarginActivity.this);
                    finish();
                }else{
                    DebugLog.e("not default trade symbol");
                }

            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    public void onClick(View v) {

    }

    /**
     * token选择
     */
    private void showSelectToken() {
        List<MarginTokenConfigResponse.MarginToken> tokens= new ArrayList<>(tokenMap.values());
        if (tokens!=null) {
            for (MarginTokenConfigResponse.MarginToken item: tokens) {
                if (!TextUtils.isEmpty(currentToken)&&item.getTokenId().equalsIgnoreCase(currentToken)) {
                    item.setSelected(true);
                } else {
                    item.setSelected(false);
                }
            }
        }
        LoanCoinListDialog dialog = new LoanCoinListDialog(this, tokens, new LoanCoinListDialog.OnDialogObserver() {
            @Override
            public void onSelectToken(MarginTokenConfigResponse.MarginToken dataBean) {
                setSelectToken(dataBean.getTokenId().toUpperCase());
            }

            @Override
            public void onDismiss() {

            }
        });
        dialog.ShowDialog();
    }

    /**
     * 设置选择的TOKEN
     *
     * @param selectToken
     */
    private void setSelectToken(String selectToken) {
        currentToken = selectToken;
        if (TextUtils.isEmpty(currentToken)) {
            currentMarginConfig = null;
            selectTokenname.setText(getString(R.string.string_placeholder));
            marginAmountTokenUnit.setText(getString(R.string.string_placeholder));
            viewFinder.textView(R.id.loan_amount_value).setText(getString(R.string.string_placeholder));
            viewFinder.textView(R.id.daily_interest_value).setText(getString(R.string.string_placeholder));
            viewFinder.textView(R.id.margin_loan_available_value).setText(getString(R.string.string_placeholder));
            viewFinder.textView(R.id.asset_available).setText(getString(R.string.string_margin_available) + " " + getString(R.string.string_placeholder));
            viewFinder.textView(R.id.leverage_tv).setText(getString(R.string.string_placeholder));

        } else {
            QuoteTokensBean.TokenItem tokenItem = AppConfigManager.GetInstance().getToken(currentToken);
            String tokenName = tokenItem==null?"":tokenItem.getTokenName();
            selectTokenname.setText(!TextUtils.isEmpty(tokenName) ? tokenName : currentToken);
            marginAmountTokenUnit.setText(!TextUtils.isEmpty(tokenName) ? tokenName : currentToken);
            currentMarginConfig = tokenMap.get(currentToken);
            if (currentMarginConfig != null) {
                pointFilter.setDecimalLength(currentMarginConfig.getQuantityPrecision());
                viewFinder.textView(R.id.leverage_tv).setText(getString(R.string.string_margin_lever_format, currentMarginConfig.getLeverage() + ""));
                token_margin_amount_et.setText("");
                token_margin_amount_et.setHint(getString(R.string.string_min_loan_hint, currentMarginConfig.getMinQuantity()));
            }
            getPresenter().getMarginFundingCross(selectToken);
            getPresenter().getMarginInterestConfig(selectToken);
        }
    }

    @Override
    public void updateMarginTokenConfig(List<MarginTokenConfigResponse.MarginToken> datas) {
        if (datas != null) {
            tokenMap.clear();
            selectTokenList.clear();
            for (MarginTokenConfigResponse.MarginToken token : datas) {
                if (token.isCanBorrow()) {
                    tokenMap.put(token.getTokenId().toUpperCase(), token);
                    selectTokenList.add(token.getTokenId().toUpperCase());
                }
            }
            if (selectTokenList.size() > 0) {
                if (!TextUtils.isEmpty(currentToken) && selectTokenList.contains(currentToken)) {
                    setSelectToken(currentToken);
                } else {
                    String token = selectTokenList.get(0);
                    setSelectToken(token);
                }
            } else {
                //没有可默认选择的token
                setSelectToken("");
            }
        } else {
            //没有可默认选择的token
            setSelectToken("");
        }
    }

    @Override
    public void updateInterestConfig(MarginUserInterestResponse dataBean) {
        if (dataBean != null) {
            double value = Double.parseDouble(dataBean.getInterest()) * 24 * 3600 / dataBean.getInterestPeriod();
            String showValue = NumberUtils.mul(NumberUtils.roundFormatUp(value, 6), "100") + "%";
            viewFinder.textView(R.id.daily_interest_value).setText(showValue);
        }
    }

    @Override
    public void updateMarginAvailable(MarginFundingCrossResponse.DataBean dataBean) {
        marginFundingCross = dataBean;
        if (dataBean != null) {
            if (TextUtils.isEmpty(currentToken) || currentMarginConfig == null) {
                return;
            }
            if (dataBean.getTokenId().equalsIgnoreCase(currentToken)) {
				String showValue = "";
                showValue = NumberUtils.roundFormatDown(dataBean.getLoanable(), currentMarginConfig.getQuantityPrecision());
                availableAsset = showValue;
                String loanedValue = NumberUtils.roundFormatDown(dataBean.getBorrowed(), currentMarginConfig.getQuantityPrecision());
                viewFinder.textView(R.id.asset_available).setText(getString(R.string.string_margin_available) + " " + showValue + currentToken);
                viewFinder.textView(R.id.margin_loan_available_value).setText(showValue + currentToken);
                viewFinder.textView(R.id.loan_amount_value).setText(loanedValue + currentToken);
            }
        }
    }
}

