package io.bhex.app.margin.adapter;

import android.content.Context;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordResponse;

public class MarginLoanRecordAdapter extends BaseQuickAdapter<QueryLoanRecordResponse.DataBean, BaseViewHolder> {

    private Context mContext;

    public MarginLoanRecordAdapter(Context context, List<QueryLoanRecordResponse.DataBean> data) {
        super(R.layout.item_margin_loan_record_layout, data);
        mContext = context;
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final QueryLoanRecordResponse.DataBean itemModel) {
        baseViewHolder.setText(R.id.tv_token,itemModel.getTokenId());
        baseViewHolder.setText(R.id.tv_amount_value,itemModel.getLoanAmount());
        // 根据状态判断是否可见
        if (itemModel.getStatus() == 1) {
            baseViewHolder.setGone(R.id.tv_repay, true);
            baseViewHolder.addOnClickListener(R.id.tv_repay);
        } else {
            baseViewHolder.setGone(R.id.tv_repay, false);
        }
        double value = Double.parseDouble(itemModel.getInterestRate1()) * 24 * 3600;
        String showValue = NumberUtils.mul(NumberUtils.roundFormatUp(value, 6), "100") + "%";
        baseViewHolder.setText(R.id.tv_daily_interest_value,showValue);
        baseViewHolder.setText(R.id.tv_date_value, DateUtils.getSimpleTimeFormat(itemModel.getCreatedAt(), "HH:mm:ss MM/dd"));
    }

}
