package io.bhex.app.margin.adapter;

import android.content.Context;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.trade.margin.bean.QueryRepayRecordResponse;

public class MarginRepayRecordAdapter extends BaseQuickAdapter<QueryRepayRecordResponse.DataBean, BaseViewHolder> {

    private Context mContext;

    public MarginRepayRecordAdapter(Context context, List<QueryRepayRecordResponse.DataBean> data) {
        super(R.layout.item_margin_repay_record_layout, data);
        mContext = context;
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final QueryRepayRecordResponse.DataBean itemModel) {
        baseViewHolder.setText(R.id.tv_token, itemModel.getTokenId());
        baseViewHolder.setText(R.id.tv_amount_value, itemModel.getAmount());
        baseViewHolder.setText(R.id.tv_repay_interest_value,itemModel.getInterest());
        baseViewHolder.setText(R.id.tv_date_value, DateUtils.getSimpleTimeFormat(itemModel.getCreatedAt(), AppData.Config.TIME_FORMAT));
    }

}
