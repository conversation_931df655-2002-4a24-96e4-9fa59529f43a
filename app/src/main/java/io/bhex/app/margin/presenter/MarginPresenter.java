package io.bhex.app.margin.presenter;

import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.trade.margin.MarginApi;
import io.bhex.sdk.trade.margin.bean.MarginFundingCrossResponse;
import io.bhex.sdk.trade.margin.bean.MarginTokenConfigResponse;
import io.bhex.sdk.trade.margin.bean.MarginUserInterestResponse;

/**
 * <AUTHOR>
 */
public class MarginPresenter extends BasePresenter<MarginPresenter.MarginUI> {


	public interface MarginUI extends AppUI {

		void updateMarginTokenConfig(List<MarginTokenConfigResponse.MarginToken> data);

		void updateInterestConfig(MarginUserInterestResponse dataBean);

		void updateMarginAvailable(MarginFundingCrossResponse.DataBean dataBean);
	}
	@Override
	public void onUIReady(BaseCoreActivity activity, MarginPresenter.MarginUI ui) {
		super.onUIReady(activity, ui);
		getMarginTokenConfig();
	}
	/**
	 * 获取融币配置
	 *
	 */
	public void getMarginTokenConfig() {
		if (!UserInfo.isLogin()) {
			//未登录状态不请求
			return;
		}
		MarginApi.getMarginTokenConfig("",new SimpleResponseListener<MarginTokenConfigResponse>() {
			@Override
			public void onBefore() {
				super.onBefore();
				getUI().showProgressDialog("","");
			}

			@Override
			public void onFinish() {
				super.onFinish();
				getUI().dismissProgressDialog();
			}

			@Override
			public void onSuccess(MarginTokenConfigResponse response) {
				super.onSuccess(response);
				if (CodeUtils.isSuccess(response, true)) {
					List<MarginTokenConfigResponse.MarginToken> data = response.getArray();
					if (data != null) {
						if (data.size() > 0) {
							getUI().updateMarginTokenConfig(data);
						}
					}
				}
			}

			@Override
			public void onError(Throwable error) {
				super.onError(error);
			}
		});
	}


	/**
	 * 获取融币利息配置
	 *
	 */
	public void getMarginInterestConfig(final String token) {
		if (!UserInfo.isLogin()) {
			//未登录状态不请求
			return;
		}
		MarginApi.getMarginUserLevelInterest(token,new SimpleResponseListener<MarginUserInterestResponse>() {
			@Override
			public void onBefore() {
				super.onBefore();
				getUI().showProgressDialog("","");
			}

			@Override
			public void onFinish() {
				super.onFinish();
				getUI().dismissProgressDialog();
			}

			@Override
			public void onSuccess(MarginUserInterestResponse response) {
				super.onSuccess(response);
				if (CodeUtils.isSuccess(response, true)) {
					if (response != null) {
						getUI().updateInterestConfig(response);
					}

				}
			}

			@Override
			public void onError(Throwable error) {
				super.onError(error);
			}
		});
	}


	/**
	 * 获取融币已借可借
	 *
	 */
	public void getMarginFundingCross(String token) {
		if (!UserInfo.isLogin()) {
			//未登录状态不请求
			return;
		}
		MarginApi.getMarginFundingCross(token,new SimpleResponseListener<MarginFundingCrossResponse>() {
			@Override
			public void onBefore() {
				super.onBefore();
				getUI().showProgressDialog("","");
			}

			@Override
			public void onFinish() {
				super.onFinish();
				getUI().dismissProgressDialog();
			}

			@Override
			public void onSuccess(MarginFundingCrossResponse response) {
				super.onSuccess(response);
				if (CodeUtils.isSuccess(response, true)) {
					List<MarginFundingCrossResponse.DataBean> data = response.getArray();
					if (data.size() > 0) {
						getUI().updateMarginAvailable(data.get(0));
					}
				}
			}

			@Override
			public void onError(Throwable error) {
				super.onError(error);
			}
		});
	}
}
