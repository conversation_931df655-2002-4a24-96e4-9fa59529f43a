package io.bhex.app.margin.ui;

import android.content.Intent;
import android.view.View;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.margin.presenter.MarginRepayDetailPresenter;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordResponse;
import io.bhex.sdk.trade.margin.bean.QueryRepayRecordResponse;

public class MarginRepayDetailActivity extends BaseActivity<MarginRepayDetailPresenter, MarginRepayDetailPresenter.MarginRepayDetailUI> implements MarginRepayDetailPresenter.MarginRepayDetailUI, View.OnClickListener {


    private TopBar topBar;
    private QueryRepayRecordResponse.DataBean mRepayRecord;
    private String currentToken;

    @Override
    protected int getContentView() {
        return R.layout.activity_margin_repay_detail;
    }

    @Override
    protected MarginRepayDetailPresenter createPresenter() {
        return new MarginRepayDetailPresenter();
    }

    @Override
    protected MarginRepayDetailPresenter.MarginRepayDetailUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        Intent intent = getIntent();
        if (intent != null) {
            mRepayRecord = (QueryRepayRecordResponse.DataBean)intent.getSerializableExtra("repayRecord");
            if (mRepayRecord==null)
                return;;
            currentToken = mRepayRecord.getTokenId();

            String returnAmountValue =mRepayRecord.getAmount();
            String interestValue = mRepayRecord.getInterest();

            viewFinder.textView(R.id.tv_token).setText(getResources().getString(R.string.string_margin_repay_token,mRepayRecord.getTokenId()));

            viewFinder.textView(R.id.tv_returned_amount_value).setText(returnAmountValue + currentToken);
            viewFinder.textView(R.id.tv_returned_interest_value).setText(interestValue + currentToken);


            viewFinder.textView(R.id.tv_type_value).setText(getString(R.string.string_customer_repay));

            viewFinder.textView(R.id.tv_repay_date_value).setText(DateUtils.getSimpleTimeFormat(mRepayRecord.getCreatedAt(),AppData.Config.TIME_FORMAT));
        }
    }



    @Override
    protected void addEvent() {
        super.addEvent();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if(mRepayRecord!=null) {
            getPresenter().queryLoanRecord(mRepayRecord.getLoanOrderId());
        }
    }


    @Override
    public void onClick(View v) {
    }

    @Override
    public void showCurrentLoanRecord(QueryLoanRecordResponse.DataBean dataBean) {

        String loanedValue = NumberUtils.roundFormatDown(dataBean.getLoanAmount(), AppData.Config.DIGIT_DEFAULT_VALUE);
        double totalInterest = NumberUtils.add(dataBean.getInterestUnpaid(), dataBean.getInterestPaid());
        String totalInterestValue = NumberUtils.roundFormatUp(totalInterest, AppData.Config.DIGIT_DEFAULT_VALUE);

        viewFinder.textView(R.id.tv_loan_amount_value).setText(loanedValue + currentToken);

        double value = Double.parseDouble(dataBean.getInterestRate1()) * 24 * 3600;
        String showValue = NumberUtils.mul(NumberUtils.roundFormatUp(value, 6), "100") + "%";
        viewFinder.textView(R.id.tv_daily_interest_value).setText(showValue);
        viewFinder.textView(R.id.tv_total_interest_value).setText(totalInterestValue + currentToken);
        viewFinder.textView(R.id.tv_loan_date_value).setText(DateUtils.getSimpleTimeFormat(dataBean.getCreatedAt(),AppData.Config.TIME_FORMAT));
    }
}
