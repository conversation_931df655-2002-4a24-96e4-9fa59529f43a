package io.bhex.app.margin.presenter;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.trade.margin.MarginApi;
import io.bhex.sdk.trade.margin.bean.QueryRepayRecordRequest;
import io.bhex.sdk.trade.margin.bean.QueryRepayRecordResponse;

public class LoanRecordDetailPresenter extends BasePresenter<LoanRecordDetailPresenter.LoanRecordDetailUI> {
    
    public void queryRepayRecord(String loanId) {
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(),getResources().getString(R.string.hint_network_not_connect));
            return;
        }

        QueryRepayRecordRequest requestData = new QueryRepayRecordRequest();
        requestData.token_id = "";
        requestData.loan_order_id = loanId;
        requestData.limit = 500;

        MarginApi.RequestRepayHistory(requestData, new SimpleResponseListener<QueryRepayRecordResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(QueryRepayRecordResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<QueryRepayRecordResponse.DataBean> data = response.getArray();
                    getUI().showRepayRecords(data);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
    public interface LoanRecordDetailUI extends AppUI {

        void showRepayRecords(List<QueryRepayRecordResponse.DataBean> currentRecords);
    }

}
