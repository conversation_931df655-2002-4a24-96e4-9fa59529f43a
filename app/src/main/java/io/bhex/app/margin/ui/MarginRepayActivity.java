package io.bhex.app.margin.ui;

import android.content.Intent;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.TextView;

import com.bhex.util.NumberUtil;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import androidx.annotation.NonNull;
import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.margin.presenter.MarginRepayPresenter;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.DialogUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.view.PointLengthFilter;
import io.bhex.app.view.ShadowDrawable;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.trade.margin.bean.MarginTokenConfigResponse;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordResponse;

public class MarginRepayActivity extends BaseActivity<MarginRepayPresenter, MarginRepayPresenter.MarginRepayUI> implements MarginRepayPresenter.MarginRepayUI, View.OnClickListener, CompoundButton.OnCheckedChangeListener {
    private TopBar topBar;
    private QueryLoanRecordResponse.DataBean mLoanRecord;
    private String currentToken;
    private String availableAsset;
    private EditText token_repay_amount_et;
    private String totalRemainValue;
    private String unPaidInterestValue;
    private MarginTokenConfigResponse.MarginToken configDataBean;
    private PointLengthFilter pointFilter;
    private TextView real_repay_amount;
    private SmartRefreshLayout swipeRefresh;
    private int showInterestPrecision = AppData.Config.DIGIT_DEFAULT_VALUE;

    @Override
    protected int getContentView() {
        return R.layout.activity_margin_repay;
    }

    @Override
    protected MarginRepayPresenter createPresenter() {
        return new MarginRepayPresenter();
    }

    @Override
    protected MarginRepayPresenter.MarginRepayUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        pointFilter = new PointLengthFilter();

        ShadowDrawable.setShadow(viewFinder.find(R.id.repay_amount_rela));
        topBar = viewFinder.find(R.id.topBar);
        topBar.getDivider().setVisibility(View.GONE);
        token_repay_amount_et = viewFinder.find(R.id.token_repay_amount_et);
        real_repay_amount = viewFinder.find(R.id.real_repay_amount);
        pointFilter.setDecimalLength(AppData.Config.DIGIT_DEFAULT_VALUE);
        token_repay_amount_et.setFilters(new InputFilter[]{pointFilter});
        Intent intent = getIntent();
        if (intent != null) {
            mLoanRecord = (QueryLoanRecordResponse.DataBean) intent.getSerializableExtra("loanRecord");
        }
        repayModel =1;
        ((RadioButton) viewFinder.find(R.id.repay_type_capital)).setChecked(true);
        updateLoanInfo();
    }

    private void updateLoanInfo() {
        if (mLoanRecord != null) {
            currentToken = mLoanRecord.getTokenId();
            String loanedValue = NumberUtils.roundFormatUp(mLoanRecord.getLoanAmount(), AppData.Config.DIGIT_DEFAULT_VALUE);
            String remainToRepayValue = NumberUtils.roundFormatUp(mLoanRecord.getUnpaidAmount(), AppData.Config.DIGIT_DEFAULT_VALUE);

            double remainSec = NumberUtils.div(1000,NumberUtils.sub(System.currentTimeMillis()+"",mLoanRecord.getInterestStart()),0);
            double toAddInterestPerUnit = NumberUtils.mul(remainSec+"",mLoanRecord.getInterestRate1());
            double toAddInterest = NumberUtils.mul(toAddInterestPerUnit+"",mLoanRecord.getUnpaidAmount());
            unPaidInterestValue =NumberUtils.roundFormatUp(NumberUtils.add2(toAddInterest+"",mLoanRecord.getInterestUnpaid()), showInterestPrecision);
            totalRemainValue = NumberUtils.add2(unPaidInterestValue, remainToRepayValue);
            double totalInterest = NumberUtils.add(unPaidInterestValue, mLoanRecord.getInterestPaid());
            String totalInterestValue = NumberUtils.roundFormatUp(totalInterest, showInterestPrecision);
            viewFinder.textView(R.id.margin_loan_amount_value).setText(loanedValue + currentToken);
            viewFinder.textView(R.id.tv_remain_to_repay_value).setText(remainToRepayValue + currentToken);
            viewFinder.textView(R.id.remain_interest_value).setText(unPaidInterestValue + currentToken);
            viewFinder.textView(R.id.tv_total_remain_to_repay_value).setText(NumberUtils.roundFormatUp(totalRemainValue, AppData.Config.DIGIT_DEFAULT_VALUE) + currentToken);
            viewFinder.textView(R.id.tv_margin_total_interest_value).setText(totalInterestValue + currentToken);
            viewFinder.textView(R.id.margin_loan_date_value).setText(DateUtils.getSimpleTimeFormat(mLoanRecord.getCreatedAt(), AppData.Config.TIME_FORMAT));
            viewFinder.textView(R.id.token_repay_amount_unit).setText(currentToken);
            viewFinder.textView(R.id.tv_token).setText(currentToken);
            double value = Double.parseDouble(mLoanRecord.getInterestRate1()) * 24 * 3600;
            String showValue = NumberUtils.mul(NumberUtils.roundFormatUp(value, 6), "100") + "%";
            viewFinder.textView(R.id.tv_daily_interest_value).setText(showValue);
            viewFinder.textView(R.id.margin_interest_start_value).setText(DateUtils.getSimpleTimeFormat(mLoanRecord.getInterestStart(), AppData.Config.TIME_FORMAT));
            updateRealRepayAmount();
        }
    }


    @Override
    protected void addEvent() {
        super.addEvent();

        swipeRefresh.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                if (mLoanRecord != null) {
                    getPresenter().queryLoanRecord(mLoanRecord.getLoanOrderId());
                }
                getPresenter().queryMarginAccountAsset(currentToken);
                getPresenter().getMarginTokenConfig(currentToken);
                refreshLayout.finishRefresh(1000);
            }
        });
        token_repay_amount_et.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                updateRealRepayAmount();
            }
        });
        viewFinder.find(R.id.btn_repay).setOnClickListener(this);
        viewFinder.find(R.id.asset_all).setOnClickListener(this);
        viewFinder.find(R.id.real_repay_amount_title).setOnClickListener(this);
        viewFinder.find(R.id.tv_remain_interest_title).setOnClickListener(this);

        ((RadioButton) viewFinder.find(R.id.repay_type_capital)).setOnCheckedChangeListener(this);
        ((RadioButton) viewFinder.find(R.id.repay_type_total)).setOnCheckedChangeListener(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mLoanRecord != null) {
            getPresenter().queryLoanRecord(mLoanRecord.getLoanOrderId());
        }
        getPresenter().queryMarginAccountAsset(currentToken);
        getPresenter().getMarginTokenConfig(currentToken);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_repay:
                if (mLoanRecord == null) {
                    ToastUtils.showLong(getString(R.string.string_please_select_margin_record));
                    return;
                }
                if (configDataBean == null) {
                    return;
                }

                String repayAmountStr = token_repay_amount_et.getText().toString();
                if (TextUtils.isEmpty(repayAmountStr)) {
                    ToastUtils.showLong(getString(R.string.string_margin_repay_amount_need));
                    return;
                }
                // 目前超过待还数量调用全部还币接口
                if (repayModel==1||NumberUtils.sub(repayAmountStr,totalRemainValue+"")<0) {
                    String realRepayAmount = "0";
                    if (repayModel == 1) {
                        realRepayAmount = NumberUtils.add2(repayAmountStr, unPaidInterestValue);
                    } else {
                        realRepayAmount = repayAmountStr;
                    }
                    if (NumberUtils.sub(availableAsset, realRepayAmount) < 0) {
                        ToastUtils.showLong(getString(R.string.string_margin_asset_not_enough, currentToken));
                        return;
                    }



                    if (repayModel == 1) {
                        String minRepayQty = configDataBean.getRepayMinQuantity();
                        if (NumberUtils.sub(mLoanRecord.getUnpaidAmount(), configDataBean.getRepayMinQuantity()) < 0) {
                            minRepayQty = mLoanRecord.getUnpaidAmount();
                        }
                        if (io.bhex.baselib.utils.NumberUtils.sub(repayAmountStr, minRepayQty) < 0) {
                            ToastUtils.showLong(this, getString(R.string.string_margin_repay_qty_not_be_less_tips, minRepayQty, currentToken));
                            return;
                        }
                    } else {
                        String minRepayQty = configDataBean.getRepayMinQuantity();
                        if (NumberUtils.sub(totalRemainValue + "", configDataBean.getRepayMinQuantity()) < 0) {
                            minRepayQty = totalRemainValue + "";
                        } else if (NumberUtils.mul(minRepayQty, unPaidInterestValue) < 0) {
                            // 最小还币数量不能小于未还利息
                            minRepayQty = unPaidInterestValue;
                        }
                        if (io.bhex.baselib.utils.NumberUtils.sub(repayAmountStr, minRepayQty) < 0) {
                            ToastUtils.showLong(this, getString(R.string.string_margin_repay_qty_not_be_less_tips, minRepayQty, currentToken));
                            return;
                        }
                    }

                    getPresenter().queryMarginRepay(repayModel, mLoanRecord.getLoanOrderId(), repayAmountStr, mLoanRecord.getAccountId());


                } else {
                    if (!TextUtils.isEmpty(availableAsset)) {
                        if (NumberUtil.sub(availableAsset, totalRemainValue+"") < 0) {
                            ToastUtils.showLong(getString(R.string.string_margin_asset_not_enough, currentToken));
                            return;
                        }
                    } else {
                        ToastUtils.showLong(getString(R.string.string_margin_asset_not_enough, currentToken));
                        return;
                    }
                    getPresenter().queryMarginRepayAll(mLoanRecord.getLoanOrderId(), mLoanRecord.getAccountId());
                }
                break;


            case R.id.asset_all:
                if (!TextUtils.isEmpty(availableAsset)) {
                    String realAvail = availableAsset;
                    String remainAll = totalRemainValue + "";
                    if (NumberUtil.sub(totalRemainValue + "", availableAsset) < 0) {
                        if (repayModel == 1) {
                            realAvail = NumberUtils.roundFormatUp(mLoanRecord.getUnpaidAmount(), AppData.Config.DIGIT_DEFAULT_VALUE);
                        } else {
                            realAvail = NumberUtils.roundFormatUp(remainAll, AppData.Config.DIGIT_DEFAULT_VALUE);
                        }
                    } else {
                        if (repayModel == 1) {
                            double realAmount= NumberUtils.sub(realAvail,unPaidInterestValue);
                            if (realAmount>0) {
                                realAvail =  NumberUtils.roundFormatDown(realAmount, AppData.Config.DIGIT_DEFAULT_VALUE);
                            } else {
                                realAvail = "";
                            }
                        } else {
                            realAvail = NumberUtils.roundFormatDown(realAvail, AppData.Config.DIGIT_DEFAULT_VALUE);
                        }
                    }
                    token_repay_amount_et.setText(realAvail);
                } else {
                    token_repay_amount_et.setText("");
                }
                break;

            case R.id.real_repay_amount_title:
                DialogUtils.showDialogOneBtn_new(this, getString(R.string.string_real_repay_tip_title), getString(R.string.string_real_repay_tip), getString(R.string.string_i_know), true, null);

                break;
            case R.id.tv_remain_interest_title:
                DialogUtils.showDialogOneBtn_new(this, getString(R.string.string_remain_interest_tip_title), getString(R.string.string_remain_interest_tip), getString(R.string.string_i_know), true, null);
                break;

        }
    }

    @Override
    public void showMarginAvailQty(String free) {
        availableAsset = free;
        String showValue = NumberUtils.roundFormatDown(free, AppData.Config.DIGIT_DEFAULT_VALUE);
        viewFinder.textView(R.id.asset_available).setText(getString(R.string.string_available) + " " + showValue + currentToken);
    }

    @Override
    public void updateMarginTokenConfig(MarginTokenConfigResponse.MarginToken dataBean) {
        configDataBean = dataBean;
        if (configDataBean != null) {
            token_repay_amount_et.setHint(getString(R.string.string_min_repay_hint, configDataBean.getRepayMinQuantity()));
            showInterestPrecision = dataBean.getShowInterestPrecision();
            updateLoanInfo();
        }
    }

    @Override
    public void showCurrentLoanRecord(QueryLoanRecordResponse.DataBean dataBean) {
        mLoanRecord = dataBean;
        updateLoanInfo();
    }

    private int repayModel = 1;

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        if (isChecked) {
            switch (buttonView.getId()) {
                case R.id.repay_type_capital:
                    repayModel = 1;
                    updateRealRepayAmount();
                    break;
                case R.id.repay_type_total:
                    repayModel = 2;
                    updateRealRepayAmount();
                    break;
            }
        }

    }

    private void updateRealRepayAmount() {
        String repayAmountStr = token_repay_amount_et.getText().toString();
        String realRepayAmount = getString(R.string.string_placeholder);
        if (!TextUtils.isEmpty(repayAmountStr)) {

            if (repayModel == 1) {
                realRepayAmount = NumberUtils.add2(repayAmountStr, unPaidInterestValue);

            } else {
                realRepayAmount = repayAmountStr;
            }
            realRepayAmount = NumberUtils.roundFormatUp(realRepayAmount, AppData.Config.DIGIT_DEFAULT_VALUE);
            if (NumberUtils.sub(realRepayAmount,totalRemainValue+"")>0) {
                realRepayAmount = totalRemainValue+"";
            }
        }

        real_repay_amount.setText("≈"+realRepayAmount+currentToken);
    }
}
