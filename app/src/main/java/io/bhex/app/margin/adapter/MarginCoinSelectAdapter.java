package io.bhex.app.margin.adapter;

import android.content.Context;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.CommonUtil;
import io.bhex.sdk.trade.margin.bean.MarginTokenConfigResponse;

public class MarginCoinSelectAdapter extends BaseQuickAdapter<MarginTokenConfigResponse.MarginToken, BaseViewHolder> {

    private Context mContext;

    public MarginCoinSelectAdapter(Context context, List<MarginTokenConfigResponse.MarginToken> data) {
        super(R.layout.item_margin_coin_select_layout, data);
        mContext = context;
    }

    private int getItemPosition(MarginTokenConfigResponse.MarginToken item) {
        return item != null && mData != null && !mData.isEmpty() ? mData.indexOf(item) : -1;
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final MarginTokenConfigResponse.MarginToken itemModel) {
        baseViewHolder.setText(R.id.tvAlert, itemModel.getTokenId());
        baseViewHolder.setText(R.id.leverage_tv, mContext.getString(R.string.string_margin_lever_format, itemModel.getLeverage() + ""));
        int position = getItemPosition(itemModel);
        if (itemModel.isSelected()) {
            if (getItemCount() == 1) {
                baseViewHolder.setBackgroundRes(R.id.item_ll, R.drawable.bg_alertbutton_bottom_selected);
            } if (position == getItemCount() - 1) {
                baseViewHolder.setBackgroundRes(R.id.item_ll, R.drawable.bg_alertbutton_bottom_selected);
            } else {
                baseViewHolder.setBackgroundRes(R.id.item_ll, R.drawable.bg_alertbutton_selected);
            }
            baseViewHolder.setTextColor(R.id.tvAlert,mContext.getResources().getColor(CommonUtil.isBlackMode() ? R.color.textColor_alert_button_destructive_night : R.color.textColor_alert_button_destructive));
        } else {
            if (getItemCount() == 1) {
                baseViewHolder.setBackgroundRes(R.id.item_ll, R.drawable.bg_alertbutton_bottom);  // 有title
            } else if (position == getItemCount() - 1) {
                baseViewHolder.setBackgroundRes(R.id.item_ll, R.drawable.bg_alertbutton_bottom);
            } else {
                baseViewHolder.setBackgroundRes(R.id.item_ll, R.drawable.bg_alertbutton_none);
            }
            baseViewHolder.setTextColor(R.id.tvAlert,mContext.getResources().getColor(CommonUtil.isBlackMode() ? R.color.textColor_alert_button_others_night : R.color.textColor_alert_button_others));
        }
    }

}
