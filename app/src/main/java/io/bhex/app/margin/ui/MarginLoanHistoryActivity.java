package io.bhex.app.margin.ui;

import android.content.Intent;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.margin.adapter.MarginLoanRecordAdapter;
import io.bhex.app.margin.presenter.MarginLoanHistoryPresenter;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.app.view.alertview.AlertView;
import io.bhex.app.view.alertview.OnDismissListener;
import io.bhex.app.view.alertview.OnItemClickListener;
import io.bhex.sdk.trade.margin.bean.MarginTokenConfigResponse;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordResponse;

public class MarginLoanHistoryActivity extends BaseActivity<MarginLoanHistoryPresenter,MarginLoanHistoryPresenter.MarginLoanHistoryUI> implements MarginLoanHistoryPresenter.MarginLoanHistoryUI, View.OnClickListener, BaseQuickAdapter.RequestLoadMoreListener, OnRefreshListener {
    private RecyclerView recyclerView;
    private MarginLoanRecordAdapter adapter;
    private SmartRefreshLayout swipeRefresh;
    private View emptyView;
    private String currentToken ="";
    private TopBar topBar;
    private ArrayList<String> selectTokenList;
    private List<QueryLoanRecordResponse.DataBean> mCurrentOrders;

    @Override
    protected int getContentView() {
        return R.layout.activity_margin_loan_history;
    }

    @Override
    protected MarginLoanHistoryPresenter createPresenter() {
        return new MarginLoanHistoryPresenter();
    }

    @Override
    protected MarginLoanHistoryPresenter.MarginLoanHistoryUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        selectTokenList = new ArrayList<>();
        Intent intent = getIntent();
        if (intent != null) {
            currentToken = intent.getStringExtra(AppData.INTENT.MARGIN_LOAN_TOKEN);
        }
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        recyclerView = viewFinder.find(R.id.recyclerView);
        topBar = viewFinder.find(R.id.topBar);
        topBar.getDivider().setVisibility(View.GONE);
        topBar.setRightImg(R.mipmap.icon_margin_filter);
        topBar.setRightOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                List<String> allTokenList =new ArrayList<>();
                allTokenList.add(getString(R.string.string_all));
                allTokenList.addAll(selectTokenList);
                List<String> selectArr = new ArrayList<>();
                selectArr.add(TextUtils.isEmpty(currentToken)?getString(R.string.string_all):currentToken);

                AlertView selectPhotoAlert = new AlertView(null, null, getString(R.string.string_cancel), selectArr, allTokenList, MarginLoanHistoryActivity.this, AlertView.Style.ActionSheet, new OnItemClickListener() {
                    @Override
                    public void onItemClick(Object o, int position) {
                        if (position == -1) {
                            return;
                        }

                        if (position==0) {
                            currentToken = "";
                            getPresenter().getHistoryRecords(false);
                        } else if (position < allTokenList.size()) {
                            currentToken = allTokenList.get(position);
                            getPresenter().getHistoryRecords(false);
                        }
                    }
                });
                selectPhotoAlert.setOnDismissListener(new OnDismissListener() {
                    @Override
                    public void onDismiss(Object o) {

                    }
                });
                selectPhotoAlert.show();
            }
        });

        LayoutInflater layoutInflater = LayoutInflater.from(MarginLoanHistoryActivity.this);
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
        emptyView.setLayoutParams(layoutParams);
    }



    @Override
    protected void addEvent() {
        super.addEvent();
        swipeRefresh.setOnRefreshListener(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    public void onClick(View v) {

    }

    @Override
    public void onLoadMoreRequested() {
        getPresenter().loadMore();
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        getPresenter().getMarginTokenConfig();
        getPresenter().getHistoryRecords(false);
        refreshLayout.finishRefresh(1000);
    }

    @Override
    public void loadMoreComplete() {
        if (adapter != null) {
            adapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {
        if (adapter != null) {
            adapter.loadMoreFail();
        }
    }

    @Override
    public void loadEnd() {
        if (adapter != null) {
            adapter.loadMoreEnd();
        }
    }

    @Override
    public String getFilterTokenId() {
        return currentToken;
    }

    @Override
    public void updateMarginTokenConfig(List<MarginTokenConfigResponse.MarginToken> datas) {
        if (datas != null) {
            selectTokenList.clear();
            for (MarginTokenConfigResponse.MarginToken token : datas) {
                selectTokenList.add(token.getTokenId().toUpperCase());
            }
        }
    }


    @Override
    public void showRecords(List<QueryLoanRecordResponse.DataBean> currentOrders) {
        mCurrentOrders = currentOrders;
        if (adapter == null) {
            adapter = new MarginLoanRecordAdapter(MarginLoanHistoryActivity.this, currentOrders);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this,recyclerView);
            adapter.setEmptyView(emptyView);
            adapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                    QueryLoanRecordResponse.DataBean dataBean = mCurrentOrders.get(position);
                    IntentUtils.goMarginRecordDetail(MarginLoanHistoryActivity.this,dataBean);
                }
            });
            recyclerView.setLayoutManager(new LinearLayoutManager(MarginLoanHistoryActivity.this));

            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(currentOrders);
        }
    }
}
