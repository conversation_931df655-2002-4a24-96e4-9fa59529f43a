package io.bhex.app.margin.presenter;

import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.trade.margin.MarginApi;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordRequest;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordResponse;

public class MarginRepayDetailPresenter  extends BasePresenter<MarginRepayDetailPresenter.MarginRepayDetailUI> {


    public interface MarginRepayDetailUI extends AppUI {


        void showCurrentLoanRecord(QueryLoanRecordResponse.DataBean dataBean);
    }


    public void queryLoanRecord(String loandId) {

        QueryLoanRecordRequest requestData = new QueryLoanRecordRequest();
        requestData.token_id = "";
        requestData.loan_id = loandId;
        requestData.status = 1;
        requestData.limit = AppData.Config.PAGE_LIMIT;

        MarginApi.RequestLoanHistory(requestData, new SimpleResponseListener<QueryLoanRecordResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(QueryLoanRecordResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<QueryLoanRecordResponse.DataBean> data = response.getArray();
                    if (data!=null &&data.size()>0) {
                        getUI().showCurrentLoanRecord(data.get(0));
                    }

                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

}
