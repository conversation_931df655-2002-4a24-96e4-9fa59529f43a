package io.bhex.app.margin.presenter;

import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.trade.margin.MarginApi;
import io.bhex.sdk.trade.margin.bean.MarginTokenConfigResponse;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordRequest;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordResponse;

public class MarginToRepayPresenter extends BasePresenter<MarginToRepayPresenter.MarginToRepayUI> {


    public void queryToRepayRecord() {

        QueryLoanRecordRequest requestData = new QueryLoanRecordRequest();
        requestData.token_id = getUI().getFilterTokenId();
        requestData.loan_id = "";
        requestData.status = 1;
        requestData.from_loan_id = "";
        requestData.limit = 500;   // 0表示不分页

        MarginApi.RequestLoanHistory(requestData, new SimpleResponseListener<QueryLoanRecordResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(QueryLoanRecordResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<QueryLoanRecordResponse.DataBean> data = response.getArray();

                    getUI().showCurrentLoanRecords(data);

                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }


    /**
     * 获取融币配置
     *
     */
    public void getMarginTokenConfig() {
        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        }
        MarginApi.getMarginTokenConfig("",new SimpleResponseListener<MarginTokenConfigResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(MarginTokenConfigResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<MarginTokenConfigResponse.MarginToken> data = response.getArray();
                    if (data != null) {
                        if (data.size() > 0) {
                            getUI().updateMarginTokenConfig(data);
                        }
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
    public interface MarginToRepayUI extends AppUI {

        void showCurrentLoanRecords(List<QueryLoanRecordResponse.DataBean> currentRecords);

        String getFilterTokenId();

        void updateMarginTokenConfig(List<MarginTokenConfigResponse.MarginToken> data);
    }
}
