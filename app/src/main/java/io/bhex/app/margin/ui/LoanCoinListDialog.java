package io.bhex.app.margin.ui;

import android.app.Dialog;
import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chad.library.adapter.base.BaseQuickAdapter;

import java.util.List;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import io.bhex.app.R;
import io.bhex.app.margin.adapter.MarginCoinSelectAdapter;
import io.bhex.app.skin.view.SkinTabLayout;
import io.bhex.sdk.trade.margin.bean.MarginTokenConfigResponse;

/**
 * ================================================
 * 描   述：借币币种
 * ================================================
 */
public class LoanCoinListDialog implements  View.OnClickListener {
    private OnDialogObserver mOnDialogObserver;

    private RecyclerView recyclerView;
    private SkinTabLayout tab;

    private MarginCoinSelectAdapter adapter;
    private List<MarginTokenConfigResponse.MarginToken> mData;
    private Dialog bottomDialog;
    private Context mContext;
    private View mContentView;

    public interface OnDialogObserver {

        void onSelectToken(MarginTokenConfigResponse.MarginToken dataBean);

        void onDismiss();

    }
    public LoanCoinListDialog(Context context, List<MarginTokenConfigResponse.MarginToken> dataBeans, OnDialogObserver onDialogObserver) {
        mContext = context;
        mData = dataBeans;
        mOnDialogObserver = onDialogObserver;
    }


    public void ShowDialog() {
        bottomDialog = new Dialog(mContext, R.style.BottomDialog);
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.dialog_margin_coin_select_layout, null);
        bottomDialog.setContentView(mContentView);
        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) mContentView.getLayoutParams();
        params.width = mContext.getResources().getDisplayMetrics().widthPixels;
        mContentView.setLayoutParams(params);
        initView();
        bottomDialog.setCanceledOnTouchOutside(true);
        bottomDialog.getWindow().setGravity(Gravity.BOTTOM);
        bottomDialog.getWindow().setWindowAnimations(R.style.BottomDialog_Animation);
        bottomDialog.show();
    }

    //初始化view
    private void initView() {
        recyclerView = mContentView.findViewById(R.id.recyclerView);

        if (adapter != null) {
            adapter.setNewData(mData);
            return;
        }
        adapter = new MarginCoinSelectAdapter(mContext, mData);
        adapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (bottomDialog != null && bottomDialog.isShowing()) {
                    bottomDialog.dismiss();
                }

                if (mOnDialogObserver != null) {
                    mOnDialogObserver.onSelectToken(mData.get(position));
                }
            }
        });
        mContentView.findViewById(R.id.tvAlertCancel).setOnClickListener(this);
        recyclerView.setLayoutManager(new LinearLayoutManager(mContext));
        recyclerView.setAdapter(adapter);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvAlertCancel:
                if (bottomDialog != null && bottomDialog.isShowing()) {
                    bottomDialog.dismiss();
                }
                if (mOnDialogObserver != null) {
                    mOnDialogObserver.onDismiss();
                }
                break;
        }
    }
}

