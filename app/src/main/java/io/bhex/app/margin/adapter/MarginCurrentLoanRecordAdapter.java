package io.bhex.app.margin.adapter;

import android.content.Context;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.trade.margin.bean.MarginTokenConfigResponse;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordResponse;

public class MarginCurrentLoanRecordAdapter extends BaseQuickAdapter<QueryLoanRecordResponse.DataBean, BaseViewHolder> {

    private Context mContext;

    public MarginCurrentLoanRecordAdapter(Context context, List<QueryLoanRecordResponse.DataBean> data) {
        super(R.layout.item_margin_current_loan_record_layout, data);
        mContext = context;
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final QueryLoanRecordResponse.DataBean itemModel) {
        baseViewHolder.setText(R.id.tv_token,itemModel.getTokenId());
        baseViewHolder.setText(R.id.tv_amount_value,itemModel.getLoanAmount());
        double remainSec = NumberUtils.div(1000,NumberUtils.sub(System.currentTimeMillis()+"",itemModel.getInterestStart()),0);
        double toAddInterestPerUnit = NumberUtils.mul(remainSec+"",itemModel.getInterestRate1());
        double toAddInterest = NumberUtils.mul(toAddInterestPerUnit+"",itemModel.getUnpaidAmount());
        int showInterestPrecision = AppData.Config.DIGIT_DEFAULT_VALUE;
        MarginTokenConfigResponse.MarginToken marginToken = AppConfigManager.GetInstance().getMarginTokenItemByTokenId(itemModel.getTokenId());
        if (marginToken!=null) {
            showInterestPrecision = marginToken.getShowInterestPrecision();
        }
        String remainInterestValue =NumberUtils.roundFormatUp(NumberUtils.add2(toAddInterest+"",itemModel.getInterestUnpaid()), showInterestPrecision);

        String remainToRepayValue = NumberUtils.roundFormatUp(itemModel.getUnpaidAmount(), AppData.Config.DIGIT_DEFAULT_VALUE);
//        String remainInterestValue = NumberUtils.roundFormatUp(itemModel.getInterestUnpaid(), AppData.Config.DIGIT_DEFAULT_VALUE);
        baseViewHolder.setText(R.id.tv_remain_to_repay_value,remainToRepayValue);
        baseViewHolder.setText(R.id.tv_remain_interest_value,remainInterestValue);
        // 根据状态判断是否可见
        if (itemModel.getStatus() == 1) {
            baseViewHolder.setGone(R.id.tv_repay, true);
            baseViewHolder.addOnClickListener(R.id.tv_repay);
        } else {
            baseViewHolder.setGone(R.id.tv_repay, false);
        }
    }

}
