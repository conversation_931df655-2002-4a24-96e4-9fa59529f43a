package io.bhex.app.margin.presenter;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.presenter.HistoryMarginFragmentPresenter;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.trade.margin.MarginApi;
import io.bhex.sdk.trade.margin.bean.MarginTokenConfigResponse;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordRequest;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordResponse;
import io.bhex.sdk.trade.margin.bean.QueryRepayRecordRequest;
import io.bhex.sdk.trade.margin.bean.QueryRepayRecordResponse;

public class MarginRepayHistoryPresenter extends BasePresenter<MarginRepayHistoryPresenter.MarginRepayHistoryUI> {


    private List<QueryRepayRecordResponse.DataBean> currentRecords=new ArrayList<>();
    protected String mPageId = "";

    public void getHistoryRecords(final boolean isLoadMore) {
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(),getResources().getString(R.string.hint_network_not_connect));
            return;
        }

        if (isLoadMore) {
            if (currentRecords != null) {
                if (!currentRecords.isEmpty()) {
                    mPageId = currentRecords.get(currentRecords.size() - 1).getRepayOrderId();
                }
            }
        }else{
            mPageId ="";
        }
        QueryRepayRecordRequest requestData = new QueryRepayRecordRequest();
        requestData.token_id = getUI().getFilterTokenId();
        requestData.loan_order_id = "0";
        requestData.from_repay_id =mPageId;
        requestData.limit = AppData.Config.PAGE_LIMIT;

        MarginApi.RequestRepayHistory(requestData, new SimpleResponseListener<QueryRepayRecordResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
                getUI().loadMoreComplete();
            }

            @Override
            public void onSuccess(QueryRepayRecordResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<QueryRepayRecordResponse.DataBean> data = response.getArray();
                    if (data != null) {
                        if (isLoadMore) {

                            if (data != null) {
                                currentRecords.addAll(data);
                            }

                        } else {
                            if (data != null) {
                                currentRecords.clear();
                                currentRecords = data;
                            }
                        }
                        getUI().showRecords(currentRecords);
                        if (data.size()< AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        }else{
                            getUI().loadMoreComplete();
                        }
                    }else{
                        getUI().loadMoreComplete();
                    }

                }else{
                    getUI().loadMoreFailed();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public void loadMore() {
        getHistoryRecords(true);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, MarginRepayHistoryPresenter.MarginRepayHistoryUI ui) {
        super.onUIReady(activity, ui);
        getHistoryRecords(false);
    }

    public interface MarginRepayHistoryUI extends AppUI {
        void loadMoreComplete();

        void showRecords(List<QueryRepayRecordResponse.DataBean> currentRecords);

        void loadMoreFailed();

        void loadEnd();

        String getFilterTokenId();
    }

}
