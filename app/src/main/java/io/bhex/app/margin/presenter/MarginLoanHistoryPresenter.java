package io.bhex.app.margin.presenter;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.trade.margin.MarginApi;
import io.bhex.sdk.trade.margin.bean.MarginTokenConfigResponse;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordRequest;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordResponse;

public class MarginLoanHistoryPresenter extends BasePresenter<MarginLoanHistoryPresenter.MarginLoanHistoryUI> {
    @Override
    public void onUIReady(BaseCoreActivity activity, MarginLoanHistoryPresenter.MarginLoanHistoryUI ui) {
        super.onUIReady(activity, ui);
        getHistoryRecords(false);
        getMarginTokenConfig();
    }
    private List<QueryLoanRecordResponse.DataBean> currentRecords=new ArrayList<>();
    protected String mPageId = "";
    public void getHistoryRecords(final boolean isLoadMore) {
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(),getResources().getString(R.string.hint_network_not_connect));
            return;
        }

        String loanId ="";

        if (isLoadMore) {
            if (currentRecords != null) {
                if (!currentRecords.isEmpty()) {
                    mPageId = currentRecords.get(currentRecords.size() - 1).getLoanOrderId();
                }
            }
        }else{
            mPageId ="";
        }

        QueryLoanRecordRequest requestData = new QueryLoanRecordRequest();
        requestData.token_id = getUI().getFilterTokenId();
        requestData.loan_id = "0";
        requestData.status = 2;
        requestData.from_loan_id =mPageId;
        requestData.limit = AppData.Config.PAGE_LIMIT;

        MarginApi.RequestLoanHistory(requestData, new SimpleResponseListener<QueryLoanRecordResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
                getUI().loadMoreComplete();
            }

            @Override
            public void onSuccess(QueryLoanRecordResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<QueryLoanRecordResponse.DataBean> data = response.getArray();
                    if (data != null) {
                        if (isLoadMore) {

                            if (data != null) {
                                currentRecords.addAll(data);
                            }

                        } else {
                            if (data != null) {
                                currentRecords.clear();
                                currentRecords = data;
                            }
                        }
                        getUI().showRecords(currentRecords);
                        if (data.size()< AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        }else{
                            getUI().loadMoreComplete();
                        }
                    }else{
                        getUI().loadMoreComplete();
                    }

                }else{
                    getUI().loadMoreFailed();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public void loadMore() {
        getHistoryRecords(true);
    }

    /**
     * 获取融币配置
     *
     */
    public void getMarginTokenConfig() {
        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        }
        MarginApi.getMarginTokenConfig("",new SimpleResponseListener<MarginTokenConfigResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(MarginTokenConfigResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<MarginTokenConfigResponse.MarginToken> data = response.getArray();
                    if (data != null) {
                        if (data.size() > 0) {
                            getUI().updateMarginTokenConfig(data);
                        }
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public interface MarginLoanHistoryUI extends AppUI {
        void loadMoreComplete();

        void showRecords(List<QueryLoanRecordResponse.DataBean> currentRecords);

        void loadMoreFailed();

        void loadEnd();

        String getFilterTokenId();

        void updateMarginTokenConfig(List<MarginTokenConfigResponse.MarginToken> data);
    }

}

