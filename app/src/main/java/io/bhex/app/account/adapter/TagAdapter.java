/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AdsAdapter.java
 *   @Date: 19-4-25 下午6:09
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.adapter;

import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.account.bean.UserInfoBean;

/**
 * ================================================
 * 描   述：个性标签
 * ================================================
 */

public class TagAdapter extends BaseQuickAdapter<UserInfoBean.CustomLabelListBean, BaseViewHolder> {

    public TagAdapter(List<UserInfoBean.CustomLabelListBean> data) {
        super(R.layout.item_tag_layout, data);
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final UserInfoBean.CustomLabelListBean itemModel) {
        baseViewHolder.setText(R.id.tagName, itemModel.getLabelValue());
        baseViewHolder.setTextColor(R.id.tagName, Color.parseColor(itemModel.getColorCode()));
        View tagNameView = baseViewHolder.getView(R.id.tagName);
//        GradientDrawable gradientDrawable = (GradientDrawable) tagNameView.getBackground();
        GradientDrawable gradientDrawable = new GradientDrawable();

        gradientDrawable.setStroke(PixelUtils.dp2px(1),Color.parseColor(itemModel.getColorCode()));
        tagNameView.setBackground(gradientDrawable);


    }
}
