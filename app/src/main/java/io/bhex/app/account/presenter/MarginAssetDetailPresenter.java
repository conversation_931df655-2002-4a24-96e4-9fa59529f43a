/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AssetDetailPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.LoginApi;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.trade.AssetApi;
import io.bhex.sdk.trade.bean.AssetDataResponse;
import io.bhex.sdk.trade.bean.FeeBeanResponse;
import io.bhex.sdk.trade.margin.MarginApi;
import io.bhex.sdk.trade.margin.bean.MarginAccountAssetResponse;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordRequest;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordResponse;


public class MarginAssetDetailPresenter extends BasePresenter<MarginAssetDetailPresenter.AssetDetailUI> {


    public interface AssetDetailUI extends AppUI{
        void showAsset(MarginAccountAssetResponse.DataBean assetBean);

        void showCurrentLoanRecords(List<QueryLoanRecordResponse.DataBean> currentRecords);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, AssetDetailUI ui) {
        super.onUIReady(activity, ui);
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    /**
     * 获取资产
     * @param tokenId
     */
    public void getAsset(final String tokenId) {
        MarginApi.RequestTokenIdAsset(tokenId, new SimpleResponseListener<MarginAccountAssetResponse>(){
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(MarginAccountAssetResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    List<MarginAccountAssetResponse.DataBean> data = response.getArray();
                    if (data != null) {
                        if (data.size()>0) {
                            for (MarginAccountAssetResponse.DataBean assetBean : data) {
                                if (assetBean.getTokenId().equalsIgnoreCase(tokenId)) {
                                    getUI().showAsset(assetBean);
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        } );
    }

    public void queryToRepayRecord(String token) {


        QueryLoanRecordRequest requestData = new QueryLoanRecordRequest();
        requestData.token_id = token;
        requestData.loan_id = "";
        requestData.status = 1;
        requestData.limit = 500;   // 0表示不分页

        MarginApi.RequestLoanHistory(requestData, new SimpleResponseListener<QueryLoanRecordResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(QueryLoanRecordResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<QueryLoanRecordResponse.DataBean> data = response.getArray();

                    getUI().showCurrentLoanRecords(data);

                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
