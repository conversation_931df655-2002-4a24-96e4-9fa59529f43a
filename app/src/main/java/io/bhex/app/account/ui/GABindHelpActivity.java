/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: GABindHelpActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageView;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.Toolbar;

import com.bumptech.glide.Glide;
import com.google.android.material.appbar.CollapsingToolbarLayout;

import java.io.IOException;

import io.bhex.app.R;
import io.bhex.app.account.presenter.GABindHelpPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.sdk.socket.Base64;
import io.bhex.sdk.security.bean.GAInfoResponse;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.view.ShadowDrawable;

/**
 * ================================================
 * 描   述：Ga绑定帮助
 * ================================================
 */

public class GABindHelpActivity extends BaseActivity<GABindHelpPresenter, GABindHelpPresenter.GABindHelpUI> implements GABindHelpPresenter.GABindHelpUI, View.OnClickListener {
    @Override
    protected int getContentView() {
        return R.layout.activity_ga_help2_layout;
    }

    @Override
    protected GABindHelpPresenter createPresenter() {
        return new GABindHelpPresenter();
    }

    @Override
    protected GABindHelpPresenter.GABindHelpUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        Toolbar toolbar = findViewById(R.id.toolbar);
        toolbar.setTitleTextColor(SkinColorUtil.getDark(this));
        CollapsingToolbarLayout collapsingToolbarLayout = findViewById(R.id.collapsing_toolbar);
        // 硬编码黑白版Toolbar标题栏title字色
        collapsingToolbarLayout.setCollapsedTitleTextColor(SkinColorUtil.getDark(this));
        collapsingToolbarLayout.setExpandedTitleColor(SkinColorUtil.getDark(this));

        //显示返回按钮
        setSupportActionBar(toolbar);
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setDisplayHomeAsUpEnabled(true);
        }

        viewFinder.textView(R.id.ga_bind_tips_b).setText(getString(R.string.string_ga_bind_tips_b,getString(R.string.app_name)));
        ShadowDrawable.setShadow(viewFinder.find(R.id.ga_keystore_rela));

    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()){
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.btn_nextstep).setOnClickListener(this);
        viewFinder.find(R.id.ga_key_copy).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_nextstep:
                finish();
                IntentUtils.goBindGA(this);
                break;
            case R.id.ga_key_copy:
                CommonUtil.copyText(this, viewFinder.textView(R.id.ga_keystore).getText().toString());
                break;
        }
    }

    @Override
    public void showGABindInfo(GAInfoResponse response) {
        viewFinder.textView(R.id.ga_keystore).setText(response.getSecretKey());
        ImageView qrCode = viewFinder.imageView(R.id.ga_bind_qrcode);
        if (!TextUtils.isEmpty(response.getQrcode())) {
            try {
                byte[] qrCodeByte = Base64.decode(response.getQrcode(), Base64.DECODE);
                Glide.with(this).load(qrCodeByte).into(qrCode);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

}
