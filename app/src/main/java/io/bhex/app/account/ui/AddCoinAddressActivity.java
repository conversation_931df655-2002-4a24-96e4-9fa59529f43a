/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AddCoinAddressActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.content.Intent;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.presenter.AddCoinAddressPresenter;
import io.bhex.app.account.viewhandler.ChainTypeViewController;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.VerifyUtil;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.core.SPEx;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.quote.bean.ChainType;
import io.bhex.sdk.trade.bean.TwoVerifyBean;

/**
 * ================================================
 * 描   述：添加币地址
 * ================================================
 */

public class AddCoinAddressActivity extends BaseActivity<AddCoinAddressPresenter,AddCoinAddressPresenter.AddCoinAddressUI> implements AddCoinAddressPresenter.AddCoinAddressUI, View.OnClickListener {
    private static final int REQUEST_CODE_OF_ADD_ADDRESS = 0x006;
    private static final int REQUEST_CODE_OF_SCAN = 0x007;
    private TopBar topBar;
    private UserInfoBean userInfo;
    private boolean bindGA=false;
    private String mobile;
    private String email;
    private boolean isBindMobile=false;
    private boolean isBindEmail=false;
    private boolean isVerifyEmail=true;
    private String tokenId="";
    private String tokenName="";
    private String tokenFullName="";
    private String iconUrl="";
    private EditText addressEt;
    private EditText addressRemarkEt;
    private EditText addressExtEt;
    private Boolean isEOS = false;
    private TextView chainsTitle;
    private RecyclerView chainTypeRV;
    private String selectChainType="";
    private boolean isNeedChainSelect=false;

    @Override
    protected int getContentView() {
        return R.layout.activity_add_coin_address_layout;
    }

    @Override
    protected AddCoinAddressPresenter createPresenter() {
        return new AddCoinAddressPresenter();
    }

    @Override
    protected AddCoinAddressPresenter.AddCoinAddressUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        topBar.setLeftImg(R.mipmap.btn_head_back);
        topBar.setLeftOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        userInfo = UserManager.getInstance().getUserInfo();
        if (userInfo != null) {
            bindGA = userInfo.isBindGA();
            mobile = userInfo.getMobile();
            email = userInfo.getEmail();
            isBindMobile = !TextUtils.isEmpty(mobile)&&userInfo.getRegisterType()!=1;
            isBindEmail = !TextUtils.isEmpty(email)&&userInfo.getRegisterType()!=2;

            /**
             * 验证的是与登录模式相反的 eg.邮箱登录二次验证手机，手机登录二次验证邮箱
             */
            isVerifyEmail = !SPEx.get(AppData.SPKEY.USER_ACCOUNT_MODE_KEY,true);
        }

        Intent intent = getIntent();
        if (intent != null) {
            tokenId = intent.getStringExtra("tokenId");
            selectChainType = intent.getStringExtra("chainType");
            tokenName = intent.getStringExtra("tokenName");
            tokenFullName = intent.getStringExtra("tokenFullName");
            iconUrl = intent.getStringExtra("icon");
            topBar.setTitle(getString(R.string.string_add_address_format,tokenName));

            isEOS = intent.getBooleanExtra("isEOS", false);
            initChainTypeChoice(tokenId);
        }

        addressEt = viewFinder.editText(R.id.address_et);
        addressRemarkEt = viewFinder.editText(R.id.address_remark_et);
        addressExtEt = viewFinder.editText(R.id.address_eostag_et);
        if(isEOS == true){
            viewFinder.editText(R.id.address_eostag_et).setVisibility(View.VISIBLE);
        }
        else {
            viewFinder.editText(R.id.address_eostag_et).setVisibility(View.GONE);
        }


    }

    /**
     * 初始化-连路类型选择
     * @param tokenId
     */
    private void initChainTypeChoice(String tokenId) {
        chainsTitle = viewFinder.textView(R.id.chainsTitle);
        chainTypeRV = viewFinder.find(R.id.chainTypeRV);
        List<io.bhex.sdk.quote.bean.ChainType> chainTypes = AppConfigManager.GetInstance().getTokenChainTypesByTokenId(tokenId);
        if (chainTypes != null&&chainTypes.size()>0) {
            isNeedChainSelect = true;
            chainsTitle.setVisibility(View.VISIBLE);
            chainTypeRV.setVisibility(View.VISIBLE);
            //设置默认选择值(默认第一个类型)
            for (int i = 0; i < chainTypes.size(); i++) {
                ChainType chainType = chainTypes.get(i);
                if (chainType.getChainType().equals(selectChainType)) {
                    chainType.setSelect(true);
                }else{
                    chainType.setSelect(false);
                }

//                ChainType chainType = chainTypes.get(0);
//                selectChainType = chainType.getChainType();
//                if (i==0) {
//                    chainType.setSelect(true);
//                }else{
//                    chainType.setSelect(false);
//                }
            }
            ChainTypeViewController.getInstance().showChainTypesGrid(this, chainTypes,chainTypeRV, 3, new ChainTypeViewController.ChainTypeSelectListener() {
                @Override
                public void onItemSelect(ChainType selectChain) {
                    //切换链路
//                    ToastUtils.showShort(selectChain.getChainType());
                    //链路类型
                    selectChainType = selectChain.getChainType();

                }
            });
        }else{
            isNeedChainSelect = false;
            chainsTitle.setVisibility(View.GONE);
            chainTypeRV.setVisibility(View.GONE);
        }
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.btn_sure).setOnClickListener(this);
        viewFinder.find(R.id.btn_scan).setOnClickListener(this);
        addressEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                autoCorrectAddress();
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
    }

    /**
     * 纠正imtoken地址
     */
    private void autoCorrectAddress() {
        String address = addressEt.getText().toString().trim();
        if (address.startsWith("bitcoin:") || address.startsWith("ethereum:")) {
            if (address.length()>address.indexOf(":")) {
                if (address.contains("?") && address.indexOf(":") < address.indexOf("?")) {
                    address = address.substring(address.indexOf(":")+1,address.indexOf("?"));
                }else{
                    address = address.substring(address.indexOf(":")+1,address.length());
                }
                //设置地址写在判断里，防止造成死循环判断
                addressEt.setText(address);
            }
        }
        if (address.startsWith("bitcoincash:")) {
            if (address.length()>address.indexOf(":")) {
                address = address.substring(address.indexOf(":")+1,address.length());
                //设置地址写在判断里，防止造成死循环判断
                addressEt.setText(address);
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(requestCode == REQUEST_CODE_OF_ADD_ADDRESS && resultCode == RESULT_OK){
            if (data != null) {
                TwoVerifyBean twoVerifyBean = (TwoVerifyBean)data.getSerializableExtra("twoVerify");
                //去提币地址
                if (twoVerifyBean!=null) {
                    getPresenter().addAddress(isNeedChainSelect,tokenId,selectChainType, addressEt, addressRemarkEt, addressExtEt, twoVerifyBean);
                }else{
                    ToastUtils.showShort(this,getString(R.string.string_wait_retry));
                }
            }
        }else if(requestCode ==REQUEST_CODE_OF_SCAN && resultCode == RESULT_OK){
            if (data!=null){
                String result = data.getStringExtra("result");
                if (!TextUtils.isEmpty(result)) {
                    addressEt.setText(result);
                }
            }
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.btn_sure:
                String address = addressEt.getText().toString().trim();
                if (TextUtils.isEmpty(address)) {
                    ToastUtils.showShort(this,getString(R.string.input_address));
                    return;
                }
                String remark = addressRemarkEt.getText().toString().trim();
                if (TextUtils.isEmpty(remark)) {
                    ToastUtils.showShort(this,getString(R.string.input_remark));
                    return;
                }
                if (isNeedChainSelect) {
                    if (TextUtils.isEmpty(selectChainType)) {
                        ToastUtils.showShort(this,getString(R.string.hint_select_chain_please));
                        return;
                    }
                }

                getPresenter().getUserInfo();
                break;
            case R.id.btn_scan:
                //扫码
                IntentUtils.goScan(this,REQUEST_CODE_OF_SCAN);
                break;
        }
    }

    @Override
    public void requestUserInfoSuccess(UserInfoBean data) {
        if (userInfo != null) {
            VerifyUtil.is2FA(this, userInfo, new VerifyUtil.VerifyListener() {
                @Override
                public void on2FAVerify(boolean isVerify2FA) {
                    if (!isVerify2FA) {
                        //没有二次绑定认证
                        return;
                    }else{
                        bindGA = userInfo.isBindGA();
                        mobile = userInfo.getMobile();
                        email = userInfo.getEmail();
                        isBindMobile = !TextUtils.isEmpty(mobile)&&userInfo.getRegisterType()!=1;
                        isBindEmail = !TextUtils.isEmpty(email)&&userInfo.getRegisterType()!=2;

                        //去二次验证
                        IntentUtils.goTwoVerify(AddCoinAddressActivity.this,REQUEST_CODE_OF_ADD_ADDRESS,"from_address_add","","8",bindGA,isBindMobile,isBindEmail,isVerifyEmail);
                    }
                }
            });

        }
    }
}
