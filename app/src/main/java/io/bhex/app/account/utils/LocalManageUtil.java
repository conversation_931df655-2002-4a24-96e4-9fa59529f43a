package io.bhex.app.account.utils;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.os.LocaleList;
import android.util.DisplayMetrics;
import android.util.Log;

import java.util.Locale;

import io.bhex.baselib.utils.DebugLog;
import io.bhex.sdk.data_manager.RateAndLocalManager;

/**
 * created by gongdongyang
 * on 2020/2/27
 */
public class LocalManageUtil  {

    private final static String TAG = LocalManageUtil.class.getSimpleName();

    /**
     * 赋予context对象 语言属性新值
     * @param context
     * @param language
     * @return
     */
    public static Context attachBaseContext(Context context, String language) {

        Locale locale = getSetLanguageLocale(context);
        DebugLog.d("LocalManageUtil====>:","locale=="+locale.getLanguage());
        return  createConfigurationResources(context,locale.getLanguage());
    }

     /*
     * 获取设置的语言
     * @param context
     * @return
     */
    public static Locale getSetLanguageLocale(Context context) {
       RateAndLocalManager.LocalKind localKind = RateAndLocalManager.GetInstance(context).getCurLocalKind();
       if(localKind.code.equalsIgnoreCase("zh")){
           //return new Locale("zh-CN");
           return Locale.CHINESE;
       }else if(localKind.code.equalsIgnoreCase("en")){
           //return "en-US";
           //return new Locale("en-US");
           return Locale.ENGLISH;
       }else if(localKind.code.equalsIgnoreCase("ko")){
           //return "ko";
           return new Locale("ko");

       } else if(localKind.code.equalsIgnoreCase("vi")){
           //return "vi-VN";
           return new Locale("vi");

       }else if(localKind.code.equalsIgnoreCase("ja")){
           //return "ja-jp";
           //return new Locale("ja-jp");
           return  Locale.JAPANESE;
       }else if(localKind.code.equalsIgnoreCase("tr")){
           //return "tr";
           return new Locale("tr");
       }else{
           //return "en-US";
           return Locale.ENGLISH;
       }
    }

    @TargetApi(Build.VERSION_CODES.N)
    public static Context createConfigurationResources(Context context, String language) {
        Resources resources = context.getResources();
        Configuration configuration = resources.getConfiguration();
        DisplayMetrics dm = resources.getDisplayMetrics();

        Locale locale = new Locale(language);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            LocaleList localeList = new LocaleList(locale);
            LocaleList.setDefault(localeList);
            configuration.setLocales(localeList);
        } else {
            configuration.locale = locale;
        }

        resources.updateConfiguration(configuration, dm);
        return context;
    }

   /* public static void applyLanguage(Context context, String newLanguage) {
        Resources resources = context.getResources();
        Configuration configuration = resources.getConfiguration();
        Locale locale = new Locale(newLanguage);
        DisplayMetrics dm = resources.getDisplayMetrics();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            // apply locale
            configuration.setLocale(locale);
            resources.updateConfiguration(configuration, dm);
        } else {
            // updateConfiguration
            configuration.locale = locale;

            resources.updateConfiguration(configuration, dm);
        }
    }*/


}
