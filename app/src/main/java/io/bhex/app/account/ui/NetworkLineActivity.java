package io.bhex.app.account.ui;

import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.io.IOException;
import java.net.SocketException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.TreeMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import javax.annotation.Nullable;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import io.bhex.app.R;
import io.bhex.app.account.bean.PingResponse;
import io.bhex.app.account.presenter.NetworkLinePresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.web.ui.WebActivity;
import io.bhex.baselib.network.BParamsBuilder;
import io.bhex.baselib.network.Utils.Convert;
import io.bhex.baselib.network.params.GetParams;
import io.bhex.baselib.network.request.RequestFactory;
import io.bhex.sdk.Urls;
import io.bhex.sdk.UrlsConfig;
import io.bhex.sdk.config.bean.BackupDomainBean;
import io.bhex.sdk.config.domain.BackupDomainManager;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import okio.ByteString;

/**
 * *******************************************************************
 *
 * @项目名称: BHEX Android
 * @文件名称: NetworkLineActivity
 * @Date: 2020/9/8 下午8:35
 * @Author: ppzhao
 * @Copyright（C）: 2020 BlueHelix Inc.   All rights reserved.
 * 注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 * *******************************************************************
 **/
public class NetworkLineActivity extends BaseActivity<NetworkLinePresenter, NetworkLinePresenter.NetworkLineUI> implements NetworkLinePresenter.NetworkLineUI {
    private static final String KEY_API = "_api";
    private static final String KEY_OTC = "_otc";
    private static final String KEY_WS = "_ws";
    private static final long DELAY_MILLISS = 2000;
    private static final int SEND_PING = 1;
    private RecyclerView recyclerView;
    private LinesAdapter adapter;
    private Timer timer;
    private TimerTask task;
    private TreeMap<String,String> costTimeMap = new TreeMap<>();
    private ConcurrentHashMap<String,WebSocket> webSocketMap = new ConcurrentHashMap<>();
    private ConcurrentHashMap<String,Long> pingTimeMap = new ConcurrentHashMap<>();
    private List<BackupDomainBean> backupDomainList;
    private Handler handler;
    private OkHttpClient okHttpClient;
    private StringBuilder domainParams;
//    private boolean isOpen;
//    private boolean isConnecting;

    @Override
    protected int getContentView() {
        return R.layout.activity_network_line_layout;
    }

    @Override
    protected NetworkLinePresenter createPresenter() {
        return new NetworkLinePresenter();
    }

    @Override
    protected NetworkLinePresenter.NetworkLineUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        handler = new Handler(){
            @Override
            public void handleMessage(@NonNull Message msg) {
                super.handleMessage(msg);
                switch (msg.what){
                    case 0:
                        adapter.setNewData(backupDomainList);
                        break;
                    case SEND_PING:
                        String wsUrl = (String) msg.obj;
                        sendPing(wsUrl);
                        break;
                }
            }
        };

        recyclerView = viewFinder.find(R.id.recyclerView);

        buildOkClient();
        backupDomainList = BackupDomainManager.getInstance().getBackupDomainList();
        showLines(backupDomainList);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.btn_network_detection).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Iterator<BackupDomainBean> iterator = backupDomainList.iterator();
                String detectDomain = UrlsConfig.API_SERVER_URL;
                detectDomain = detectDomain.replaceFirst("app.","www.");
                int length = detectDomain.length();
                detectDomain = detectDomain.substring(0,length-1);
                domainParams = new StringBuilder();
                domainParams.append("?domain="+ detectDomain);
//                domainParams.append("?domain=");
//                while (iterator.hasNext()){
//                    BackupDomainBean backupDomainBean = iterator.next();
//                    if (backupDomainBean != null) {
//                        //TODO 待处理多个域名探测 和 不同子域名H5交互传递数据格式问题
//                        String domain = backupDomainBean.getDomain();
//                        domainParams.append(domain+",");
//                    }
//                }

                WebActivity.runActivity(NetworkLineActivity.this,getString(R.string.string_network_detection), Urls.H5_NETWORK_DETECTION+domainParams);
            }
        });
    }

    private void buildOkClient() {
        OkHttpClient.Builder okHttpBuilder = new OkHttpClient.Builder();
        okHttpBuilder.connectTimeout(5, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)//允许失败重试
                //.connectTimeout(2, TimeUnit.SECONDS)
                .readTimeout(5, TimeUnit.SECONDS)
                .writeTimeout(5, TimeUnit.SECONDS)
                .sslSocketFactory(createSSLSocketFactory());
        okHttpClient = okHttpBuilder.build();

    }

    @Override
    protected void onResume() {
        super.onResume();
        startTimer();
    }

    private void startTimer() {
        timer = new Timer();
        task = new TimerTask() {
            @Override
            public void run() {
                if (backupDomainList != null) {
                    for (BackupDomainBean backupDomainBean : backupDomainList) {
                        //轮询探测接口
                        loopProbe(backupDomainBean);
                    }
//                    handler.sendEmptyMessage(0);//通知UI刷新
                }
            }
        };
        timer.schedule(task,100,DELAY_MILLISS);
    }

    /**
     * 发起循环探测
     * @param backupDomainBean
     */
    private synchronized void loopProbe(BackupDomainBean backupDomainBean) {
        if (backupDomainBean == null) {
            return;
        }

        apiProbe(backupDomainBean);
        wsProbe(backupDomainBean);
    }

    /**
     * Api 探测
     * @param backupDomainBean
     */
    private void apiProbe(BackupDomainBean backupDomainBean) {
        String domain = backupDomainBean.getDomain();
        if (TextUtils.isEmpty(domain)) {
            return;
        }

        String apiServer = loadApiServer(backupDomainBean);
        if (TextUtils.isEmpty(apiServer)) {
            return;
        }
        String pingUrl = apiServer +"api/time";

        GetParams params = BParamsBuilder.get()
                .url(pingUrl)
                .build();
        Request request = RequestFactory.createRequest(params);
//        if (!TextUtils.isEmpty(body)) {
//            RequestBody jsonBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), body);
//            request = request.newBuilder().post(jsonBody).build();
//        }

        Call call = okHttpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                costTimeMap.put(domain+KEY_API,"");
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (getUI() !=null && isAlive()) {
                    long requestCostTime = response.receivedResponseAtMillis() - response.sentRequestAtMillis();
                    costTimeMap.put(domain+KEY_API,String.valueOf(requestCostTime));
                    handler.sendEmptyMessage(0);//通知UI刷新
                }
            }
        });
    }

    /**
     * ws 探测
     * @param backupDomainBean
     */
    private void wsProbe(BackupDomainBean backupDomainBean) {
        String domain = backupDomainBean.getDomain();
        if (TextUtils.isEmpty(domain)) {
            return;
        }

        String wsServer = loadWsServer(backupDomainBean);
        if (TextUtils.isEmpty(wsServer)) {
            return;
        }
        String wsUrl = wsServer +"ws/quote/v1";

        Request.Builder builder = new Request.Builder().url(wsUrl);

        Request request = builder.build();
//        if ((webSocket == null || !isOpen) && !isConnecting) {
        if (webSocketMap.get(wsUrl)==null) {
//            isConnecting = true;
            WebSocket webSocket = okHttpClient.newWebSocket(request, new WebSocketListener() {
                @Override
                public void onOpen(WebSocket webSocket, Response response) {
                    super.onOpen(webSocket, response);
                    webSocketMap.put(wsUrl,webSocket);
//                    isConnecting = false;
//                    isOpen = true;
                    long requestCostTime = response.receivedResponseAtMillis() - response.sentRequestAtMillis();
                    costTimeMap.put(domain + KEY_WS, String.valueOf(requestCostTime));
                    handler.sendEmptyMessage(0);//通知UI刷新

                    sendPing(wsUrl);//ping
                }

                @Override
                public void onMessage(WebSocket webSocket, String text) {
                    super.onMessage(webSocket, text);
                    if (!TextUtils.isEmpty(text)) {
                        long receiveTime = System.currentTimeMillis();
                        PingResponse pingResponse = Convert.fromJson(text, PingResponse.class);
                        if (pingResponse != null) {
                            long pingCostTime = receiveTime - pingTimeMap.get(wsUrl);
                            costTimeMap.put(domain + KEY_WS, String.valueOf(pingCostTime));
                            handler.sendEmptyMessage(0);//通知UI刷新
                            Message message = new Message();
                            message.what = SEND_PING;
                            message.obj = wsUrl;
                            handler.sendMessageDelayed(message,DELAY_MILLISS);//通知UI刷新

                        }
                    }
                }

                @Override
                public void onMessage(WebSocket webSocket, ByteString bytes) {
                    super.onMessage(webSocket, bytes);
                }

                @Override
                public void onClosing(WebSocket webSocket, int code, String reason) {
                    super.onClosing(webSocket, code, reason);
                }

                @Override
                public void onClosed(WebSocket webSocket, int code, String reason) {
                    super.onClosed(webSocket, code, reason);
                    webSocketMap.remove(wsUrl);
//                    isOpen = false;
//                    isConnecting = false;
                    costTimeMap.put(domain + KEY_WS, "--");
                    handler.sendEmptyMessage(0);//通知UI刷新
                }

                @Override
                public void onFailure(WebSocket webSocket, Throwable t, @Nullable Response response) {
                    super.onFailure(webSocket, t, response);
                    webSocketMap.remove(wsUrl);
//                    isOpen = false;
//                    isConnecting = false;
//                    DebugLog.e("XXXXXXXXX",t.getClass().getSimpleName()+" "+t.getMessage());
                    if (t instanceof SocketException) {
                        //正常关闭
                        costTimeMap.put(domain + KEY_WS, "--");
                    }else{
                        costTimeMap.put(domain + KEY_WS, "");
                    }
                    handler.sendEmptyMessage(0);//通知UI刷新
                }
            });
        }
    }

    private void sendPing(String wsUrl) {
        WebSocket webSocket = webSocketMap.get(wsUrl);
        if (webSocket != null) {
            long sendTime = System.currentTimeMillis();
            pingTimeMap.put(wsUrl,sendTime);
            webSocket.send("{\"ping\":\"" + sendTime + "\",\"sendTime\":"+sendTime+"}");
        }
    }

    private String loadApiServer(BackupDomainBean backupDomainBean) {
        String REST_URL_ONLINE = "";
//        String OTC_URL_ONLINE = "";
//        String SOCKET_ONLINE = "";
        String domain = backupDomainBean.getDomain();
        int level = backupDomainBean.getLevel();
        if (!TextUtils.isEmpty(domain)) {
            if (level == 1) {
                //一级备用域名
                REST_URL_ONLINE = "https://app."+domain+"/";
//                OTC_URL_ONLINE = "https://otc."+domain+"/";
//                SOCKET_ONLINE = "wss://ws."+domain+"/";

            }else if(level == 2){
                //二级备用域名
                REST_URL_ONLINE = "https://"+domain+"/";
//                OTC_URL_ONLINE = "https://"+domain+"/";
//                SOCKET_ONLINE = "wss://"+domain+"/";
            }
        }
        return REST_URL_ONLINE;
    }

    private String loadWsServer(BackupDomainBean backupDomainBean) {
        String SOCKET_ONLINE = "";
        String domain = backupDomainBean.getDomain();
        int level = backupDomainBean.getLevel();
        if (!TextUtils.isEmpty(domain)) {
            if (level == 1) {
                //一级备用域名
                SOCKET_ONLINE = "wss://ws."+domain+"/";

            }else if(level == 2){
                //二级备用域名
                SOCKET_ONLINE = "wss://"+domain+"/";
            }
        }
        return SOCKET_ONLINE;
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (task != null) {
            task.cancel();
        }
        if (timer != null) {
            timer.cancel();
        }

        if (webSocketMap != null) {
            for (String key : webSocketMap.keySet()) {
                WebSocket webSocket = webSocketMap.get(key);
                if (webSocket != null) {
                    webSocket.cancel();
                    webSocket = null;
                }
            }
            webSocketMap.clear();
        }

    }

    public void showLines(final List<BackupDomainBean> datas) {
        if (datas == null) {
            return;
        }
        if (adapter == null) {
            adapter = new LinesAdapter(datas);
            adapter.isFirstOnly(false);
            recyclerView.setLayoutManager(new LinearLayoutManager(this));
            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(datas);
        }
    }

    public class LinesAdapter extends BaseQuickAdapter<BackupDomainBean,BaseViewHolder> {

        private BackupDomainBean currentDomain;

        public LinesAdapter(List<BackupDomainBean> datas) {
            super(R.layout.item_lines_layout, datas);
            currentDomain = BackupDomainManager.getInstance().getCurrentDomain();
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final BackupDomainBean itemModel) {

            String domain = itemModel.getDomain();
            int lineNo = baseViewHolder.getLayoutPosition()+1;
            baseViewHolder.setText(R.id.item_value,getString(R.string.string_line)+lineNo);

            String currentKey = domain+KEY_API;
            String apiCostTime = costTimeMap.get(currentKey);
            if (apiCostTime == null) {
                baseViewHolder.setText(R.id.item_api_cost_time,mContext.getResources().getString(R.string.string_placeholder));
            }else{
                if (TextUtils.isEmpty(apiCostTime)) {
                    baseViewHolder.setText(R.id.item_api_cost_time,getString(R.string.string_time_out));
                    baseViewHolder.setTextColor(R.id.item_api_cost_time, getResources().getColor( R.color.red));
                }else{
                    baseViewHolder.setText(R.id.item_api_cost_time,apiCostTime+" ms");
                    baseViewHolder.setTextColor(R.id.item_api_cost_time, getResources().getColor( NumberUtils.sub(apiCostTime,"300") <= 0 ? R.color.green : R.color.orange));
                }
            }
            String wsCostTime = costTimeMap.get(domain+KEY_WS);
            if (wsCostTime == null || wsCostTime.equals("--")) {
                baseViewHolder.setText(R.id.item_ws_cost_time,mContext.getResources().getString(R.string.string_placeholder));
            }else{
                if (TextUtils.isEmpty(wsCostTime)) {
                    baseViewHolder.setText(R.id.item_ws_cost_time,getString(R.string.string_time_out));
                    baseViewHolder.setTextColor(R.id.item_ws_cost_time, getResources().getColor( R.color.red));
                }else{
                    baseViewHolder.setText(R.id.item_ws_cost_time,wsCostTime+" ms");
                    baseViewHolder.setTextColor(R.id.item_ws_cost_time, getResources().getColor( NumberUtils.sub(wsCostTime,"300") <= 0 ? R.color.green : R.color.orange));
                }
            }

            if (currentDomain != null) {
                if(currentDomain.getDomain().equals(itemModel.getDomain())){
                    baseViewHolder.getConvertView().findViewById(R.id.item_selected).setBackgroundResource(R.mipmap.icon_checkbox_checked);
                }else{
                    baseViewHolder.getConvertView().findViewById(R.id.item_selected).setBackgroundResource(R.mipmap.icon_checkbox_normal);
                }
            }

            baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    currentDomain = itemModel;
                    BackupDomainManager.getInstance().switchDomain(itemModel);
                    notifyDataSetChanged();
                }

            });
        }

    }

    private static SSLSocketFactory createSSLSocketFactory() {
        SSLSocketFactory ssfFactory = null;

        try {
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, new TrustManager[]{new TrustAllCerts()}, new SecureRandom());

            ssfFactory = sc.getSocketFactory();
        } catch (Exception e) {
        }

        return ssfFactory;
    }
}

class TrustAllCerts implements X509TrustManager {
    @Override
    public void checkClientTrusted(X509Certificate[] chain, String authType) {
    }

    @Override
    public void checkServerTrusted(X509Certificate[] chain, String authType) {
    }

    @Override
    public X509Certificate[] getAcceptedIssuers() {
        return new X509Certificate[0];
    }
}
