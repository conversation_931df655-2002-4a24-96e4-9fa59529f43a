/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: HistoryOrderActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import io.bhex.app.R;
import io.bhex.app.account.presenter.HistoryOrderPresenter;
import io.bhex.app.base.BaseActivity;

/**
 * ================================================
 * 描   述：历史订单
 * ================================================
 */

public class HistoryOrderActivity extends BaseActivity<HistoryOrderPresenter,HistoryOrderPresenter.HistoryOrderUI> implements HistoryOrderPresenter.HistoryOrderUI {

    @Override
    protected int getContentView() {
        return R.layout.activity_history_order_layout;
    }

    @Override
    protected HistoryOrderPresenter createPresenter() {
        return new HistoryOrderPresenter();
    }

    @Override
    protected HistoryOrderPresenter.HistoryOrderUI getUI() {
        return this;
    }
}
