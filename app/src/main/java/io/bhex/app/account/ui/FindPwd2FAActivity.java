package io.bhex.app.account.ui;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;

import io.bhex.app.R;
import io.bhex.app.account.presenter.FindPwd2FAPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.app.view.InputView;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.bean.FindPwdCheckResponse;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-10-22
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class FindPwd2FAActivity extends BaseActivity<FindPwd2FAPresenter, FindPwd2FAPresenter.FindPwd2FAUI> implements FindPwd2FAPresenter.FindPwd2FAUI, View.OnClickListener, TextWatcher {
    private static final int VERIFY_REQUEST_CODE = 0x18;
    private View verifyCodeRela;
    private TextView sendVerifyCodeTv;
    private Button btnSubmit;
    private InputView verifyEt;
    private String requestId="";
    private boolean isVerifyGA;
    private boolean isVerifyEmail;
    private boolean isVerifyMobile;
    //选择验证的模式，如果绑定了GA默认就是选择GA验证 true
    private String currentOrderId="";
//    private String token="";
    private boolean isEmail;
    private String account;
    private String nationalCode;
    private FindPwdCheckResponse findPwdCheckResponse;
    private boolean isNeed2FA;
    private boolean isVerifyIdCard;

    @Override
    protected int getContentView() {
        return R.layout.activity_find_pwd_twofa_layout;
    }

    @Override
    protected FindPwd2FAPresenter createPresenter() {
        return new FindPwd2FAPresenter();
    }

    @Override
    protected FindPwd2FAPresenter.FindPwd2FAUI getUI() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //设置禁止系统截屏、录制
//        getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
    }

    @Override
    protected void initView() {
        super.initView();
        Intent intent = getIntent();
//
//        ntent.putExtra("account", account);
//        intent.putExtra("nationalCode", nationalCode);
//        intent.putExtra("findPwdCheckResponse", findPwdCheckResponse);
        
        //来自登录的requestId
        isEmail = intent.getBooleanExtra("isEmail",false);
        account = intent.getStringExtra("account");
        nationalCode = intent.getStringExtra("nationalCode");
        findPwdCheckResponse = (FindPwdCheckResponse) intent.getSerializableExtra("findPwdCheckResponse");
        if (findPwdCheckResponse != null) {
            requestId = findPwdCheckResponse.getRequestId();
            isNeed2FA = findPwdCheckResponse.isNeed2FA();
            String authType = findPwdCheckResponse.getAuthType();
            if (isNeed2FA) {
                if (!TextUtils.isEmpty(authType)) {
                    isVerifyGA = authType.equals("GA");
                    isVerifyMobile = authType.equals("MOBILE");
                    isVerifyEmail = authType.equals("EMAIL");
                    isVerifyIdCard = authType.equals("ID_CARD");
                }
            }
        }

        verifyCodeRela =  viewFinder.find(R.id.verify_code_rela);
        sendVerifyCodeTv =  viewFinder.textView(R.id.get_verify_code);
        btnSubmit = viewFinder.find(R.id.btn_submit);
        verifyEt = viewFinder.find(R.id.verify_code_et);
        verifyEt.setPaddingLeft(PixelUtils.dp2px(8));
        verifyEt.setInputHint(getString(R.string.input_verify));
        verifyEt.setInputMode(InputView.SILENTMODE);

        if (isVerifyGA || isVerifyIdCard) { //GA 后者身份证验证
            //绑定了GA默认就是选择GA验证 true
            verifyEt.setPaddingRight(PixelUtils.dp2px(40));
            viewFinder.find(R.id.tab_a_rela).setVisibility(View.VISIBLE);
            viewFinder.find(R.id.tab_a_indicator).setVisibility(View.VISIBLE);
            viewFinder.find(R.id.tab_b_rela).setVisibility(View.GONE);
            viewFinder.find(R.id.tab_b_indicator).setVisibility(View.GONE);
            viewFinder.find(R.id.get_verify_code).setVisibility(View.GONE);
            if (isVerifyGA) {
                viewFinder.textView(R.id.tab_a_name).setText(getString(R.string.string_google_auth));
                verifyEt.setInputHint(getString(R.string.input_verify));
                viewFinder.find(R.id.paste).setVisibility(View.VISIBLE);
                viewFinder.find(R.id.authIdCardTips).setVisibility(View.GONE);
                verifyEt.setInputMode(InputView.SILENTMODE);
                verifyEt.setInputType(InputType.TYPE_CLASS_NUMBER);
            }
            if (isVerifyIdCard) {
                viewFinder.textView(R.id.tab_a_name).setText(getString(R.string.string_idcard_auth));
                verifyEt.setInputHint(getString(R.string.string_hint_idcard));
                viewFinder.find(R.id.paste).setVisibility(View.VISIBLE);
                viewFinder.find(R.id.authIdCardTips).setVisibility(View.VISIBLE);
                verifyEt.setInputMode(InputView.NORMALMODE);
                verifyEt.setInputType(InputType.TYPE_CLASS_TEXT);
            }
        }else{ //手机或者邮箱验证
            verifyEt.setPaddingRight(PixelUtils.dp2px(80));
            viewFinder.find(R.id.tab_a_rela).setVisibility(View.GONE);
            viewFinder.find(R.id.tab_a_indicator).setVisibility(View.GONE);
            viewFinder.find(R.id.paste).setVisibility(View.GONE);
            viewFinder.find(R.id.tab_b_indicator).setVisibility(View.VISIBLE);
            viewFinder.find(R.id.tab_b_rela).setVisibility(View.VISIBLE);
            viewFinder.find(R.id.get_verify_code).setVisibility(View.VISIBLE);

            if (isVerifyMobile) {
                viewFinder.textView(R.id.tab_b_name).setText(getString(R.string.string_sms_auth));
            }else if(isVerifyEmail){
                viewFinder.textView(R.id.tab_b_name).setText(getString(R.string.string_email_auth));
            }else{
                viewFinder.find(R.id.tab_b_rela).setVisibility(View.GONE);
            }
        }



        if (!isVerifyEmail&&!isVerifyGA&&!isVerifyMobile&&!isVerifyIdCard){
            btnSubmit.setVisibility(View.GONE);
            ToastUtils.showShort(this, getString(R.string.verify_service_exception));
        }

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.tab_a_rela).setOnClickListener(this);
        viewFinder.find(R.id.tab_b_rela).setOnClickListener(this);
        viewFinder.find(R.id.btn_submit).setOnClickListener(this);
        viewFinder.find(R.id.paste).setOnClickListener(this);
        viewFinder.find(R.id.get_verify_code).setOnClickListener(this);
        verifyEt.addTextWatch(this);
    }


    @Override
    public void setAuthTv(String s) {
        sendVerifyCodeTv.setText(s);
    }

    @Override
    public void setAuthTvStatus(boolean b) {
        sendVerifyCodeTv.setEnabled(b);
        sendVerifyCodeTv.setTextColor(b ? getResources().getColor(R.color.blue) : SkinColorUtil.getDark50(this));
    }

    @Override
    public void updateOrderId(String orderId) {
        currentOrderId = orderId;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.paste:
                verifyEt.setInputString(CommonUtil.pasteText(this));
                break;
            case R.id.get_verify_code:
                //其他的需要人机校验，然后在调用发送验证码
                getPresenter().FindPwdSend2FAVerifyCode(requestId);
                break;
            case R.id.btn_submit:
                getPresenter().submit(isVerifyEmail||isVerifyMobile,requestId,currentOrderId,verifyEt);
                break;

//            case R.id.tab_a_rela:
//                //tab A默认为GA  ，GA不需要点击发送验证码按钮
//                isSelectGAVerify = true;
//                viewFinder.find(R.id.paste).setVisibility(View.VISIBLE);
//                viewFinder.find(R.id.get_verify_code).setVisibility(View.GONE);
//                viewFinder.find(R.id.tab_a_indicator).setVisibility(View.VISIBLE);
//                viewFinder.find(R.id.tab_b_indicator).setVisibility(View.GONE);
//                viewFinder.textView(R.id.tab_a_name).setTextColor(getResources().getColor(R.color.blue));
//                viewFinder.textView(R.id.tab_b_name).setTextColor(getResources().getColor(CommonUtil.isBlackMode()?R.color.dark:R.color.dark));
//                break;
//            case R.id.tab_b_rela:
//                //短信或者邮箱验证需要发送验证码按钮
//                isSelectGAVerify=false;
//                viewFinder.find(R.id.paste).setVisibility(View.GONE);
//                viewFinder.find(R.id.get_verify_code).setVisibility(View.VISIBLE);
//                viewFinder.find(R.id.tab_a_indicator).setVisibility(View.GONE);
//                viewFinder.find(R.id.tab_b_indicator).setVisibility(View.VISIBLE);
//                viewFinder.textView(R.id.tab_a_name).setTextColor(getResources().getColor(CommonUtil.isBlackMode()?R.color.dark:R.color.dark));
//                viewFinder.textView(R.id.tab_b_name).setTextColor(getResources().getColor(R.color.blue));
//                break;
        }
    }


    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
        checkEditText();
    }

    @Override
    public void afterTextChanged(Editable s) {

    }

    private void checkEditText() {
        String verifyCode = verifyEt.getInputString();
        if (TextUtils.isEmpty(verifyCode)){
            btnSubmit.setEnabled(false);
        }else{
            btnSubmit.setEnabled(true);
        }
    }

    @Override
    public void goFindPwd2ConfirmStep() {
        IntentUtils.goFindPasswd(this,isEmail,account,nationalCode,requestId);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
//        if(requestCode == VERIFY_REQUEST_CODE && resultCode == RESULT_OK){
//            if (data != null) {
//                token = data.getStringExtra("token");
//
//                getPresenter().verifyAfterLogin(isVerifyEmail,token,type);
//            }
//        }
    }
}
