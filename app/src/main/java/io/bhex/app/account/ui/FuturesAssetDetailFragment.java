/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FuturesAssetDetailFragment.java
 *   @Date: 19-7-18 下午8:13
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.os.Bundle;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;

import java.util.List;

import io.bhex.app.account.adapter.AssetRecordAdapter;
import io.bhex.app.account.presenter.FuturesAssetDetailPresenter;
import io.bhex.app.base.BaseListFreshFragment;
import io.bhex.sdk.trade.bean.AssetRecordResponse;
import io.bhex.sdk.trade.bean.FuturesAssetListResponse;

import static io.bhex.baselib.constant.AppData.INTENT.KEY_ASSET;

public class FuturesAssetDetailFragment extends BaseListFreshFragment<FuturesAssetDetailPresenter, FuturesAssetDetailPresenter.FuturesAssetDetailUI> implements FuturesAssetDetailPresenter.FuturesAssetDetailUI {
    @Override
    protected FuturesAssetDetailPresenter.FuturesAssetDetailUI getUI() {
        return this;
    }

    private FuturesAssetListResponse.FuturesAssetBean assetItemBean;
    @Override
    protected FuturesAssetDetailPresenter createPresenter() {
        return new FuturesAssetDetailPresenter();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if(getArguments() != null){
            try {

                assetItemBean = (FuturesAssetListResponse.FuturesAssetBean)getArguments().getSerializable(KEY_ASSET);
            }
            catch (Exception e){

            }
        }
    }

    @Override
    public void showOrders(List<AssetRecordResponse.RecordBean> currentOrders) {
        if (adapter == null) {
            adapter = new AssetRecordAdapter(currentOrders);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this, recyclerView);
            adapter.setEnableLoadMore(true);

            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            recyclerView.setItemAnimator(new DefaultItemAnimator());

            adapter.setEmptyView(emptyView);
            recyclerView.setAdapter(adapter);

        } else {
            adapter.setNewData(currentOrders);
        }
    }

    @Override
    public String getToken() {
        if(assetItemBean != null)
            return assetItemBean.tokenId;
        return "";
    }

}
