package io.bhex.app.account.ui;

import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.adapter.RiskyAssetsDetailAdapter;
import io.bhex.app.account.presenter.RiskyAssetsDetailPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.sdk.trade.bean.FeeBeanResponse;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-01-07
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class RiskyAssetsDetailActivity extends BaseActivity<RiskyAssetsDetailPresenter, RiskyAssetsDetailPresenter.RiskyAssetsDetailUI> implements RiskyAssetsDetailPresenter.RiskyAssetsDetailUI {
    private List<FeeBeanResponse.RiskBalanceBean> riskBalanceList;
    private RecyclerView recyclerView;
    private View emptyView;

    @Override
    protected int getContentView() {
        return R.layout.activity_risky_assets_detail_layout;
    }

    @Override
    protected RiskyAssetsDetailPresenter createPresenter() {
        return new RiskyAssetsDetailPresenter();
    }

    @Override
    protected RiskyAssetsDetailPresenter.RiskyAssetsDetailUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        Intent intent = getIntent();
        if (intent != null) {
            FeeBeanResponse feeBean = (FeeBeanResponse)intent.getSerializableExtra("feeBean");
            riskBalanceList = feeBean.getRiskBalance();
        }

        LinearLayout rootView = viewFinder.find(R.id.rootView);
        LayoutInflater layoutInflater = LayoutInflater.from(this);
        emptyView = layoutInflater.inflate(R.layout.empty_layout, rootView, false);
        recyclerView = viewFinder.find(R.id.recyclerView);

        recyclerView = viewFinder.find(R.id.recyclerView);

        if (riskBalanceList != null) {
            RiskyAssetsDetailAdapter riskyAssetsDetailAdapter = new RiskyAssetsDetailAdapter(riskBalanceList);
            riskyAssetsDetailAdapter.isFirstOnly(false);
            riskyAssetsDetailAdapter.setEmptyView(emptyView);
            recyclerView.setLayoutManager(new LinearLayoutManager(this));
            recyclerView.setAdapter(riskyAssetsDetailAdapter);
        }

    }
}
