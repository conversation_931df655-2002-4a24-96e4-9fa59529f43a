/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FuturesHoldOrderFragment.java
 *   @Date: 19-7-26 下午2:21
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.content.DialogInterface;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;

import java.util.List;

import io.bhex.app.account.presenter.FuturesHoldOrderFragmentPresenter;
import io.bhex.app.base.BaseListFreshFragment;
import io.bhex.app.trade.adapter.FuturesPositionAdapter;
import io.bhex.app.trade.ui.MarginAdjustDialog;
import io.bhex.sdk.trade.futures.bean.FuturesPositionOrder;

/**
 * 期货持仓单
 */
public class FuturesHoldOrderFragment extends BaseListFreshFragment<FuturesHoldOrderFragmentPresenter, FuturesHoldOrderFragmentPresenter.HoldOrderFragmentUI> implements FuturesHoldOrderFragmentPresenter.HoldOrderFragmentUI {
    @Override
    protected FuturesHoldOrderFragmentPresenter.HoldOrderFragmentUI getUI() {
        return this;
    }

    @Override
    protected FuturesHoldOrderFragmentPresenter createPresenter() {
        return new FuturesHoldOrderFragmentPresenter();
    }

    @Override
    public void showOrders(List<FuturesPositionOrder> currentOrders) {
        if (adapter == null) {
            adapter = new FuturesPositionAdapter(getActivity(), currentOrders,new MarginAdjustDialog.OnDialogObserver(){
                @Override
                public void onShow(DialogInterface dialog) {

                }

                @Override
                public void onDismiss(DialogInterface dialog) {

                }

                @Override
                public void onReqHttpSuccess() {
                    getPresenter().getData(false);
                }

                @Override
                public void onReqHttpFaile() {

                }
            });
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this, recyclerView);
            adapter.setEnableLoadMore(true);

            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            recyclerView.setItemAnimator(new DefaultItemAnimator());

            adapter.setEmptyView(emptyView);
            recyclerView.setAdapter(adapter);

        } else {
            adapter.setNewData(currentOrders);
        }
    }

}
