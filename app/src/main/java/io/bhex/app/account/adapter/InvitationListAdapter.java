/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: InvitationListAdapter.java
 *   @Date: 19-8-26 下午4:14
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.adapter;


import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.DateUtils;
import io.bhex.sdk.invite.bean.InvitationRelationBean;

public class InvitationListAdapter extends BaseQuickAdapter<InvitationRelationBean,BaseViewHolder> {
    public InvitationListAdapter(@Nullable List<InvitationRelationBean> data) {
        super(R.layout.item_invite_record_layout, data);
    }

    @Override
    protected void convert(BaseViewHolder baseViewHolder, InvitationRelationBean itemModel) {
        baseViewHolder.setText(R.id.invite_form,itemModel.getInviteType().equals("1")?mContext.getString(R.string.string_invite_direct_num):mContext.getString(R.string.string_invite_indirect_num));
        baseViewHolder.setText(R.id.invite_friend,itemModel.getRegisterType()==1?itemModel.getMobile():itemModel.getEmail());
        baseViewHolder.setText(R.id.invite_date,DateUtils.getSimpleTimeFormat(itemModel.getRegisterDate()));
//        baseViewHolder.setText(R.id.invite_status,);
    }
}
