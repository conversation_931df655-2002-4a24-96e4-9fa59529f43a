/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: SetLoginPasswdPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import android.os.CountDownTimer;
import android.text.TextUtils;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.view.InputView;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.AccountInfoApi;
import io.bhex.sdk.account.bean.SetPasswdVerifyCodeResponse;
import io.bhex.sdk.security.SecurityApi;
import io.bhex.sdk.security.bean.OrderIdParamResponse;

public class SetLoginPasswdPresenter extends BasePresenter<SetLoginPasswdPresenter.SetLoginPasswdUI> {
    private String orderId="";

    public boolean checkInputContentIsEmpty(InputView inputPasswd, InputView inputPasswd2, InputView inputVerify) {
            String passwd = inputPasswd.getInputString();
            String comfirmPasswd = inputPasswd2.getInputString();
            String code = inputVerify.getInputString();

            return !TextUtils.isEmpty(passwd) && !TextUtils.isEmpty(comfirmPasswd) && !TextUtils.isEmpty(code);
    }

    public void sendVerifyCode() {
        AccountInfoApi.RequestGetSetPWDVerifyCode(new SimpleResponseListener<SetPasswdVerifyCodeResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }
            @Override
            public void onSuccess(SetPasswdVerifyCodeResponse data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data,true)) {
                    orderId = data.getOrderId();
                    getUI().setAuthTvStatus(false);
                    timer.start();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    /**
     * 设置登录密码
     * @param passwdFirst
     * @param passwdSecond
     * @param verifyCode
     */
    public void setLoginPasswd(String passwdFirst, String passwdSecond, String verifyCode) {
        if(TextUtils.isEmpty(orderId)) {
            ToastUtils.showShort(getString(R.string.string_verify_code_invalid));
            return;
        }
        AccountInfoApi.RequestSetPWD(passwdFirst,passwdSecond,verifyCode,orderId,new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    ToastUtils.showShort(getString(R.string.string_set_success));
                    getActivity().finish();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(getString(R.string.string_set_failed));
            }
        });
    }

    public interface SetLoginPasswdUI extends AppUI{

        void setAuthTvStatus(boolean b);

        void setAuthTv(String tips);
    }


    /**
     * 倒计时
     */
    private CountDownTimer timer = new CountDownTimer(AppData.DOWN_TIME_CODE, AppData.DOWN_TIME_INTERVAL_CODE) {
        @Override
        public void onTick(long millisUntilFinished) {
            getUI().setAuthTv((millisUntilFinished / 1000)
                    + getActivity().getResources().getString(
                    R.string.after_second));
        }

        @Override
        public void onFinish() {
            getUI().setAuthTvStatus(true);
            getUI().setAuthTv(getResources().getString(
                    R.string.string_get_auth_code));
        }
    };

}
