/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: LoginResponse.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.bean;

import io.bhex.baselib.network.response.BaseResponse;


public class LoginResponse extends BaseResponse {

    /**
     * user : {"id":**********,"email":"<EMAIL>","accountId":0}
     * token : eyJhbGciOiJIUzUxMiJ9.********************************************************************************************.7yhd2fhx-oZIcvX8fg22x7gaLcHI8jVUl3f4A4QbcJQoBS5Ui_Fh5Oflx6z6AH9ksl8_neg3OQ5y-XecmcWmnQ
     */

    private UserBean user;
    private String token;

    public UserBean getUser() {
        return user;
    }

    public void setUser(UserBean user) {
        this.user = user;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public static class UserBean {
        /**
         * id : **********
         * email : <EMAIL>
         * accountId : 0
         */

        private long id;
        private String email;
        private int accountId;

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public int getAccountId() {
            return accountId;
        }

        public void setAccountId(int accountId) {
            this.accountId = accountId;
        }
    }
}
