/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: LocalLangSelectPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.sdk.data_manager.RateAndLocalManager;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;

public class LocalLangSelectPresenter extends BasePresenter<LocalLangSelectPresenter.LocalListUI> {

    public RateAndLocalManager.LocalKind[] mLocals;
    public RateAndLocalManager.LocalKind mSelectLocal;

    public void saveSelect() {
        RateAndLocalManager.GetInstance(this.getActivity()).SetCurLocalKind(getActivity(),mSelectLocal);
    }


    public void saveSelectLanguage() {
        RateAndLocalManager.GetInstance(this.getActivity()).SetCurLocalKindExt(getActivity(),mSelectLocal);
    }

    public void setSelect(RateAndLocalManager.LocalKind localKind) {
        mSelectLocal = localKind;
    }

    public interface LocalListUI extends AppUI {

        void showLocalList(RateAndLocalManager.LocalKind[] rates);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, LocalLangSelectPresenter.LocalListUI ui) {
        super.onUIReady(activity, ui);
        mLocals = RateAndLocalManager.LocalKind.values();
        mSelectLocal = RateAndLocalManager.GetInstance(this.getActivity()).getCurLocalKind();
        getUI().showLocalList(mLocals);
    }

}