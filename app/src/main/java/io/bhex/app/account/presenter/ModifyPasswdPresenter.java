/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: ModifyPasswdPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import android.text.TextUtils;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.app.utils.RegexUtils;
import io.bhex.app.view.InputView;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.Utils.CookieUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.AccountInfoApi;
import io.bhex.sdk.account.UserManager;

public class ModifyPasswdPresenter extends BasePresenter<ModifyPasswdPresenter.ModifyPasswdUI> {
    public interface ModifyPasswdUI extends AppUI{


    }

    /**
     * 更新密码
     * @param passwdOld
     * @param passwdNew
     * @param passwdNew2
     */
    public void updatePasswd(InputView passwdOld, InputView passwdNew, InputView passwdNew2) {
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }
        String oldPasswd = passwdOld.getInputString();
        if (TextUtils.isEmpty(oldPasswd)) {
            ToastUtils.showShort(getActivity(), getString(R.string.string_input_old_passwd));
            return;
        }
        String newPasswd = passwdNew.getInputString();
        if (TextUtils.isEmpty(newPasswd)) {
            ToastUtils.showShort(getActivity(), getString(R.string.string_input_new_passwd));
            return;
        }
        String newPasswd2 = passwdNew2.getInputString();
        if (TextUtils.isEmpty(newPasswd2)) {
            ToastUtils.showShort(getActivity(), getString(R.string.string_input_confirm_passwd));
            return;
        }

        if (!RegexUtils.checkPasswd(newPasswd)) {
            passwdNew.setError(getResources().getString(R.string.input_pwd_reg_no_match));
            return;
        }else{
            passwdNew.setError("");
        }

        if (!newPasswd.equals(newPasswd2)) {
            ToastUtils.showShort(getActivity(), getString(R.string.string_input_two_passwd_no_equal));
            return;
        }
        AccountInfoApi.RequestModifyPWD(oldPasswd, newPasswd, newPasswd2,new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }
            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    ToastUtils.showShort(getActivity(), getString(R.string.string_update_passwd_success));
                    UserManager.getInstance().clearUserInfo();
                    CookieUtils.getInstance().clearCookies(getActivity());
                    IntentUtils.goLogin(getActivity(),null);
                    getActivity().finish();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

}
