/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AnnouncePresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import java.util.List;

import io.bhex.app.R;
import io.bhex.sdk.utils.UtilsApi;
import io.bhex.sdk.utils.bean.AnnouncementsResponse;
import io.bhex.app.base.AppUI;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.utils.ToastUtils;


public class AnnouncePresenter extends BasePresenter<AnnouncePresenter.AnnounceUI> {
    public interface AnnounceUI extends AppUI{

        void showAnnouncements(List<AnnouncementsResponse.AnnounceBean> data);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, AnnounceUI ui) {
        super.onUIReady(activity, ui);
        getAnnouncements();
    }

    /**
     * 获取公告
     */
    private void getAnnouncements() {
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }
        UtilsApi.RequestAnnouncements(new SimpleResponseListener<AnnouncementsResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }
            @Override
            public void onSuccess(AnnouncementsResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    List<AnnouncementsResponse.AnnounceBean> data = response.getArray();
                    if (data != null) {
                        getUI().showAnnouncements(data);
                    }

                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
