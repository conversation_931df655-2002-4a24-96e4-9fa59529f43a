package io.bhex.app.account.presenter;

import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.account.AccountInfoApi;
import io.bhex.sdk.account.KycApi;
import io.bhex.sdk.account.bean.UserVerifyInfo;
import io.bhex.sdk.account.bean.kyc.KycLevelBean;
import io.bhex.sdk.account.bean.kyc.KycLevelConfigResponse;
import io.bhex.sdk.account.bean.kyc.KycLevelsResponse;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-11-15
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class KycTwoComfirmInfoPresenter extends BasePresenter<KycTwoComfirmInfoPresenter.KycTwoComfirmInfoUI> {
    public void refreshKycLevelInfo(final KycLevelBean kycLevelBean) {
        KycApi.RequestKycLevels(new SimpleResponseListener<KycLevelsResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(KycLevelsResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    List<KycLevelBean> levels = response.getArray();
                    if (levels != null) {
                        for (KycLevelBean level : levels) {
                            if (level != null) {
                                if (level.getDisplayLevel().equalsIgnoreCase(kycLevelBean.getDisplayLevel())) {
                                    getUI().updateKycLevelInfo(level);
                                }
                            }
                        }
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public interface KycTwoComfirmInfoUI extends AppUI{

        void showVerifyInfo(UserVerifyInfo response);

        void updateKycLevelInfo(KycLevelBean response);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, KycTwoComfirmInfoUI ui) {
        super.onUIReady(activity, ui);

    }

    @Override
    public void onResume() {
        super.onResume();
        getVerifyInfo();
    }

    private void getVerifyInfo() {
        AccountInfoApi.RequestGetUserVerifyInfo(new SimpleResponseListener<UserVerifyInfo>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(UserVerifyInfo response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showVerifyInfo(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
