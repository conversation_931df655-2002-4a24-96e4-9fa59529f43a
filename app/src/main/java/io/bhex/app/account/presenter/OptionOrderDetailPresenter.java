/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OrderDetailPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import android.content.Intent;
import android.text.TextUtils;

import java.util.ArrayList;
import java.util.List;

import io.bhex.sdk.trade.OptionApi;
import io.bhex.sdk.trade.TradeApi;
import io.bhex.sdk.trade.bean.OrderBean;
import io.bhex.sdk.trade.bean.OrderDealDetailResponse;
import io.bhex.app.base.AppUI;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;

public class OptionOrderDetailPresenter extends BasePresenter<OptionOrderDetailPresenter.OrderDetailUI> {
    private List<OrderDealDetailResponse.DealOrderBean> currentOrders = new ArrayList<>();
    private String orderPageId;
    private String orderId="";
    private OrderBean order;

    public void loadMore() {
        getDealDetail(true);
    }

    public interface OrderDetailUI extends AppUI{

        void loadMoreComplete();

        void showOrders(List<OrderDealDetailResponse.DealOrderBean> currentOrders);

        void loadMoreFailed();

        void showHeaderOrders(OrderBean order);

        void loadEnd();
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, OrderDetailUI ui) {
        super.onUIReady(activity, ui);
        Intent intent = getActivity().getIntent();
        if (intent != null) {
            order = (OrderBean) intent.getSerializableExtra(AppData.INTENT.KEY_ORDER);
            if (order != null) {
                orderId = order.getOrderId();
                getUI().showHeaderOrders(order);
                getDealDetail(false);
            }
        }
    }

    /**
     * 获取成交详情
     *
     * @param isLoadMore
     */
    public void getDealDetail(final boolean isLoadMore) {
        if (isLoadMore) {
            if (currentOrders != null) {
                if (!currentOrders.isEmpty()) {
                    orderPageId = currentOrders.get(currentOrders.size() - 1).getTradeId();
                }
            }
        }
        String reqeustOrderPageId = "";
        if (isLoadMore && !TextUtils.isEmpty(orderPageId)) {
            //加载更多
            reqeustOrderPageId= orderPageId;

        }
        OptionApi.RequestOptionOrderMatchInfo(orderId, reqeustOrderPageId,new SimpleResponseListener<OrderDealDetailResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
                if (isLoadMore) {
                    getUI().loadMoreComplete();
                }
            }

            @Override
            public void onSuccess(OrderDealDetailResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<OrderDealDetailResponse.DealOrderBean> data = response.getArray();
                    if (data != null) {
                        if (isLoadMore) {
                            if (data != null) {
                                currentOrders.addAll(data);
                            }

                        } else {
                            if (data != null) {
                                currentOrders.clear();
                                currentOrders = data;
                            }
                        }
                        getUI().showOrders(currentOrders);
                        if (data.size()< AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        }else{
                            getUI().loadMoreComplete();
                        }
                    }else{
                        getUI().loadMoreComplete();
                    }

                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                if (isLoadMore) {
                    getUI().loadMoreFailed();
                }
            }
        });
    }
}
