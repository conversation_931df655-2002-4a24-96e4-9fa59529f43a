/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: HistoryEntrustOrderFragment.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.List;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import io.bhex.app.R;
import io.bhex.app.account.presenter.MarginCloseFragmentPresenter;
import io.bhex.app.base.BaseFragment;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.trade.bean.OrderBean;

/**
 * ================================================
 * 描   述：杠杆强平订单
 * ================================================
 */

public class MarginCloseOrderFragment extends BaseFragment<MarginCloseFragmentPresenter, MarginCloseFragmentPresenter.CloseOrderUI> implements MarginCloseFragmentPresenter.CloseOrderUI, BaseQuickAdapter.RequestLoadMoreListener, OnRefreshListener {
    private RecyclerView recyclerView;
    private HistoryEntrustAdapter adapter;
    private SmartRefreshLayout swipeRefresh;
    private View emptyView;

    @Override
    protected MarginCloseFragmentPresenter.CloseOrderUI getUI() {
        return this;
    }

    @Override
    protected MarginCloseFragmentPresenter createPresenter() {
        return new MarginCloseFragmentPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_entrust_order_layout, null, false);
    }

    @Override
    public void onStart() {
        super.onStart();
    }

    @Override
    public void onStop() {
        super.onStop();
    }

    @Override
    protected void initViews() {
        super.initViews();
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        recyclerView = viewFinder.find(R.id.recyclerView);

        Bundle arguments = getArguments();
        if (arguments != null) {
            String symbol = arguments.getString("symbol");
        }

        LayoutInflater layoutInflater = LayoutInflater.from(getActivity());
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
        emptyView.setLayoutParams(layoutParams);

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        swipeRefresh.setOnRefreshListener(this);
    }

    @Override
    public void onLoadMoreRequested() {
        getPresenter().loadMore();
    }

    @Override
    public void onRefresh(RefreshLayout refreshLayout) {
        getPresenter().getHistoryEntrustOrders(false);
        refreshLayout.finishRefresh(1000);
    }

    @Override
    public void loadMoreComplete() {
//        if (swipeRefresh.isRefreshing()) {
//            swipeRefresh.setRefreshing(false);
//        }
        if (adapter != null) {
            adapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {
        if (adapter != null) {
            adapter.loadMoreFail();
        }
    }

    @Override
    public void loadEnd() {
        if (adapter != null) {
            adapter.loadMoreEnd();
        }
    }

    @Override
    public void showOrders(List<OrderBean> currentOrders) {
        if (adapter == null) {

            adapter = new HistoryEntrustAdapter(getActivity(), currentOrders);
//            adapter.openLoadAnimation(BaseQuickAdapter.SCALEIN);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this,recyclerView);
            adapter.setEmptyView(emptyView);

//            swipeRefresh.setColorSchemeColors(Color.RED, Color.BLUE, Color.GREEN);
//            swipeRefresh.setOnRefreshListener(this);

            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
//        recyclerView.setItemAnimator(new DefaultItemAnimator());
//        recyclerView.addItemDecoration(new DividerItemDecoration(getContext(), LinearLayoutManager.VERTICAL));

            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(currentOrders);
        }
    }

    public static class HistoryEntrustAdapter extends BaseQuickAdapter<OrderBean,BaseViewHolder> {

        private Context mContext;

        public HistoryEntrustAdapter(Context context, List<OrderBean> data) {
            super(R.layout.item_history_entrust_order_layout, data);
            mContext = context;
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final OrderBean itemModel) {
            baseViewHolder.setVisible(R.id.item_divider, baseViewHolder.getAdapterPosition() != mData.size());
            String title = itemModel.getBaseTokenName() + " / " + itemModel.getQuoteTokenName();
            baseViewHolder.setText(R.id.order_name, title);
            baseViewHolder.setTextColor(R.id.order_type,KlineUtils.getBuyOrSellColor(mContext,itemModel.getSide()));
            baseViewHolder.setText(R.id.order_type,KlineUtils.getPriceModeTxt(mContext, itemModel.getType()) +" " + KlineUtils.getBuyOrSellTxt(mContext, itemModel.getSide()));
            baseViewHolder.setText(R.id.order_time, DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getTime()), "HH:mm:ss yyyy/MM/dd"));
            baseViewHolder.setText(R.id.title, mContext.getString(R.string.string_price));
            baseViewHolder.setText(R.id.value, KlineUtils.getPrice(mContext,itemModel));
            baseViewHolder.setText(R.id.title2,  KlineUtils.getEntrustTitle(mContext,itemModel));
            baseViewHolder.setText(R.id.value2, KlineUtils.getOrderEntrustAndUnit(itemModel));
            baseViewHolder.setText(R.id.title3, mContext.getString(R.string.string_order_deal_amount));
            baseViewHolder.setText(R.id.value3, KlineUtils.getDealAmount(mContext,itemModel));
            baseViewHolder.setText(R.id.title4, mContext.getString(R.string.string_order_deal_average_price));
            baseViewHolder.setText(R.id.value4, KlineUtils.getAvgPrice(mContext,itemModel));
            baseViewHolder.setText(R.id.title5, mContext.getString(R.string.string_order_deal_money));
            baseViewHolder.setText(R.id.value5, KlineUtils.getDealMoney(mContext,itemModel));
            baseViewHolder.setText(R.id.value6, KlineUtils.getOrderStatus(mContext,itemModel));
            baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    IntentUtils.goMarginOrderDetail(mContext,itemModel);
                }
            });
        }

    }
}
