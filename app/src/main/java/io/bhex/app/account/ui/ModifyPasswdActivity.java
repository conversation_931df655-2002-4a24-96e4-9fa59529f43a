/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: ModifyPasswdActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;

import io.bhex.app.R;
import io.bhex.app.account.presenter.ModifyPasswdPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.view.InputView;
import io.bhex.app.view.TopBar;


public class ModifyPasswdActivity extends BaseActivity<ModifyPasswdPresenter,ModifyPasswdPresenter.ModifyPasswdUI> implements ModifyPasswdPresenter.ModifyPasswdUI, View.OnClickListener, TextWatcher {
    private TopBar topBar;
    private InputView passwdOld;
    private InputView passwdNew;
    private InputView passwdNew2;
    private Button btnSure;

    @Override
    protected int getContentView() {
        return R.layout.activity_modify_passwd_layout;
    }

    @Override
    protected ModifyPasswdPresenter createPresenter() {
        return new ModifyPasswdPresenter();
    }

    @Override
    protected ModifyPasswdPresenter.ModifyPasswdUI getUI() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //设置禁止系统截屏、录制
//        getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        topBar.setLeftImg(R.mipmap.btn_head_back);
        topBar.setLeftOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        btnSure = viewFinder.find(R.id.btn_sure);
        passwdOld = viewFinder.find(R.id.passwd_old);
        passwdOld.setInputHint(getString(R.string.string_passwd_old));
        passwdOld.setInputMode(InputView.PWDMODE);
        passwdNew = viewFinder.find(R.id.passwd_new);
        passwdNew.setInputHint(getString(R.string.string_passwd_new));
        passwdNew.setInputMode(InputView.PWDMODE);
        passwdNew2 = viewFinder.find(R.id.passwd_new2);
        passwdNew2.setInputHint(getString(R.string.string_passwd_new2));
        passwdNew2.setInputMode(InputView.PWDMODE);


    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.btn_sure).setOnClickListener(this);
        passwdOld.addTextWatch(this);
        passwdNew.addTextWatch(this);
        passwdNew2.addTextWatch(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.btn_sure:
                getPresenter().updatePasswd(passwdOld,passwdNew,passwdNew2);
                break;
        }
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
        checkEditText();
    }

    @Override
    public void afterTextChanged(Editable s) {

    }

    private void checkEditText() {
        String oldPasswd = passwdOld.getInputString();
        String newPasswd = passwdNew.getInputString();
        String newPasswd2 = passwdNew2.getInputString();
        if (TextUtils.isEmpty(oldPasswd)||TextUtils.isEmpty(newPasswd)||TextUtils.isEmpty(newPasswd2)){
            btnSure.setEnabled(false);
        }else{
            btnSure.setEnabled(true);
        }
    }

}
