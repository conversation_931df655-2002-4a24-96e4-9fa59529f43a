package io.bhex.app.account.ui;

import android.content.Intent;
import android.text.TextUtils;
import android.view.View;

import io.bhex.app.R;
import io.bhex.app.account.presenter.ScanLoginPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.baselib.utils.ToastUtils;

/**
 * *******************************************************************
 *
 * @项目名称: BHEX Android
 * @文件名称: ScanLoginActivity
 * @Date: 2020/10/22 下午8:07
 * @Author: ppzhao
 * @Copyright（C）: 2020 BlueHelix Inc.   All rights reserved.
 * 注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 * *******************************************************************
 **/
public class ScanLoginActivity extends BaseActivity<ScanLoginPresenter, ScanLoginPresenter.ScanLoginUI> implements ScanLoginPresenter.ScanLoginUI, View.OnClickListener {

    private String loginQRCode;

    @Override
    protected int getContentView() {
        return R.layout.activity_scan_login_layout;
    }

    @Override
    protected ScanLoginPresenter createPresenter() {
        return new ScanLoginPresenter();
    }

    @Override
    protected ScanLoginPresenter.ScanLoginUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();

        Intent intent = getIntent();
        if (intent != null) {
            loginQRCode = intent.getStringExtra("loginQRCode");
        }

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.login).setOnClickListener(this);
        viewFinder.find(R.id.cancelLogin).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.login:
                if (TextUtils.isEmpty(loginQRCode)) {
                    ToastUtils.showLong(getString(R.string.string_login_invalid_scan_again));
                    return;
                }
                getPresenter().authSacanLogin(loginQRCode);
                break;
            case R.id.cancelLogin:
                finish();
                break;
        }
    }

    @Override
    public void authSuccess() {
        ToastUtils.showLong(getString(R.string.string_auth_success));
        finish();
    }
}
