/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BindMobilePresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import android.os.CountDownTimer;
import android.text.TextUtils;

import io.bhex.app.BuildConfig;
import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.sdk.security.SecurityApi;
import io.bhex.sdk.security.bean.OrderIdParamResponse;
import io.bhex.app.view.InputView;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;


public class BindMobilePresenter extends BasePresenter<BindMobilePresenter.BindMobileUI> {
    private String orderIdOfEmail="";
    private String orderIdOfMobile="";

    public void requestMobileCode(String nationalCode, String mobile, String token) {
        SecurityApi.requestMobileVerifyCodeOfBindMobile(mobile,token,BuildConfig.DEEPKNOW_ID,nationalCode,new SimpleResponseListener<OrderIdParamResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("",getString(R.string.string_loading_hint_text));
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(OrderIdParamResponse data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data,true)) {
                    orderIdOfMobile = data.getOrderId();
                    getUI().setAuthTvStatus(false,false);
                    timerOfMobile.start();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);

            }
        });

    }

    public void sendEmailCode() {
        SecurityApi.requestEmailVerifyCodeOfBindMobile(new SimpleResponseListener<OrderIdParamResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("",getString(R.string.string_loading_hint_text));
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(OrderIdParamResponse data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data,true)) {
                    orderIdOfEmail = data.getOrderId();
                    getUI().setAuthTvStatus(true,false);
                    timerOfEmail.start();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);

            }
        });
    }

    /**
     * 请求绑定手机
     * @param nationCode
     * @param mobile
     * @param emailCode
     * @param mobileCode
     */
    public void requestBindMobile(String nationCode, String mobile, String emailCode, String mobileCode) {
        SecurityApi.bindMobile(nationCode,mobile,orderIdOfMobile,mobileCode,orderIdOfEmail,emailCode,new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    ToastUtils.showShort(getActivity(), getString(R.string.string_bind_mobile_success));
                    getActivity().finish();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(getActivity(), getString(R.string.string_bind_mobile_error));
            }
        });
    }

    public boolean checkInputContentIsEmpty(InputView inputMobile, InputView inputVerifyEmail, InputView inputVerifyMobile) {
        String mobile = inputMobile.getInputString();
        String codeOfemail = inputVerifyEmail.getInputString();
        String codeOfMobile = inputVerifyMobile.getInputString();

        return !TextUtils.isEmpty(mobile) && !TextUtils.isEmpty(codeOfemail) && !TextUtils.isEmpty(codeOfMobile);
    }

    public interface BindMobileUI extends AppUI{

        void setAuthTvStatus(boolean b, boolean b1);

        void setAuthTv(boolean b, String s);
    }

    /**
     * 倒计时
     */
    private CountDownTimer timerOfEmail = new CountDownTimer(AppData.DOWN_TIME_CODE, AppData.DOWN_TIME_INTERVAL_CODE) {
        @Override
        public void onTick(long millisUntilFinished) {
            getUI().setAuthTv(true,(millisUntilFinished / 1000)
                    + getActivity().getResources().getString(
                    R.string.after_second));
        }

        @Override
        public void onFinish() {
            getUI().setAuthTvStatus(true,true);
            getUI().setAuthTv(true,getResources().getString(
                    R.string.string_get_auth_code));
        }
    };

    /**
     * 倒计时
     */
    private CountDownTimer timerOfMobile = new CountDownTimer(AppData.DOWN_TIME_CODE, AppData.DOWN_TIME_INTERVAL_CODE) {
        @Override
        public void onTick(long millisUntilFinished) {
            getUI().setAuthTv(false,(millisUntilFinished / 1000)
                    + getActivity().getResources().getString(
                    R.string.after_second));
        }

        @Override
        public void onFinish() {
            getUI().setAuthTvStatus(false,true);
            getUI().setAuthTv(false,getResources().getString(
                    R.string.string_get_auth_code));
        }
    };
}
