package io.bhex.app.account.ui;

import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.TextView;

import io.bhex.app.R;
import io.bhex.app.account.presenter.AntiPhishingCodePresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.safe.DeepKnowVerify;
import io.bhex.app.safe.DeepSEListener;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.app.utils.RegexUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.app.view.InputView;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-07-13
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class AntiPhishingCodeActivity extends BaseActivity<AntiPhishingCodePresenter, AntiPhishingCodePresenter.AntiPhishingCodeUI> implements AntiPhishingCodePresenter.AntiPhishingCodeUI, View.OnClickListener {
    private boolean hasSetAntiPhishingCode;
    private DeepKnowVerify deepKnowVerify;
    private InputView inputPhishingCode;
    private InputView inputVerifyEmail;
    private TextView sendVerifyCodeTvOfEmail;

    @Override
    protected int getContentView() {
        return R.layout.activity_anti_phishing_code_layout;
    }

    @Override
    protected AntiPhishingCodePresenter createPresenter() {
        return new AntiPhishingCodePresenter();
    }

    @Override
    protected AntiPhishingCodePresenter.AntiPhishingCodeUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        Intent intent = getIntent();
        hasSetAntiPhishingCode = intent.getBooleanExtra("hasSetAntiPhishingCode",false);
        viewFinder.textView(R.id.btn_sure).setText(getString(hasSetAntiPhishingCode ? R.string.string_reset : R.string.string_sure));
        viewFinder.textView(R.id.phishing_code_tips).setText(getString(R.string.string_phishing_code_tips));

        deepKnowVerify = DeepKnowVerify.getInstance(this);
        inputPhishingCode = viewFinder.find(R.id.phishing_code_input);
        inputVerifyEmail = viewFinder.find(R.id.email_verify_code_et);
        inputVerifyEmail.setPaddingRight(PixelUtils.dp2px(80));
        sendVerifyCodeTvOfEmail = viewFinder.textView(R.id.get_email_verify_code);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        deepKnowVerify.ignoreDPView(viewFinder.find(R.id.get_email_verify_code),"phishingCode");
        viewFinder.find(R.id.get_email_verify_code).setOnClickListener(this);
        viewFinder.find(R.id.btn_sure).setOnClickListener(this);

        inputVerifyEmail.addTextWatch(mTextWatcher);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.get_email_verify_code:
                String phishingCode1 = inputPhishingCode.getInputString();
                if (TextUtils.isEmpty(phishingCode1)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.string_input_anti_phishing_code_hint));
                    return;
                }
                if (!RegexUtils.checkLetterAndNum(phishingCode1,3,10)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.string_input_anti_phishing_code_hint));
                    return;
                }

                if (!NetWorkStatus.isConnected(this)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.hint_network_not_connect));
                    return;
                }
//                getUI().showProgressDialog("","");
//                deepKnowVerify.verify(baseSEListener);
                getPresenter().requestEmailVerifyCode();
                break;
            case R.id.btn_sure:
                String phishingCode = inputPhishingCode.getInputString();
                if (TextUtils.isEmpty(phishingCode)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.string_input_anti_phishing_code_hint));
                    return;
                } else {
                    inputPhishingCode.setError("");
                }
                String emailCode = inputVerifyEmail.getInputString();
                if (TextUtils.isEmpty(emailCode)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.string_input_email_verify_code));
                    return;
                } else {
                    inputVerifyEmail.setError("");
                }
                if (!NetWorkStatus.isConnected(this)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.hint_network_not_connect));
                    return;
                }
                getPresenter().requestSetAntiPhishingCode(phishingCode,emailCode);
                break;
        }
    }

//    private DeepSEListener baseSEListener = new DeepSEListener() {
//
//        /**
//         * SDK内部show loading dialog
//         */
//        @Override
//        public void onShowDialog() {
//            getUI().dismissProgressDialog();
//        }
//
//        @Override
//        public void onError(String errorCode, String error) {
////            DebugLog.i(TAG,"onError-->errorCode:"+errorCode+", error: "+error);
////            ToastUtils.showShort(error);
//            getUI().dismissProgressDialog();
//            ToastUtils.showShort(getString(R.string.string_net_exception)+errorCode);
//        }
//
//        /**
//         * 验证码Dialog关闭
//         * 1：webview的叉按钮关闭
//         * 2：点击屏幕外关闭
//         * 3：点击回退键关闭
//         *
//         * @param num
//         */
//        @Override
//        public void onCloseDialog(int num) {
////            DebugLog.i(TAG, "onCloseDialog-->" + num);
//        }
//
//        /**
//         * show 验证码webview
//         */
//        @Override
//        public void onDialogReady() {
////            DebugLog.i(TAG,"onDialogReady-->SDK show captcha webview dialog! ");
//        }
//
//        /**
//         * 验证成功
//         * @param token
//         */
//        @Override
//        public void onResult(String token) {
////            DebugLog.i(TAG,"onResult: "+token);
//            final String phishingCode = inputPhishingCode.getInputString();
//            if (TextUtils.isEmpty(phishingCode)) {
//                ToastUtils.showShort(AntiPhishingCodeActivity.this, getResources().getString(R.string.string_input_anti_phishing_code_hint));
//                return;
//            } else {
//                inputPhishingCode.setError("");
//            }
//            if (!NetWorkStatus.isConnected(AntiPhishingCodeActivity.this)) {
//                ToastUtils.showShort(AntiPhishingCodeActivity.this, getResources().getString(R.string.hint_network_not_connect));
//                return;
//            }
//            getPresenter().requestEmailVerifyCode(token);
//
//        }
//    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        deepKnowVerify.destroy();
    }

    @Override
    public void setAuthTv(String s) {
        sendVerifyCodeTvOfEmail.setText(s);
    }

    @Override
    public void setAuthTvStatus(boolean b) {
        sendVerifyCodeTvOfEmail.setEnabled(b);
        sendVerifyCodeTvOfEmail.setTextColor(b ? getResources().getColor(R.color.blue) : SkinColorUtil.getDark50(this));
    }

    /**
     * 编辑框监听器
     */
    private TextWatcher mTextWatcher = new TextWatcher() {

        /** 改变前*/
        @Override
        public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
        }

        /** 内容改变*/
        @Override
        public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            if (getPresenter().checkInputContentIsEmpty(inputPhishingCode,inputVerifyEmail)) {
                viewFinder.find(R.id.btn_sure).setEnabled(true);
            } else {
                viewFinder.find(R.id.btn_sure).setEnabled(false);
            }

        }

        @Override
        public void afterTextChanged(Editable s) {

        }
    };
}
