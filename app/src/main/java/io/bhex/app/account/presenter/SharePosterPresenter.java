/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: SharePosterPresenter.java
 *   @Date: 18-12-25 下午4:45
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.app.view.InputView;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.invite.InviteApi;
import io.bhex.sdk.invite.bean.InviteResponse;

public class SharePosterPresenter extends BasePresenter<SharePosterPresenter.SharePosterUI> {
    public interface SharePosterUI extends AppUI{

    }

    @Override
    public void onUIReady(BaseCoreActivity activity, SharePosterUI ui) {
        super.onUIReady(activity, ui);
    }

}
