/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AssetRecordFragmentPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import java.util.List;

import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.trade.AssetApi;
import io.bhex.sdk.trade.bean.AssetRecordResponse;
import io.bhex.sdk.trade.bean.RecordBeanResponse;
import io.bhex.app.base.AppUI;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BaseFragmentPresenter;


public class AssetRecordFragmentPresenter extends BaseFragmentPresenter<AssetRecordFragmentPresenter.AssetRecordFragmentUI> {


    private List<RecordBeanResponse.RecordItem> currentData;
    private String pageId="";


    public interface AssetRecordFragmentUI extends AppUI {

        void showRecordList(List<RecordBeanResponse.RecordItem> datas);

        void loadComplete();

        void loadEnd();

        void loadFailed();
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, AssetRecordFragmentUI ui) {
        super.onUIReady(activity, ui);
    }

    public void loadMore(int tabType, String tokenId, boolean isLoadMore) {
        getRecord(tabType, tokenId,  isLoadMore);
    }

    //获取记录
    public void getRecord(int tabType, String tokenId, final boolean isLoadMore) {
        if (isLoadMore) {
            if (currentData != null) {
                if (!currentData.isEmpty()) {
                    pageId = currentData.get(currentData.size()-1).getOrderId();
                }
            }
        }else {
            pageId ="";
        }

        AssetApi.RequestAssetRecord(tabType,tokenId,pageId, new SimpleResponseListener<RecordBeanResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
                if (isLoadMore) {
                    getUI().loadComplete();
                }

            }

            @Override
            public void onSuccess(RecordBeanResponse response) {
                super.onSuccess(response);

                if (CodeUtils.isSuccess(response, true)) {
                    List<RecordBeanResponse.RecordItem> datas = response.getArray();
                    if (datas != null) {
                        if (isLoadMore) {
                            currentData.addAll(datas);
                        }else{
                            currentData = datas;
                        }

                        getUI().showRecordList(currentData);
                        if (datas.size()<AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        }else{
                            getUI().loadComplete();
                        }
                    }else{
                        getUI().loadComplete();
                    }
                }else{
                    getUI().loadFailed();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
