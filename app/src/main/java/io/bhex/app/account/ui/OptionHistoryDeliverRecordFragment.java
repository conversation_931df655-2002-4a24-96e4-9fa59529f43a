/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OptionHistoryDeliverRecordFragment.java
 *   @Date: 1/25/19 4:32 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.account.ui;

import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.presenter.OptionHistoryDeliverRecordFragmentPresenter;
import io.bhex.app.base.BaseListFreshFragment;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.sdk.trade.bean.OptionDeliveryRecordResponse;

public class OptionHistoryDeliverRecordFragment extends BaseListFreshFragment<OptionHistoryDeliverRecordFragmentPresenter, OptionHistoryDeliverRecordFragmentPresenter.OptionHistoryDeliverRecordFragmentUI> implements OptionHistoryDeliverRecordFragmentPresenter.OptionHistoryDeliverRecordFragmentUI{
    @Override
    protected OptionHistoryDeliverRecordFragmentPresenter.OptionHistoryDeliverRecordFragmentUI getUI() {
        return this;
    }

    @Override
    protected OptionHistoryDeliverRecordFragmentPresenter createPresenter() {
        return new OptionHistoryDeliverRecordFragmentPresenter();
    }

    @Override
    public void showOrders(List<OptionDeliveryRecordResponse.OptionDeliveryRecordBean> currentOrders) {
        if (adapter == null) {
            adapter = new OptionDeliveryRecordAdapter(currentOrders);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this,recyclerView);
            adapter.setEnableLoadMore(true);

            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            recyclerView.setItemAnimator(new DefaultItemAnimator());

            adapter.setEmptyView(emptyView);
            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(currentOrders);
        }
    }

    public class OptionDeliveryRecordAdapter  extends BaseQuickAdapter<OptionDeliveryRecordResponse.OptionDeliveryRecordBean, BaseViewHolder> {

        public OptionDeliveryRecordAdapter(List<OptionDeliveryRecordResponse.OptionDeliveryRecordBean> data) {
            super(R.layout.item_option_delivery_record_layout, data);
        }


        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final OptionDeliveryRecordResponse.OptionDeliveryRecordBean itemModel) {
            try {

                String title = "";
                int color = 0;
                if (itemModel.available.startsWith("-")) {
                    title = KlineUtils.getOptionBuyOrSellTxt(mContext, false);
                    color = KlineUtils.getBuyOrSellColor(mContext, false);
                    baseViewHolder.getView(R.id.option_margin_order).setVisibility(View.VISIBLE);
                    baseViewHolder.getView(R.id.string_option_margin_order_title).setVisibility(View.VISIBLE);
                } else {
                    title = KlineUtils.getOptionBuyOrSellTxt(mContext, true);
                    color = KlineUtils.getBuyOrSellColor(mContext, true);
                    baseViewHolder.getView(R.id.option_margin_order).setVisibility(View.GONE);
                    baseViewHolder.getView(R.id.string_option_margin_order_title).setVisibility(View.GONE);
                }
                baseViewHolder.setText(R.id.order_buy_type, title);
                baseViewHolder.setTextColor(R.id.order_buy_type, color);
                baseViewHolder.setText(R.id.order_coin_name, itemModel.symbolName);

                baseViewHolder.setText(R.id.option_delivery_price, itemModel.settlementPrice + " " + itemModel.quoteTokenName);
                baseViewHolder.setText(R.id.option_strikePrice, itemModel.strikePrice + " " + itemModel.quoteTokenName);
                baseViewHolder.setText(R.id.option_maxPayOff, itemModel.maxPayOff + " " + itemModel.quoteTokenName + "/" + getString(R.string.string_option_unit));
                baseViewHolder.setText(R.id.option_hold_price, itemModel.averagePrice + " " + itemModel.quoteTokenName);
                baseViewHolder.setText(R.id.option_position, String.valueOf(Math.abs(Double.valueOf(itemModel.available))) + " " + getString(R.string.string_option_unit));

                baseViewHolder.setText(R.id.option_time_order, DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.settlementTime), "HH:mm:ss yyyy/MM/dd"));
                baseViewHolder.setText(R.id.option_changed, itemModel.changed + " " + itemModel.quoteTokenName);
                baseViewHolder.setText(R.id.option_changed_rate, "(" + itemModel.changedRate +  "%)");
                int colorChange = 0;
                if (!TextUtils.isEmpty(itemModel.changed) && itemModel.changed.startsWith("-")) {
                    colorChange = KlineUtils.getBuyOrSellColor(mContext, false);
                } else {
                    colorChange = KlineUtils.getBuyOrSellColor(mContext, true);
                }
                baseViewHolder.setTextColor(R.id.option_changed_rate, colorChange);

                baseViewHolder.setText(R.id.option_margin_order, itemModel.margin + " " + itemModel.quoteTokenName);
                baseViewHolder.getView(R.id.order_share).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //TODO 分享
                        IntentUtils.goShareProfit(mContext,null,itemModel);
                    }
                });

            } catch (Exception e) {

            }
        }
    }
}

