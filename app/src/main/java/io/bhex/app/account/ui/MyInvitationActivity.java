/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MyInvitationActivity.java
 *   @Date: 18-12-24 下午4:51
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.adapter.InviteRewardListAdapter;
import io.bhex.app.account.presenter.MyInvitationPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.share.ShareUtils;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.view.ShadowDrawable;
import io.bhex.app.view.TopBar;
import io.bhex.app.web.ui.WebActivity;
import io.bhex.baselib.images.CImageLoader;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.config.bean.LanguageListResponse;
import io.bhex.sdk.invite.bean.InviteInfoResponse;
import io.bhex.sdk.invite.bean.InviteResponse;
import io.bhex.sdk.invite.bean.InviteRewardListResponse;

public class MyInvitationActivity extends BaseActivity<MyInvitationPresenter,MyInvitationPresenter.MyInvitationUI> implements MyInvitationPresenter.MyInvitationUI, View.OnClickListener,BaseQuickAdapter.RequestLoadMoreListener, OnRefreshListener {
    private InviteResponse shareInfo;
    private TopBar topBar;
    private LayoutInflater layoutInflater;
    private View headerView;
    private TextView inviteTotal;
    private TextView directNum;
    private TextView inviteCode;
    private TextView indirectNum;
    private SmartRefreshLayout swipeRefresh;
    private RecyclerView recyclerView;
    private View emptyView;
    private InviteRewardListAdapter adapter;
    private ImageView topPoster;

    @Override
    protected int getContentView() {
        return R.layout.activity_my_invitation_layout;
    }

    @Override
    protected MyInvitationPresenter createPresenter() {
        return new MyInvitationPresenter();
    }

    @Override
    protected MyInvitationPresenter.MyInvitationUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        this.setTopbarNight(topBar);
        ShadowDrawable.setShadow(viewFinder.find(R.id.copy_btn));
        layoutInflater = LayoutInflater.from(this);
        headerView = layoutInflater.inflate(R.layout.header_invitation_layout, null);
        topPoster = headerView.findViewById(R.id.top_poster);
        ViewGroup.LayoutParams topPosterLayoutParams = topPoster.getLayoutParams();
        int screenWidth = PixelUtils.getScreenWidth();
        int topPosterHeight = screenWidth*412/1107;
        topPosterLayoutParams.width = screenWidth;
        topPosterLayoutParams.height = topPosterHeight;
        topPoster.setLayoutParams(topPosterLayoutParams);
        inviteTotal = headerView.findViewById(R.id.invite_total);
        directNum = headerView.findViewById(R.id.invite_direct_num);
        indirectNum = headerView.findViewById(R.id.invite_indirect_num);

        inviteCode = viewFinder.find(R.id.invite_code);

        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        swipeRefresh.setOnRefreshListener(this);
        recyclerView = viewFinder.find(R.id.recyclerView);
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
        emptyView.setLayoutParams(layoutParams);

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        headerView.findViewById(R.id.arrow).setOnClickListener(this);
        headerView.findViewById(R.id.arrow_reward).setOnClickListener(this);
        viewFinder.find(R.id.copy_btn).setOnClickListener(this);
        viewFinder.find(R.id.btn_share_invite_friends).setOnClickListener(this);
        viewFinder.find(R.id.btn_poster).setOnClickListener(this);
    }

    @Override
    public void showInviteInfo(InviteInfoResponse response) {
//        final String ruleUrl = response.getRuleUrl();
//        if (!TextUtils.isEmpty(ruleUrl)) {
//            topBar.setRightText(getString(R.string.string_title_rule));
//            topBar.setRightOnClickListener(new View.OnClickListener() {
//                @Override
//                public void onClick(View v) {
//                    if (!TextUtils.isEmpty(ruleUrl)) {
//                        WebActivity.runActivity(MyInvitationActivity.this,"",ruleUrl);
//                    }
//                }
//            });
//        }

        InviteInfoResponse.InviteInfoDTOBean inviteInfoDTO = response.getInviteInfoDTO();
        if (inviteInfoDTO != null) {
            inviteTotal.setText(inviteInfoDTO.getInviteCount()+"");
            directNum.setText(inviteInfoDTO.getInviteDirectVaildCount()+"");
            indirectNum.setText(inviteInfoDTO.getInviteIndirectVaildCount()+"");
            inviteCode.setText(response.getInviteCode());
        }
    }

    @Override
    public void showShareInfo(InviteResponse response) {
        shareInfo = response;
    }

    @Override
    public void onClick(View v) {
        switch(v.getId()){
            case R.id.arrow:
                //邀请记录
                IntentUtils.goRewardDetail(this,true);
                break;
            case R.id.arrow_reward:
                //返佣明细更多
                IntentUtils.goRewardDetail(this,false);
                break;
            case R.id.copy_btn:
                CommonUtil.copyText(this, viewFinder.textView(R.id.invite_code).getText().toString());
                break;
            case R.id.btn_share_invite_friends:
//                ShareManager.getInstance().shareWeb(shareInfo.getShareTitle(),shareInfo.getShareContent(),shareInfo.getShareUrl());

                if (shareInfo != null) {
                    ShareUtils.shareWeb(this,shareInfo.getShareUrl(),shareInfo.getShareTitle(),shareInfo.getShareContent());
                }
                break;
            case R.id.btn_poster:
                if (shareInfo != null) {
                    IntentUtils.goInvitePoster(this,shareInfo);
                }

                break;
        }
    }

    @Override
    public void showRewardList(List<InviteRewardListResponse.RewardBean> rewardList) {
        if (rewardList != null) {
            if (adapter == null) {
                adapter = new InviteRewardListAdapter(rewardList);
                adapter.isFirstOnly(false);
                adapter.setOnLoadMoreListener(this,recyclerView);
                adapter.setEnableLoadMore(true);

                recyclerView.setLayoutManager(new LinearLayoutManager(this));
                recyclerView.setItemAnimator(new DefaultItemAnimator());

                adapter.addHeaderView(headerView);
                adapter.setEmptyView(emptyView);
                adapter.setHeaderAndEmpty(true);
                recyclerView.setAdapter(adapter);
            } else {
                adapter.setNewData(rewardList);
            }
        }
    }

    @Override
    public void loadMoreComplete() {
        if (adapter != null) {
            adapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {
        if (adapter != null) {
            adapter.loadMoreFail();
        }
    }

    @Override
    public void showLanguageList(LanguageListResponse response) {
        if (response != null) {
            LanguageListResponse.DataBean data = response.getData();
            if (data != null && data.getList() != null) {
                LanguageListResponse.DataBean.ListBean list = data.getList();
                final String ruleUrl = list.getInvite_activity_rule_url();
                if (!TextUtils.isEmpty(ruleUrl)) {
                    topBar.setRightText(getString(R.string.string_title_rule));
                    topBar.setRightOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (!TextUtils.isEmpty(ruleUrl)) {
                                WebActivity.runActivity(MyInvitationActivity.this,"",ruleUrl);
                            }
                        }
                    });
                }

                String topPosterUrl = list.getInvite_title_pic_app();
                if (!TextUtils.isEmpty(topPosterUrl)) {
                    CImageLoader.getInstance().load(topPoster,topPosterUrl,R.mipmap.icon_default_banner);
                }
            }
        }
    }

    @Override
    public void loadEnd() {
        if (adapter != null) {
            adapter.loadMoreEnd();
        }
    }

    @Override
    public void onLoadMoreRequested() {
        getPresenter().getRewardList(true);
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        getPresenter().getRewardList(false);
        refreshLayout.finishRefresh(1000);
    }
}
