package io.bhex.app.account.presenter;

import android.os.CountDownTimer;
import android.text.TextUtils;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.view.InputView;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.security.SecurityApi;
import io.bhex.sdk.security.bean.OrderIdParamResponse;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-07-13
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class AntiPhishingCodePresenter extends BasePresenter<AntiPhishingCodePresenter.AntiPhishingCodeUI> {
    public interface AntiPhishingCodeUI extends AppUI{
        void setAuthTvStatus(boolean b);

        void setAuthTv(String s);
    }

    private String orderIdOfEmail="";

    public void requestEmailVerifyCode() {
        SecurityApi.sendAntiPhishingCodeVerifyCode(true, new SimpleResponseListener<OrderIdParamResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("",getString(R.string.string_loading_hint_text));
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(OrderIdParamResponse data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data,true)) {
                    orderIdOfEmail = data.getOrderId();
                    getUI().setAuthTvStatus(false);
                    timerOfEmail.start();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);

            }
        });
    }


    /**
     * 设置钓鱼码
     * @param phishingCode
     * @param emailCode
     */
    public void requestSetAntiPhishingCode(String phishingCode, String emailCode) {
        if(TextUtils.isEmpty(orderIdOfEmail)) {
            ToastUtils.showShort(getActivity(),getString(R.string.string_verify_code_invalid));
            return;
        }
        SecurityApi.setAntiPhishingCode(phishingCode,orderIdOfEmail,emailCode,new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    ToastUtils.showShort(getActivity(),getString(R.string.string_set_success));
                    getActivity().finish();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(getActivity(),getString(R.string.string_set_failed));
            }
        });


    }

    public boolean checkInputContentIsEmpty(InputView inputPhishingCode, InputView inputVerifyEmail) {
        String phishingCode = inputPhishingCode.getInputString();
        String codeOfemail = inputVerifyEmail.getInputString();

        return !TextUtils.isEmpty(phishingCode) && !TextUtils.isEmpty(codeOfemail);
    }

    /**
     * 倒计时
     */
    private CountDownTimer timerOfEmail = new CountDownTimer(AppData.DOWN_TIME_CODE, AppData.DOWN_TIME_INTERVAL_CODE) {
        @Override
        public void onTick(long millisUntilFinished) {
            getUI().setAuthTv((millisUntilFinished / 1000)
                    + getActivity().getResources().getString(
                    R.string.after_second));
        }

        @Override
        public void onFinish() {
            getUI().setAuthTvStatus(true);
            getUI().setAuthTv(getResources().getString(
                    R.string.string_get_auth_code));
        }
    };

}
