/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: WithDrawResultPresenter.java
 *   @Date: 19-8-27 上午11:45
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BasePresenter;

public class WithDrawResultPresenter extends BasePresenter<WithDrawResultPresenter.WithDrawResultUI> {
    public interface WithDrawResultUI extends AppUI {

    }
}
