/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: TwoVerificaionActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import io.bhex.app.BuildConfig;
import io.bhex.app.R;
import io.bhex.app.utils.KeyBoardUtil;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.sdk.trade.bean.TwoVerifyBean;
import io.bhex.app.account.presenter.TwoVerificaionPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.baselib.constant.Fields;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.view.InputView;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;



public class TwoVerificaionActivity extends BaseActivity<TwoVerificaionPresenter,TwoVerificaionPresenter.TwoVerificaionUI> implements TwoVerificaionPresenter.TwoVerificaionUI, View.OnClickListener, TextWatcher {
    private static final String FROM_LOGIN = "fromlogin";
    private static final int VERIFY_REQUEST_CODE = 0x18;
    private View verifyCodeRela;
    private TextView sendVerifyCodeTv;
    private Button btnSubmit;
    private InputView verifyEt;
    private String from="";
    private String requestId="";
    private boolean bindGA;
    private boolean bindMobile;
    private boolean bindEmail;
    private boolean isVerifyEmail;
    //选择验证的模式，如果绑定了GA默认就是选择GA验证 true
    private boolean isSelectGAVerify=true;
    //短信类型
    private String type="";
    private String currentOrderId="";
    private String token="";
    private InputView financePasswd;
    //是否来自提现
    private boolean isFromWithdraw=false;


    @Override
    protected int getContentView() {
        return R.layout.activity_two_verify_layout;
    }

    @Override
    protected TwoVerificaionPresenter createPresenter() {
        return new TwoVerificaionPresenter();
    }

    @Override
    protected TwoVerificaionPresenter.TwoVerificaionUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        Intent intent = getIntent();
        from = intent.getStringExtra("from");
        //来自登录的requestId
        requestId = intent.getStringExtra("requestId");
        type = intent.getStringExtra("type");
        bindGA = intent.getBooleanExtra("bindGA", false);
         bindMobile = intent.getBooleanExtra("bindMobile", false);
         bindEmail = intent.getBooleanExtra("bindEmail", false);
        //当前要验证状态
//         isVerifyEmail = intent.getBooleanExtra("isVerifyEmail", true);
         isVerifyEmail = bindEmail;

        verifyCodeRela =  viewFinder.find(R.id.verify_code_rela);

        if (bindGA) {
            viewFinder.find(R.id.tab_a_rela).setVisibility(View.VISIBLE);
            //绑定了GA默认就是选择GA验证 true
            isSelectGAVerify = true;
            viewFinder.find(R.id.tab_a_indicator).setVisibility(View.VISIBLE);
            viewFinder.find(R.id.tab_b_indicator).setVisibility(View.GONE);
            viewFinder.find(R.id.paste).setVisibility(View.VISIBLE);
            viewFinder.find(R.id.get_verify_code).setVisibility(View.GONE);
        }else{
            viewFinder.find(R.id.tab_a_rela).setVisibility(View.GONE);
            isSelectGAVerify = false;
            viewFinder.find(R.id.tab_a_indicator).setVisibility(View.GONE);
            viewFinder.find(R.id.tab_b_indicator).setVisibility(View.VISIBLE);
            viewFinder.find(R.id.paste).setVisibility(View.GONE);
            viewFinder.find(R.id.get_verify_code).setVisibility(View.VISIBLE);
        }


        if (!isVerifyEmail && bindMobile) {
            viewFinder.find(R.id.tab_b_rela).setVisibility(View.VISIBLE);
            viewFinder.textView(R.id.tab_b_name).setText(getString(R.string.string_sms_auth));
        }else if(isVerifyEmail && bindEmail){
            viewFinder.find(R.id.tab_b_rela).setVisibility(View.VISIBLE);
            viewFinder.textView(R.id.tab_b_name).setText(getString(R.string.string_email_auth));
        }else{
            viewFinder.find(R.id.tab_b_rela).setVisibility(View.GONE);
        }

        financePasswd = viewFinder.find(R.id.verify_finance_passwd);
        if (from.equals("from_withdraw")) {
            isFromWithdraw = true;
            financePasswd.setVisibility(View.VISIBLE);
        }

        sendVerifyCodeTv =  viewFinder.textView(R.id.get_verify_code);
        btnSubmit = viewFinder.find(R.id.btn_submit);
        verifyEt = viewFinder.find(R.id.verify_code_et);
        verifyEt.setPaddingLeft(PixelUtils.dp2px(8));
        verifyEt.setPaddingRight(PixelUtils.dp2px(80));
        verifyEt.setInputHint(getString(R.string.input_verify));
        verifyEt.setInputMode(InputView.SILENTMODE);

        if (!bindGA&&!bindEmail&&!bindMobile){
            btnSubmit.setVisibility(View.GONE);
            ToastUtils.showShort(this, getString(R.string.verify_service_exception));
        }

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.tab_a_rela).setOnClickListener(this);
        viewFinder.find(R.id.tab_b_rela).setOnClickListener(this);
        viewFinder.find(R.id.btn_submit).setOnClickListener(this);
        viewFinder.find(R.id.paste).setOnClickListener(this);
        viewFinder.find(R.id.get_verify_code).setOnClickListener(this);
        verifyEt.addTextWatch(this);
    }


    @Override
    public void setAuthTv(String s) {
        sendVerifyCodeTv.setText(s);
    }

    @Override
    public void setAuthTvStatus(boolean b) {
        sendVerifyCodeTv.setEnabled(b);
        sendVerifyCodeTv.setTextColor(b ? getResources().getColor(R.color.blue) : SkinColorUtil.getDark50(this));
    }

    @Override
    public void updateOrderId(String orderId) {
        currentOrderId = orderId;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.mobile_code_linear:
                //选择国家区号
                IntentUtils.goMobileCodeList(this, Fields.REQUEST_CODE_FIND, Fields.FIELD_COUNTRY_PARAM_TYPE_AREA_CODE);
                break;
            case R.id.paste:
                verifyEt.setInputString(CommonUtil.pasteText(this));
                break;
            case R.id.get_verify_code:
                if (from.equals(FROM_LOGIN)) {
                    //来自登录，不需要人机校验
                    getPresenter().verifyBeforLogin(isVerifyEmail,requestId);
                }else{
                    //其他的需要人机校验，然后在调用发送验证码
                    getPresenter().verifyAfterLogin(isVerifyEmail,token,type);
                }
                break;
            case R.id.btn_submit:
                submit(isSelectGAVerify,isVerifyEmail,verifyEt,currentOrderId);
                break;

            case R.id.tab_a_rela:
                //tab A默认为GA  ，GA不需要点击发送验证码按钮
                isSelectGAVerify = true;
                viewFinder.find(R.id.paste).setVisibility(View.VISIBLE);
                viewFinder.find(R.id.get_verify_code).setVisibility(View.GONE);
                viewFinder.find(R.id.tab_a_indicator).setVisibility(View.VISIBLE);
                viewFinder.find(R.id.tab_b_indicator).setVisibility(View.GONE);
                viewFinder.textView(R.id.tab_a_name).setTextColor(getResources().getColor(R.color.blue));
                viewFinder.textView(R.id.tab_b_name).setTextColor(getResources().getColor(CommonUtil.isBlackMode()?R.color.dark_night:R.color.dark));
                break;
            case R.id.tab_b_rela:
                //短信或者邮箱验证需要发送验证码按钮
                isSelectGAVerify=false;
                viewFinder.find(R.id.paste).setVisibility(View.GONE);
                viewFinder.find(R.id.get_verify_code).setVisibility(View.VISIBLE);
                viewFinder.find(R.id.tab_a_indicator).setVisibility(View.GONE);
                viewFinder.find(R.id.tab_b_indicator).setVisibility(View.VISIBLE);
                viewFinder.textView(R.id.tab_a_name).setTextColor(getResources().getColor(CommonUtil.isBlackMode()?R.color.dark_night:R.color.dark));
                viewFinder.textView(R.id.tab_b_name).setTextColor(getResources().getColor(R.color.blue));
                break;
        }
    }

    /**
     * 提交验证结果
     * @param isSelectGAVerify
     * @param isVerifyEmail
     * @param verifyEt
     */
    private void submit(boolean isSelectGAVerify, boolean isVerifyEmail, InputView verifyEt, String orderId) {
        if (!isSelectGAVerify&&TextUtils.isEmpty(orderId)) {
            ToastUtils.showShort(this, getString(R.string.string_verify_code_invalid));
            return;
        }
        String financePwd = financePasswd.getInputString();
        if (isFromWithdraw) {
            if (TextUtils.isEmpty(financePwd)) {
                ToastUtils.showShort(this, getString(R.string.input_finance_passwd));
                return;
            }
        }

        String verifyCode = verifyEt.getInputString();
        if (TextUtils.isEmpty(verifyCode)) {
            ToastUtils.showShort(this, getString(R.string.input_verify));
            return;
        }

        Intent intent = new Intent();
        TwoVerifyBean twoVerifyBean = new TwoVerifyBean();
        twoVerifyBean.setAuth_type(isSelectGAVerify?3:isVerifyEmail?2:1);
        twoVerifyBean.setCaptcha_response(token);
        twoVerifyBean.setCaptcha_id(BuildConfig.DEEPKNOW_ID);
        twoVerifyBean.setOrder_id(orderId);
        twoVerifyBean.setRequest_id(requestId);
        twoVerifyBean.setVerify_code(verifyCode);
        twoVerifyBean.setTradePwd(financePwd);
        intent.putExtra("twoVerify",twoVerifyBean);
        setResult(RESULT_OK,intent);
        finish();
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
        checkEditText();
    }

    @Override
    public void afterTextChanged(Editable s) {

    }

    private void checkEditText() {
        String verifyCode = verifyEt.getInputString();
        if (TextUtils.isEmpty(verifyCode)){
            btnSubmit.setEnabled(false);
        }else{
            btnSubmit.setEnabled(true);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(requestCode == VERIFY_REQUEST_CODE && resultCode == RESULT_OK){
            if (data != null) {
                token = data.getStringExtra("token");

                getPresenter().verifyAfterLogin(isVerifyEmail,token,type);
            }
        }
    }
}
