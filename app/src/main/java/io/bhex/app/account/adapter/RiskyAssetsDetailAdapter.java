/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AssetRecordAdapter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.adapter;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.DateUtils;
import io.bhex.sdk.trade.bean.FeeBeanResponse;

/**
 * ================================================
 * 描   述：单个币对的转账记录
 * ================================================
 */

public class RiskyAssetsDetailAdapter extends BaseQuickAdapter<FeeBeanResponse.RiskBalanceBean, BaseViewHolder> {


    public RiskyAssetsDetailAdapter(List<FeeBeanResponse.RiskBalanceBean> data) {
        super(R.layout.item_risky_assets_detail_layout, data);
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final FeeBeanResponse.RiskBalanceBean itemModel) {


        baseViewHolder.setText(R.id.amount, itemModel.getAmount() +" "+itemModel.getTokenName() +" ≈ "+itemModel.getBtcAmount()+"BTC");
        baseViewHolder.setText(R.id.desc, itemModel.getReason());
//        baseViewHolder.setText(R.id.item_asset_record_status, mContext.getString(R.string.string_success));
        baseViewHolder.setText(R.id.time, DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getCreatedAt()), "HH:mm:ss yyyy/MM/dd"));
    }

}