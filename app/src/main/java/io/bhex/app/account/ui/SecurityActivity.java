/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: SecurityActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.content.Intent;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.Toolbar;

import com.google.android.material.appbar.CollapsingToolbarLayout;

import io.bhex.app.R;
import io.bhex.app.account.presenter.SecurityPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.gesture.lock.GestureEditActivity;
import io.bhex.app.safe.SafeUilts;
import io.bhex.app.safe.bean.FingerSwitcher;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.core.SPEx;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;

/**
 * ================================================
 * 描   述：安全中心
 * ================================================
 */

public class SecurityActivity extends BaseActivity<SecurityPresenter,SecurityPresenter.SecurityUI> implements SecurityPresenter.SecurityUI, View.OnClickListener, CompoundButton.OnCheckedChangeListener {
    private CheckBox gestureCb;
    private CheckBox fingerCb;
    private UserInfoBean userInfo;
    private boolean bindGA;
    private String email;
    private String mobile;

    @Override
    protected int getContentView() {
        return R.layout.activity_security_layout;
    }

    @Override
    protected SecurityPresenter createPresenter() {
        return new SecurityPresenter();
    }

    @Override
    protected SecurityPresenter.SecurityUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        Toolbar toolbar= findViewById(R.id.toolbar);
        CollapsingToolbarLayout collapsingToolbarLayout= findViewById(R.id.collapsing_toolbar);
        collapsingToolbarLayout.setCollapsedTitleTextColor(SkinColorUtil.getDark(this));
        collapsingToolbarLayout.setExpandedTitleColor(SkinColorUtil.getDark(this));
        //显示返回按钮
        setSupportActionBar(toolbar);
        ActionBar actionBar=getSupportActionBar();
        if (actionBar!=null){
            actionBar.setDisplayHomeAsUpEnabled(true);
        }

        userInfo = UserManager.getInstance().getUserInfo();
        setUserInfoStatus(userInfo);

        gestureCb = viewFinder.find(R.id.gesture_switch);
        fingerCb = viewFinder.find(R.id.fingerprint_switch);


        if (!SafeUilts.isSupportFinger(this)) {
            viewFinder.find(R.id.fingerprint_rela).setVisibility(View.GONE);
        }

        // 硬编码黑白版Toolbar标题栏title字色
        collapsingToolbarLayout.setCollapsedTitleTextColor(SkinColorUtil.getDark(this));
        collapsingToolbarLayout.setExpandedTitleColor(SkinColorUtil.getDark(this));
    }

    private void setUserInfoStatus(UserInfoBean userInfo) {
        if (userInfo != null) {
            bindGA = userInfo.isBindGA();
            if (bindGA) {
                viewFinder.textView(R.id.auth_ga_status).setText(getString(R.string.string_binded));
                viewFinder.textView(R.id.auth_ga_status).setTextColor(SkinColorUtil.getDark50(this));
            }else{
                viewFinder.textView(R.id.auth_ga_status).setText(getString(R.string.string_not_bind));
                viewFinder.textView(R.id.auth_ga_status).setTextColor(getResources().getColor(R.color.red));
            }
            email = userInfo.getEmail();
            if (!TextUtils.isEmpty(email)) {
                viewFinder.textView(R.id.auth_email_status).setText(getString(R.string.string_binded));
                viewFinder.textView(R.id.auth_email_status).setTextColor(SkinColorUtil.getDark50(this));
            }else{
                viewFinder.textView(R.id.auth_email_status).setText(getString(R.string.string_not_bind));
                viewFinder.textView(R.id.auth_email_status).setTextColor(getResources().getColor(R.color.red));
            }
            mobile = userInfo.getMobile();
            if (!TextUtils.isEmpty(mobile)) {
                viewFinder.textView(R.id.auth_msm_status).setText(getString(R.string.string_binded));
                viewFinder.textView(R.id.auth_msm_status).setTextColor(SkinColorUtil.getDark50(this));
            }else{
                viewFinder.textView(R.id.auth_msm_status).setText(getString(R.string.string_not_bind));
                viewFinder.textView(R.id.auth_msm_status).setTextColor(getResources().getColor(R.color.red));
            }
            if (userInfo.isBindTradePwd()) {
                viewFinder.textView(R.id.finance_passed_oporate).setText(getString(R.string.string_modify));
                viewFinder.textView(R.id.finance_passed_oporate).setTextColor(SkinColorUtil.getDark50(this));
            }else{
                viewFinder.textView(R.id.finance_passed_oporate).setText(getString(R.string.string_set));
                viewFinder.textView(R.id.finance_passed_oporate).setTextColor(getResources().getColor(R.color.red));
            }
            if (userInfo.isBindPassword()) {
                viewFinder.textView(R.id.passwd_status).setText(getString(R.string.string_modify));
                viewFinder.textView(R.id.passwd_status).setTextColor(SkinColorUtil.getDark50(this));
            }else{
                viewFinder.textView(R.id.passwd_status).setText(getString(R.string.string_set));
                viewFinder.textView(R.id.passwd_status).setTextColor(getResources().getColor(R.color.red));
            }
            String antiPhishingCode = userInfo.getAntiPhishingCode();
            if (!TextUtils.isEmpty(antiPhishingCode)) {
                viewFinder.textView(R.id.phishing_code_status).setText(antiPhishingCode);
                viewFinder.textView(R.id.phishing_code_status).setTextColor(SkinColorUtil.getDark50(this));
            }else{
                viewFinder.textView(R.id.phishing_code_status).setText(getString(R.string.string_set));
                viewFinder.textView(R.id.phishing_code_status).setTextColor(getResources().getColor(R.color.red));
            }
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()){
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }
    @Override
    protected void onResume() {
        super.onResume();
        gestureCb.setOnCheckedChangeListener(null);
        fingerCb.setOnCheckedChangeListener(null);
        String gesturePwd = SPEx.get(AppData.SPKEY.GESTURE_PWD_KEY,"");
        if (!TextUtils.isEmpty(gesturePwd)) {
            gestureCb.setChecked(true);
            gestureCb.setButtonDrawable(R.mipmap.icon_switch_button_on);
        }else{
            gestureCb.setChecked(false);
            gestureCb.setButtonDrawable(R.mipmap.icon_switch_button_off);
        }

        boolean fingerOpen = UserManager.getInstance().isFingerSetOpenStatus();
        fingerCb.setChecked(fingerOpen);
        if (fingerOpen) {
            fingerCb.setButtonDrawable(R.mipmap.icon_switch_button_on);
        }else{
            fingerCb.setButtonDrawable(R.mipmap.icon_switch_button_off);
        }
        gestureCb.setOnCheckedChangeListener(this);
        fingerCb.setOnCheckedChangeListener(this);
        if(UserInfo.isLogin())
            getPresenter().getUserInfo();
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.passwd_rela).setOnClickListener(this);
        viewFinder.find(R.id.finance_passwd_rela).setOnClickListener(this);
        viewFinder.find(R.id.ga_rela).setOnClickListener(this);
        viewFinder.find(R.id.msm_rela).setOnClickListener(this);
        viewFinder.find(R.id.email_rela).setOnClickListener(this);
        viewFinder.find(R.id.phishing_code_rela).setOnClickListener(this);


    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.finance_passwd_rela:
                if (userInfo != null) {
                    IntentUtils.goFinancePasswd(this,userInfo.isBindTradePwd());
                }else{
                    getPresenter().getUserInfo();
                }
                break;
            case R.id.passwd_rela:
                if (userInfo != null) {
                    if (userInfo.isBindPassword()) {
                        IntentUtils.goUpdatePasswd(this);
                    }else{
                        IntentUtils.goSetPwd(this);
                    }
                }else{
                    getPresenter().getUserInfo();
                }
                break;
            case R.id.ga_rela:

                if (!bindGA) {
                    IntentUtils.goBindGAHelp(this);
                }else{
                    IntentUtils.goBindInfo(this,"ga",mobile);
                }
                break;
            case R.id.msm_rela:
                if (TextUtils.isEmpty(mobile)) {
                    IntentUtils.goBindMobile(this);
                }else{
                    IntentUtils.goBindInfo(this,"mobile",mobile);
                }
                break;
            case R.id.email_rela:
                if (TextUtils.isEmpty(email)) {
                    IntentUtils.goBindEmail(this);
                }else{
                    IntentUtils.goBindInfo(this,"email",email);
                }
                break;
            case R.id.phishing_code_rela:
                if (TextUtils.isEmpty(email)) {
                    ToastUtils.showShort(getString(R.string.string_bing_email_please));
                }else{
                    if (userInfo != null) {
                        if (TextUtils.isEmpty(userInfo.getAntiPhishingCode())) {
                            IntentUtils.goSetAntiPhishingCode(this,false);
                        }else{
                            IntentUtils.goSetAntiPhishingCode(this,true);
                        }
                    }
                }
                break;
        }
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        switch (buttonView.getId()){
            case R.id.gesture_switch:
                if (isChecked) {
                    gestureCb.setButtonDrawable(R.mipmap.icon_switch_button_off);
                    Intent intent = new Intent(this, GestureEditActivity.class);
                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    startActivity(intent);
                }else{
                    SPEx.remove(AppData.SPKEY.GESTURE_PWD_KEY);
                    ToastUtils.showShort(SecurityActivity.this, getString(R.string.string_gesture_close_success));
                    gestureCb.setButtonDrawable(R.mipmap.icon_switch_button_off);
                }
                 break;
            case R.id.fingerprint_switch:
                if (isChecked) {
                    if (SafeUilts.isFinger(this)) {
                        IntentUtils.openFinger(this,AppData.INTENT.REQUEST_CODE_FINGER_OPEN,AppData.INTENT.FINGER_CALLER_SECURITY);

//                        SPEx.set(AppData.SPKEY.FINGER_PWD_KEY,true);
//                        ToastUtils.showShort(getString(R.string.string_fingerprint_open_success));
//                        fingerCb.setButtonDrawable(R.mipmap.icon_switch_button_on);
                    }else{
                        fingerCb.setButtonDrawable(R.mipmap.icon_switch_button_off);
                        fingerCb.setChecked(false);
                    }
                }else{
                    UserManager.getInstance().updateFingerSetOpenStatus(false);
                    ToastUtils.showShort(SecurityActivity.this,getString(R.string.string_fingerprint_close_success));
                    fingerCb.setButtonDrawable(R.mipmap.icon_switch_button_off);
                }
                 break;
        }
    }

    @Override
    public void getUserInfoSuccess(UserInfoBean data) {
        if (data != null) {
            userInfo = data;
            setUserInfoStatus(userInfo);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode==AppData.INTENT.REQUEST_CODE_FINGER_OPEN&&resultCode==RESULT_OK){
           FingerSwitcher fingerauth = (FingerSwitcher) data.getSerializableExtra("fingerauth");
           if (fingerauth.isAuthSuccess()) {
               UserManager.getInstance().updateFingerSetOpenStatus(true);
               ToastUtils.showShort(SecurityActivity.this,getString(R.string.string_fingerprint_open_success));
               fingerCb.setButtonDrawable(R.mipmap.icon_switch_button_on);
           }
        }
    }
}
