/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OptionAssetDetailFragment.java
 *   @Date: 2/15/19 2:54 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.account.ui;

import android.os.Bundle;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;

import java.util.List;

import io.bhex.app.account.adapter.AssetRecordAdapter;
import io.bhex.app.account.presenter.OptionAssetDetailPresenter;
import io.bhex.app.base.BaseListFreshFragment;
import io.bhex.sdk.trade.bean.AssetRecordResponse;
import io.bhex.sdk.trade.bean.OptionAssetListResponse;

import static io.bhex.baselib.constant.AppData.INTENT.KEY_ASSET;

public class OptionAssetDetailFragment extends BaseListFreshFragment<OptionAssetDetailPresenter, OptionAssetDetailPresenter.OptionAssetDetailUI> implements OptionAssetDetailPresenter.OptionAssetDetailUI {
    @Override
    protected OptionAssetDetailPresenter.OptionAssetDetailUI getUI() {
        return this;
    }

    private OptionAssetListResponse.OptionAssetBean assetItemBean;
    @Override
    protected OptionAssetDetailPresenter createPresenter() {
        return new OptionAssetDetailPresenter();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if(getArguments() != null){
            try {

                assetItemBean = (OptionAssetListResponse.OptionAssetBean)getArguments().getSerializable(KEY_ASSET);
            }
            catch (Exception e){

            }
        }
    }

    @Override
    public void showOrders(List<AssetRecordResponse.RecordBean> currentOrders) {
        if (adapter == null) {
            adapter = new AssetRecordAdapter(currentOrders);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this, recyclerView);
            adapter.setEnableLoadMore(true);

            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            recyclerView.setItemAnimator(new DefaultItemAnimator());

            adapter.setEmptyView(emptyView);
            recyclerView.setAdapter(adapter);

        } else {
            adapter.setNewData(currentOrders);
        }
    }

    @Override
    public String getToken() {
        if(assetItemBean != null)
            return assetItemBean.tokenId;
        return "";
    }

}
