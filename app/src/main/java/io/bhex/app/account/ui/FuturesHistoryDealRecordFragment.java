/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FuturesHistoryDealRecordFragment.java
 *   @Date: 19-7-26 下午6:36
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;


import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;

import java.util.List;

import io.bhex.app.account.presenter.FuturesHistoryDealRecordFragmentPresenter;
import io.bhex.app.base.BaseListFreshFragment;
import io.bhex.app.trade.adapter.FuturesHistoryTradeAdapter;
import io.bhex.sdk.trade.futures.bean.DeliveryOrder;

public class FuturesHistoryDealRecordFragment extends BaseListFreshFragment<FuturesHistoryDealRecordFragmentPresenter, FuturesHistoryDealRecordFragmentPresenter.FuturesHistoryDealRecordFragmentUI> implements FuturesHistoryDealRecordFragmentPresenter.FuturesHistoryDealRecordFragmentUI{
    @Override
    protected FuturesHistoryDealRecordFragmentPresenter.FuturesHistoryDealRecordFragmentUI getUI() {
        return this;
    }

    @Override
    protected FuturesHistoryDealRecordFragmentPresenter createPresenter() {
        return new FuturesHistoryDealRecordFragmentPresenter();
    }

    @Override
    public void showOrders(List<DeliveryOrder> currentOrders) {
        if (adapter == null) {
            adapter = new FuturesHistoryTradeAdapter(getActivity(),currentOrders);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this,recyclerView);
            adapter.setEnableLoadMore(true);

            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            recyclerView.setItemAnimator(new DefaultItemAnimator());

            adapter.setEmptyView(emptyView);
            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(currentOrders);
        }
    }

}


