package io.bhex.app.account.presenter;

import android.text.TextUtils;

import java.io.File;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.AccountInfoApi;
import io.bhex.sdk.account.KycApi;
import io.bhex.sdk.account.bean.UserVerifyInfo;
import io.bhex.sdk.account.bean.kyc.KycVideoUploadResponse;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-11-14
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class KycLevelVideoPresenter extends BasePresenter<KycLevelVideoPresenter.KycLevelVideoUI> {

    public interface KycLevelVideoUI extends AppUI{

        void showVerifyInfo(UserVerifyInfo response);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, KycLevelVideoUI ui) {
        super.onUIReady(activity, ui);
//        getVerifyInfo();
    }

    private void getVerifyInfo() {
        AccountInfoApi.RequestGetUserVerifyInfo(new SimpleResponseListener<UserVerifyInfo>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(UserVerifyInfo response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showVerifyInfo(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public void uploadFile(String videoFile) {
        if (!TextUtils.isEmpty(videoFile)) {
            File file = new File(videoFile);
            if (file.exists()) {
                KycApi.RequestKycVideoUpload(file,new SimpleResponseListener<KycVideoUploadResponse>(){
                    @Override
                    public void onBefore() {
                        super.onBefore();
                        getUI().showProgressDialog("","");
                    }

                    @Override
                    public void onFinish() {
                        super.onFinish();
                        getUI().dismissProgressDialog();
                    }

                    @Override
                    public void onSuccess(KycVideoUploadResponse response) {
                        super.onSuccess(response);
                        if (CodeUtils.isSuccess(response,true)) {
                            String videoUrl = response.getUrl();
                            if (!TextUtils.isEmpty(videoUrl)) {
                                submitVideoAuth(videoUrl);
                            }else{
                                ToastUtils.showShort(getString(R.string.string_upload_failed));
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable error) {
                        super.onError(error);
                    }
                });
            }
        }
    }

    /**
     * 提交视频认证信息
     * @param videoUrl
     */
    private void submitVideoAuth(String videoUrl) {
        KycApi.RequestKycVideoVerify(videoUrl,new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    ToastUtils.showShort(getString(R.string.string_submit_success));
                    getActivity().finish();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });

    }
}
