/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: ResetPasswdPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import android.text.TextUtils;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.app.utils.RegexUtils;
import io.bhex.app.view.InputView;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.Utils.CookieUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.AccountInfoApi;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.FindPasswordRequest;


public class ResetPasswdPresenter extends BasePresenter<ResetPasswdPresenter.ResetPasswdUI> {

    /**
     * 找回密码
     * @param passwdNew
     * @param passwdNew2
     */
    public void resetPasswd(boolean isEmail,String account,String nationalCode,String orderId,InputView passwdNew, InputView passwdNew2) {
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }

        String newPasswd = passwdNew.getInputString();
        if (TextUtils.isEmpty(newPasswd)) {
            ToastUtils.showShort(getActivity(), getString(R.string.string_input_new_passwd));
            return;
        }
        String newPasswd2 = passwdNew2.getInputString();
        if (TextUtils.isEmpty(newPasswd2)) {
            ToastUtils.showShort(getActivity(), getString(R.string.string_input_confirm_passwd));
            return;
        }

        if (!RegexUtils.checkPasswd(newPasswd)) {
            passwdNew.setError(getResources().getString(R.string.input_pwd_reg_no_match));
            return;
        }else{
            passwdNew.setError("");
        }

        if (!newPasswd.equals(newPasswd2)) {
            ToastUtils.showShort(getActivity(), getString(R.string.string_input_two_passwd_no_equal));
            return;
        }

        FindPasswordRequest requestData = new FindPasswordRequest();
        requestData.orderId = orderId;
        requestData.newPasswd = newPasswd;
        requestData.newPasswd2 = newPasswd2;
        AccountInfoApi.RequestFindPWD(requestData, new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    ToastUtils.showShort(getActivity(), getString(R.string.string_update_passwd_success));
                    UserManager.getInstance().clearUserInfo();
                    CookieUtils.getInstance().clearCookies(getActivity());
                    IntentUtils.goLogin(getActivity(),null);
                    getActivity().finish();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public interface ResetPasswdUI extends AppUI{}

}
