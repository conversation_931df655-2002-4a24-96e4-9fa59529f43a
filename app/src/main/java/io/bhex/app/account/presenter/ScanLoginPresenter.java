package io.bhex.app.account.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.sdk.account.LoginApi;

/**
 * *******************************************************************
 *
 * @项目名称: BHEX Android
 * @文件名称: ScanLoginPresenter
 * @Date: 2020/10/22 下午8:06
 * @Author: ppzhao
 * @Copyright（C）: 2020 BlueHelix Inc.   All rights reserved.
 * 注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 * *******************************************************************
 **/
public class ScanLoginPresenter extends BasePresenter<ScanLoginPresenter.ScanLoginUI> {

    public interface ScanLoginUI extends AppUI{

        void authSuccess();
    }

    public void authSacanLogin(String loginQRCode) {
        LoginApi.authQRCodeLogin(loginQRCode,true,new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog();
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    getUI().authSuccess();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

}
