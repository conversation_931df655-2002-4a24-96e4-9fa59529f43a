/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: SettingPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import com.umeng.analytics.MobclickAgent;

import org.greenrobot.eventbus.EventBus;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.core.SPEx;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CookieUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.EventLogin;
import io.bhex.sdk.account.LoginApi;
import io.bhex.sdk.account.UserManager;

/**
 * ================================================
 * 描   述：设置
 * ================================================
 */

public class SettingPresenter extends BasePresenter<SettingPresenter.SettingUI> {

    public interface SettingUI extends AppUI {
    }

    /**
     * 退出
     */
    public void logout() {
        final boolean fingerOpen = UserManager.getInstance().isFingerSetOpenStatus();
        LoginApi.Logout(new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                if (fingerOpen) {
                    UserManager.getInstance().updateFingerAuthStatus(false);
                }
                UserManager.getInstance().clearUserInfo();
                CookieUtils.getInstance().clearCookies(getActivity());
                ToastUtils.showShort(getActivity(), getString(R.string.logout_success));
                MobclickAgent.onProfileSignOff();
                EventBus.getDefault().post(new EventLogin());
                getActivity().finish();
            }
            @Override
            public void onError(Throwable error){
            }
        });


    }

}
