/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AnnounceActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.graphics.Color;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.presenter.AnnouncePresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.view.TopBar;
import io.bhex.app.web.ui.WebActivity;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.utils.bean.AnnouncementsResponse;

/**
 * ================================================
 * 描   述：公告
 * ================================================
 */

public class AnnounceActivity extends BaseActivity<AnnouncePresenter,AnnouncePresenter.AnnounceUI> implements AnnouncePresenter.AnnounceUI, View.OnClickListener, SwipeRefreshLayout.OnRefreshListener {
    private RecyclerView recyclerView;
    private TopBar topBar;
    private SwipeRefreshLayout swipeRefresh;
    private View emptyView;
    private AnnounceListAdapter adapter;

    @Override
    protected int getContentView() {
        return R.layout.activity_announce_layout;
    }

    @Override
    protected AnnouncePresenter createPresenter() {
        return new AnnouncePresenter();
    }

    @Override
    protected AnnouncePresenter.AnnounceUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        this.setTopbarNight(topBar);
        topBar.setLeftOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        recyclerView = viewFinder.find(R.id.recyclerView);
        LayoutInflater layoutInflater = LayoutInflater.from(this);
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(300);
        emptyView.setLayoutParams(layoutParams);
        emptyView.findViewById(R.id.empty_txt).setOnClickListener(this);
        emptyView.findViewById(R.id.empty_img).setOnClickListener(this);
    }

    @Override
    public void showAnnouncements(List<AnnouncementsResponse.AnnounceBean> data) {
        if (adapter == null) {
            adapter = new AnnounceListAdapter(data);
            adapter.isFirstOnly(false);
//            adapter.setOnLoadMoreListener(this);
            swipeRefresh.setColorSchemeColors(Color.RED, Color.BLUE, Color.GREEN);
//            swipeRefresh.setOnRefreshListener(this,recyclerView);
            swipeRefresh.setEnabled(false);

            recyclerView.setLayoutManager(new LinearLayoutManager(this));
            recyclerView.setItemAnimator(new DefaultItemAnimator());
            recyclerView.setAdapter(adapter);
//            adapter.setEmptyView(true, true, emptyView);
            adapter.setEmptyView(emptyView);
        } else {
            adapter.setNewData(data);
        }
    }

    @Override
    public void onClick(View v) {

    }

//    @Override
//    public void onLoadMoreRequested() {
//        swipeRefresh.postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                adapter.loadMoreComplete();
//            }
//        }, 500);
//    }

    @Override
    public void onRefresh() {
        setRefreshing(false);
    }


    public void setRefreshing(final boolean refreshing) {
        swipeRefresh.post(new Runnable() {
            @Override
            public void run() {
                swipeRefresh.setRefreshing(refreshing);
            }
        });
    }

    @Override
    public void onPointerCaptureChanged(boolean hasCapture) {

    }

    private class AnnounceListAdapter extends BaseQuickAdapter<AnnouncementsResponse.AnnounceBean,BaseViewHolder> {

        AnnounceListAdapter(List<AnnouncementsResponse.AnnounceBean> data) {
            super(R.layout.item_announce_layout, data);
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final AnnouncementsResponse.AnnounceBean itemModel) {
            baseViewHolder.setVisible(R.id.item_divider, baseViewHolder.getAdapterPosition() != mData.size());
//            ImageView tokenIcon = baseViewHolder.getView(R.id.token_icon);
//            Glide.with(mContext).load(itemModel.getIconUrl()).into(tokenIcon);
            baseViewHolder.setText(R.id.announce_title,itemModel.getTitle());
            if (!TextUtils.isEmpty(itemModel.getPublishTime())) {
                baseViewHolder.setText(R.id.announce_time,DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getPublishTime()), "yyyy/MM/dd"));
            }else{
                baseViewHolder.setText(R.id.announce_time,"");
            }
            baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (itemModel.isIsDirect()) {
                        WebActivity.runActivity(v.getContext(),itemModel.getTitle(),itemModel.getDirectUrl());
                    }
                }
            });
        }
    }
}
