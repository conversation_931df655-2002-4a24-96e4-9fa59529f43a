package io.bhex.app.account.presenter;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.AccountInfoApi;
import io.bhex.sdk.account.KycApi;
import io.bhex.sdk.account.LoginApi;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.account.bean.UserVerifyInfo;
import io.bhex.sdk.account.bean.kyc.KycLevelsResponse;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-11-13
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class KycLevelsPresenter extends BasePresenter<KycLevelsPresenter.KycLevelsUI> {

    public interface KycLevelsUI extends AppUI{
        void showVerifyInfo(UserVerifyInfo response);

        void showKycLevels(KycLevelsResponse response);

        void requestUserInfoSuccess(UserInfoBean data);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, KycLevelsUI ui) {
        super.onUIReady(activity, ui);

    }

    @Override
    public void onResume() {
        super.onResume();
        getVerifyInfo();
        getKycLevels();
    }

    public void Refresh() {
        getVerifyInfo();
        getKycLevels();
    }

    private void getKycLevels() {
        KycApi.RequestKycLevels(new SimpleResponseListener<KycLevelsResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(KycLevelsResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showKycLevels(response);
                }

            }
        });
    }


    private void getVerifyInfo() {
        AccountInfoApi.RequestGetUserVerifyInfo(new SimpleResponseListener<UserVerifyInfo>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(UserVerifyInfo response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showVerifyInfo(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public void getUserInfo() {
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }
        LoginApi.GetUserInfo(new SimpleResponseListener<UserInfoBean>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(UserInfoBean data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data,true)) {
                    //保存用户数据
                    UserManager.getInstance().saveUserInfo(data);
                    getUI().requestUserInfoSuccess(data);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
