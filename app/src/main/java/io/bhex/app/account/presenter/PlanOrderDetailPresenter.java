package io.bhex.app.account.presenter;

import android.content.Intent;
import android.text.TextUtils;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.trade.TradeApi;
import io.bhex.sdk.trade.bean.OrderBean;
import io.bhex.sdk.trade.bean.OrderDealDetailResponse;
import io.bhex.sdk.trade.bean.PlanOrderBean;
import io.bhex.sdk.trade.bean.PlanOrderDetailResponse;

/**
 * ================================================
 * 描   述：订单详情
 * ================================================
 */

public class PlanOrderDetailPresenter extends BasePresenter<PlanOrderDetailPresenter.OrderDetailUI> {
    private List<OrderDealDetailResponse.DealOrderBean> currentOrders = new ArrayList<>();
    private String orderPageId;
    private String orderId="";
    private PlanOrderBean order;
    private int accountType;

    public interface OrderDetailUI extends AppUI{
        void showOrder(OrderBean orderBean);

        void showHeaderOrders(PlanOrderBean order);

    }

    @Override
    public void onUIReady(BaseCoreActivity activity, OrderDetailUI ui) {
        super.onUIReady(activity, ui);
        Intent intent = getActivity().getIntent();
        if (intent != null) {
            order = (PlanOrderBean) intent.getSerializableExtra(AppData.INTENT.KEY_ORDER);
            accountType = intent.getIntExtra("accountType",0);
            if (order != null) {
                orderId = order.getOrderId();
                getUI().showHeaderOrders(order);
                getOrderDetail();
            }
        }
    }

    /**
     * 获取成交详情
     *
     */
    public void getOrderDetail() {
        TradeApi.RequestPlanOrderDetailInfo(orderId, accountType,new SimpleResponseListener<PlanOrderDetailResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(PlanOrderDetailResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    OrderBean orderBean = response.getOrder();
                    if (orderBean != null) {
                        getUI().showOrder(orderBean);
                    }
                    PlanOrderBean planOrder = response.getPlanOrder();
                    if (planOrder != null) {
                        getUI().showHeaderOrders(planOrder);

                    }

                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
