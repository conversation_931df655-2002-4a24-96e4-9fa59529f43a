/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OptionHistoryDealRecordFragment.java
 *   @Date: 1/25/19 4:32 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.account.ui;

import android.view.View;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.presenter.OptionHistoryDealRecordFragmentPresenter;
import io.bhex.app.base.BaseListFreshFragment;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.trade.bean.OrderDealDetailResponse;

public class OptionHistoryDealRecordFragment extends BaseListFreshFragment<OptionHistoryDealRecordFragmentPresenter, OptionHistoryDealRecordFragmentPresenter.OptionHistoryDealRecordFragmentUI> implements OptionHistoryDealRecordFragmentPresenter.OptionHistoryDealRecordFragmentUI{
    @Override
    protected OptionHistoryDealRecordFragmentPresenter.OptionHistoryDealRecordFragmentUI getUI() {
        return this;
    }

    @Override
    protected OptionHistoryDealRecordFragmentPresenter createPresenter() {
        return new OptionHistoryDealRecordFragmentPresenter();
    }

    @Override
    public void showOrders(List<OrderDealDetailResponse.DealOrderBean> currentOrders) {
        if (adapter == null) {
            adapter = new OptionOpenOrdersAdapter(currentOrders);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this,recyclerView);
            adapter.setEnableLoadMore(true);

            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            recyclerView.setItemAnimator(new DefaultItemAnimator());

            adapter.setEmptyView(emptyView);
            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(currentOrders);
        }
    }

    public class OptionOpenOrdersAdapter  extends BaseQuickAdapter<OrderDealDetailResponse.DealOrderBean, BaseViewHolder> {

        public OptionOpenOrdersAdapter(List<OrderDealDetailResponse.DealOrderBean> data) {
            super(R.layout.item_history_option_deal_layout, data);
        }


        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final OrderDealDetailResponse.DealOrderBean itemModel) {

            if(itemModel == null)
                return;

            String title = KlineUtils.getOptionBuyOrSellTxt(mContext, itemModel.getSide());
            baseViewHolder.setText(R.id.order_buy_type, title);
            baseViewHolder.setTextColor(R.id.order_buy_type,KlineUtils.getBuyOrSellColor(mContext,itemModel.getSide()));
            baseViewHolder.setText(R.id.order_coin_name,itemModel.getSymbolName());
            baseViewHolder.setText(R.id.order_time, DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getTime()), "HH:mm:ss yyyy/MM/dd"));


            int tokenDigit = AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(itemModel.getSymbolId() + itemModel.getQuoteTokenId());
            baseViewHolder.setText(R.id.order_price, NumberUtils.roundFormatDown(itemModel.getPrice(),tokenDigit)+" " + itemModel.getQuoteTokenName());
            baseViewHolder.setText(R.id.order_deal_price, NumberUtils.roundFormatDown(itemModel.executedAmount,tokenDigit)+" " + itemModel.getQuoteTokenName());

            int baseDigit = AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(itemModel.getSymbolId() + itemModel.getBaseTokenId());
            baseViewHolder.setText(R.id.order_entrust_amount, NumberUtils.roundFormatDown(itemModel.getQuantity(),baseDigit) +" "+ getString(R.string.string_option_unit));
            baseViewHolder.setText(R.id.order_deal_fee, KlineUtils.roundFormatDown(itemModel.getFee(),AppData.Config.DIGIT_DEFAULT_VALUE)+" "+ itemModel.getFeeTokenName());
            baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                }
            });
        }
    }
}


