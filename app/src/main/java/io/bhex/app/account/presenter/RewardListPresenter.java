/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: RewardListPresenter.java
 *   @Date: 19-8-26 下午3:46
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BaseFragmentPresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.invite.InviteApi;
import io.bhex.sdk.invite.bean.InviteRewardListResponse;

public class RewardListPresenter extends BaseFragmentPresenter<RewardListPresenter.RewardListUI> {
    private List<InviteRewardListResponse.RewardBean> rewardList = new ArrayList<>();

    public interface RewardListUI extends AppUI {
        void loadMoreComplete();

        void showRewardList(List<InviteRewardListResponse.RewardBean> rewardList);

        void loadEnd();

        void loadMoreFailed();
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, RewardListUI ui) {
        super.onUIReady(activity, ui);
        getRewardList(false);
    }

    /**
     * 返佣列表
     */
    public void getRewardList(final boolean isLoadMore) {
        String from = "";
        if (isLoadMore) {
            if (rewardList != null) {
                if (!rewardList.isEmpty()) {
                    from = rewardList.get(rewardList.size() - 1).getId();
                }
            }
        } else {
            from = "";
        }

        InviteApi.getInviteRewardList(from, AppData.Config.PAGE_LIMIT, new SimpleResponseListener<InviteRewardListResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().loadMoreComplete();
            }

            @Override
            public void onSuccess(InviteRewardListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<InviteRewardListResponse.RewardBean> data = response.getRecordList();
                    if (data != null) {
                        if (isLoadMore) {
                            if (data != null) {
                                rewardList.addAll(data);
                            }
                        } else {
                            if (data != null) {
                                rewardList.clear();
                                rewardList = data;
                            }
                        }
                        getUI().showRewardList(rewardList);
                        if (data.size() < AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        } else {
                            getUI().loadMoreComplete();
                        }
                    } else {
                        getUI().loadMoreComplete();
                    }

                } else {
                    getUI().loadMoreFailed();
                }
            }
        });

    }
}
