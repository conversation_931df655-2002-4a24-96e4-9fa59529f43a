/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AssetDetailPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.LoginApi;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.trade.AssetApi;
import io.bhex.sdk.trade.bean.AssetDataResponse;
import io.bhex.sdk.trade.bean.FeeBeanResponse;


public class AssetDetailPresenter extends BasePresenter<AssetDetailPresenter.AssetDetailUI> {


    public interface AssetDetailUI extends AppUI{
        void requestUserInfoSuccess(UserInfoBean data);
        void showAsset(AssetDataResponse.ArrayBean assetBean);

        void showFeeInfo(FeeBeanResponse response);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, AssetDetailUI ui) {
        super.onUIReady(activity, ui);
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    /**
     * 获取资产
     * @param tokenId
     */
    public void getAsset(String tokenId) {
        AssetApi.RequestTokenIdAsset(tokenId, new SimpleResponseListener<AssetDataResponse>(){
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(AssetDataResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    List<AssetDataResponse.ArrayBean> data = response.getArray();
                    if (data != null) {
                        if (data.size()>0) {
                            AssetDataResponse.ArrayBean assetBean = data.get(0);
                            if (assetBean != null) {
                                getUI().showAsset(assetBean);
                            }
                        }
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        } );
    }


    public void getUserInfo() {
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }
        LoginApi.GetUserInfo(new SimpleResponseListener<UserInfoBean>(){
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(UserInfoBean data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data,true)) {
                    //保存用户数据
                    UserManager.getInstance().saveUserInfo(data);
                    getUI().requestUserInfoSuccess(data);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    /**
     * 获取手续费
     *
     * @param token
     */
    public void getQuotaInfo(String token,String chainType) {
        AssetApi.RequestQuotaInfo(token, chainType, new SimpleResponseListener<FeeBeanResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(FeeBeanResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    getUI().showFeeInfo(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
