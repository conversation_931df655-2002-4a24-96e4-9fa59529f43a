/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MyAssetPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import android.os.Handler;
import android.text.TextUtils;

import org.greenrobot.eventbus.EventBus;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.BasicFunctionsUtil;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BaseFragmentPresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.UISafeKeeper;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.LoginApi;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.config.ConfigApi;
import io.bhex.sdk.config.bean.BasicFunctionsConfig;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.data_manager.AssetUtilsManager;
import io.bhex.sdk.finance.FinanceApi;
import io.bhex.sdk.finance.bean.StakingAssetResponse;
import io.bhex.sdk.finance.bean.StakingAssetsBean;
import io.bhex.sdk.finance.bean.StakingConfigResponse;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.OptionCoinPairBean;
import io.bhex.sdk.quote.bean.QuoteTokensBean;
import io.bhex.sdk.socket.NetWorkObserver;
import io.bhex.sdk.trade.AssetApi;
import io.bhex.sdk.trade.OptionApi;
import io.bhex.sdk.trade.TradeApi;
import io.bhex.sdk.trade.bean.AssetListResponse;
import io.bhex.sdk.trade.bean.CoinplusAssetResponse;
import io.bhex.sdk.trade.bean.FuturesAssetListResponse;
import io.bhex.sdk.trade.bean.OptionAssetListResponse;
import io.bhex.sdk.trade.bean.OptionHoldOrderResponse;
import io.bhex.sdk.trade.futures.FuturesApi;
import io.bhex.sdk.trade.margin.MarginApi;
import io.bhex.sdk.trade.margin.bean.MarginAccountAssetResponse;
import io.bhex.sdk.trade.margin.bean.MarginTokenConfigResponse;
import io.bhex.sdk.utils.CacheUtils;

import static io.bhex.baselib.constant.AppData.HIDE_MIN_BTC;

/**
 * ================================================
 * 描   述：我的资产
 * ================================================
 */

public class MyAssetPresenter extends BaseFragmentPresenter<MyAssetPresenter.MyAssetUI> {

    private FuturesAssetListResponse mFuturesAssetListResponse;

    public interface MyAssetUI extends AppUI {
        //void showAsset(AssetListResponse response);

        void AssetChange();

        void showAssetList(List<AssetListResponse.BalanceBean> tokenList);

        void showOptionAssetList(List<OptionAssetListResponse.OptionAssetBean> tokenList);

        void showOptionHoldOrders(List<OptionHoldOrderResponse.OptionHoldOrderBean> tokenList);

        void showStakingAssetsLists(List<StakingAssetsBean> tokenList);

        void refreshAssetComplete();

        void showFuturesAssetList(List<FuturesAssetListResponse.FuturesAssetBean> futuresTokenList);

        void showMarginLists(List<MarginAccountAssetResponse.DataBean> tokenList);

        void updateStakingConfig(StakingConfigResponse configResponse);
    }

    LinkedHashMap<String, AssetListResponse.BalanceBean> tokenMap = new LinkedHashMap<String, AssetListResponse.BalanceBean>();
    LinkedHashMap<String, FuturesAssetListResponse.FuturesAssetBean> futuresTokenMap = new LinkedHashMap<String, FuturesAssetListResponse.FuturesAssetBean>();
    LinkedHashMap<String, OptionAssetListResponse.OptionAssetBean> optionTokenMap = new LinkedHashMap<String, OptionAssetListResponse.OptionAssetBean>();
    LinkedHashMap<String, OptionHoldOrderResponse.OptionHoldOrderBean> optionHoldOrderMap = new LinkedHashMap<String, OptionHoldOrderResponse.OptionHoldOrderBean>();
    LinkedHashMap<String, MarginAccountAssetResponse.DataBean> marginTokenMap = new LinkedHashMap<String, MarginAccountAssetResponse.DataBean>();
    List<AssetListResponse.BalanceBean> tokenList = new ArrayList<>();
    List<FuturesAssetListResponse.FuturesAssetBean> futuresTokenList = new ArrayList<>();
    List<OptionAssetListResponse.OptionAssetBean> optionTokenList = new ArrayList<>();
    List<OptionHoldOrderResponse.OptionHoldOrderBean> optionHoldOrderList = new ArrayList<>();
    List<CoinplusAssetResponse.CoinplusItemBean> coinplusList = new ArrayList<>();
    List<StakingAssetsBean> stakingAssetList = new ArrayList<>();
    List<MarginAccountAssetResponse.DataBean> marginTokenList = new ArrayList<>();

    List<AssetListResponse.BalanceBean> filterList = new ArrayList<>();
    List<MarginAccountAssetResponse.DataBean> marginFilterList = new ArrayList<>();
    AssetListResponse mAssetListResponse;
    OptionAssetListResponse mOptionAssetListResponse;
    OptionHoldOrderResponse mOptionHoldOrderListResponse;
    CoinplusAssetResponse mCoinplusAssetResponse;
    MarginAccountAssetResponse mMarginAssetResponse;
    StakingAssetResponse mStakingAssetResponse;

    @Override
    public void onUIReady(BaseCoreActivity activity, MyAssetUI ui) {
        super.onUIReady(activity, ui);

//        if (UserInfo.isLogin()) {
//            if (!NetWorkStatus.isConnected(getActivity())) {
//                ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
//                return;
//            }
//            getTokens();
//            getOptionAssetList();
//            getFuturesAssetList();
//            getOptionHoldOrderList();
//            getCoinPlusAssetList();

        loadFuturesAssetTokens();
//        }
        BasicFunctionsConfig basicFunctionsConfig = BasicFunctionsUtil.getBasicFunctionsConfig();
        if (!basicFunctionsConfig.isBonus()||!basicFunctionsConfig.isStaking()) {
            loadStakingConfigCache();
        }
    }

    /**
     * 加载期货数据
     */
    private void loadFuturesAssetTokens() {
        List<String> futuresCoinTokens = AppConfigManager.GetInstance().getFuturesCoinToken();
        if (futuresCoinTokens != null && futuresCoinTokens.size() > 0) {
            for (String futuresCoinToken : futuresCoinTokens) {
                FuturesAssetListResponse.FuturesAssetBean futuresAssetBean = new FuturesAssetListResponse.FuturesAssetBean();
                futuresAssetBean.tokenId = futuresCoinToken;
                futuresAssetBean.tokenName = futuresCoinToken;
                futuresAssetBean.availableMargin = "";
                futuresAssetBean.orderMargin = "";
                futuresAssetBean.positionMargin = "";
                futuresAssetBean.total = "";
                futuresTokenMap.put(futuresCoinToken, futuresAssetBean);
            }
        }
    }


    @Override
    public void onResume() {
        super.onResume();

        BasicFunctionsConfig basicFunctionsConfig = BasicFunctionsUtil.getBasicFunctionsConfig();
        if (!basicFunctionsConfig.isBonus()||!basicFunctionsConfig.isStaking()) {
            getStakingConfigInfo();
        }
    }
    /**
     * 缓存-加载staking配置
     */
    private void loadStakingConfigCache() {

        StakingConfigResponse configResponse = CacheUtils.getCacheObject("cust.stakingSettings",StakingConfigResponse.class);

        if (configResponse != null) {
            getUI().updateStakingConfig(configResponse);
        }

    }
    public void getStakingConfigInfo() {
        ConfigApi.getCustomKVStakingConfig("cust.stakingSettings",new SimpleResponseListener<StakingConfigResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(StakingConfigResponse response) {
                super.onSuccess(response);
                if (response != null) {
                    getUI().updateStakingConfig(response);
                    CacheUtils.saveCache("cust.stakingSettings",response);
                }
            }

            @Override
            public StakingConfigResponse parserResponse(Handler uiHandler, String response, Class<StakingConfigResponse> clazz) {
                response = response.replace("cust.stakingSettings","stakingSettings");
//                KycInfoConfigResponse kycInfoConfigResponse = Convert.fromJson(response, clazz);
                return super.parserResponse(uiHandler, response, clazz);
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    /**
     * 搜索
     *
     * @param searchContent
     * @param isFilterZero
     */
    public void search(String searchContent, boolean isFilterZero) {
        if (!tokenList.isEmpty()) {
            filterList.clear();
            for (AssetListResponse.BalanceBean BalanceBean : tokenList) {

                if (isMatch(BalanceBean, searchContent, isFilterZero)) {
                    filterList.add(BalanceBean);
                }
            }
            getUI().showAssetList(filterList);

        }
    }

    /**
     * 搜索杠杆
     *
     * @param searchContent
     * @param isFilterZero
     */
    public void searchMargin(String searchContent, boolean isFilterZero) {
        if (!marginTokenList.isEmpty()) {
            marginFilterList.clear();
            for (MarginAccountAssetResponse.DataBean BalanceBean : marginTokenList) {

                if (isMatch(BalanceBean, searchContent, isFilterZero)) {
                    marginFilterList.add(BalanceBean);
                }
            }
            getUI().showMarginLists(marginFilterList);

        }
    }

    private boolean isMatch(MarginAccountAssetResponse.DataBean balanceBean, String searchContent, boolean isFilterZero) {
        try {
            if (!TextUtils.isEmpty(searchContent)) {
                String token = balanceBean.getTokenId();
                String tokenName = balanceBean.getTokenName();
                if (!token.contains(searchContent) && !token.contains(searchContent.toUpperCase()) && !tokenName.contains(searchContent) && !tokenName.contains(searchContent.toUpperCase())) {
                    return false;
                }
            }
            if (isFilterZero) {
                String total = balanceBean.getBtcValue();
                if (!TextUtils.isEmpty(total)) {
                    double totald = Double.valueOf(total);
                    if (totald <= HIDE_MIN_BTC) {
                        return false;
                    }
                } else
                    return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return true;
    }

    private boolean isMatch(AssetListResponse.BalanceBean balanceBean, String searchContent, boolean isFilterZero) {
        try {
            if (!TextUtils.isEmpty(searchContent)) {
                String token = balanceBean.getTokenId();
                String tokenName = balanceBean.getTokenName();
                if (!token.contains(searchContent) && !token.contains(searchContent.toUpperCase()) && !tokenName.contains(searchContent) && !tokenName.contains(searchContent.toUpperCase())) {
                    return false;
                }
            }
            if (isFilterZero) {
                String total = balanceBean.getBtcValue();
                if (!TextUtils.isEmpty(total)) {
                    double totald = Double.valueOf(total);
                    if (totald <= HIDE_MIN_BTC) {
                        return false;
                    }
                } else
                    return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return true;
    }

    /**
     * 获取资产列表
     */
    public void getAssetList() {
        TradeApi.SubBalanceData(new NetWorkObserver<List<AssetListResponse.BalanceBean>>() {

            @Override
            public void onShowUI(List<AssetListResponse.BalanceBean> response) {
                if (response != null) {
                    addBalanceInfoList(response);
                }
            }

            @Override
            public void onError(String response) {

            }
        });

        TradeApi.RequestBalanceData(new SimpleResponseListener<AssetListResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
                //getUI().refreshAssetComplete();
            }

            @Override
            public void onSuccess(AssetListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    showAsset(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }

        });

    }

    /**
     * 获取资产列表
     */
    public void getFuturesAssetList() {
        futuresTokenMap.clear();
        loadFuturesAssetTokens();//重新加载token
        FuturesApi.SubFuturesBalanceData(new NetWorkObserver<List<FuturesAssetListResponse.FuturesAssetBean>>() {

            @Override
            public void onShowUI(List<FuturesAssetListResponse.FuturesAssetBean> response) {
                if (response != null) {
                    addFuturesBalanceInfoList(response);
                }
            }

            @Override
            public void onError(String response) {

            }
        });

        FuturesApi.RequestFuturesBalanceData(new SimpleResponseListener<FuturesAssetListResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
                //getUI().refreshAssetComplete();
            }

            @Override
            public void onSuccess(FuturesAssetListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    showFuturesAsset(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }

        });

    }

    /**
     * 获取资产列表
     */
    public void getOptionAssetList() {
        OptionApi.SubOptionBalanceData(new NetWorkObserver<List<OptionAssetListResponse.OptionAssetBean>>() {

            @Override
            public void onShowUI(List<OptionAssetListResponse.OptionAssetBean> response) {
                if (response != null) {
                    addOptionBalanceInfoList(response);
                }
            }

            @Override
            public void onError(String response) {

            }
        });

        OptionApi.RequestOptionBalanceData(new SimpleResponseListener<OptionAssetListResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
                //getUI().refreshAssetComplete();
            }

            @Override
            public void onSuccess(OptionAssetListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    showOptionAsset(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }

        });

    }

    public void getOptionHoldOrderList() {

        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        }

        OptionApi.SubOptionHoldOrderChange(new SimpleResponseListener<OptionHoldOrderResponse>() {
            @Override
            public void onSuccess(OptionHoldOrderResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    optionHoldOrderList = response.array;
                    if (optionHoldOrderList != null) {
                        addOptionHoldOrderList(optionHoldOrderList);
                    }
                }
            }
        });

        OptionApi.RequestOptionHoldOrder("", "", new SimpleResponseListener<OptionHoldOrderResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(OptionHoldOrderResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    showOptionHoldOrder(response);
                }
            }

        });

    }

    /**
     * 获取资产列表
     */
    public void getMarginAssetList() {
        MarginApi.SubBalanceData(new NetWorkObserver<List<MarginAccountAssetResponse.DataBean>>(){

            @Override
            public void onShowUI(List<MarginAccountAssetResponse.DataBean> response) {
                if(response != null){
                    addMarginBalanceInfoList(response);
                }
            }

            @Override
            public void onError(String response) {

            }
        });

        MarginApi.RequestTokenIdAsset("",new SimpleResponseListener<MarginAccountAssetResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
                //getUI().refreshAssetComplete();
            }

            @Override
            public void onSuccess(MarginAccountAssetResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    showMarginAsset(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }

        });

    }

    private void showAsset(AssetListResponse response) {
        mAssetListResponse = response;
        addBalanceInfoList(mAssetListResponse.getBalance());
    }

    private void showOptionAsset(OptionAssetListResponse response) {
        mOptionAssetListResponse = response;
        addOptionBalanceInfoList(mOptionAssetListResponse.array);
    }

    private void showFuturesAsset(FuturesAssetListResponse response) {
        mFuturesAssetListResponse = response;
        addFuturesBalanceInfoList(mFuturesAssetListResponse.array);
    }

    private void showMarginAsset(MarginAccountAssetResponse response) {
        mMarginAssetResponse = response;
        addMarginBalanceInfoList(mMarginAssetResponse.getArray());
    }

    private void showOptionHoldOrder(OptionHoldOrderResponse response) {

        if (response != null && response.array != null) {
            mOptionHoldOrderListResponse = response;
            addOptionHoldOrderList(mOptionHoldOrderListResponse.array);
        }

    }

    private void addBalanceInfoList(List<AssetListResponse.BalanceBean> balanceInfoList) {
        if (mAssetListResponse != null && balanceInfoList != null) {
            for (AssetListResponse.BalanceBean balanceBean : balanceInfoList) {
                String token = balanceBean.getTokenId();
                AssetListResponse.BalanceBean itemBean = tokenMap.get(token);
                if (itemBean != null) {
                    //此处币Icon赋值，方便传递到下一页
                    balanceBean.setIconUrl(itemBean.getIconUrl());
                    balanceBean.setTokenFullName(itemBean.getTokenFullName());
                    balanceBean.setAllowWithdraw(itemBean.isAllowWithdraw());
                    balanceBean.setAllowDeposit(itemBean.isAllowDeposit());
                    balanceBean.setNeedAddressTag(itemBean.isNeedAddressTag());
                    tokenMap.put(token, balanceBean);
                }
            }

            tokenList.clear();
            BigDecimal totalBtcValue = new BigDecimal("0");
            for (String key : tokenMap.keySet()) {
                AssetListResponse.BalanceBean balanceBean = tokenMap.get(key);
                tokenList.add(balanceBean);

                String btcValue = balanceBean.getBtcValue();
                if (!TextUtils.isEmpty(btcValue)) {
                    totalBtcValue = totalBtcValue.add(new BigDecimal(btcValue));
                }
            }
            mAssetListResponse.setBtcValue(totalBtcValue.toPlainString());
            mAssetListResponse.setBalance(tokenList);
            getUI().showAssetList(tokenList);
        }
    }

    private void addOptionBalanceInfoList(List<OptionAssetListResponse.OptionAssetBean> balanceInfoList) {
        if (mOptionAssetListResponse != null && balanceInfoList != null) {
            for (OptionAssetListResponse.OptionAssetBean balanceBean : balanceInfoList) {
                String token = balanceBean.tokenId;
                //OptionAssetListResponse.OptionAssetBean itemBean = optionTokenMap.get(token);
                optionTokenMap.put(token, balanceBean);
            }

            optionTokenList.clear();
            for (String key : optionTokenMap.keySet()) {
                OptionAssetListResponse.OptionAssetBean balanceBean = optionTokenMap.get(key);
                optionTokenList.add(balanceBean);
            }
            getUI().showOptionAssetList(optionTokenList);
        }
    }

    private void addFuturesBalanceInfoList(List<FuturesAssetListResponse.FuturesAssetBean> balanceInfoList) {
        if (mFuturesAssetListResponse != null && balanceInfoList != null) {
            for (FuturesAssetListResponse.FuturesAssetBean balanceBean : balanceInfoList) {
                String token = balanceBean.tokenId;
                //OptionAssetListResponse.OptionAssetBean itemBean = optionTokenMap.get(token);
                futuresTokenMap.put(token, balanceBean);
            }

            futuresTokenList.clear();
            for (String key : futuresTokenMap.keySet()) {
                FuturesAssetListResponse.FuturesAssetBean balanceBean = futuresTokenMap.get(key);
                futuresTokenList.add(balanceBean);
            }
            getUI().showFuturesAssetList(futuresTokenList);
        }
    }

    private void addOptionHoldOrderList(List<OptionHoldOrderResponse.OptionHoldOrderBean> holdOrderList) {
        if (holdOrderList != null) {
            optionHoldOrderMap.clear();
            for (OptionHoldOrderResponse.OptionHoldOrderBean balanceBean : holdOrderList) {
                String balanceId = balanceBean.balanceId;
                //OptionHoldOrderResponse.OptionHoldOrderBean itemBean = optionHoldOrderMap.get(balanceId);
                optionHoldOrderMap.put(balanceId, balanceBean);
            }

            optionHoldOrderList.clear();
            for (String key : optionHoldOrderMap.keySet()) {
                OptionHoldOrderResponse.OptionHoldOrderBean balanceBean = optionHoldOrderMap.get(key);
                optionHoldOrderList.add(balanceBean);
            }
            getUI().showOptionHoldOrders(optionHoldOrderList);
        }
    }


    private void addMarginBalanceInfoList(List<MarginAccountAssetResponse.DataBean> balanceInfoList) {
        if (mMarginAssetResponse != null && balanceInfoList != null) {
            for (MarginAccountAssetResponse.DataBean balanceBean : balanceInfoList) {
                String token = balanceBean.getTokenId();
                MarginAccountAssetResponse.DataBean itemBean = marginTokenMap.get(token);
                if (itemBean != null) {
                    //此处币Icon赋值，方便传递到下一页
                    marginTokenMap.put(token, balanceBean);
                }
            }

            marginTokenList.clear();
            BigDecimal totalBtcValue = new BigDecimal("0");
            for (String key : marginTokenMap.keySet()) {
                MarginAccountAssetResponse.DataBean balanceBean = marginTokenMap.get(key);
                marginTokenList.add(balanceBean);

                String btcValue = balanceBean.getBtcValue();
                if (!TextUtils.isEmpty(btcValue)) {
                    totalBtcValue = totalBtcValue.add(new BigDecimal(btcValue));
                }
            }
            getUI().showMarginLists(marginTokenList);
        }
    }

    /**
     * 获取Token集合
     */
    public void getTokens() {
        AssetApi.RequestTokens(UISafeKeeper.guard(getUI(),new SimpleResponseListener<List<QuoteTokensBean.TokenItem>>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(List<QuoteTokensBean.TokenItem> response) {
                super.onSuccess(response);
                List<QuoteTokensBean.TokenItem> datas = response;
                tokenMap.clear();
                if (datas != null) {
                    for (QuoteTokensBean.TokenItem data : datas) {
                        AssetListResponse.BalanceBean BalanceBean = new AssetListResponse.BalanceBean();
                        BalanceBean.setTokenId(data.getTokenId());
                        BalanceBean.setTokenName(data.getTokenName());
                        BalanceBean.setTokenFullName(data.getTokenFullName());
                        BalanceBean.setTotal("0.00");
                        BalanceBean.setIconUrl(data.getIconUrl());
                        BalanceBean.setAllowDeposit(data.isAllowDeposit());
                        BalanceBean.setAllowWithdraw(data.isAllowWithdraw());
                        BalanceBean.setNeedAddressTag(data.isNeedAddressTag());
                        tokenMap.put(data.getTokenId(), BalanceBean);
                    }
                    getAssetList();


                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        }));
    }


    /**
     * 获取报价Token集合
     */
    public void getMarginTokens() {
        UserInfoBean userInfo = UserManager.getInstance().getUserInfo();
        if (userInfo == null) {
            marginTokenList.clear();
            marginTokenMap.clear();
            getUI().showMarginLists(marginTokenList);
            return;
        }
        if(!userInfo.isOpenMargin()){
            marginTokenList.clear();
            marginTokenMap.clear();
            getUI().showMarginLists(marginTokenList);
            return;
        }

        MarginApi.getMarginTokenConfig("", new SimpleResponseListener<MarginTokenConfigResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(MarginTokenConfigResponse response) {
                super.onSuccess(response);
                List<MarginTokenConfigResponse.MarginToken> datas = response.getArray();
                marginTokenMap.clear();
                if (datas != null) {
                    for (MarginTokenConfigResponse.MarginToken data : datas) {
                        MarginAccountAssetResponse.DataBean BalanceBean = new MarginAccountAssetResponse.DataBean();
                        BalanceBean.setTokenId(data.getTokenId());
                        QuoteTokensBean.TokenItem tokenItem = AppConfigManager.GetInstance().getTokenItemByTokenId(data.getTokenId());
                        BalanceBean.setTokenName(data.getTokenId());
                        if (tokenItem!=null) {
                            BalanceBean.setTokenFullName(tokenItem.getTokenFullName());
                        } else {
                            BalanceBean.setTokenFullName(data.getTokenId());
                        }
                        BalanceBean.setTotal("0.00");
                        marginTokenMap.put(data.getTokenId(), BalanceBean);
                    }
                    getMarginAssetList();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public void getCoinPlusAssetList() {
        BasicFunctionsConfig basicFunctionsConfig = BasicFunctionsUtil.getBasicFunctionsConfig();
        if (!basicFunctionsConfig.isBonus()) {
            FinanceApi.getFinanceAssetList(UISafeKeeper.guard(getUI(), new SimpleResponseListener<CoinplusAssetResponse>() {
                @Override
                public void onBefore() {
                    super.onBefore();
                }

                @Override
                public void onFinish() {
                    super.onFinish();
                }

                @Override
                public void onSuccess(CoinplusAssetResponse response) {
                    super.onSuccess(response);
                    if(CodeUtils.isSuccess(response)) {
                        coinplusList.clear();
                        if (response != null && response.balanceInfoList != null) {
                            mCoinplusAssetResponse = response;
                            coinplusList = response.balanceInfoList;
                        }
                        updateStakingAssets();
                    }
                }

                @Override
                public void onError(Throwable error) {
                    super.onError(error);
                }
            }));
        }
        if (!basicFunctionsConfig.isStaking()) {
            FinanceApi.getStakingAssetList(UISafeKeeper.guard(getUI(), new SimpleResponseListener<StakingAssetResponse>() {
                @Override
                public void onBefore() {
                    super.onBefore();
                }

                @Override
                public void onFinish() {
                    super.onFinish();
                }

                @Override
                public void onSuccess(StakingAssetResponse response) {
                    super.onSuccess(response);

                    if(CodeUtils.isSuccess(response)) {
                        stakingAssetList.clear();
                        if (response != null && response.getAssetInfoList() != null) {
                            mStakingAssetResponse = response;
                            for (StakingAssetResponse.AssetInfoListBean listBean:response.getAssetInfoList()) {
                                if (listBean.getType()!=AppData.Config.STAKING_PRODUCE_CURRENT&&listBean.getAssets()!=null &&listBean.getAssets().size()>0) {
                                    for (StakingAssetsBean assetBean : listBean.getAssets()) {
                                        assetBean.setType(listBean.getType());
                                        stakingAssetList.add(assetBean);
                                    }
                                }
                            }
                        }
                        updateStakingAssets();
                    }
                }

                @Override
                public void onError(Throwable error) {
                    super.onError(error);
                }
            }));
        }
    }

    private void updateStakingAssets() {
        List<StakingAssetsBean> list =new ArrayList<>();
        if (coinplusList!=null) {
            for (CoinplusAssetResponse.CoinplusItemBean bean: coinplusList) {
                StakingAssetsBean compatibleBean = new StakingAssetsBean();
                compatibleBean.Copy(bean);
                list.add(compatibleBean);
            }
        }
        if (stakingAssetList!=null) {
            list.addAll(stakingAssetList);
        }
        getUI().showStakingAssetsLists(list);
    }

    public void RequestOptionSymbols(final String symbolId) {
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }


        OptionApi.RequestOptionSymbols(symbolId, new SimpleResponseListener<OptionCoinPairBean>() {
            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("", "");
            }

            @Override
            public void onSuccess(OptionCoinPairBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<CoinPairBean> data = response.array;
                    if (data != null) {
                        for (CoinPairBean bean : data) {
                            if (bean != null && !TextUtils.isEmpty(symbolId) && symbolId.equalsIgnoreCase(bean.getSymbolId())) {
                                bean.setBuyMode(true);
                                bean.setNeedSwitchTradeTab(true);
                                getActivity().finish();
                                EventBus.getDefault().postSticky(bean);
                                return;
                            }
                        }
                    }
                }
                ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            }
        });
    }

    public void getUserInfo() {
        LoginApi.GetUserInfo(new SimpleResponseListener<UserInfoBean>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(UserInfoBean data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data, true)) {
                    //保存用户数据
                    UserManager.getInstance().saveUserInfo(data);
                    // 开完户重新查询
                    getMarginTokens();
                    AssetUtilsManager.GetInstance().requestAllMarginPosition();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

}
