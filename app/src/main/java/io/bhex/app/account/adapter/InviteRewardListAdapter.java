/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: InviteRewardListAdapter.java
 *   @Date: 18-12-25 下午7:25
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.adapter;


import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.sdk.invite.bean.InviteRewardListResponse;

public class InviteRewardListAdapter extends BaseQuickAdapter<InviteRewardListResponse.RewardBean,BaseViewHolder> {
    public InviteRewardListAdapter(@Nullable List<InviteRewardListResponse.RewardBean> data) {
        super(R.layout.item_invitation_reward_layout, data);
    }

    @Override
    protected void convert(BaseViewHolder baseViewHolder, InviteRewardListResponse.RewardBean itemModel) {
        baseViewHolder.setText(R.id.item_amount,itemModel.getBonusAmount());
        baseViewHolder.setText(R.id.item_token,itemModel.getToken());
//        baseViewHolder.setText(R.id.item_time,DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getStatisticsTime()), AppData.Config.TIME_FORMAT));
        baseViewHolder.setText(R.id.item_time,itemModel.getStatisticsTime());
    }
}
