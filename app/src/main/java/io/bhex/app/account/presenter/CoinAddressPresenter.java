/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CoinAddressPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import java.util.List;

import io.bhex.app.R;
import io.bhex.sdk.account.LoginResultCallback;
import io.bhex.sdk.trade.AssetApi;
import io.bhex.sdk.trade.bean.AddressListResponse;
import io.bhex.app.base.AppUI;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.quote.bean.QuoteTokensBean;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.sdk.account.UserInfo;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.response.UISafeKeeper;
import io.bhex.baselib.utils.ToastUtils;



public class CoinAddressPresenter extends BasePresenter<CoinAddressPresenter.CoinAddressUI> {
    private List<QuoteTokensBean.TokenItem> datasOfTokenList;

    public interface CoinAddressUI extends AppUI{
        void showQuotaTokens(List<QuoteTokensBean.TokenItem> datas);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, CoinAddressUI ui) {
        super.onUIReady(activity, ui);
    }

    @Override
    public void onResume() {
        super.onResume();
        if (UserInfo.isLogin()) {
            getTokens();
        }
    }

    /**
     * 获取Token集合
     */
    public void getTokens() {
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }

        AssetApi.RequestTokens( UISafeKeeper.guard(getUI(), new SimpleResponseListener<List<QuoteTokensBean.TokenItem>>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(List<QuoteTokensBean.TokenItem> response) {
                super.onSuccess(response);
                datasOfTokenList = response;
                getUI().showQuotaTokens(datasOfTokenList);
                getAddressList();

            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        }));
    }

    /**
     * 获取报价Token集合
     */
    public void getAddressList() {
        AssetApi.RequestAllAddress(UISafeKeeper.guard(getUI(), new SimpleResponseListener<AddressListResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(AddressListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    List<AddressListResponse.AddressBean> datas = response.getArray();
                    if (datasOfTokenList != null) {
                        for (QuoteTokensBean.TokenItem tokenItem : datasOfTokenList) {
                            String tokenId = tokenItem.getTokenId();
                            tokenItem.setAddressCount(0);
                            for (AddressListResponse.AddressBean data : datas) {
                                String addressTokenId = data.getTokenId();
                                if (tokenId.equals(addressTokenId)) {
                                    tokenItem.setAddressCount(tokenItem.getAddressCount() + 1);
                                }

                            }
                        }
                        getUI().showQuotaTokens(datasOfTokenList);
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        }));
    }
}
