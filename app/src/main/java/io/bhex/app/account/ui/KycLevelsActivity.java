package io.bhex.app.account.ui;

import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.Toolbar;

import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.presenter.KycLevelsPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.DialogUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.account.bean.UserVerifyInfo;
import io.bhex.sdk.account.bean.enums.VERIFY_STATUS;
import io.bhex.sdk.account.bean.kyc.KycLevelBean;
import io.bhex.sdk.account.bean.kyc.KycLevelsResponse;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-11-13
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class KycLevelsActivity extends BaseActivity<KycLevelsPresenter, KycLevelsPresenter.KycLevelsUI> implements KycLevelsPresenter.KycLevelsUI, View.OnClickListener, OnRefreshListener {
    private TextView kycOneTx;
    private TextView kycTwoTx;
    private TextView kycThreeTx;
    private KycLevelBean oneLevel;
    private KycLevelBean twoLevel;
    private KycLevelBean threeLevel;
    private View kycOneRl;
    private View kycTwoRl;
    private View kycThreeRl;
    private String refusedReason="";
    private SmartRefreshLayout smartRefresh;

    @Override
    protected int getContentView() {
        return R.layout.activity_kyc_levels_layout;
    }

    @Override
    protected KycLevelsPresenter createPresenter() {
        return new KycLevelsPresenter();
    }

    @Override
    protected KycLevelsPresenter.KycLevelsUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        Toolbar toolbar= findViewById(R.id.toolbar);
        CollapsingToolbarLayout collapsingToolbarLayout= findViewById(R.id.collapsing_toolbar);
        collapsingToolbarLayout.setCollapsedTitleTextColor(SkinColorUtil.getDark(this));
        collapsingToolbarLayout.setExpandedTitleColor(SkinColorUtil.getDark(this));
        //显示返回按钮
        setSupportActionBar(toolbar);
        ActionBar actionBar=getSupportActionBar();
        if (actionBar!=null){
            actionBar.setDisplayHomeAsUpEnabled(true);
        }
        smartRefresh = viewFinder.find(R.id.smartRefresh);
        kycOneRl = viewFinder.find(R.id.kyc_one_item);
        kycTwoRl = viewFinder.find(R.id.kyc_two_item);
        kycThreeRl = viewFinder.find(R.id.kyc_three_item);
        kycOneTx = viewFinder.textView(R.id.auth_kyc_one);
        kycTwoTx = viewFinder.textView(R.id.auth_kyc_two);
        kycThreeTx = viewFinder.textView(R.id.auth_kyc_three);

    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()){
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    private void setAuthStatusTv(View kycRl, TextView authStatusTv, KycLevelBean kycLevelBean) {
        boolean isAuth = false;
        int verifyStatus = kycLevelBean.getVerifyStatus();
        if (verifyStatus == 0) {
            isAuth = false;
            authStatusTv.setText(getResources().getString(R.string.string_go_auth));
            authStatusTv.setTextAppearance(this,R.style.BodyS_Blue);
            authStatusTv.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(R.mipmap.icon_tips_blue), null, null, null);
        }else if(verifyStatus == 1){
            isAuth = false;
            authStatusTv.setText(VERIFY_STATUS.getDescByStatus(verifyStatus));
            authStatusTv.setTextAppearance(this,R.style.BodyS_Orange);
            authStatusTv.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(R.mipmap.icon_checking), null, null, null);
        }else if(verifyStatus == 2){
            isAuth = true;
            authStatusTv.setText(VERIFY_STATUS.getDescByStatus(verifyStatus));
            authStatusTv.setTextAppearance(this,R.style.BodyS_Green);
            authStatusTv.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(R.mipmap.icon_success), null, null, null);
        }else if(verifyStatus == 3){
            isAuth = false;
            authStatusTv.setText(VERIFY_STATUS.getDescByStatus(verifyStatus));
            authStatusTv.setTextAppearance(this,R.style.BodyS_Red);
            authStatusTv.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(R.mipmap.icon_failed), null, null, null);
        }
        kycRl.setClickable(!isAuth);
//        if (isAuth) {
//            authStatusTv.setTextAppearance(this,R.style.BodyS_Dark);
//        }else{
//            authStatusTv.setTextAppearance(this,R.style.BodyS_Blue_Bold);
//        }
//        authStatusTv.setText(getResources().getString(isAuth ? R.string.string_had_auth : R.string.string_go_auth));

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.kyc_one_item).setOnClickListener(this);
        viewFinder.find(R.id.kyc_two_item).setOnClickListener(this);
        viewFinder.find(R.id.kyc_three_item).setOnClickListener(this);
        smartRefresh.setOnRefreshListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.kyc_one_item:
                if (oneLevel != null) {
                    if (oneLevel.getVerifyStatus() == VERIFY_STATUS.VERIFY_NO.getmStatus()) {
                        IntentUtils.goAuthLV1(this);
                    }else if (oneLevel.getVerifyStatus() == VERIFY_STATUS.VERIFY_CHECKING.getmStatus() || oneLevel.getVerifyStatus() == VERIFY_STATUS.VERIFY_CHECK_FAILED.getmStatus()){
                        IntentUtils.goAuthStatus(this,oneLevel,oneLevel.getVerifyStatus(),refusedReason);
                    }
                }
                break;
            case R.id.kyc_two_item:
                if (twoLevel != null && oneLevel !=null) {
                    if (oneLevel.getVerifyStatus() == VERIFY_STATUS.VERIFY_CHECKED.getmStatus()) {
                        if (twoLevel.getVerifyStatus() == VERIFY_STATUS.VERIFY_NO.getmStatus()) {
                            IntentUtils.goAuthLV2ComfirmInfo(this,twoLevel);
                        }else if (twoLevel.getVerifyStatus() == VERIFY_STATUS.VERIFY_CHECKING.getmStatus() || twoLevel.getVerifyStatus() == VERIFY_STATUS.VERIFY_CHECK_FAILED.getmStatus()){
                            IntentUtils.goAuthStatus(this, twoLevel, twoLevel.getVerifyStatus(),refusedReason);
                        }
                    }
                }
                break;
            case R.id.kyc_three_item:
                if (threeLevel != null) {
                    if (threeLevel.getVerifyStatus() == VERIFY_STATUS.VERIFY_CHECKING.getmStatus() || threeLevel.getVerifyStatus() == VERIFY_STATUS.VERIFY_CHECK_FAILED.getmStatus()){
                        IntentUtils.goAuthStatus(KycLevelsActivity.this, threeLevel, threeLevel.getVerifyStatus(),refusedReason);
                        return;
                    }
                }
                getPresenter().getUserInfo();
                break;
        }
    }

    @Override
    public void requestUserInfoSuccess(final UserInfoBean userInfo) {
        if (userInfo != null) {
            String mobile = userInfo.getMobile();
            String email = userInfo.getEmail();
//            isBindMobile = !TextUtils.isEmpty(mobile) && userInfo.getRegisterType() != 1;
//            isBindEmail = !TextUtils.isEmpty(email) && userInfo.getRegisterType() != 2;
//            int verifyStatus = userInfo.getVerifyStatus();
//            boolean isVerify = false;
//            if (verifyStatus == VERIFY_STATUS.VERIFY_CHECKED.getmStatus()) {
//                isVerify = true;
//            }

            if (TextUtils.isEmpty(mobile)) {
                IntentUtils.goBindMobile(this);
                return;
            }
            if (TextUtils.isEmpty(email)) {
                IntentUtils.goBindEmail(this);
                return;
            }

            DialogUtils.showKYCThreeConditons(this,getString(R.string.string_kyc_level_three_comfirm_tips),getString(R.string.string_start_auth),getString(R.string.string_wait_next_time),false,new DialogUtils.OnButtonEventListener(){
                @Override
                public void onConfirm() {
                    if (threeLevel != null) {
                        if (threeLevel.getVerifyStatus() == VERIFY_STATUS.VERIFY_NO.getmStatus()) {
                            IntentUtils.goAuthLV3(KycLevelsActivity.this,threeLevel);
                        }else if (threeLevel.getVerifyStatus() == VERIFY_STATUS.VERIFY_CHECKING.getmStatus() || threeLevel.getVerifyStatus() == VERIFY_STATUS.VERIFY_CHECK_FAILED.getmStatus()){
                            IntentUtils.goAuthStatus(KycLevelsActivity.this, threeLevel, threeLevel.getVerifyStatus(),refusedReason);
                        }
                    }
                }

                @Override
                public void onCancel() {

                }
            });
        }
    }

    @Override
    public void showVerifyInfo(UserVerifyInfo response) {
        if (response != null) {
            refusedReason = response.getRefusedReason();
            viewFinder.textView(R.id.kyc_userinfo).setVisibility(View.VISIBLE);
            viewFinder.textView(R.id.kyc_userinfo).setText(response.getFirstName()+response.getSecondName()+"  "+response.getCardNo());
        }else{
            viewFinder.textView(R.id.kyc_userinfo).setVisibility(View.GONE);
        }
    }

    @Override
    public void showKycLevels(KycLevelsResponse response) {
        List<KycLevelBean> kycLevels = response.getArray();
        if (kycLevels != null&&kycLevels.size()>0) {
            for (KycLevelBean kycLevel : kycLevels) {
                showLevelInfo(kycLevel);
            }
            
        }

    }

    /**
     * 显示各个级别的认证信息
     * @param kycLevel
     */
    private void showLevelInfo(KycLevelBean kycLevel) {
        if (kycLevel != null) {
            String displayLevel = kycLevel.getDisplayLevel();
            if (!TextUtils.isEmpty(displayLevel)) {
                if (displayLevel.equals("1")) {
                    //一级认证
                    oneLevel = kycLevel;
                    viewFinder.find(R.id.kyc_one_item).setVisibility(View.VISIBLE);
                    if (kycLevel.getWithdrawDailyLimit().equals("0")||kycLevel.getOtcDailyLimit().equals("0")) {
                        viewFinder.find(R.id.kyc_one_tips).setVisibility(View.GONE);
                    }else{
                        viewFinder.find(R.id.kyc_one_tips).setVisibility(View.VISIBLE);
                        viewFinder.textView(R.id.kyc_one_tips).setText(getString(R.string.string_kyc_one_tips,kycLevel.getWithdrawDailyLimit(),kycLevel.getWithdrawLimitToken(),kycLevel.getOtcDailyLimit(),kycLevel.getOtcLimitCurrency()));
                    }

                    setAuthStatusTv(kycOneRl,kycOneTx,kycLevel);
                }else if (displayLevel.equals("2")) {
                    //二级认证
                    twoLevel = kycLevel;
                    viewFinder.find(R.id.kyc_two_item).setVisibility(View.VISIBLE);
                    if (kycLevel.getWithdrawDailyLimit().equals("0")||kycLevel.getOtcDailyLimit().equals("0")) {
                        viewFinder.find(R.id.kyc_two_tips).setVisibility(View.GONE);
                    }else{
                        viewFinder.find(R.id.kyc_two_tips).setVisibility(View.VISIBLE);
                        viewFinder.textView(R.id.kyc_two_tips).setText(getString(R.string.string_kyc_one_tips,kycLevel.getWithdrawDailyLimit(),kycLevel.getWithdrawLimitToken(),kycLevel.getOtcDailyLimit(),kycLevel.getOtcLimitCurrency()));
                    }

                    setAuthStatusTv(kycTwoRl,kycTwoTx,kycLevel);
                }else if (displayLevel.equals("3")) {
                    //三级认证
                    threeLevel = kycLevel;
                    viewFinder.find(R.id.kyc_three_item).setVisibility(View.VISIBLE);
                    if (kycLevel.getWithdrawDailyLimit().equals("0")||kycLevel.getOtcDailyLimit().equals("0")) {
                        viewFinder.find(R.id.kyc_three_tips).setVisibility(View.GONE);
                    }else{
                        viewFinder.find(R.id.kyc_three_tips).setVisibility(View.VISIBLE);
                        viewFinder.textView(R.id.kyc_three_tips).setText(getString(R.string.string_kyc_one_tips,kycLevel.getWithdrawDailyLimit(),kycLevel.getWithdrawLimitToken(),kycLevel.getOtcDailyLimit(),kycLevel.getOtcLimitCurrency()));
                    }

                    setAuthStatusTv(kycThreeRl,kycThreeTx,kycLevel);
                }

                //更新认证按钮状态提示
                if (oneLevel != null) {
                    if (oneLevel.getVerifyStatus() != VERIFY_STATUS.VERIFY_CHECKED.getmStatus()) {
                        kycTwoTx.setText(getString(R.string.string_kyc_level_one_complete_first));
                        kycThreeTx.setText(getString(R.string.string_kyc_level_one_complete_first));
                        kycTwoRl.setClickable(false);
                        kycThreeRl.setClickable(false);
                        kycTwoTx.setTextAppearance(this,R.style.BodyS_Grey);
                        kycThreeTx.setTextAppearance(this,R.style.BodyS_Grey);
                        kycTwoTx.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
                        kycThreeTx.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);

                    }else if(twoLevel!=null){
                        if (twoLevel.getVerifyStatus() != VERIFY_STATUS.VERIFY_CHECKED.getmStatus()) {
                            kycThreeTx.setText(getString(R.string.string_kyc_level_two_complete_first));
                            kycThreeRl.setClickable(false);
                            kycThreeTx.setTextAppearance(this,R.style.BodyS_Grey);
                            kycThreeTx.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
                        }
                    }
                }
            }
        }
        
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        getPresenter().Refresh();
        smartRefresh.finishRefresh(1000/*,false*/);//传入false表示刷新失败
    }
}
