/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BindGAPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import android.os.CountDownTimer;
import android.text.TextUtils;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.sdk.security.SecurityApi;
import io.bhex.sdk.security.bean.OrderIdParamResponse;
import io.bhex.app.view.InputView;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;

public class BindGAPresenter extends BasePresenter<BindGAPresenter.BindGAUI> {
    private String orderId="";

    public void requestVerifyCodeOfBindGA(boolean isEmail, String token) {
        SecurityApi.sendBindGAVerifyCode(isEmail,new SimpleResponseListener<OrderIdParamResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("",getString(R.string.string_loading_hint_text));
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(OrderIdParamResponse data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data,true)) {
                    orderId = data.getOrderId();
                    getUI().setAuthTvStatus(false);
                    timer.start();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);

            }
        });
    }

    public void requestBindGA(String verifyCode, String googleVerifyCode) {
        if(TextUtils.isEmpty(orderId)) {
            ToastUtils.showShort(getActivity(),getString(R.string.string_verify_code_invalid));
            return;
        }
        SecurityApi.bindGA(orderId,verifyCode,googleVerifyCode,new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("",getString(R.string.string_loading_hint_text));
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ResultResponse data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data,true)) {
                    ToastUtils.showLong(getActivity(),getString(R.string.string_bing_ga_success));
                    getActivity().finish();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showLong(getActivity(),getString(R.string.string_bing_ga_failed));
            }
        });

    }

    public boolean checkInputContentIsEmpty(InputView inputVerifyCode, InputView inputGoogleCode) {
        String verifyCode = inputVerifyCode.getInputString();
        String googleCode = inputGoogleCode.getInputString();

        return !TextUtils.isEmpty(verifyCode) && !TextUtils.isEmpty(googleCode);
    }

    public interface BindGAUI extends AppUI {

        void setAuthTvStatus(boolean b);

        void setAuthTv(String s);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, BindGAUI ui) {
        super.onUIReady(activity, ui);

    }

    /**
     * 倒计时
     */
    private CountDownTimer timer = new CountDownTimer(AppData.DOWN_TIME_CODE, AppData.DOWN_TIME_INTERVAL_CODE) {
        @Override
        public void onTick(long millisUntilFinished) {
            getUI().setAuthTv((millisUntilFinished / 1000)
                    + getActivity().getResources().getString(
                    R.string.after_second));
        }

        @Override
        public void onFinish() {
            getUI().setAuthTvStatus(true);
            getUI().setAuthTv(getResources().getString(
                    R.string.string_get_auth_code));
        }
    };

}
