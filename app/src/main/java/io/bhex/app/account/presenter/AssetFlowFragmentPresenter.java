/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AssetFlowFragmentPresenter.java
 *   @Date: 12/17/18 2:23 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.account.presenter;

import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BaseFragmentPresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.trade.AssetApi;
import io.bhex.sdk.trade.bean.AssetRecordResponse;

public class AssetFlowFragmentPresenter extends BaseFragmentPresenter<AssetFlowFragmentPresenter.AssetFlowFragmentUI> {


    private List<AssetRecordResponse.RecordBean> currentData;
    private String pageId="";


    public interface AssetFlowFragmentUI extends AppUI {

        void showRecordList(List<AssetRecordResponse.RecordBean> datas);

        void loadComplete();

        void loadEnd();

        void loadFailed();
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, AssetFlowFragmentPresenter.AssetFlowFragmentUI ui) {
        super.onUIReady(activity, ui);
    }

    public void loadMore(int tabType, String tokenId, boolean isLoadMore) {
        getRecord(tabType, tokenId,  isLoadMore);
    }

    //获取记录
    public void getRecord(int tabType, String tokenId, final boolean isLoadMore) {
        if (isLoadMore) {
            if (currentData != null) {
                if (!currentData.isEmpty()) {
                    pageId = currentData.get(currentData.size()-1).getId();
                }
            }
        }else {
            pageId ="";
        }

        AssetApi.RequestAssetRecordHistory(tokenId,pageId, new SimpleResponseListener<AssetRecordResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
                if (isLoadMore) {
                    getUI().loadComplete();
                }

            }

            @Override
            public void onSuccess(AssetRecordResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<AssetRecordResponse.RecordBean> datas = response.getArray();
                    if (datas != null) {
                        if (isLoadMore) {
                            currentData.addAll(datas);
                        }else{
                            currentData = datas;
                        }

                        getUI().showRecordList(currentData);
                        if (datas.size()<AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        }else{
                            getUI().loadComplete();
                        }
                    }else{
                        getUI().loadComplete();
                    }
                }else{
                    getUI().loadFailed();
                }

            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
