/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: SettingActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.BuildConfig;
import io.bhex.app.R;
import io.bhex.app.account.event.LocaleChangeEvent;
import io.bhex.app.account.presenter.SettingPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.DialogUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.VersionUpdateUtil;
import io.bhex.app.view.TopBar;
import io.bhex.app.view.alertview.AlertView;
import io.bhex.app.view.alertview.OnItemClickListener;
import io.bhex.baselib.utils.DevicesUtil;
import io.bhex.baselib.utils.MD5Utils;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.UrlsConfig;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.config.AppSetting;
import io.bhex.sdk.config.bean.BackupDomainBean;
import io.bhex.sdk.config.domain.BackupDomainManager;
import io.bhex.sdk.data_manager.RateAndLocalManager;

/**
 * ================================================
 * 描   述：设置
 * ================================================
 */

public class SettingActivity extends BaseActivity<SettingPresenter, SettingPresenter.SettingUI> implements SettingPresenter.SettingUI, View.OnClickListener {

    private int clickCount = 0;
    private TopBar topBar;
    private AlertView redGreen;

    @Override
    protected int getContentView() {
        return R.layout.activity_setting_layout;
    }

    @Override
    protected SettingPresenter createPresenter() {
        return new SettingPresenter();
    }

    @Override
    protected SettingPresenter.SettingUI getUI() {
        return this;
    }

    @Override
    protected void onResume() {
        super.onResume();
        initView();
        if (UserInfo.isLogin()) {
            viewFinder.find(R.id.btn_logout).setVisibility(View.VISIBLE);
        } else {
            viewFinder.find(R.id.btn_logout).setVisibility(View.GONE);
        }

        setRedGreenStatus();
    }

    private void setRedGreenStatus() {
        String redGreen = getString(AppSetting.getInstance().isGreenRose() ? R.string.string_green_rose_red_fell : R.string.string_red_rose_green_fell);
        viewFinder.textView(R.id.setting_redGreen).setText(redGreen);
    }

    @Override
    protected void initView() {
        super.initView();

        topBar = viewFinder.find(R.id.topBar);
        this.setTopbarNight(topBar);

        viewFinder.textView(R.id.setting_language_select_tx).setText(RateAndLocalManager.GetInstance(this).getCurLocalKind().name);
        viewFinder.textView(R.id.setting_exchange_rate_select_tx).setText(RateAndLocalManager.GetInstance(this).getCurRateKind().name);
        viewFinder.textView(R.id.setting_version_tx).setText(DevicesUtil.getAppVersionName(this));
        TextView gitVersion = viewFinder.textView(R.id.setting_git_version);
        String gitCommitId = BuildConfig.GIT_COMMIT_ID;
        if (!TextUtils.isEmpty(gitCommitId)) {
            if (gitCommitId.length() > 6) {
                gitCommitId = gitCommitId.substring(0, 6);
            }
            gitVersion.setText("(" + gitCommitId + ")");
        }

        //设置当前的线路
        List<BackupDomainBean> backupDomainList = BackupDomainManager.getInstance().getBackupDomainList();
        BackupDomainBean currentDomain = BackupDomainManager.getInstance().getCurrentDomain();
        if (backupDomainList!=null && currentDomain != null) {
            for (int i = 0; i < backupDomainList.size(); i++) {
                BackupDomainBean backupDomainBean = backupDomainList.get(i);
                if (backupDomainBean.getDomain().equals(currentDomain.getDomain())) {
                    int lineNo = i+1;
                    viewFinder.textView(R.id.setting_select_lines).setText(getString(R.string.string_line)+lineNo);
                    break;
                }
            }
//            viewFinder.textView(R.id.setting_select_lines).setText(currentDomain.getDomain());
        }

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.setting_exchange_rate_rela).setOnClickListener(this);
        viewFinder.find(R.id.setting_language_rela).setOnClickListener(this);
        viewFinder.find(R.id.setting_cache_rela).setOnClickListener(this);
        viewFinder.find(R.id.setting_redGreen_rela).setOnClickListener(this);
        viewFinder.find(R.id.setting_lines_rela).setOnClickListener(this);
        viewFinder.find(R.id.setting_version_rela).setOnClickListener(this);
        viewFinder.find(R.id.btn_logout).setOnClickListener(this);

        EventBus.getDefault().register(this);

    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.setting_exchange_rate_rela:
                //汇率
                startActivity(new Intent().setClass(this, RateSelectActivity.class));
                break;
            case R.id.setting_language_rela:
                //语言
                startActivity(new Intent().setClass(this, LocalLangSelectActivity.class));

                break;
            case R.id.setting_version_rela:

                VersionUpdateUtil.checkVersionUpdate(this, false);

                clickCount++;
                if (clickCount >= 5) {
                    String tip = MD5Utils.encode(UrlsConfig.API_SERVER_URL + "bhex.com");
                    if (!TextUtils.isEmpty(tip) && tip.length() > 10)
                        ToastUtils.showLong(this, tip.substring(0, 10));
                    clickCount = 0;
                }
                break;
            case R.id.setting_redGreen_rela:
                //设置涨跌颜色
                showRedGreenSelectDialog(this);
                break;
            case R.id.setting_lines_rela:
                //切换线路
                IntentUtils.goSwitchLines(this);
                break;
            case R.id.setting_cache_rela:
                //清楚缓存

                break;
            case R.id.btn_logout:
                //退出
                DialogUtils.showDialog(this, "", getString(R.string.string_are_you_sure_logout), getString(R.string.string_sure), getString(R.string.string_cancel), true, new DialogUtils.OnButtonEventListener() {
                    @Override
                    public void onConfirm() {
                        getPresenter().logout();
                    }

                    @Override
                    public void onCancel() {

                    }
                });

                break;
        }
    }

    /**
     * 设置涨跌颜色
     * @param context
     */
    private void showRedGreenSelectDialog(Context context) {

        List<String> redGreenArray = new ArrayList<>();
        redGreenArray.add(getString(R.string.string_green_rose_red_fell));
        redGreenArray.add(getString(R.string.string_red_rose_green_fell));
        List<String> selectArray = new ArrayList<>();
        boolean greenRose = AppSetting.getInstance().isGreenRose();
        selectArray.add(getString(greenRose ? R.string.string_green_rose_red_fell : R.string.string_red_rose_green_fell));
        redGreen = new AlertView(null, null, getString(R.string.string_cancel), selectArray, redGreenArray, context, AlertView.Style.ActionSheet, new OnItemClickListener() {
            @Override
            public void onItemClick(Object o, int position) {
                if (position == -1) {
                    return;
                }
                if (position == 0) {
                    //切换到:绿涨红跌
                    AppSetting.getInstance().setGreenRose(true);
                } else {
                    //切换到:红涨绿跌
                    AppSetting.getInstance().setGreenRose(false);
                }
                setRedGreenStatus();
            }
        });
        redGreen.show();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void changeLanguage(LocaleChangeEvent changeEvent){
        misNeedRomve = 0;
        //recreate();
        //restartApp();
    }
}
