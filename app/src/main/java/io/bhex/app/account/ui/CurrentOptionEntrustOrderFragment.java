/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CurrentOptionEntrustOrderFragment.java
 *   @Date: 1/24/19 8:12 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.account.ui;

import android.content.Context;
import android.view.View;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.presenter.CurrentOptionEntrustOrderFragmentPresenter;
import io.bhex.app.base.BaseListFreshFragment;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.trade.bean.OrderBean;

public class CurrentOptionEntrustOrderFragment extends BaseListFreshFragment<CurrentOptionEntrustOrderFragmentPresenter, CurrentOptionEntrustOrderFragmentPresenter.CurrentOptionEntrustOrderFragmentUI> implements CurrentOptionEntrustOrderFragmentPresenter.CurrentOptionEntrustOrderFragmentUI{
    @Override
    protected CurrentOptionEntrustOrderFragmentPresenter.CurrentOptionEntrustOrderFragmentUI getUI() {
        return this;
    }

    @Override
    protected CurrentOptionEntrustOrderFragmentPresenter createPresenter() {
        return new CurrentOptionEntrustOrderFragmentPresenter();
    }

    @Override
    public void showOrders(List<OrderBean> currentOrders) {
        if (adapter == null) {
            adapter = new OptionOpenOrdersAdapter(getActivity(),currentOrders);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this,recyclerView);
            adapter.setEnableLoadMore(true);

            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            recyclerView.setItemAnimator(new DefaultItemAnimator());

            adapter.setEmptyView(emptyView);
            recyclerView.setAdapter(adapter);
            adapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                    if (view.getId() == R.id.revoke_order) {
                        OrderBean itemModel = (OrderBean) adapter.getData().get(position);
                        getPresenter().cancelOrder(itemModel.getOrderId(),false);
                    }
                }
            });
        } else {
            adapter.setNewData(currentOrders);
        }
    }

    public static class OptionOpenOrdersAdapter  extends BaseQuickAdapter<OrderBean, BaseViewHolder> {

        private Context mContext;
        public OptionOpenOrdersAdapter(Context context, List<OrderBean> data) {
            super(R.layout.item_current_option_order_layout, data);
            mContext = context;
        }


        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final OrderBean itemModel) {
//        String title = KlineUtils.getBuyOrSellTxt(mContext, itemModel.getSide()) + itemModel.getBaseTokenName() + " / " + itemModel.getQuoteTokenName();
            if(itemModel == null)
                return;
            String title = KlineUtils.getOptionBuyOrSellTxt(mContext, itemModel.getSide());
            baseViewHolder.setText(R.id.order_buy_type, title);
            baseViewHolder.setTextColor(R.id.order_buy_type,KlineUtils.getBuyOrSellColor(mContext,itemModel.getSide()));
            baseViewHolder.setText(R.id.order_coin_name,itemModel.getSymbolName());
            baseViewHolder.setText(R.id.order_time, DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getTime()), "HH:mm:ss yyyy/MM/dd"));
            baseViewHolder.setText(R.id.order_price, KlineUtils.getPrice(mContext, itemModel));

            int tokenDigit = AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(itemModel.getSymbolId() + itemModel.getQuoteTokenId());
            baseViewHolder.setText(R.id.order_entrust_amount_money, NumberUtils.roundFormatDown(itemModel.amount,tokenDigit)+" " + itemModel.getQuoteTokenName());
            baseViewHolder.setText(R.id.order_entrust_amount, itemModel.getOrigQty() +" "+ mContext.getString(R.string.string_option_unit));
            int baseDigit = AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(itemModel.getSymbolId() + itemModel.getBaseTokenId());
            baseViewHolder.setText(R.id.order_deal_amount, NumberUtils.roundFormatDown(itemModel.getExecutedQty(),baseDigit)+" "+ mContext.getString(R.string.string_option_unit));
            baseViewHolder.setText(R.id.order_undeal_amount, NumberUtils.roundFormatDown(itemModel.noExecutedQty,baseDigit)+" "+ mContext.getString(R.string.string_option_unit));

            baseViewHolder.addOnClickListener(R.id.revoke_order);
            baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                }
            });
        }
    }
}
