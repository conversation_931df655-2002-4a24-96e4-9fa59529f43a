/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: HistoryEntrustOrderFragment.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.List;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import io.bhex.app.R;
import io.bhex.app.account.presenter.MarginHistoryDealRecordFragmentPresenter;
import io.bhex.app.base.BaseFragment;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.trade.bean.OrderDealDetailResponse;

/**
 * ================================================
 * 描   述：杠杆委托订单
 * ================================================
 */

public class MarginHistoryDealRecordFragment extends BaseFragment<MarginHistoryDealRecordFragmentPresenter, MarginHistoryDealRecordFragmentPresenter.HistoryDealRecordOrderUI> implements MarginHistoryDealRecordFragmentPresenter.HistoryDealRecordOrderUI, BaseQuickAdapter.RequestLoadMoreListener, OnRefreshListener {
    private RecyclerView recyclerView;
    private HistoryDealRecordAdapter adapter;
    private SmartRefreshLayout swipeRefresh;
    private View emptyView;

    @Override
    protected MarginHistoryDealRecordFragmentPresenter.HistoryDealRecordOrderUI getUI() {
        return this;
    }

    @Override
    protected MarginHistoryDealRecordFragmentPresenter createPresenter() {
        return new MarginHistoryDealRecordFragmentPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_entrust_order_layout, null, false);
    }

    @Override
    public void onStart() {
        super.onStart();
    }

    @Override
    public void onStop() {
        super.onStop();
    }

    @Override
    protected void initViews() {
        super.initViews();
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        recyclerView = viewFinder.find(R.id.recyclerView);
        viewFinder.find(R.id.tab_ll).setVisibility(View.GONE);

        Bundle arguments = getArguments();
        if (arguments != null) {
            String symbol = arguments.getString("symbol");
        }

        LayoutInflater layoutInflater = LayoutInflater.from(getActivity());
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
        emptyView.setLayoutParams(layoutParams);

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        swipeRefresh.setOnRefreshListener(this);
    }

    @Override
    public void onLoadMoreRequested() {
        getPresenter().loadMore();
    }

    @Override
    public void onRefresh(RefreshLayout refreshLayout) {
        getPresenter().getHistoryDealRecords(false);
        refreshLayout.finishRefresh(1000);
    }

    @Override
    public void loadMoreComplete() {
//        if (swipeRefresh.isRefreshing()) {
//            swipeRefresh.setRefreshing(false);
//        }
        if (adapter != null) {
            adapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {
        if (adapter != null) {
            adapter.loadMoreFail();
        }
    }

    @Override
    public void loadEnd() {
        if (adapter != null) {
            adapter.loadMoreEnd();
        }
    }

    @Override
    public void showOrders(List<OrderDealDetailResponse.DealOrderBean> currentOrders) {
        if (adapter == null) {

            adapter = new HistoryDealRecordAdapter(getActivity(), currentOrders);
//            adapter.openLoadAnimation(BaseQuickAdapter.SCALEIN);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this,recyclerView);
            adapter.setEmptyView(emptyView);

//            swipeRefresh.setColorSchemeColors(Color.RED, Color.BLUE, Color.GREEN);
//            swipeRefresh.setOnRefreshListener(this);

            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
//        recyclerView.setItemAnimator(new DefaultItemAnimator());
//        recyclerView.addItemDecoration(new DividerItemDecoration(getContext(), LinearLayoutManager.VERTICAL));

            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(currentOrders);
        }
    }

    public static class HistoryDealRecordAdapter extends BaseQuickAdapter<OrderDealDetailResponse.DealOrderBean,BaseViewHolder> {

        private Context mContext;

        public HistoryDealRecordAdapter(Context context, List<OrderDealDetailResponse.DealOrderBean> data) {
            super(R.layout.item_history_deal_record_layout, data);
            mContext = context;
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final OrderDealDetailResponse.DealOrderBean itemModel) {
            int tokenDigit = AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(itemModel.getSymbolId() + itemModel.getQuoteTokenId());
            int baseDigit = AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(itemModel.getSymbolId() + itemModel.getBaseTokenId());
            int amountDigit = AppConfigManager.GetInstance().getAmountDigitBySymbolIdAndTokenId(itemModel.getSymbolId() + itemModel.getQuoteTokenId());

            baseViewHolder.setVisible(R.id.item_divider, baseViewHolder.getAdapterPosition() != mData.size());
            String title = itemModel.getBaseTokenName() + " / " + itemModel.getQuoteTokenName();
            baseViewHolder.setText(R.id.order_name, title);
            baseViewHolder.setTextColor(R.id.order_type,KlineUtils.getBuyOrSellColor(mContext,itemModel.getSide()));
            baseViewHolder.setText(R.id.order_type,KlineUtils.getPriceModeTxt(mContext, itemModel.getType()) +" " + KlineUtils.getBuyOrSellTxt(mContext, itemModel.getSide()));
            baseViewHolder.setText(R.id.title, mContext.getString(R.string.string_time));
            baseViewHolder.setText(R.id.value, DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getTime()), "HH:mm:ss MM/dd"));
            baseViewHolder.setText(R.id.title2,  mContext.getString(R.string.string_deal_price));
            baseViewHolder.setText(R.id.value2, KlineUtils.roundFormatDown(itemModel.getPrice(),tokenDigit)+" "+itemModel.getQuoteTokenName());
            baseViewHolder.setText(R.id.title3, mContext.getString(R.string.string_amount));
            baseViewHolder.setText(R.id.value3, KlineUtils.roundFormatDown(itemModel.getQuantity(),baseDigit)+" "+itemModel.getBaseTokenName());
            baseViewHolder.setText(R.id.title4, mContext.getString(R.string.string_order_deal_money));
            baseViewHolder.setText(R.id.value4, KlineUtils.roundFormatDown(String.valueOf(NumberUtils.mul(itemModel.getQuantity(),itemModel.getPrice())),amountDigit)+" "+itemModel.getQuoteTokenName());
            baseViewHolder.setText(R.id.title5, mContext.getString(R.string.string_fee));
            if (!TextUtils.isEmpty(itemModel.getFee())) {
                if (Double.valueOf(itemModel.getFee())>0) {
                    baseViewHolder.setText(R.id.value5, KlineUtils.roundFormatDown(itemModel.getFee(), AppData.Config.DIGIT_DEFAULT_VALUE)+" "+itemModel.getFeeTokenName());
                }else{
                    baseViewHolder.setText(R.id.value5, "0");
                }
            }else{
                baseViewHolder.setText(R.id.value5, "0");
            }
            baseViewHolder.setText(R.id.title6, mContext.getString(R.string.string_trade_order_id));
            baseViewHolder.setText(R.id.value6,itemModel.getTradeId());
            baseViewHolder.getView(R.id.title6).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    CommonUtil.copyText(mContext,itemModel.getTradeId());
                }
            });
        }

    }
}
