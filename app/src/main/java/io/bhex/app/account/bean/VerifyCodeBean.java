/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: VerifyCodeBean.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.bean;

import io.bhex.baselib.network.response.BaseResponse;

/**
 * ================================================
 * 描   述：验证码
 * ================================================
 */

public class VerifyCodeBean extends BaseResponse {
    //标识id
    private String orderId;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
}
