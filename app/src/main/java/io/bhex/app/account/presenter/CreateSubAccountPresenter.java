package io.bhex.app.account.presenter;

import io.bhex.app.R;
import io.bhex.app.account.bean.AccountTypeBean;
import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.AccountInfoApi;
import io.bhex.sdk.account.bean.AccountTypesResponse;
import io.bhex.sdk.account.bean.SubAccountBean;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-10-31
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class CreateSubAccountPresenter extends BasePresenter<CreateSubAccountPresenter.CreateSubAccountUI> {

    public interface CreateSubAccountUI extends AppUI{

        void showAccountTypes(AccountTypesResponse response);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, CreateSubAccountUI ui) {
        super.onUIReady(activity, ui);
        getAccountTypes();
    }

    /**
     * 获取可创建的账户类型
     */
    private void getAccountTypes() {
        AccountInfoApi.getAccountTypes(new SimpleResponseListener<AccountTypesResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(AccountTypesResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showAccountTypes(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    /**
     * 创建子账户
     * @param currentAccountType
     * @param accountName
     */
    public void createSubAccount(AccountTypeBean currentAccountType, String accountName) {
        AccountInfoApi.subAccountCreate(currentAccountType.getAccountType(),accountName,new SimpleResponseListener<SubAccountBean>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(SubAccountBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    ToastUtils.showShort(getActivity().getResources().getString(R.string.string_create_success));
                    getActivity().finish();
                }
//                else{
//                    ToastUtils.showShort(getActivity().getResources().getString(R.string.string_create_failed));
//                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(getActivity().getResources().getString(R.string.string_net_exception));
            }
        });
    }
}
