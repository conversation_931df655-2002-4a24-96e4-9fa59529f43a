package io.bhex.app.account.ui;

import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.bean.AccountTypeBean;
import io.bhex.app.account.presenter.CreateSubAccountPresenter;
import io.bhex.app.account.utils.AccountUtil;
import io.bhex.app.base.BaseActivity;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.app.view.alertview.AlertView;
import io.bhex.sdk.account.bean.AccountTypesResponse;
import io.bhex.sdk.enums.ORDER_ENTRUST_TYPE;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-10-31
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class CreateSubAccountActivity extends BaseActivity<CreateSubAccountPresenter, CreateSubAccountPresenter.CreateSubAccountUI> implements CreateSubAccountPresenter.CreateSubAccountUI, View.OnClickListener {
    private EditText selectSubAccountTypeEt;
    private EditText subAccountNameEt;
    private ArrayList<AccountTypeBean> accountTypeList = new ArrayList<>();
    private AccountTypeBean currentAccountType;

    @Override
    protected int getContentView() {
        return R.layout.activity_create_sub_account_layout;
    }

    @Override
    protected CreateSubAccountPresenter createPresenter() {
        return new CreateSubAccountPresenter();
    }

    @Override
    protected CreateSubAccountPresenter.CreateSubAccountUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        selectSubAccountTypeEt = viewFinder.editText(R.id.selectSubAccountType);
        subAccountNameEt = viewFinder.editText(R.id.subAccountName);

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.selectSubAccountType).setOnClickListener(this);
        viewFinder.find(R.id.createBtn).setOnClickListener(this);

    }



    @Override
    public void showAccountTypes(AccountTypesResponse response) {
        if (response != null) {
            List<Integer> accountTypes = response.getArray();
            if (accountTypes != null && accountTypes.size()>0) {
                ArrayList<AccountTypeBean> accountTypeBeans = handleAccountTypeData(accountTypes);
                if (accountTypeBeans != null && accountTypeBeans.size()>0) {
                    AccountTypeBean accountTypeBean = accountTypeBeans.get(0);
                    if (accountTypeBean != null) {
                        setCurrentAccountType(accountTypeBean);
                    }
                }
            }

        }
    }

    /**
     * 设置当前accountType
     * @param accountTypeBean
     */
    private void setCurrentAccountType(AccountTypeBean accountTypeBean) {
        if (accountTypeBean != null) {
            currentAccountType = accountTypeBean;
            selectSubAccountTypeEt.setText(accountTypeBean.getAccountName());
        }

    }

    /**
     * 处理账户类型数据
     * @param accountTypes
     * @return
     */
    private ArrayList<AccountTypeBean> handleAccountTypeData(List<Integer> accountTypes) {
        if (accountTypes != null) {
            accountTypeList.clear();
            for (Integer accountType : accountTypes) {
                AccountTypeBean accountTypeBean = new AccountTypeBean();
                accountTypeBean.setAccountType(accountType);
                String accountName = AccountUtil.getAccountTypeName(this, accountType);
                accountTypeBean.setAccountName(accountName);
                accountTypeList.add(accountTypeBean);
            }

        }

        return accountTypeList;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.selectSubAccountType:
                if (accountTypeList != null) {
                    showAccountTypeSelect();
                }
                break;
            case R.id.createBtn:
                if (currentAccountType == null) {
                    ToastUtils.showShort(getString(R.string.string_select_account_type_please));
                    return;
                }
                String accountName = subAccountNameEt.getText().toString().trim();
                if (TextUtils.isEmpty(accountName)) {
                    ToastUtils.showShort(getString(R.string.string_hint_account_name_please));
                    return;
                }

                getPresenter().createSubAccount(currentAccountType,accountName);

                break;
        }
    }

    /**
     * 显示要创建的账户类型列表
     */
    protected void showAccountTypeSelect() {
        closeKeyBoard(subAccountNameEt);
        final ArrayList<String> currentSelectList = new ArrayList<>();
        if (accountTypeList != null) {
            for (AccountTypeBean accountTypeBean : accountTypeList) {
                currentSelectList.add(accountTypeBean.getAccountName());
            }
        }
        if (currentSelectList != null&&currentSelectList.size()>0) {
            List<String> selectArr = new ArrayList<>();
            selectArr.add(currentAccountType==null?"":currentAccountType.getAccountName());

            AlertView.showSheet(this, selectArr,currentSelectList, getString(R.string.string_alert_title_select_sub_account_type), null, getString(R.string.string_cancel), new AlertView.DialogListener() {
                @Override
                public void onShow(final AlertView alert) {

                }

                @Override
                public void onDissmiss(AlertView alert) {

                }

                @Override
                public void onItemClick(int position, String item) {
                    if (position == -1) {
                        return;
                    }

                    if (accountTypeList != null) {
                        if (position < accountTypeList.size()) {
                            AccountTypeBean accountTypeBean = accountTypeList.get(position);
                            setCurrentAccountType(accountTypeBean);
                        }
                    }
                }
            });
        }else{
            DebugLog.e("current can set levers is null");
        }

    }
}
