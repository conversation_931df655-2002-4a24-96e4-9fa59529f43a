/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CoinAddressActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.presenter.CoinAddressPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.sdk.quote.bean.QuoteTokensBean;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.utils.PixelUtils;

/**
 * ================================================
 * 描   述：提币地址
 * ================================================
 */

public class CoinAddressActivity extends BaseActivity<CoinAddressPresenter,CoinAddressPresenter.CoinAddressUI> implements CoinAddressPresenter.CoinAddressUI, View.OnClickListener, SwipeRefreshLayout.OnRefreshListener, BaseQuickAdapter.RequestLoadMoreListener {
    private TopBar topBar;
    private RecyclerView recyclerView;
    private TokenListAdapter adapter;
    private SwipeRefreshLayout swipeRefresh;
    private View emptyView;

    @Override
    protected int getContentView() {
        return R.layout.activity_coin_address_layout;
    }

    @Override
    protected CoinAddressPresenter createPresenter() {
        return new CoinAddressPresenter();
    }

    @Override
    protected CoinAddressPresenter.CoinAddressUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        this.setTopbarNight(topBar);
        topBar.setLeftOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        recyclerView = viewFinder.find(R.id.recyclerView);
        LayoutInflater layoutInflater = LayoutInflater.from(this);
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(300);
        emptyView.setLayoutParams(layoutParams);
        emptyView.findViewById(R.id.empty_txt).setOnClickListener(this);
        emptyView.findViewById(R.id.empty_img).setOnClickListener(this);



    }

    @Override
    public void showQuotaTokens(List<QuoteTokensBean.TokenItem> datas) {
        if (adapter == null) {
            adapter = new TokenListAdapter(datas);
//            adapter.openLoadAnimation(BaseQuickAdapter.SCALEIN);
            adapter.isFirstOnly(false);
//            adapter.setOnLoadMoreListener(this);
            swipeRefresh.setColorSchemeColors(Color.RED, Color.BLUE, Color.GREEN);
            swipeRefresh.setOnRefreshListener(this);
            swipeRefresh.setEnabled(false);

            recyclerView.setLayoutManager(new LinearLayoutManager(this));
            recyclerView.setItemAnimator(new DefaultItemAnimator());
//            DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(getContext(), LinearLayoutManager.VERTICAL);
//            RecycleViewDivider dividerItemDecoration = new RecycleViewDivider(getContext(), LinearLayoutManager.HORIZONTAL, 2, SPEx.get(AppData.SPKEY.SKIN_IS_BLACK_MODE,false)?getResources().getColor(R.color.divider_line_color_night):getResources().getColor(R.color.divider_line_color), PixelUtils.dp2px(10), PixelUtils.dp2px(10));
//            dividerItemDecoration.setDrawable(ContextCompat.getDrawable(getActivity(),R.drawable.divider));
//            recyclerView.addItemDecoration(dividerItemDecoration);
            recyclerView.setAdapter(adapter);
//            adapter.setEmptyView(true, true, emptyView);
            adapter.setEmptyView(emptyView);
        } else {
            adapter.setNewData(datas);
        }
    }

    @Override
    public void onClick(View v) {

    }

    @Override
    public void onLoadMoreRequested() {
        swipeRefresh.postDelayed(new Runnable() {
            @Override
            public void run() {
                adapter.loadMoreComplete();
            }
        }, 500);
    }

    @Override
    public void onRefresh() {
        setRefreshing(false);
    }


    public void setRefreshing(final boolean refreshing) {
        swipeRefresh.post(new Runnable() {
            @Override
            public void run() {
                swipeRefresh.setRefreshing(refreshing);
            }
        });
    }

    private class TokenListAdapter extends BaseQuickAdapter<QuoteTokensBean.TokenItem,BaseViewHolder> {

        TokenListAdapter(List<QuoteTokensBean.TokenItem> data) {
            super(R.layout.item_quota_token_list_layout, data);
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final QuoteTokensBean.TokenItem itemModel) {
            baseViewHolder.setVisible(R.id.item_divider, baseViewHolder.getAdapterPosition() != mData.size());
//            ImageView tokenIcon = baseViewHolder.getView(R.id.token_icon);
//            Glide.with(mContext).load(itemModel.getIconUrl()).into(tokenIcon);
            baseViewHolder.setText(R.id.token_name,itemModel.getTokenName());
            baseViewHolder.setText(R.id.account_auth_status,itemModel.getAddressCount()+"");
            baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    IntentUtils.goCoinAddressList(CoinAddressActivity.this,itemModel);
                }
            });
        }
    }
}
