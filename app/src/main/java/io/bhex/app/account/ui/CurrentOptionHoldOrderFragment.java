/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CurrentOptionHoldOrderFragment.java
 *   @Date: 1/25/19 4:28 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.account.ui;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.presenter.CurrentOptionHoldOrderFragmentPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.base.BaseListFreshFragment;
import io.bhex.app.trade.ui.CreateOrderPopDialog;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.trade.bean.OptionHoldOrderResponse;

public class CurrentOptionHoldOrderFragment extends BaseListFreshFragment<CurrentOptionHoldOrderFragmentPresenter, CurrentOptionHoldOrderFragmentPresenter.CurrentOptionHoldOrderFragmentUI> implements CurrentOptionHoldOrderFragmentPresenter.CurrentOptionHoldOrderFragmentUI {
    @Override
    protected CurrentOptionHoldOrderFragmentPresenter.CurrentOptionHoldOrderFragmentUI getUI() {
        return this;
    }

    @Override
    protected CurrentOptionHoldOrderFragmentPresenter createPresenter() {
        return new CurrentOptionHoldOrderFragmentPresenter();
    }

    @Override
    public void showOrders(List<OptionHoldOrderResponse.OptionHoldOrderBean> currentOrders) {
        if (adapter == null) {
            adapter = new OptionHoldOrdersAdapter(getActivity(), currentOrders);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this, recyclerView);
            adapter.setEnableLoadMore(true);

            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            recyclerView.setItemAnimator(new DefaultItemAnimator());

            adapter.setEmptyView(emptyView);
            recyclerView.setAdapter(adapter);

        } else {
            adapter.setNewData(currentOrders);
        }
    }

    public static class OptionHoldOrdersAdapter extends BaseQuickAdapter<OptionHoldOrderResponse.OptionHoldOrderBean, BaseViewHolder> {

        private Context mContext;

        public OptionHoldOrdersAdapter(Context context, List<OptionHoldOrderResponse.OptionHoldOrderBean> data) {
            super(R.layout.item_option_hold_order_layout, data);
            mContext = context;
        }


        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final OptionHoldOrderResponse.OptionHoldOrderBean itemModel) {
            try {
                if(itemModel == null)
                    return;

                String title = "";
                int color = 0;
                if (itemModel.position.startsWith("-")) {
                    title = KlineUtils.getOptionBuyOrSellTxt(mContext, false);
                    color = KlineUtils.getBuyOrSellColor(mContext, false);
                } else {
                    title = KlineUtils.getOptionBuyOrSellTxt(mContext, true);
                    color = KlineUtils.getBuyOrSellColor(mContext, true);
                }
                baseViewHolder.setText(R.id.order_buy_type, title);
                baseViewHolder.setTextColor(R.id.order_buy_type, color);

                baseViewHolder.setText(R.id.order_coin_name, itemModel.symbolName);
                int tokenDigit = AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(itemModel.symbolId + itemModel.quoteTokenId);

                baseViewHolder.setText(R.id.option_indices, NumberUtils.roundFormatDown(itemModel.indices,tokenDigit) +" " + itemModel.quoteTokenName);
                baseViewHolder.setText(R.id.option_strikePrice, NumberUtils.roundFormatDown(itemModel.strikePrice,tokenDigit) +" " + itemModel.quoteTokenName);
                baseViewHolder.setText(R.id.option_hold_price, NumberUtils.roundFormatDown(itemModel.averagePrice,tokenDigit) +" " + itemModel.quoteTokenName);
                baseViewHolder.setText(R.id.option_market_price, itemModel.price +" " + itemModel.quoteTokenName);
                baseViewHolder.setText(R.id.option_position, String.valueOf(Math.abs(Double.valueOf(itemModel.position))) +" " + mContext.getString(R.string.string_option_unit));
                baseViewHolder.setText(R.id.option_availPosition, String.valueOf(Math.abs(Double.valueOf(itemModel.availPosition))) +" " + mContext.getString(R.string.string_option_unit));
                baseViewHolder.setText(R.id.option_changed, NumberUtils.roundFormatDown(itemModel.changed,tokenDigit)+" " + itemModel.quoteTokenName);
                if(itemModel.position.startsWith("-")){
                    baseViewHolder.setGone(R.id.option_margin_title, true);
                    baseViewHolder.setGone(R.id.option_margin, true);
                }
                else {
                    baseViewHolder.setGone(R.id.option_margin_title, false);
                    baseViewHolder.setGone(R.id.option_margin, false);
                }
                baseViewHolder.setText(R.id.option_margin, NumberUtils.roundFormatDown(itemModel.margin,tokenDigit)+" " + itemModel.quoteTokenName);

                baseViewHolder.setText(R.id.option_positionRights, NumberUtils.roundFormatDown(itemModel.positionRights,tokenDigit)+" " + itemModel.quoteTokenName);
                baseViewHolder.setText(R.id.option_changed_rate, "(" + itemModel.changedRate +  "%)");
                int colorChange = 0;
                if (!TextUtils.isEmpty(itemModel.changed) && itemModel.changed.startsWith("-")) {
                    colorChange = KlineUtils.getBuyOrSellColor(mContext, false);
                } else {
                    colorChange = KlineUtils.getBuyOrSellColor(mContext, true);
                }
                baseViewHolder.setTextColor(R.id.option_changed_rate, colorChange);

                baseViewHolder.getView(R.id.order_close).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //平仓
                        CreateOrderPopDialog dialog = new CreateOrderPopDialog(mContext, itemModel, new CreateOrderPopDialog.OnLoadingObserver() {
                            @Override
                            public void showLoading() {
                                if(mContext instanceof BaseActivity)
                                    ((BaseActivity)mContext).showProgressDialog("","");
                            }

                            @Override
                            public void hideLoading() {
                                if(mContext instanceof BaseActivity)
                                    ((BaseActivity)mContext).dismissProgressDialog();
                            }
                        });
                        dialog.ShowDialog();
                    }
                });
                baseViewHolder.getView(R.id.order_share).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //TODO 分享
                        IntentUtils.goShareProfit(mContext,itemModel, null);
                    }
                });
            } catch (Exception e) {

            }
        }
    }
}
