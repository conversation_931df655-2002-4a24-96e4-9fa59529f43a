/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FuturesAssetDetailActivity.java
 *   @Date: 19-7-18 下午8:11
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.view.TopBar;
import io.bhex.app.web.presenter.NullPresenter;
import io.bhex.sdk.trade.bean.FuturesAssetListResponse;
import io.bhex.sdk.trade.bean.OptionAssetListResponse;

import static io.bhex.baselib.constant.AppData.INTENT.KEY_ASSET;

public class FuturesAssetDetailActivity extends BaseActivity<NullPresenter,NullPresenter.NullUI> implements NullPresenter.NullUI {
    private FuturesAssetDetailFragment mFuturesAssetDetailFragment;
    private FuturesAssetListResponse.FuturesAssetBean assetItemBean;
    private TopBar topBar;

    @Override
    protected int getContentView() {
        return R.layout.activity_fragment_container_layout;
    }

    @Override
    protected NullPresenter createPresenter() {
        return new NullPresenter();
    }

    @Override
    protected NullPresenter.NullUI getUI() {
        return this;
    }



    @Override
    protected void initView() {
        super.initView();
        topBar = this.findViewById(R.id.topBar);
        topBar.setLeftOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        mFuturesAssetDetailFragment = new FuturesAssetDetailFragment();
        Intent intent = getIntent();
        if (intent != null) {
            assetItemBean = (FuturesAssetListResponse.FuturesAssetBean) intent.getSerializableExtra(KEY_ASSET);
            if (assetItemBean != null) {
                topBar.setTitle(assetItemBean.tokenName);

                Bundle bundle = new Bundle();
                bundle.putSerializable(KEY_ASSET,assetItemBean);
                mFuturesAssetDetailFragment.setArguments(bundle);
                getSupportFragmentManager()    //
                        .beginTransaction()
                        .add(R.id.fragment_container, mFuturesAssetDetailFragment)   // 此处的R.id.fragment_container是要盛放fragment的父容器
                        .commit();
            }
        }




    }

}

