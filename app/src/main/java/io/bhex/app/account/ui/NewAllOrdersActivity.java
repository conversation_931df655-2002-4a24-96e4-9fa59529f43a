/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: NewAllOrdersActivty.java
 *   @Date: 1/11/19 4:02 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.account.ui;

import android.view.View;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.BasicFunctionsUtil;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.mvp.BaseUI;
import io.bhex.sdk.config.bean.BasicFunctionsConfig;

public class NewAllOrdersActivity extends BaseActivity {
    private TopBar topBar;
    private BasicFunctionsConfig basicFunctionsConfig;

    @Override
    protected void initView() {
        super.initView();
        //功能模块显示控制
        basicFunctionsConfig = BasicFunctionsUtil.getBasicFunctionsConfig();
        findViewById(R.id.trade_orders_layout).setVisibility(basicFunctionsConfig.isExchange()?View.GONE:View.VISIBLE);
        findViewById(R.id.trade_orders_layout_divider).setVisibility(basicFunctionsConfig.isExchange()?View.GONE:View.VISIBLE);
        findViewById(R.id.futures_orders_layout).setVisibility(basicFunctionsConfig.isFuture()?View.GONE:View.VISIBLE);
        findViewById(R.id.futures_orders_layout_divider).setVisibility(basicFunctionsConfig.isFuture()?View.GONE:View.VISIBLE);
        findViewById(R.id.option_orders_layout).setVisibility(basicFunctionsConfig.isOption()?View.GONE:View.VISIBLE);
        findViewById(R.id.option_orders_layout_divider).setVisibility(basicFunctionsConfig.isOption()?View.GONE:View.VISIBLE);
        findViewById(R.id.otc_orders_layout).setVisibility(basicFunctionsConfig.isOtc()?View.GONE:View.VISIBLE);
        findViewById(R.id.otc_orders_layout_divider).setVisibility(basicFunctionsConfig.isOtc()?View.GONE:View.VISIBLE);

        findViewById(R.id.coinplus_orders_layout).setVisibility(basicFunctionsConfig.isBonus()?View.GONE:View.VISIBLE);
        findViewById(R.id.coinplus_orders_layout_divider).setVisibility(basicFunctionsConfig.isBonus()?View.GONE:View.VISIBLE);
        findViewById(R.id.staking_orders_layout).setVisibility(basicFunctionsConfig.isStaking()?View.GONE:View.VISIBLE);
        findViewById(R.id.staking_orders_layout_divider).setVisibility(basicFunctionsConfig.isStaking()?View.GONE:View.VISIBLE);

        findViewById(R.id.margin_orders_layout_divider).setVisibility(basicFunctionsConfig.isMargin()?View.GONE:View.VISIBLE);
        findViewById(R.id.margin_orders_layout).setVisibility(basicFunctionsConfig.isMargin()?View.GONE:View.VISIBLE);

        topBar = findViewById(R.id.topBar);
        this.setTopbarNight(topBar);
        topBar.setLeftOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        findViewById(R.id.trade_orders_layout).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IntentUtils.goAllOrders(NewAllOrdersActivity.this);
            }
        });
        //合约
        findViewById(R.id.futures_orders_layout).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IntentUtils.goAllFuturesOrders(NewAllOrdersActivity.this);
            }
        });
        findViewById(R.id.option_orders_layout).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IntentUtils.goAllOptionOrders(NewAllOrdersActivity.this);
            }
        });
        findViewById(R.id.otc_orders_layout).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IntentUtils.goOtcOrders(NewAllOrdersActivity.this);
            }
        });
        findViewById(R.id.coinplus_orders_layout).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IntentUtils.goCoinPlusOrders(NewAllOrdersActivity.this);
            }
        });
        findViewById(R.id.margin_orders_layout).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IntentUtils.goMarginOrders(NewAllOrdersActivity.this);
            }
        });
        findViewById(R.id.staking_orders_layout).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IntentUtils.goStakingOrders(NewAllOrdersActivity.this);
            }
        });

    }

    public static class NewAllOrdersPresenter extends BasePresenter<NewAllOrdersPresenter.NewAllOrdersUI> {
        public interface NewAllOrdersUI extends AppUI {

        }

    }

    @Override
    protected NewAllOrdersPresenter createPresenter() {
        return new NewAllOrdersPresenter();
    }

    @Override
    protected BaseUI getUI() {
        return this;
    }

    @Override
    protected int getContentView() {
        return R.layout.new_all_orders_layout;
    }

}
