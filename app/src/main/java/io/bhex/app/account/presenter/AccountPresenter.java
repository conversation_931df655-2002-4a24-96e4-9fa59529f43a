/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AccountPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.account.LoginApi;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.account.bean.enums.ERROR_CODE;
import io.bhex.sdk.config.bean.FunctionsBean;
import io.bhex.sdk.data_manager.AppConfigManager;

public class AccountPresenter extends BasePresenter<AccountPresenter.AccountUI> {
    public interface AccountUI extends AppUI {

        void resetUnloginStatus();

        void showUserInfo(UserInfoBean data);

        void updateUserLevelVisible(boolean userLevel);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, AccountUI ui) {
        super.onUIReady(activity, ui);

    }

    @Override
    public void onResume() {
        super.onResume();
        updateShowUserLevel();
        if (UserInfo.isLogin()) {
            getUserInfo();
        }
    }

    private void updateShowUserLevel() {
        FunctionsBean functions = AppConfigManager.GetInstance().getFunctions();
        if (functions!=null) {
            getUI().updateUserLevelVisible(functions.isUserLevel());
        } else {
            getUI().updateUserLevelVisible(false);
        }
    }

    /**
     * 获取用户信息
     */
    public void getUserInfo() {
        LoginApi.GetUserInfo(new SimpleResponseListener<UserInfoBean>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(UserInfoBean data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data, true)) {
                    //保存用户数据
                    UserManager.getInstance().saveUserInfo(data);
                    getUI().showUserInfo(data);
                } else {
                    if (data.getCode().equals(ERROR_CODE.NO_LOGIN.getCode())) {
                        getUI().resetUnloginStatus();
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

}
