/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: HistoryEntrustOrderFragment.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.flyco.tablayout.SegmentTabLayout;
import com.flyco.tablayout.listener.OnTabSelectListener;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.List;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import io.bhex.app.R;
import io.bhex.app.account.presenter.HistoryEntrustFragmentPresenter;
import io.bhex.app.base.BaseFragment;
import io.bhex.app.trade.adapter.HistoryPlanOrdersAdapter;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.enums.ACCOUNT_TYPE;
import io.bhex.sdk.enums.ORDER_TYPE;
import io.bhex.sdk.trade.bean.OrderBean;
import io.bhex.sdk.trade.bean.PlanOrderBean;

/**
 * ================================================
 * 描   述：委托订单
 * ================================================
 */

public class HistoryEntrustOrderFragment extends BaseFragment<HistoryEntrustFragmentPresenter, HistoryEntrustFragmentPresenter.HistoryEntrustOrderUI> implements HistoryEntrustFragmentPresenter.HistoryEntrustOrderUI, BaseQuickAdapter.RequestLoadMoreListener, OnRefreshListener {
    private RecyclerView recyclerView;
    private HistoryEntrustAdapter adapter;
    private SmartRefreshLayout swipeRefresh;

    private HistoryPlanOrdersAdapter planOrdersAdapter;
    private SegmentTabLayout entrustTab;
    private int currentShowOrdersTab = ORDER_TYPE.ORDER_TYPE_HISTOREY_GENERAL_ENTRUSTMENT.getOrderType();//默认显示历史委托tab
    private List<OrderBean> historyOrders = new ArrayList<>();
    private List<PlanOrderBean> historyPlanOrders = new ArrayList<>();

    @Override
    protected HistoryEntrustFragmentPresenter.HistoryEntrustOrderUI getUI() {
        return this;
    }

    @Override
    protected HistoryEntrustFragmentPresenter createPresenter() {
        return new HistoryEntrustFragmentPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_entrust_order_layout, null, false);
    }

    @Override
    public void onStart() {
        super.onStart();
    }

    @Override
    public void onStop() {
        super.onStop();
    }

    @Override
    protected void initViews() {
        super.initViews();
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        recyclerView = viewFinder.find(R.id.recyclerView);

        Bundle arguments = getArguments();
        if (arguments != null) {
            String symbol = arguments.getString("symbol");
        }

        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        recyclerView.setItemAnimator(new DefaultItemAnimator());

        LayoutInflater layoutInflater = LayoutInflater.from(getActivity());
        View emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
        emptyView.setLayoutParams(layoutParams);

        adapter = new HistoryEntrustAdapter(getActivity(), historyOrders);
        adapter.isFirstOnly(false);
        adapter.setOnLoadMoreListener(this,recyclerView);
        adapter.setEnableLoadMore(true);


        adapter.setEmptyView(emptyView);
        recyclerView.setAdapter(adapter);

        View emptyView1 = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams1 = emptyView1.getLayoutParams();
        layoutParams1.height = PixelUtils.dp2px(200);
        emptyView1.setLayoutParams(layoutParams1);

        planOrdersAdapter = new HistoryPlanOrdersAdapter(historyPlanOrders, ACCOUNT_TYPE.ASSET_WALLET.getType());
        planOrdersAdapter.isFirstOnly(false);
        planOrdersAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                getPresenter().getHistoryPlanOrders(true);
            }
        },recyclerView);
        planOrdersAdapter.setEnableLoadMore(true);

        planOrdersAdapter.setEmptyView(emptyView1);
        entrustTab = viewFinder.find(R.id.tabEntrust);
        entrustTab.setTabData(new String[]{getString(R.string.string_ordinary_order),getString(R.string.string_trigger_order)});
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        swipeRefresh.setOnRefreshListener(this);

        entrustTab.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                if (position ==0) {
                    currentShowOrdersTab=ORDER_TYPE.ORDER_TYPE_HISTOREY_GENERAL_ENTRUSTMENT.getOrderType();
                    recyclerView.setAdapter(adapter);
                    //普通委托
                    getPresenter().getHistoryEntrustOrders(false);
                }else if(position ==1){
                    currentShowOrdersTab=ORDER_TYPE.ORDER_TYPE_HISTOREY_PLANNING_ENTRUSTMENT.getOrderType();
                    recyclerView.setAdapter(planOrdersAdapter);
                    //计划委托
                    getPresenter().getHistoryPlanOrders(false);
                }
            }

            @Override
            public void onTabReselect(int position) {

            }
        });
    }

    @Override
    public void onLoadMoreRequested() {
        getPresenter().loadMore();
    }

    @Override
    public void onRefresh(RefreshLayout refreshLayout) {
        if (currentShowOrdersTab == ORDER_TYPE.ORDER_TYPE_GENERAL_ENTRUSTMENT.getOrderType()) {
            getPresenter().getHistoryEntrustOrders(false);
        } else {
            getPresenter().getHistoryPlanOrders(false);
        }
        refreshLayout.finishRefresh(1000);
    }

    @Override
    public void loadMoreComplete() {
//        if (swipeRefresh.isRefreshing()) {
//            swipeRefresh.setRefreshing(false);
//        }
        if (adapter != null) {
            adapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {
        if (adapter != null) {
            adapter.loadMoreFail();
        }
    }

    @Override
    public void loadEnd() {
        if (adapter != null) {
            adapter.loadMoreEnd();
        }
    }

    @Override
    public void loadMorePlanOrderComplete() {
        if (planOrdersAdapter != null) {
            planOrdersAdapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMorePlanOrderFailed() {
        if (planOrdersAdapter != null) {
            planOrdersAdapter.loadMoreFail();
        }
    }

    @Override
    public void loadPlanOrderEnd() {
        if (planOrdersAdapter != null) {
            planOrdersAdapter.loadMoreEnd();
        }
    }
    @Override
    public void showOrders(List<OrderBean> datas) {
//        if (currentShowOrdersTab != ORDER_TYPE.ORDER_TYPE_GENERAL_ENTRUSTMENT.getOrderType()) {
//            return;
//        }
        if (!UserInfo.isLogin()) {
            if (!historyOrders.isEmpty()) {
                historyOrders.clear();
                adapter.notifyDataSetChanged();
            }
            return;
        }
        if (datas != null) {
            historyOrders.clear();
            historyOrders.addAll(datas);
            adapter.notifyDataSetChanged();
        }
    }
    @Override
    public void showPlanOrders(List<PlanOrderBean> datas) {
//        if (currentShowOrdersTab != ORDER_TYPE.ORDER_TYPE_PLANNING_ENTRUSTMENT.getOrderType()) {
//            return;
//        }
        if (!UserInfo.isLogin()) {
            if (!historyPlanOrders.isEmpty()) {
                historyPlanOrders.clear();
                planOrdersAdapter.notifyDataSetChanged();
            }
            return;
        }
        if (datas != null) {
            historyPlanOrders.clear();
            historyPlanOrders.addAll(datas);
            planOrdersAdapter.notifyDataSetChanged();
        }
    }

    public static class HistoryEntrustAdapter extends BaseQuickAdapter<OrderBean,BaseViewHolder> {

        private Context mContext;

        public HistoryEntrustAdapter(Context context, List<OrderBean> data) {
            super(R.layout.item_history_entrust_order_layout, data);
            mContext = context;
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final OrderBean itemModel) {
            baseViewHolder.setVisible(R.id.item_divider, baseViewHolder.getAdapterPosition() != mData.size());
            String title = itemModel.getBaseTokenName() + " / " + itemModel.getQuoteTokenName();
            baseViewHolder.setText(R.id.order_name, title);
            baseViewHolder.setTextColor(R.id.order_type,KlineUtils.getBuyOrSellColor(mContext,itemModel.getSide()));
            baseViewHolder.setText(R.id.order_type,KlineUtils.getPriceModeTxt(mContext, itemModel.getType()) +" " + KlineUtils.getBuyOrSellTxt(mContext, itemModel.getSide()));
            baseViewHolder.setText(R.id.order_time, DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getTime()), "HH:mm:ss yyyy/MM/dd"));
            baseViewHolder.setText(R.id.title, mContext.getString(R.string.string_price));
            baseViewHolder.setText(R.id.value, KlineUtils.getPrice(mContext,itemModel));
            baseViewHolder.setText(R.id.title2,  KlineUtils.getEntrustTitle(mContext,itemModel));
            baseViewHolder.setText(R.id.value2, KlineUtils.getOrderEntrustAndUnit(itemModel));
            baseViewHolder.setText(R.id.title3, mContext.getString(R.string.string_order_deal_amount));
            baseViewHolder.setText(R.id.value3, KlineUtils.getDealAmount(mContext,itemModel));
            baseViewHolder.setText(R.id.title4, mContext.getString(R.string.string_order_deal_average_price));
            baseViewHolder.setText(R.id.value4, KlineUtils.getAvgPrice(mContext,itemModel));
            baseViewHolder.setText(R.id.title5, mContext.getString(R.string.string_order_deal_money));
            baseViewHolder.setText(R.id.value5, KlineUtils.getDealMoney(mContext,itemModel));
            baseViewHolder.setText(R.id.value6, KlineUtils.getOrderStatus(mContext,itemModel));
            baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    IntentUtils.goOrderDetail(mContext,itemModel);
                }
            });
        }

    }
}
