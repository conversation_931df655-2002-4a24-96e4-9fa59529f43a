/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AuthenticatePresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.KycApi;
import io.bhex.sdk.config.ConfigApi;
import io.bhex.sdk.config.bean.KycCardTypeResponse;
import io.bhex.sdk.utils.UtilsApi;
import io.bhex.sdk.utils.bean.UploadImgResponse;

/**
 * ================================================
 * 描   述：身份认证
 * ================================================
 */

public class AuthenticatePresenter extends BasePresenter<AuthenticatePresenter.AuthenticateUI> {

    public interface AuthenticateUI extends AppUI{

        void showKycType(List<KycCardTypeResponse.IdCardTypeBean> kycCardTypeList);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, AuthenticateUI ui) {
        super.onUIReady(activity, ui);
        getConfig();
    }

    private void getConfig() {
        ConfigApi.getIdCardTypes(new SimpleResponseListener<KycCardTypeResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(KycCardTypeResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    List<KycCardTypeResponse.IdCardTypeBean> kycCardTypeList = response.getArray();
                    getUI().showKycType(kycCardTypeList);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);

            }
        });
    }

    public void uploadImg(String imagePath){
        UtilsApi.UploadVerifyImg(imagePath, new SimpleResponseListener<UploadImgResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(UploadImgResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {

                    ToastUtils.showShort(getActivity(),response.getUrl());
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public void requestBasicVerify(String country, String name, String firstName, String lastName, int idType, String id) {
        KycApi.RequestKycBasicVerify(country,name,firstName,lastName,idType,id,new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    if (response.isSuccess()) {
                        ToastUtils.showShort(getString(R.string.string_submit_success));
                        getActivity().finish();
                    }else{
                        ToastUtils.showShort(getString(R.string.string_submit_failed));
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

}
