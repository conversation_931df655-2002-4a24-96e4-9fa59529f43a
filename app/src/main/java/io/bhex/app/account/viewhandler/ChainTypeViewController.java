/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: ChainTypeViewController.java
 *   @Date: 19-8-9 下午2:47
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.viewhandler;

import android.content.Context;
import android.view.View;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.adapter.ChainTypeSelectAdapter;
import io.bhex.app.utils.DialogUtils;
import io.bhex.sdk.quote.bean.ChainType;

public class ChainTypeViewController {

    private static ChainTypeViewController instance;

    public ChainTypeViewController() {
    }

    public static ChainTypeViewController getInstance() {
        if (instance == null) {
            instance = new ChainTypeViewController();
        }
        return instance;
    }

    /**
     * 显示链路类型选择
     * @param context
     * @param recyclerView
     * @param recyclerView
     * @param selectListener
     * @param action 操作行为来源 1 充币行为  2 提币行为 3 添加提币地址行为
     */
    public void showChainTypesGrid(final Context context, List<ChainType> chainTypes, RecyclerView recyclerView, final int action, final ChainTypeSelectListener selectListener) {
//        List<ChainType> chainTypes = new ArrayList<>();
//        ChainType chainType = new ChainType();
//        chainType.setChainType("T-ERC20");
//        chainType.setAllowDeposit(true);
//        chainType.setAllowWithdraw(true);
//        ChainType chainType1 = new ChainType();
//        chainType1.setChainType("T-OMNI");
//        chainType1.setAllowDeposit(true);
//        chainType1.setAllowWithdraw(false);
//        chainType1.setSelect(true);
//        ChainType chainType2 = new ChainType();
//        chainType2.setChainType("T-TRC20");
//        chainType2.setAllowDeposit(false);
//        chainType2.setAllowWithdraw(true);
//        chainTypes.add(chainType);
//        chainTypes.add(chainType1);
//        chainTypes.add(chainType2);
        if (chainTypes != null) {
            final ChainTypeSelectAdapter chainTypeSelectAdapter = new ChainTypeSelectAdapter(chainTypes);
            chainTypeSelectAdapter.isFirstOnly(false);
//            chainTypeSelectAdapter.setEmptyView(emptyView);
            int size = chainTypes.size();
            recyclerView.setLayoutManager(new GridLayoutManager(context, size < 3 ? 2 : 3));
//            recyclerView.setItemAnimator(new DefaultItemAnimator());
            recyclerView.setAdapter(chainTypeSelectAdapter);

            chainTypeSelectAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                    boolean isNeedModifySelectedStatus = true;
                    List<ChainType> chainTypeListData = adapter.getData();
                    ChainType selectChainType = chainTypeListData.get(position);
                    if (action==1) {
                        //充币
                        if (!selectChainType.isAllowDeposit()) {
                            showTips(context,selectChainType.getChainType()+" "+context.getString(R.string.string_suspeng_deposit));
                            isNeedModifySelectedStatus = false;
                        }else{
                            selectListener.onItemSelect(selectChainType);
                        }
                    }else if (action==2) {
                        //提币
                        if (!selectChainType.isAllowWithdraw()) {
                            showTips(context,selectChainType.getChainType()+" "+context.getString(R.string.string_suspeng_withdraw));
                            isNeedModifySelectedStatus = false;
                        }else{
                            selectListener.onItemSelect(selectChainType);
                        }
                    }else{
                        selectListener.onItemSelect(selectChainType);
                    }

                    for (int i = 0; i < chainTypeListData.size(); i++) {
                        ChainType chainType = chainTypeListData.get(i);
                        if (isNeedModifySelectedStatus) {
                            chainType.setSelect(i == position);
                        }else{
                            if (i==position){
                                chainType.setSelect(false);
                            }
                        }

                    }
                    chainTypeSelectAdapter.notifyDataSetChanged();

                }
            });
        }
    }

    /**
     * 提示暂停充币 提币Dialog
     * @param context
     * @param tipsContent
     */
    private void showTips(Context context, String tipsContent) {
        DialogUtils.showLockCoinTipsDialogOneBtn(context, tipsContent, "", context.getString(R.string.string_i_know), false, new DialogUtils.OnButtonEventListener() {
            @Override
            public void onConfirm() {
            }

            @Override
            public void onCancel() {

            }
        });
    }

    public interface ChainTypeSelectListener {
        void onItemSelect(ChainType selectChain);
    }

}
