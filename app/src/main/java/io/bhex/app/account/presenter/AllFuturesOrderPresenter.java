/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AllFuturesOrderPresenter.java
 *   @Date: 19-6-25 上午11:19
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;

public class AllFuturesOrderPresenter extends BasePresenter<AllFuturesOrderPresenter.AllFuturesOrderUI> {
    public interface AllFuturesOrderUI extends AppUI {

    }

    @Override
    public void onUIReady(BaseCoreActivity activity, AllFuturesOrderUI ui) {
        super.onUIReady(activity, ui);
    }

}