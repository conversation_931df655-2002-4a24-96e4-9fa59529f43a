/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: InviteRecordsPresenter.java
 *   @Date: 19-8-26 下午3:29
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BaseFragmentPresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.invite.InviteApi;
import io.bhex.sdk.invite.bean.InvitationRelationBean;
import io.bhex.sdk.invite.bean.InviteRelationListResponse;

public class InviteRecordsPresenter extends BaseFragmentPresenter<InviteRecordsPresenter.InviteRecordsUI> {
    private List<InvitationRelationBean> invitationList = new ArrayList<>();

    public interface InviteRecordsUI extends AppUI {
        void loadMoreComplete();

        void showInvationList(List<InvitationRelationBean> rewardList);

        void loadEnd();

        void loadMoreFailed();
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, InviteRecordsUI ui) {
        super.onUIReady(activity, ui);
        getInvitaionList(false);
    }

    /**
     * 邀请列表
     */
    public void getInvitaionList(final boolean isLoadMore) {
        String from = "";
        if (isLoadMore) {
            if (invitationList != null) {
                if (!invitationList.isEmpty()) {
                    from = invitationList.get(invitationList.size() - 1).getInviteId();
                }
            }
        } else {
            from = "";
        }

        InviteApi.getInviteRelationList(from, AppData.Config.PAGE_LIMIT, new SimpleResponseListener<InviteRelationListResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().loadMoreComplete();
            }

            @Override
            public void onSuccess(InviteRelationListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<InvitationRelationBean> data = response.getData();
                    if (data != null) {
                        if (isLoadMore) {
                            if (data != null) {
                                invitationList.addAll(data);
                            }
                        } else {
                            if (data != null) {
                                invitationList.clear();
                                invitationList = data;
                            }
                        }
                        getUI().showInvationList(invitationList);
                        if (data.size() < AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        } else {
                            getUI().loadMoreComplete();
                        }
                    } else {
                        getUI().loadMoreComplete();
                    }

                } else {
                    getUI().loadMoreFailed();
                }
            }
        });

    }
}
