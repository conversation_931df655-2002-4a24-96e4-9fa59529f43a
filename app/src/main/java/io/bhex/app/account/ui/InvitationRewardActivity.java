package io.bhex.app.account.ui;

import android.content.Intent;
import android.view.View;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.flyco.tablayout.SegmentTabLayout;
import com.flyco.tablayout.listener.OnTabSelectListener;

import java.util.ArrayList;

import io.bhex.app.R;
import io.bhex.app.account.presenter.InvitationRewardPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.share.ShareUtils;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.IntentUtils;
import io.bhex.sdk.invite.bean.InviteResponse;

public class InvitationRewardActivity extends BaseActivity<InvitationRewardPresenter, InvitationRewardPresenter.InvitationRewardUI> implements InvitationRewardPresenter.InvitationRewardUI, View.OnClickListener {

    private SegmentTabLayout titleSegment;
    private String[] mTitles;
    private ArrayList<Fragment> fragments = new ArrayList<>();
    private TextView inviteCode;
    private InviteResponse shareInfo;

    @Override
    protected int getContentView() {
        return R.layout.activity_invitation_reward_layout;
    }

    @Override
    protected InvitationRewardPresenter createPresenter() {
        return new InvitationRewardPresenter();
    }

    @Override
    protected InvitationRewardPresenter.InvitationRewardUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        mTitles = new String[]{getString(R.string.string_title_invite_detail), getString(R.string.string_title_reward_detail)};
        fragments.add(new InviteRecordsFragment());
        fragments.add(new RewardListFragment());
        titleSegment = findViewById(R.id.title_segment);
        titleSegment.setTabData(mTitles, this, R.id.frameLayout, fragments);
        Intent intent = getIntent();
        if (intent != null) {
            boolean isInviteList = intent.getBooleanExtra("isInviteList", true);
            titleSegment.setCurrentTab(isInviteList ? 0 : 1);
        }
        inviteCode = viewFinder.find(R.id.invite_code);

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        titleSegment.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {

            }

            @Override
            public void onTabReselect(int position) {

            }
        });

        viewFinder.find(R.id.copy_btn).setOnClickListener(this);
        viewFinder.find(R.id.btn_share_invite_friends).setOnClickListener(this);
        viewFinder.find(R.id.btn_poster).setOnClickListener(this);
    }

    @Override
    public void showShareInfo(InviteResponse response) {
        shareInfo = response;
        if (shareInfo != null) {
            inviteCode.setText(shareInfo.getInviteCode());
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.copy_btn:
                CommonUtil.copyText(this, viewFinder.textView(R.id.invite_code).getText().toString());
                break;
            case R.id.btn_share_invite_friends:
                if (shareInfo != null) {
                    ShareUtils.shareWeb(this,shareInfo.getShareUrl(),shareInfo.getShareTitle(),shareInfo.getShareContent());
                }
                break;
            case R.id.btn_poster:
                if (shareInfo != null) {
                    IntentUtils.goInvitePoster(this,shareInfo);
                }

                break;
        }
    }
}
