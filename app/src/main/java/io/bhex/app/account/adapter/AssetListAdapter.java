package io.bhex.app.account.adapter;

import android.text.TextUtils;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.data_manager.RateDataManager;
import io.bhex.sdk.trade.bean.AssetListResponse;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-10-31
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class AssetListAdapter extends BaseQuickAdapter<AssetListResponse.BalanceBean, BaseViewHolder> {


    private boolean isOpenEye = true;

    public AssetListAdapter(List<AssetListResponse.BalanceBean> data) {
        super(R.layout.item_asset_list_layout, data);
    }

    public void setOpenEye(boolean mOpenEye) {
        isOpenEye = mOpenEye;
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final AssetListResponse.BalanceBean itemModel) {
        if (itemModel == null)
            return;
        baseViewHolder.setText(R.id.item_asset_coin_name, itemModel.getTokenName());

        String free = "0";
        if (!TextUtils.isEmpty(itemModel.getFree())) {
            free = itemModel.getFree();
        }
        baseViewHolder.setText(R.id.item_asset_available, NumberUtils.roundFormatDown(free, AppData.Config.DIGIT_DEFAULT_VALUE));

        String locked = "0";
        if (!TextUtils.isEmpty(itemModel.getLocked())) {
            locked = itemModel.getLocked();
        }
        baseViewHolder.setText(R.id.item_asset_frozen, NumberUtils.roundFormatDown(locked, AppData.Config.DIGIT_DEFAULT_VALUE));

//            String total_about = RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(itemModel.getTokenId(),itemModel.getTotal()), AppData.DIGIT_LEGAL_MONEY);
        String total_about = RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(AppData.Config.LEGAL_MONEY_ABOUT_VALUE_CAL_UNIT, itemModel.getBtcValue()), AppData.DIGIT_LEGAL_MONEY);

        baseViewHolder.setText(R.id.item_asset_total_asset_about, "≈" + total_about);
        if (!TextUtils.isEmpty(itemModel.getTotal()) && Double.valueOf(itemModel.getTotal()) > 0d) {
            baseViewHolder.setTextColor(R.id.item_asset_total_asset_about, mContext.getResources().getColor(R.color.blue));
        } else {
            baseViewHolder.setTextColor(R.id.item_asset_total_asset_about, SkinColorUtil.getDark50(mContext));
        }
//            baseViewHolder.setText(R.id.item_asset_curreny_amount, RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(itemModel.getTokenId(),itemModel.getTotal()), AppData.DIGIT_LEGAL_MONEY));
        baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                IntentUtils.goAssetDetail(mContext, itemModel);
            }
        });
        if (isOpenEye == false) {
            baseViewHolder.setText(R.id.item_asset_available, mContext.getResources().getString(R.string.string_star_star));
            baseViewHolder.setText(R.id.item_asset_frozen, mContext.getResources().getString(R.string.string_star_star));
            baseViewHolder.setText(R.id.item_asset_total_asset_about, mContext.getResources().getString(R.string.string_star_star));

        }
    }

}
