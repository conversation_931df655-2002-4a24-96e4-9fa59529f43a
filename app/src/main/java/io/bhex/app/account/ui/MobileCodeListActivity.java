/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MobileCodeListActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.content.Intent;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.sdk.account.bean.MobileCodeListBean;
import io.bhex.app.account.presenter.MobileCodeListPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.baselib.constant.Fields;

/**
 * ================================================
 * 描   述：手机区号列表
 * ================================================
 */

public class MobileCodeListActivity extends BaseActivity<MobileCodeListPresenter,MobileCodeListPresenter.MobileCodeListUI> implements MobileCodeListPresenter.MobileCodeListUI, View.OnClickListener, TextWatcher {
    private RecyclerView recyclerView;
    private View emptyView;
    private CodeListAdapter adapter;

    @Override
    protected int getContentView() {
        return R.layout.activity_mobile_code_list_layout;
    }

    @Override
    protected MobileCodeListPresenter createPresenter() {
        return new MobileCodeListPresenter();
    }

    @Override
    protected MobileCodeListPresenter.MobileCodeListUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        LinearLayout linear = viewFinder.find(R.id.linear);
        recyclerView = viewFinder.find(R.id.recyclerView);
        LayoutInflater layoutInflater = LayoutInflater.from(this);
        emptyView = layoutInflater.inflate(R.layout.empty_layout, linear, false);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.search_cancel).setOnClickListener(this);
        viewFinder.editText(R.id.search_edit).addTextChangedListener(this);
        findViewById(R.id.input_clear).setOnClickListener(this);

    }

    @Override
    public void showCodeList(List<MobileCodeListBean.MobileCodeBean> datas) {
        if (datas == null) {
            return;
        }
        if (adapter == null) {
            adapter = new CodeListAdapter(datas);
//            adapter.openLoadAnimation(BaseQuickAdapter.SCALEIN);
            adapter.isFirstOnly(false);
            recyclerView.setLayoutManager(new LinearLayoutManager(this));
//            recyclerView.setItemAnimator(new DefaultItemAnimator());
//            DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(getContext(), LinearLayoutManager.VERTICAL);
//            RecycleViewDivider dividerItemDecoration = new RecycleViewDivider(this, LinearLayoutManager.HORIZONTAL, 2, getResources().getColor(R.color.divider_line_color), PixelUtils.dp2px(10), PixelUtils.dp2px(10));
//            recyclerView.addItemDecoration(dividerItemDecoration);
            recyclerView.setAdapter(adapter);
//            adapter.setEmptyView(true, true, emptyView);
            adapter.setEmptyView(emptyView);
        } else {
            adapter.setNewData(datas);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.search_cancel:
                finish();
                break;
            case R.id.input_clear:
                viewFinder.editText(R.id.search_edit).setText("");
                break;

        }
    }
    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
//        if (TextUtils.isEmpty(s.toString())) {
//            viewFinder.find(R.id.input_clear).setVisibility(View.GONE);
//        }else{
//            viewFinder.find(R.id.input_clear).setVisibility(View.VISIBLE);
//        }
        getPresenter().search(s.toString());
    }

    @Override
    public void afterTextChanged(Editable s) {

    }

    private class CodeListAdapter extends BaseQuickAdapter<MobileCodeListBean.MobileCodeBean,BaseViewHolder> {

        CodeListAdapter(List<MobileCodeListBean.MobileCodeBean> data) {
            super(R.layout.item_mobile_code_list_layout, data);
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final MobileCodeListBean.MobileCodeBean itemModel) {
            baseViewHolder.setVisible(R.id.item_divider, baseViewHolder.getAdapterPosition() != mData.size());
            baseViewHolder.setText(R.id.item_country,itemModel.getCountryName());
            baseViewHolder.setText(R.id.item_code,"+"+itemModel.getNationalCode());

            baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent();
                    intent.putExtra(Fields.INTENT_MOBILE_CODE,itemModel);
                    setResult(RESULT_OK,intent);
                    finish();
                }

            });
        }
    }
}
