/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AddressListPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.network.response.UISafeKeeper;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.LoginApi;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.trade.AssetApi;
import io.bhex.sdk.trade.bean.AddressListResponse;
import io.bhex.sdk.trade.bean.TwoVerifyBean;


public class AddressListPresenter extends BasePresenter<AddressListPresenter.AddressListUI> {

    public interface AddressListUI extends AppUI{

        void showAddressList(List<AddressListResponse.AddressBean> datas);

        void requestUserInfoSuccess(UserInfoBean data);

        String getChainType();
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, AddressListUI ui) {
        super.onUIReady(activity, ui);
    }

    /**
     * 获取报价Token集合
     * @param tokenId
     */
    public void getAddressListByToken(String tokenId,String chainType) {
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }

        AssetApi.RequestAddressListByToken(tokenId,chainType, UISafeKeeper.guard(getUI(), new SimpleResponseListener<AddressListResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(AddressListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<AddressListResponse.AddressBean> datas = response.getArray();
                    getUI().showAddressList(datas);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        }));
    }

    /**
     * 删除地址 去除2FA校验
     * @param deleteAddressBean
     */
    public void deleteAddress(final AddressListResponse.AddressBean deleteAddressBean) {
        AssetApi.RequestDeleteAddress(deleteAddressBean.getId(), UISafeKeeper.guard(getUI(), new SimpleResponseListener<ResultResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    ToastUtils.showShort(getActivity(), getString(R.string.string_delete_success));
                    //删除成功，重新刷新一遍数据
                    getAddressListByToken(deleteAddressBean.getTokenId(),getUI().getChainType());
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        }));
    }

    @Deprecated
    public void deleteAddress(final AddressListResponse.AddressBean deleteAddressBean, TwoVerifyBean verifyBean) {
        if(deleteAddressBean == null || verifyBean == null)
            return;
        AssetApi.RequestDeleteAddress(deleteAddressBean.getId(), verifyBean, UISafeKeeper.guard(getUI(), new SimpleResponseListener<ResultResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    ToastUtils.showShort(getActivity(), getString(R.string.string_delete_success));
                    //删除成功，重新刷新一遍数据
                    getAddressListByToken(deleteAddressBean.getTokenId(),getUI().getChainType());
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        }));
    }

    public void getUserInfo() {
        LoginApi.GetUserInfo(new SimpleResponseListener<UserInfoBean>(){
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(UserInfoBean data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data,true)) {
                    //保存用户数据
                    UserManager.getInstance().saveUserInfo(data);
                    getUI().requestUserInfoSuccess(data);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
