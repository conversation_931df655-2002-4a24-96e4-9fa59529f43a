/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AssetDetailActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.content.Intent;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import io.bhex.app.R;
import io.bhex.app.account.presenter.MarginAssetDetailPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.margin.adapter.MarginCurrentLoanRecordAdapter;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.app.view.alertview.AlertView;
import io.bhex.app.view.alertview.OnDismissListener;
import io.bhex.app.view.alertview.OnItemClickListener;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.data_manager.RateDataManager;
import io.bhex.sdk.enums.ACCOUNT_TYPE;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.trade.bean.FeeBeanResponse;
import io.bhex.sdk.trade.margin.bean.MarginAccountAssetResponse;
import io.bhex.sdk.trade.margin.bean.QueryLoanRecordResponse;

/**
 * ================================================
 * 描   述：资产详情
 * ================================================
 */

public class MarginAssetDetailActivity extends BaseActivity<MarginAssetDetailPresenter, MarginAssetDetailPresenter.AssetDetailUI> implements MarginAssetDetailPresenter.AssetDetailUI, View.OnClickListener {
    private MarginAccountAssetResponse.DataBean assetItemBean;
    private TopBar topBar;
    private TextView assetAvailable;
    private TextView assetFrozen;
    private TextView tokenAboutCurrenyTitle;
    private TextView tokenAboutCurreny;
    private LayoutInflater layoutInflater;
    private RelativeLayout rootView;
    private int digit;
    private String currentToken ="";

    private SmartRefreshLayout swipeRefresh;
    private RecyclerView recyclerView;
    private MarginCurrentLoanRecordAdapter adapter;
    private View emptyView;
    private List<QueryLoanRecordResponse.DataBean> mCurrentRecords;

    private FeeBeanResponse currentTokenFee;
    private String selectChainType="";
    private TextView tvLoanedValue;

    @Override
    protected int getContentView() {
        return R.layout.activity_margin_asset_detail;
    }

    @Override
    protected MarginAssetDetailPresenter createPresenter() {
        return new MarginAssetDetailPresenter();
    }

    @Override
    protected MarginAssetDetailPresenter.AssetDetailUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        Intent intent = getIntent();
        topBar = viewFinder.find(R.id.topBar);
        this.setTopbarNight(topBar);
        rootView = viewFinder.find(R.id.rootView);
        layoutInflater = LayoutInflater.from(this);
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);


        if (intent != null) {
            assetItemBean = (MarginAccountAssetResponse.DataBean) intent.getSerializableExtra(AppData.INTENT.KEY_MARGIN_ASSET);
            if (assetItemBean != null) {
                digit = AppData.Config.DIGIT_DEFAULT_VALUE;
                topBar.setTitle(assetItemBean.getTokenName());

                assetAvailable = viewFinder.find(R.id.tv_available_value);
                assetFrozen = viewFinder.find(R.id.tv_frozen_value);

                tokenAboutCurrenyTitle = viewFinder.find(R.id.tv_asset_about_title);
                tokenAboutCurreny = viewFinder.find(R.id.tv_asset_about_value);
                tvLoanedValue = viewFinder.find(R.id.tv_loaned_value);

                assetAvailable.setText(NumberUtils.roundFormatDown(assetItemBean.getFree(), digit));
                assetFrozen.setText(NumberUtils.roundFormatDown(assetItemBean.getLocked(), digit));
                tokenAboutCurrenyTitle.setText(getString(R.string.string_abount_curreny, RateDataManager.CurRateCode()));
                tokenAboutCurreny.setText("≈" + RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(AppData.Config.LEGAL_MONEY_ABOUT_VALUE_CAL_UNIT, assetItemBean.getBtcValue()), AppData.DIGIT_LEGAL_MONEY));
                tvLoanedValue.setText(NumberUtils.roundFormatDown(assetItemBean.getLoanAmount(), digit));
            }
        }

        CoinPairBean coinPairBean = AppConfigManager.GetInstance().getSymbolByToken(assetItemBean.getTokenId());
        if(coinPairBean == null) {
            viewFinder.find(R.id.goTrade).setVisibility(View.GONE);
        }
        recyclerView = viewFinder.find(R.id.recyclerView);

        LayoutInflater layoutInflater = LayoutInflater.from(MarginAssetDetailActivity.this);
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
        emptyView.setLayoutParams(layoutParams);

        adapter = new MarginCurrentLoanRecordAdapter(MarginAssetDetailActivity.this, mCurrentRecords);
        adapter.isFirstOnly(false);
        adapter.setEmptyView(emptyView);
        adapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                QueryLoanRecordResponse.DataBean item  = mCurrentRecords.get(position);
                IntentUtils.goMarginRepay(MarginAssetDetailActivity.this,item);

            }
        });
        recyclerView.setLayoutManager(new LinearLayoutManager(MarginAssetDetailActivity.this));

        recyclerView.setAdapter(adapter);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (assetItemBean != null) {
            if (!NetWorkStatus.isConnected(this)) {
                ToastUtils.showShort(this, getResources().getString(R.string.hint_network_not_connect));
                return;
            }
            if (UserInfo.isLogin())
                getPresenter().getAsset(assetItemBean.getTokenId());
                getPresenter().queryToRepayRecord(assetItemBean.getTokenId());
        }
    }

    public void showAsset(MarginAccountAssetResponse.DataBean assetBean) {
        if (assetBean != null) {
            assetItemBean = assetBean;
            assetAvailable.setText(NumberUtils.roundFormatDown(assetBean.getFree(), digit));
            assetFrozen.setText(NumberUtils.roundFormatDown(assetBean.getLocked(), digit));
            tokenAboutCurrenyTitle.setText(getString(R.string.string_abount_curreny, RateDataManager.CurRateCode()));
            tokenAboutCurreny.setText("≈" + RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(AppData.Config.LEGAL_MONEY_ABOUT_VALUE_CAL_UNIT, assetBean.getBtcValue()), AppData.DIGIT_LEGAL_MONEY));
            tvLoanedValue.setText(NumberUtils.roundFormatDown(assetBean.getLoanAmount(), digit));

        }
    }


    @Override
    public void showCurrentLoanRecords(List<QueryLoanRecordResponse.DataBean> currentRecords) {
        mCurrentRecords = currentRecords;
        adapter.setNewData(mCurrentRecords);
    }


    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.loan).setOnClickListener(this);
        viewFinder.find(R.id.transfer).setOnClickListener(this);
        viewFinder.find(R.id.goTrade).setOnClickListener(this);
        viewFinder.find(R.id.ll_history).setOnClickListener(this);

        swipeRefresh.setOnRefreshListener(new OnRefreshListener() {

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                getPresenter().getAsset(assetItemBean.getTokenId());
                refreshLayout.finishRefresh(1000);
            }
        });
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.transfer:
                //TODO 暂无功能
                if (assetItemBean != null) {
                    String tokenId = assetItemBean.getTokenId();
                    IntentUtils.goAssetTransfer(this,tokenId, ACCOUNT_TYPE.ASSET_MARGIN.getType());
                }
                break;
            case R.id.loan:
                if (assetItemBean != null) {
                    String tokenId = assetItemBean.getTokenId();
                    IntentUtils.goMargin(this,tokenId);
                }
                break;
            case R.id.goTrade:
                goTrade();
                break;
            case R.id.ll_history:
                String tokenId = "";
//                if (assetItemBean != null) {
//                    tokenId = assetItemBean.getTokenId();
//                }
                IntentUtils.goMarginLoanHistory(this,tokenId);
                break;
        }
    }

    private void goTrade() {
        if (assetItemBean != null) {
            CoinPairBean coinPairBean;
            //默认xxx/USDT币对
            coinPairBean = AppConfigManager.GetInstance().getMarginSymbolInfoById(assetItemBean.getTokenId()+"USDT");
            if (coinPairBean != null && !TextUtils.isEmpty(coinPairBean.getSymbolId())) {
            }else{
                //如果没有xxx/USDT币对,则查询相关tokenId的xxx/ 币对
                coinPairBean = AppConfigManager.GetInstance().getMarginSymbolByToken(assetItemBean.getTokenId());
            }
            if(coinPairBean != null && !TextUtils.isEmpty(coinPairBean.getSymbolId())) {
                coinPairBean.setNeedSwitchTradeTab(true);
                EventBus.getDefault().postSticky(coinPairBean);
                IntentUtils.goMain(MarginAssetDetailActivity.this);
                finish();
            }else{
                //如果没有 则使用默认币对
                CoinPairBean defaultTradeCoinPair = AppConfigManager.GetInstance().getDefaultMarginCoinPair();
                if(defaultTradeCoinPair != null && !TextUtils.isEmpty(defaultTradeCoinPair.getSymbolId())) {
                    defaultTradeCoinPair.setNeedSwitchTradeTab(true);
                    EventBus.getDefault().postSticky(defaultTradeCoinPair);
                    IntentUtils.goMain(MarginAssetDetailActivity.this);
                    finish();
                }else{
                    DebugLog.e("not default trade symbol");
                }

            }
        }
    }
}
