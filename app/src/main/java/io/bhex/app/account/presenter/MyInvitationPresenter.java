/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MyInvitationPresenter.java
 *   @Date: 18-12-24 下午4:50
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.config.ConfigApi;
import io.bhex.sdk.config.bean.LanguageListResponse;
import io.bhex.sdk.invite.InviteApi;
import io.bhex.sdk.invite.bean.InviteInfoResponse;
import io.bhex.sdk.invite.bean.InviteResponse;
import io.bhex.sdk.invite.bean.InviteRewardListResponse;

public class MyInvitationPresenter extends BasePresenter<MyInvitationPresenter.MyInvitationUI> {
    private List<InviteRewardListResponse.RewardBean> rewardList = new ArrayList<>();

    public interface MyInvitationUI extends AppUI{

        void showInviteInfo(InviteInfoResponse response);

        void showShareInfo(InviteResponse response);

        void loadMoreComplete();

        void showRewardList(List<InviteRewardListResponse.RewardBean> rewardList);

        void loadEnd();

        void loadMoreFailed();

        void showLanguageList(LanguageListResponse response);
    }

    /**
     * 返佣列表
     */
    public void getRewardList(final boolean isLoadMore) {
        String from ="";
        if (isLoadMore) {
            if (rewardList != null) {
                if (!rewardList.isEmpty()) {
                    from = rewardList.get(rewardList.size()-1).getId();
                }
            }
        }else{
            from ="";
        }

        InviteApi.getInviteRewardList(from,AppData.Config.PAGE_LIMIT,new SimpleResponseListener<InviteRewardListResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().loadMoreComplete();
            }

            @Override
            public void onSuccess(InviteRewardListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    List<InviteRewardListResponse.RewardBean> data = response.getRecordList();
                    if (data != null) {
                        if (isLoadMore) {
                            if (data != null) {
                                rewardList.addAll(data);
                            }
                        } else {
                            if (data != null) {
                                rewardList.clear();
                                rewardList = data;
                            }
                        }
                        getUI().showRewardList(rewardList);
                        if (data.size()< AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        }else{
                            getUI().loadMoreComplete();
                        }
                    }else{
                        getUI().loadMoreComplete();
                    }

                }else{
                    getUI().loadMoreFailed();
                }
            }
        });

    }

    @Override
    public void onUIReady(BaseCoreActivity activity, MyInvitationUI ui) {
        super.onUIReady(activity, ui);
        getInviteInfo();
        getShareInfo();
        getLanguageList();
        getRewardList(false);
    }

    private void getLanguageList() {
        ConfigApi.getLanguage(new SimpleResponseListener<LanguageListResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(LanguageListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showLanguageList(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });

    }

    private void getInviteInfo() {
        InviteApi.getInviteInfo(new SimpleResponseListener<InviteInfoResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(InviteInfoResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showInviteInfo(response);
                }
            }
        });
    }

    public void getShareInfo(){
        InviteApi.inviteShareInfo(new SimpleResponseListener<InviteResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(InviteResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showShareInfo(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
