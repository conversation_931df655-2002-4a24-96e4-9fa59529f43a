/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CurrentEntrustOrderFragment.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.flyco.tablayout.SegmentTabLayout;
import com.flyco.tablayout.listener.OnTabSelectListener;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import io.bhex.app.R;
import io.bhex.app.account.presenter.CurrentMarginFragmentPresenter;
import io.bhex.app.base.BaseFragment;
import io.bhex.app.event.RevokeOrderEvent;
import io.bhex.app.trade.adapter.OpenOrdersAdapter;
import io.bhex.app.trade.adapter.PlanOrdersAdapter;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.enums.ORDER_TYPE;
import io.bhex.sdk.trade.bean.OrderBean;
import io.bhex.sdk.trade.bean.PlanOrderBean;

/**
 * ================================================
 * 描   述：杠杆委托订单
 * ================================================
 */

public class CurrentMarginOrderFragment extends BaseFragment<CurrentMarginFragmentPresenter, CurrentMarginFragmentPresenter.EntrustOrderUI> implements CurrentMarginFragmentPresenter.EntrustOrderUI, BaseQuickAdapter.RequestLoadMoreListener, OnRefreshListener {
    private RecyclerView recyclerView;
    private OpenOrdersAdapter adapter;
    private SmartRefreshLayout swipeRefresh;

    //当前显示的订单
    private List<OrderBean> showOrders;
    private PlanOrdersAdapter planOrdersAdapter;
    private SegmentTabLayout entrustTab;
    private int currentShowOrdersTab = ORDER_TYPE.ORDER_TYPE_GENERAL_ENTRUSTMENT.getOrderType();//默认显示当前委托tab
    private List<OrderBean> currentOrders = new ArrayList<>();
    private List<PlanOrderBean> currentPlanOrders = new ArrayList<>();



    @Override
    protected CurrentMarginFragmentPresenter.EntrustOrderUI getUI() {
        return this;
    }

    @Override
    protected CurrentMarginFragmentPresenter createPresenter() {
        return new CurrentMarginFragmentPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_entrust_order_layout, null, false);
    }

    @Override
    public void onStart() {
        super.onStart();
        EventBus.getDefault().register(this);
    }

    @Override
    public void onStop() {
        super.onStop();
        EventBus.getDefault().unregister(this);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(RevokeOrderEvent event){
        getPresenter().revokeAllOrders();
    }

    @Override
    protected void initViews() {
        super.initViews();
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        swipeRefresh.setOnRefreshListener(this);
        entrustTab = viewFinder.find(R.id.tabEntrust);
        entrustTab.setTabData(new String[]{getString(R.string.string_ordinary_order),getString(R.string.string_trigger_order)});

        recyclerView = viewFinder.find(R.id.recyclerView);

        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        recyclerView.setItemAnimator(new DefaultItemAnimator());

        LayoutInflater layoutInflater = LayoutInflater.from(getActivity());
        View emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
        emptyView.setLayoutParams(layoutParams);
        adapter = new OpenOrdersAdapter(currentOrders);
        adapter.isFirstOnly(false);
        adapter.setOnLoadMoreListener(this,recyclerView);
        adapter.setEnableLoadMore(true);
        adapter.setEmptyView(emptyView);
        recyclerView.setAdapter(adapter);
        adapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                if (view.getId() == R.id.revoke_order) {
                    OrderBean itemModel = (OrderBean) adapter.getData().get(position);
                    getPresenter().cancelOrder(itemModel.getOrderId(),false);
                }
            }
        });
        View emptyView1 = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams1 = emptyView1.getLayoutParams();
        layoutParams1.height = PixelUtils.dp2px(200);
        emptyView1.setLayoutParams(layoutParams1);

        planOrdersAdapter = new PlanOrdersAdapter(currentPlanOrders);
        planOrdersAdapter.isFirstOnly(false);
        planOrdersAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                getPresenter().getCurrentPlanOrders(true);
            }
        }, recyclerView);
        planOrdersAdapter.setEnableLoadMore(true);

        planOrdersAdapter.setEmptyView(emptyView1);


        planOrdersAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                if (view.getId() == R.id.revoke_order) {
                    PlanOrderBean itemModel = (PlanOrderBean) adapter.getData().get(position);
                    getPresenter().cancelPlanOrder(itemModel.getOrderId());
                }
            }
        });

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        entrustTab.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                if (position ==0) {
                    currentShowOrdersTab=ORDER_TYPE.ORDER_TYPE_GENERAL_ENTRUSTMENT.getOrderType();
                    recyclerView.setAdapter(adapter);
                    //普通委托
                    getPresenter().getCurrentEntrustOrders(false);
                }else if(position ==1){
                    currentShowOrdersTab=ORDER_TYPE.ORDER_TYPE_PLANNING_ENTRUSTMENT.getOrderType();
                    recyclerView.setAdapter(planOrdersAdapter);
                    //计划委托
                    getPresenter().getCurrentPlanOrders(false);
                }
            }

            @Override
            public void onTabReselect(int position) {

            }
        });

    }

    @Override
    public void onLoadMoreRequested() {
//        swipeRefresh.postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                adapter.loadComplete();
//            }
//        }, 500);
        getPresenter().loadMore();
    }

    @Override
    public void loadMoreComplete() {
        if (adapter != null) {
            adapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {
        if (adapter != null) {
            adapter.loadMoreFail();
        }
    }

    @Override
    public void loadEnd() {
        if (adapter != null) {
            adapter.loadMoreEnd();
        }
    }

    @Override
    public void loadMorePlanOrderComplete() {
        if (planOrdersAdapter != null) {
            planOrdersAdapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMorePlanOrderFailed() {
        if (planOrdersAdapter != null) {
            planOrdersAdapter.loadMoreFail();
        }
    }

    @Override
    public void loadPlanOrderEnd() {
        if (planOrdersAdapter != null) {
            planOrdersAdapter.loadMoreEnd();
        }
    }

    @Override
    public int getCurrentTabType() {
        return currentShowOrdersTab;
    }


    @Override
    public void onRefresh(RefreshLayout refreshLayout) {
        if (currentShowOrdersTab == ORDER_TYPE.ORDER_TYPE_GENERAL_ENTRUSTMENT.getOrderType()) {
            getPresenter().getCurrentEntrustOrders(false);
        } else {
            getPresenter().getCurrentPlanOrders(false);
        }

        refreshLayout.finishRefresh(1000);
    }

    @Override
    public void showOrders(List<OrderBean> datas) {
//        if (currentShowOrdersTab != ORDER_TYPE.ORDER_TYPE_GENERAL_ENTRUSTMENT.getOrderType()) {
//            return;
//        }
        if (!UserInfo.isLogin()) {
            if (!currentOrders.isEmpty()) {
                currentOrders.clear();
                adapter.notifyDataSetChanged();
            }
            return;
        }
        if (datas != null) {
            currentOrders.clear();
            currentOrders.addAll(datas);
            adapter.notifyDataSetChanged();
        }
    }
    @Override
    public void showPlanOrders(List<PlanOrderBean> datas) {
//        if (currentShowOrdersTab != ORDER_TYPE.ORDER_TYPE_PLANNING_ENTRUSTMENT.getOrderType()) {
//            return;
//        }
        if (!UserInfo.isLogin()) {
            if (!currentPlanOrders.isEmpty()) {
                currentPlanOrders.clear();
                planOrdersAdapter.notifyDataSetChanged();
            }
            return;
        }
        if (datas != null) {
            currentPlanOrders.clear();
            currentPlanOrders.addAll(datas);
            planOrdersAdapter.notifyDataSetChanged();
        }
    }

}
