/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OrderDetailActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.presenter.ContractOrderDetailPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.trade.futures.bean.DeliveryOrder;
import io.bhex.sdk.trade.futures.bean.FuturesOrderResponse;

/**
 * ================================================
 * 描   述：订单详情
 * ================================================
 */

public class ContractOrderDetailActivity extends BaseActivity<ContractOrderDetailPresenter, ContractOrderDetailPresenter.OrderDetailUI> implements ContractOrderDetailPresenter.OrderDetailUI, BaseQuickAdapter.RequestLoadMoreListener, OnRefreshListener {
    private SmartRefreshLayout swipeRefresh;
    private RecyclerView recyclerView;
    private View headerView;
    private OrderDetailAdapter adapter;
    private TextView coinPair;
    private TextView buyMode;
    private TextView detailStatus;
    private TextView detailOrderPrice;
    private TextView detailOrderDealAveragePrice;
    private TextView detailOrderEntrustAmount;
    private TextView detailOrderDealAmount;
//    private TextView detailOrderTotalDealMoney;
    private TextView detailOrderTotalFee;
    private List<DeliveryOrder> currentOrderDatas=new ArrayList<>();
    private TextView detailOrderEntrustAmountTitle;
    private TextView detailOrderId;
    private FuturesOrderResponse currentOrder;

    @Override
    protected int getContentView() {
        return R.layout.activity_contract_order_detail_layout;
    }

    @Override
    protected ContractOrderDetailPresenter createPresenter() {
        return new ContractOrderDetailPresenter();
    }

    @Override
    protected ContractOrderDetailPresenter.OrderDetailUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        swipeRefresh.setEnabled(true);
        recyclerView = viewFinder.find(R.id.recyclerView);
        LayoutInflater layoutInflater = LayoutInflater.from(this);
        headerView = layoutInflater.inflate(R.layout.header_contract_order_detail, null);
        coinPair = headerView.findViewById(R.id.detail_coinpair);
        buyMode = headerView.findViewById(R.id.detail_buymode);
        detailStatus = headerView.findViewById(R.id.detail_status);
        detailOrderPrice = headerView.findViewById(R.id.detail_order_price);
        detailOrderDealAveragePrice = headerView.findViewById(R.id.detail_order_deal_average_price);
        detailOrderEntrustAmountTitle = headerView.findViewById(R.id.detail_order_entrust_amount_title);
        detailOrderEntrustAmount = headerView.findViewById(R.id.detail_order_entrust_amount);
        detailOrderDealAmount = headerView.findViewById(R.id.detail_order_deal_amount);
//        detailOrderTotalDealMoney = headerView.findViewById(R.id.detail_order_total_deal_money);
        detailOrderId = headerView.findViewById(R.id.detail_order_id);

        adapter = new OrderDetailAdapter(currentOrderDatas);
//            adapter.openLoadAnimation(BaseQuickAdapter.SCALEIN);
        adapter.isFirstOnly(false);
        adapter.setOnLoadMoreListener(this,recyclerView);
//            swipeRefresh.setColorSchemeColors(Color.RED, Color.BLUE, Color.GREEN);
//            swipeRefresh.setOnRefreshListener(this);
        adapter.addHeaderView(headerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));

        recyclerView.setAdapter(adapter);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        swipeRefresh.setOnRefreshListener(this);
        headerView.findViewById(R.id.copy_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (currentOrder != null) {
                    CommonUtil.copyText(ContractOrderDetailActivity.this,currentOrder.getOrderId());
                }
            }
        });
    }


    @Override
    public void showHeaderOrders(FuturesOrderResponse order) {
        currentOrder = order;
        coinPair.setText(order.getSymbolName());
        String orderSide = KlineUtils.getFuturesOrderSideTxt(this, order.getSide());
        String lever = KlineUtils.isFuturesOpenOrder(this,order.getSide()) ? "·" + order.getLeverage() + "X" :"";

        buyMode.setText(orderSide + lever);
        buyMode.setTextColor(KlineUtils.getFuturesOrderSideColor(this, order.getSide()));
        detailStatus.setText(KlineUtils.getFuturesOrderStatus(this,order));
        detailOrderPrice.setText(KlineUtils.getFuturesPrice(this, order));
        detailOrderDealAveragePrice.setText(KlineUtils.getFuturesAvgPrice(this,order));
//        detailOrderEntrustAmountTitle.setText(KlineUtils.getEntrustTitle(this,order));
        detailOrderEntrustAmount.setText(TextUtils.isEmpty(order.getOrigQty())?this.getString(R.string.string_placeholder):order.getOrigQty() + " " + this.getString(R.string.string_option_unit));
        detailOrderDealAmount.setText(TextUtils.isEmpty(order.getExecutedQty())?this.getString(R.string.string_placeholder):order.getExecutedQty()+" " + this.getString(R.string.string_option_unit));
//        detailOrderTotalDealMoney.setText(KlineUtils.getDealMoney(this,order));
        detailOrderId.setText(order.getOrderId());
    }

    @Override
    public void loadMoreComplete() {
        if (adapter != null) {
            adapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {

    }

    @Override
    public void loadEnd() {
        if (adapter != null) {
            adapter.loadMoreEnd();
        }
    }

    @Override
    public void showOrders(List<DeliveryOrder> datas) {
        if (datas != null) {
            currentOrderDatas = datas;
            adapter.setNewData(datas);
        }
    }

    @Override
    public void onLoadMoreRequested() {
        getPresenter().loadMore();
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        getPresenter().getDealDetail(false);
    }

    private class OrderDetailAdapter extends BaseQuickAdapter<DeliveryOrder,BaseViewHolder> {

        OrderDetailAdapter(List<DeliveryOrder> data) {
            super(R.layout.item_contract_order_detail__layout, data);
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final DeliveryOrder itemModel) {
            baseViewHolder.setText(R.id.detail_order_deal_time, DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getTime()), "HH:mm:ss yyyy/MM/dd"));
            int tokenDigit = AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(itemModel.getSymbolId() + itemModel.getQuoteTokenId());
            int baseDigit = AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(itemModel.getSymbolId() + itemModel.getBaseTokenId());
            int amountDigit = AppConfigManager.GetInstance().getAmountDigitBySymbolIdAndTokenId(itemModel.getSymbolId() + itemModel.getQuoteTokenId());
            baseViewHolder.setText(R.id.detail_order_deal_price,  KlineUtils.roundFormatDown(itemModel.getPrice(),tokenDigit)+" "+KlineUtils.getFuturesPriceUnit(itemModel.getSymbolId()));
            baseViewHolder.setText(R.id.detail_order_deal_amount, KlineUtils.roundFormatDown(itemModel.getQuantity(),baseDigit)+" "+mContext.getResources().getString(R.string.string_option_unit));
//            baseViewHolder.setText(R.id.detail_order_deal_money, KlineUtils.roundFormatDown(String.valueOf(NumberUtils.mul(itemModel.getQuantity(),itemModel.getPrice())),amountDigit)+" "+itemModel.getQuoteTokenName());
            if (!TextUtils.isEmpty(itemModel.getFee())) {
                if (Double.valueOf(itemModel.getFee())>0) {
                    baseViewHolder.setText(R.id.detail_order_fee, KlineUtils.roundFormatDown(itemModel.getFee(),AppData.Config.DIGIT_DEFAULT_VALUE)+" "+itemModel.getFeeTokenName());
                }else{
                    baseViewHolder.setText(R.id.detail_order_fee, "0");
                }
            }else{
                baseViewHolder.setText(R.id.detail_order_fee, "0");
            }
            boolean futuresCloseOrder = KlineUtils.isFuturesCloseOrder(mContext, itemModel.getSide());
            baseViewHolder.setGone(R.id.detail_order_profit_loss_rela,futuresCloseOrder);
            if (futuresCloseOrder) {
                baseViewHolder.setText(R.id.detail_order_profit_loss,itemModel.getPnl()+" "+KlineUtils.getFuturesProfitLossUnit(itemModel.getSymbolId()));
            }
            baseViewHolder.setText(R.id.detail_order_id, itemModel.getTradeId());
            baseViewHolder.getView(R.id.trade_id_copy_btn).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    CommonUtil.copyText(mContext,itemModel.getTradeId());
                }
            });

        }

    }
}
