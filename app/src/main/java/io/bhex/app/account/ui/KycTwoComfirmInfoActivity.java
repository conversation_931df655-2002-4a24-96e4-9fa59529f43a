package io.bhex.app.account.ui;

import android.content.Intent;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.Toolbar;

import com.google.android.material.appbar.CollapsingToolbarLayout;

import io.bhex.app.R;
import io.bhex.app.account.presenter.KycTwoComfirmInfoPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.sdk.account.bean.UserVerifyInfo;
import io.bhex.sdk.account.bean.kyc.KycLevelBean;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-11-15
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class KycTwoComfirmInfoActivity extends BaseActivity<KycTwoComfirmInfoPresenter, KycTwoComfirmInfoPresenter.KycTwoComfirmInfoUI> implements KycTwoComfirmInfoPresenter.KycTwoComfirmInfoUI, View.OnClickListener {
    private static final int REQUEST_CODE = 0x15;
    private KycLevelBean kycLevelBean;

    @Override
    protected int getContentView() {
        return R.layout.activity_kyc_level_two_comfirm_info_layout;
    }

    @Override
    protected KycTwoComfirmInfoPresenter createPresenter() {
        return new KycTwoComfirmInfoPresenter();
    }

    @Override
    protected KycTwoComfirmInfoPresenter.KycTwoComfirmInfoUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        Toolbar toolbar= findViewById(R.id.toolbar);
        CollapsingToolbarLayout collapsingToolbarLayout= findViewById(R.id.collapsing_toolbar);
        collapsingToolbarLayout.setCollapsedTitleTextColor(SkinColorUtil.getDark(this));
        collapsingToolbarLayout.setExpandedTitleColor(SkinColorUtil.getDark(this));
        //显示返回按钮
        setSupportActionBar(toolbar);
        ActionBar actionBar=getSupportActionBar();
        if (actionBar!=null){
            actionBar.setDisplayHomeAsUpEnabled(true);
        }

        Intent intent = getIntent();
        if (intent != null) {
            kycLevelBean = (KycLevelBean) intent.getSerializableExtra("kycLevelBean");
        }

    }

    @Override
    protected void onResume() {
        super.onResume();
        if (kycLevelBean != null) {
            getPresenter().refreshKycLevelInfo(kycLevelBean);
        }
    }

    @Override
    public void updateKycLevelInfo(KycLevelBean response) {

        if (response != null) {
            kycLevelBean = response;
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()){
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.btnReAuthKycOne).setOnClickListener(this);
        viewFinder.find(R.id.btnSure).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.btnReAuthKycOne:
                IntentUtils.goAuthLV1(this);
                break;
            case R.id.btnSure:
                if (kycLevelBean != null) {
                    IntentUtils.goAuthLV2(this,kycLevelBean,REQUEST_CODE);
                }
                break;
        }
    }

    @Override
    public void showVerifyInfo(UserVerifyInfo response) {
        if (response != null) {
            String countryCode = response.getCountryCode();
            if (!TextUtils.isEmpty(countryCode)) {
                if (countryCode.equals("CN")) {
                    viewFinder.find(R.id.nameRela).setVisibility(View.VISIBLE);
                    viewFinder.find(R.id.name2Linear).setVisibility(View.GONE);
                }else{
                    viewFinder.find(R.id.nameRela).setVisibility(View.GONE);
                    viewFinder.find(R.id.name2Linear).setVisibility(View.VISIBLE);
                }
            }
            viewFinder.textView(R.id.value1).setText(response.getNationality());
            viewFinder.textView(R.id.value20).setText(response.getFirstName());
            viewFinder.textView(R.id.value2).setText(response.getFirstName());
            viewFinder.textView(R.id.value3).setText(response.getSecondName());
            viewFinder.textView(R.id.value4).setText(response.getCardType());
            viewFinder.textView(R.id.value5).setText(response.getCardNo());
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK){
            if (requestCode == REQUEST_CODE) {
                finish();
            }
        }
    }
}
