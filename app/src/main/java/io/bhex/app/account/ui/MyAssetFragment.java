/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MyAssetFragment.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.MultipleItemRvAdapter;
import com.chad.library.adapter.base.provider.BaseItemProvider;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

import androidx.annotation.NonNull;
import androidx.core.widget.NestedScrollView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;
import io.bhex.app.R;
import io.bhex.app.account.adapter.MyAssetMultiplePagerAdapter;
import io.bhex.app.account.presenter.MyAssetPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.app.BHexApplication;
import io.bhex.app.base.BaseFragment;
import io.bhex.app.skin.view.SkinTabLayout;
import io.bhex.app.trade.ui.MarginAgreementDialog;
import io.bhex.app.utils.BasicFunctionsUtil;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.app.view.ShadowDrawable;
import io.bhex.app.view.TopBar;
import io.bhex.app.view.alertview.AlertView;
import io.bhex.app.view.alertview.OnItemClickListener;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.core.SPEx;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.LoginApi;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.config.bean.BasicFunctionsConfig;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.data_manager.AssetUtilsManager;
import io.bhex.sdk.data_manager.RateDataManager;
import io.bhex.sdk.finance.bean.StakingAssetsBean;
import io.bhex.sdk.finance.bean.StakingConfigResponse;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.trade.bean.AllAssetResponse;
import io.bhex.sdk.trade.bean.AssetListResponse;
import io.bhex.sdk.trade.bean.CoinplusAssetResponse;
import io.bhex.sdk.trade.bean.FuturesAssetListResponse;
import io.bhex.sdk.trade.bean.OptionAssetListResponse;
import io.bhex.sdk.trade.bean.OptionHoldOrderResponse;
import io.bhex.sdk.trade.margin.MarginApi;
import io.bhex.sdk.trade.margin.bean.MarginAccountAssetResponse;
import io.bhex.sdk.trade.margin.bean.MarginAllPositionResponse;

/**
 * ================================================
 * 描   述：资产详情
 * ================================================
 */

public class MyAssetFragment extends BaseFragment<MyAssetPresenter,MyAssetPresenter.MyAssetUI> implements MyAssetPresenter.MyAssetUI, CompoundButton.OnCheckedChangeListener, TextWatcher, AssetUtilsManager.AssetChangeInterface, View.OnClickListener {
    private static final String TAG = "MyAssetFragments";
    public static final int TAB_WALLET = 0;//现货钱包tab
    public static final int TAB_FUTURES = 1;//合约钱包
    public static final int TAB_OPTION = 2;//期权钱包
    public static final int TAB_FINANCE = 3;//理财钱包
    public static final int TAB_MARGIN = 4;//杠杆钱包
    //private View headerView;
    private SmartRefreshLayout swipeRefresh;
    private String totalAsset="";
    private String totalAssetCurrency="";
    private TextView totalAssetTx;
    private TextView totalAssetCurrencyTx;
    private EditText searchEditText;
    private CheckBox isInvisibleCbox;
    private CheckBox eyeCheckbox;
    private boolean isOpenEye;
    private boolean isVisibleAsset;
    private TextView option_asset_available;
    private TextView option_asset_frozen;
    private TextView option_asset_earnest_money;
    private LinearLayout bb_asset_layout;
    private LinearLayout option_asset_layout;
    private LinearLayout coinplus_asset_layout;

    private LinearLayout margin_asset_layout;

    private RecyclerView recyclerView;
    private AssetListAdapter assetListAdapter;

    //合约
    private RecyclerView futures_recyclerView;
    private FuturesAssetListAdapter futuresAssetListAdapter;

    //期权
    private RecyclerView option_recyclerView;
    private OptionAssetListAdapter optionAssetListAdapter;

    private RecyclerView option_hold_recyclerView;
    private OptionHoldOrdersAdapter optionHoldOrdersAdapter;
    //币多多
    private RecyclerView coinplus_recyclerView;
    private StakingAssetMultiAdapter stakingAssetsAdapter;
    private RecyclerView margin_recyclerView;
    private MarginAssetListAdapter marginAdapter;

    private EditText marginSearchEditText;
    private CheckBox isMarginInvisibleCbox;

    private ViewPager walletVp;
    private MyAssetMultiplePagerAdapter walletAdapter;

    private List<AssetListResponse.BalanceBean> mTokenList = new ArrayList<>();
    private List<OptionAssetListResponse.OptionAssetBean> mOptionTokenList = new ArrayList<>();
    private List<FuturesAssetListResponse.FuturesAssetBean> mfuturesTokenList = new ArrayList<>();
    private List<OptionHoldOrderResponse.OptionHoldOrderBean> mOptionHoldOrderList = new ArrayList<>();
//    private List<CoinplusAssetResponse.CoinplusItemBean> mCoinPlusTokenList = new ArrayList<>();
    private List<StakingAssetsBean> mStakingAssetsTokenList = new ArrayList<>();
    private List<MarginAccountAssetResponse.DataBean> mMarginTokenList = new ArrayList<>();
    private MarginAllPositionResponse mMarginPositionResponse;
    private AllAssetResponse mAllAssetResponse;
    private int defaultTab = 0;
    private BasicFunctionsConfig basicFunctionsConfig;
    private int currentTab = -1;
    private LinearLayout futures_asset_layout;
    private TextView totalAssetTitleTx;
    private TopBar topBar;
    private SkinTabLayout tabAssetCategory;
    private String[] assetTitlesArray;
    private TextView categoryTotalAssetTitleTx;
    private TextView categoryTotalAssetTx;
    private TextView categoryTotalAssetCurrencyTx;

    private TextView marginTotalAssetTitleTx;
    private TextView marginTotalAssetTx;
    private TextView marginTotalAssetCurrencyTx;
    private TextView marginDepositAssetTitleTx;
    private TextView marginDepositAssetTx;
    private RelativeLayout category_asset_rela;
    private RelativeLayout margin_asset_rela;
    private AlertView moreAlertView;
    private String stakingTitle;
    private TextView marginTotalDepositCurrencyTx;
    private Handler mHandler;
    private static final int REFRESH_ASSET = 0x10;//刷新资产
    private TimerTask timerTask;
    private Timer timer;
    private boolean isScrolling;

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.activity_my_asset_layout, null, false);
    }

    @Override
    protected MyAssetPresenter createPresenter() {
        return new MyAssetPresenter();
    }

    @Override
    protected MyAssetPresenter.MyAssetUI getUI() {
        return this;
    }

    @Override
    protected void initViews() {
        super.initViews();

        mHandler = new Handler(){
            @Override
            public void handleMessage(@NonNull Message msg) {
                super.handleMessage(msg);
                switch (msg.what){
                    case REFRESH_ASSET:
                        refreshAssetList();
                        break;
                }

            }
        };

        topBar = viewFinder.find(R.id.topBar);
        this.setTopbarNight(topBar);
        topBar.setRightOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showMoreAction();
            }
        });
        basicFunctionsConfig = BasicFunctionsUtil.getBasicFunctionsConfig();
//        viewFinder.find(R.id.transferBtn).setVisibility((basicFunctionsConfig.isOption()&&basicFunctionsConfig.isFuture())?View.GONE:View.VISIBLE);
        LayoutInflater layoutInflater = LayoutInflater.from(getActivity());
        /*headerView = layoutInflater.inflate(R.layout.header_asset_list_layout, null);
        View assetView = headerView.findViewById(R.id.asset_rela);
        ShadowDrawable.setShadow(assetView);*/
        totalAssetTitleTx = viewFinder.find(R.id.asset_total_txt);
        totalAssetTx = viewFinder.find(R.id.asset_total);
        totalAssetCurrencyTx = viewFinder.find(R.id.asset_total_currency);

        categoryTotalAssetTitleTx = viewFinder.find(R.id.category_asset_total_txt);
        categoryTotalAssetTx = viewFinder.find(R.id.category_asset_total);
        categoryTotalAssetCurrencyTx = viewFinder.find(R.id.category_asset_total_currency);

        marginTotalAssetTitleTx = viewFinder.find(R.id.margin_asset_total_txt);
        marginTotalAssetTx = viewFinder.find(R.id.margin_asset_total);
        marginTotalAssetCurrencyTx = viewFinder.find(R.id.margin_asset_total_currency);
        marginDepositAssetTitleTx = viewFinder.find(R.id.margin_deposit_asset_txt);
        marginDepositAssetTx = viewFinder.find(R.id.margin_deposit_total);
        marginTotalDepositCurrencyTx = viewFinder.find(R.id.margin_deposit_total_currency);

        category_asset_rela = viewFinder.find(R.id.category_asset_rela);
        margin_asset_rela = viewFinder.find(R.id.margin_asset_rela);

        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        eyeCheckbox = viewFinder.find(R.id.asset_eye);

        swipeRefresh.setBackgroundColor(getResources().getColor(CommonUtil.isBlackMode() ? R.color.white_night : R.color.white));
        bb_asset_layout = viewFinder.find(R.id.bb_asset_layout);
        bb_asset_layout.setVisibility(View.VISIBLE);
        recyclerView = viewFinder.find(R.id.recyclerView);
        //钱包
        assetListAdapter = new AssetListAdapter(mTokenList);
        View bb_emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = bb_emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(300);
        bb_emptyView.setLayoutParams(layoutParams);
        assetListAdapter.setHeaderAndEmpty(true);
        assetListAdapter.setEmptyView(bb_emptyView);

        assetListAdapter.isFirstOnly(false);
        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));

        recyclerView.setAdapter(assetListAdapter);

        //搜索
        searchEditText = viewFinder.find(R.id.search_edit);
        ShadowDrawable.setShadow(searchEditText);
        searchEditText.clearFocus();
        totalAssetTx.requestFocus();
        isInvisibleCbox = viewFinder.find(R.id.btn_invisible_other_entrust);

        //合约
        futures_asset_layout = viewFinder.find(R.id.futures_asset_layout);
        futures_asset_layout.setVisibility(View.GONE);
        futures_recyclerView = viewFinder.find(R.id.futures_recyclerView);
        futuresAssetListAdapter = new FuturesAssetListAdapter(mfuturesTokenList);

        View futures_emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams futureslayoutParams = futures_emptyView.getLayoutParams();
        futureslayoutParams.height = PixelUtils.dp2px(200);
        futures_emptyView.setLayoutParams(futureslayoutParams);
        futuresAssetListAdapter.setHeaderAndEmpty(true);
        futuresAssetListAdapter.setEmptyView(futures_emptyView);

        futuresAssetListAdapter.isFirstOnly(false);
        futures_recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        futures_recyclerView.setAdapter(futuresAssetListAdapter);

        //期权
        option_asset_layout = viewFinder.find(R.id.option_asset_layout);
        option_asset_layout.setVisibility(View.GONE);
        option_recyclerView = viewFinder.find(R.id.option_recyclerView);
        optionAssetListAdapter = new OptionAssetListAdapter(mOptionTokenList);

        View option_emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams optionlayoutParams = option_emptyView.getLayoutParams();
        optionlayoutParams.height = PixelUtils.dp2px(200);
        option_emptyView.setLayoutParams(optionlayoutParams);
        optionAssetListAdapter.setHeaderAndEmpty(true);
        optionAssetListAdapter.setEmptyView(option_emptyView);

        optionAssetListAdapter.isFirstOnly(false);
        option_recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        option_recyclerView.setAdapter(optionAssetListAdapter);


        option_hold_recyclerView = viewFinder.find(R.id.option_hold_recyclerView);
        optionHoldOrdersAdapter = new OptionHoldOrdersAdapter(mOptionHoldOrderList);
        //View hold_emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        //ViewGroup.LayoutParams holdlayoutParams = hold_emptyView.getLayoutParams();
        //holdlayoutParams.height = PixelUtils.dp2px(300);
        //hold_emptyView.setLayoutParams(holdlayoutParams);
        //optionHoldOrdersAdapter.setHeaderAndEmpty(true);
        //optionHoldOrdersAdapter.setEmptyView(hold_emptyView);

        optionHoldOrdersAdapter.isFirstOnly(false);
        option_hold_recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        option_hold_recyclerView.setAdapter(optionHoldOrdersAdapter);

        //理财
        coinplus_asset_layout = viewFinder.find(R.id.coinplus_asset_layout);
        coinplus_asset_layout.setVisibility(View.GONE);
        coinplus_recyclerView = viewFinder.find(R.id.coinplus_recyclerView);
        stakingAssetsAdapter = new StakingAssetMultiAdapter(getActivity(),mStakingAssetsTokenList);
        View coinplus_emptyView = layoutInflater.inflate(R.layout.coinplus_empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams coinplusLayoutParams = coinplus_emptyView.getLayoutParams();
        coinplusLayoutParams.height = PixelUtils.dp2px(320);
        coinplus_emptyView.setLayoutParams(coinplusLayoutParams);
        viewFinder.find(R.id.btn_gobuy).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IntentUtils.goFinanceList(getActivity());
            }
        });
        stakingAssetsAdapter.setHeaderAndEmpty(true);
        stakingAssetsAdapter.setEmptyView(coinplus_emptyView);

        stakingAssetsAdapter.isFirstOnly(false);
        coinplus_recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        coinplus_recyclerView.setAdapter(stakingAssetsAdapter);


        margin_asset_layout = viewFinder.find(R.id.margin_asset_layout);
        margin_asset_layout.setVisibility(View.GONE);
        margin_recyclerView = viewFinder.find(R.id.margin_recyclerView);
        marginAdapter = new MarginAssetListAdapter(mMarginTokenList);

        View margin_emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams marginLayoutParams = margin_emptyView.getLayoutParams();
        marginLayoutParams.height = PixelUtils.dp2px(300);
        margin_emptyView.setLayoutParams(marginLayoutParams);

        marginAdapter.setHeaderAndEmpty(true);
        marginAdapter.setEmptyView(margin_emptyView);

        marginAdapter.isFirstOnly(false);
        margin_recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        margin_recyclerView.setAdapter(marginAdapter);
        marginSearchEditText = viewFinder.find(R.id.margin_search_edit);
        ShadowDrawable.setShadow(marginSearchEditText);
        marginSearchEditText.clearFocus();

        isMarginInvisibleCbox = viewFinder.find(R.id.btn_margin_invisible_other_entrust);

        walletVp = viewFinder.find(R.id.walletVp);
        walletVp.setOffscreenPageLimit(3);

        mAllAssetResponse = AssetUtilsManager.GetInstance().GetAllAsset();
        mMarginPositionResponse = AssetUtilsManager.GetInstance().GetAllMarginAsset();
        showAssetView();
        //资产分类tab
        tabAssetCategory = viewFinder.find(R.id.tabAssetCategory);
        initAssetCategoryTab();

    }

    private void showMoreAction() {
        String[] moreActions = {getString(R.string.string_withdraw_address), getString(R.string.string_sub_acount)};
        moreAlertView = new AlertView(null, null, getString(R.string.string_cancel), null, moreActions, getActivity(), AlertView.Style.ActionSheet, new OnItemClickListener() {
            @Override
            public void onItemClick(Object o, int position) {
                if (position == -1) {
                    return;
                }
                if (position == 0) {
                    //切换到:地址管理
                    IntentUtils.goCoinAddress(getActivity());
                } else {
                    //切换到:子账户
                    IntentUtils.goSubAccount(getActivity());
                }
            }
        });

        moreAlertView.show();

    }

    private ArrayList<String> mTabEntities = new ArrayList<>();
    private ArrayList<Fragment> fragments = new ArrayList<>();

    private void initAssetCategoryTab() {

        BasicFunctionsConfig basicFunctionsConfig = BasicFunctionsUtil.getBasicFunctionsConfig();

        mTabEntities.clear();

        //钱包默认账户，不能隐藏
        mTabEntities.add(getString(R.string.string_asset_bb));

        if (!basicFunctionsConfig.isFuture()) {
            mTabEntities.add(getString(R.string.string_asset_futures));
        }
        if (!basicFunctionsConfig.isMargin()) {
            mTabEntities.add(getString(R.string.string_asset_margin));
        }
        if (!basicFunctionsConfig.isOption()) {
            mTabEntities.add(getString(R.string.string_asset_option));
        }
        if (!basicFunctionsConfig.isBonus()||!basicFunctionsConfig.isStaking()) {
            if (TextUtils.isEmpty(stakingTitle)){
                stakingTitle= getString(R.string.string_staking);
            }
            mTabEntities.add(stakingTitle);
        }

        for (String mTabEntity : mTabEntities) {
            fragments.add(new Fragment());
        }

        String[] strArray = new String[mTabEntities.size()];
        assetTitlesArray = mTabEntities.toArray(strArray);
        tabAssetCategory.setupWithViewPager(walletVp);

    }

    private class MyPagerAdapter extends FragmentPagerAdapter {
        public MyPagerAdapter(FragmentManager fm) {
            super(fm);
        }

        @Override
        public int getCount() {
            return fragments.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return mTabEntities.get(position);
        }

        @Override
        public Fragment getItem(int position) {
            return fragments.get(position);
        }
    }


    @Override
    public void onResume() {
        super.onResume();
//        AssetUtilsManager.GetInstance().AddAssetObserver(this);
        topBar.setBgColor(getResources().getColor(R.color.blue));
        ShadowDrawable.setShadow(searchEditText);
        isOpenEye = SPEx.get(AppData.SPKEY.ASSET_EYE_SWITCHER, true);
        eyeCheckbox.setChecked(isOpenEye);
        if (UserInfo.isLogin()) {
            if (!NetWorkStatus.isConnected(getActivity())) {
                ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
                return;
            }
        }
        tabAssetCategory.setTabTextColors(SkinColorUtil.getDark(this.getActivity()), getResources().getColor(R.color.blue));
    }


    @Override
    public void onPause() {
        super.onPause();
//        AssetUtilsManager.GetInstance().removeAssetObserver(this);
    }

    @Override
    protected void onVisibleChanged(boolean visible) {
        super.onVisibleChanged(visible);
        if (visible) {
            AssetUtilsManager.GetInstance().AddAssetObserver(this);
            swipeRefresh.setBackgroundColor(getResources().getColor(CommonUtil.isBlackMode() ? R.color.white_night : R.color.white));
            AssetUtilsManager.GetInstance().requestAllAsset();
            AssetUtilsManager.GetInstance().requestAllMarginPosition();
            getPresenter().getTokens();
            getPresenter().getMarginTokens();
            getPresenter().getOptionAssetList();
            getPresenter().getOptionHoldOrderList();
            getPresenter().getFuturesAssetList();
            getPresenter().getCoinPlusAssetList();
            // 判断是不是选择的是杠杆，如果是的话去判断是否开户
            doOpenMarginAccount();
//            startTimer();
        } else {
            AssetUtilsManager.GetInstance().removeAssetObserver(this);
//            stopTimer();
        }
    }

    private void startTimer() {
        timerTask = new TimerTask(){
            @Override
            public void run() {
                mHandler.sendEmptyMessage(REFRESH_ASSET);
            }
        };
        timer = new Timer();
        timer.schedule(timerTask, 300,3000);

    }

    private void stopTimer() {
        if (timerTask != null) {
            timerTask.cancel();
        }
        if (timer != null) {
            timer.cancel();
        }
    }

    private void showAssetCategoryList(int position) {
//        TextView titleView = tabAssetCategory.getTitleView(position);
//        String tabName = titleView.getText().toString();
        String tabName = tabAssetCategory.getTabAt(position).getText().toString();
        if (tabName.equals(getString(R.string.string_asset_bb))) {
            bb_asset_layout.setVisibility(View.VISIBLE);
            futures_asset_layout.setVisibility(View.GONE);
            option_asset_layout.setVisibility(View.GONE);
            coinplus_asset_layout.setVisibility(View.GONE);
            margin_asset_layout.setVisibility(View.GONE);
            category_asset_rela.setVisibility(View.VISIBLE);
            margin_asset_rela.setVisibility(View.GONE);
        }else if(tabName.equals(getString(R.string.string_asset_futures))){
            bb_asset_layout.setVisibility(View.GONE);
            futures_asset_layout.setVisibility(View.VISIBLE);
            option_asset_layout.setVisibility(View.GONE);
            coinplus_asset_layout.setVisibility(View.GONE);
            margin_asset_layout.setVisibility(View.GONE);
            category_asset_rela.setVisibility(View.VISIBLE);
            margin_asset_rela.setVisibility(View.GONE);
        }else if(tabName.equals(getString(R.string.string_asset_option))){
            bb_asset_layout.setVisibility(View.GONE);
            futures_asset_layout.setVisibility(View.GONE);
            option_asset_layout.setVisibility(View.VISIBLE);
            coinplus_asset_layout.setVisibility(View.GONE);
            margin_asset_layout.setVisibility(View.GONE);
            category_asset_rela.setVisibility(View.VISIBLE);
            margin_asset_rela.setVisibility(View.GONE);
        }else if(tabName.equals(getString(R.string.string_asset_coinplus))){
            bb_asset_layout.setVisibility(View.GONE);
            futures_asset_layout.setVisibility(View.GONE);
            option_asset_layout.setVisibility(View.GONE);
            coinplus_asset_layout.setVisibility(View.VISIBLE);
            margin_asset_layout.setVisibility(View.GONE);
            category_asset_rela.setVisibility(View.VISIBLE);
            margin_asset_rela.setVisibility(View.GONE);
        }else if(tabName.equals(getString(R.string.string_asset_margin))){
            bb_asset_layout.setVisibility(View.GONE);
            futures_asset_layout.setVisibility(View.GONE);
            option_asset_layout.setVisibility(View.GONE);
            coinplus_asset_layout.setVisibility(View.GONE);
            margin_asset_layout.setVisibility(View.VISIBLE);
            category_asset_rela.setVisibility(View.GONE);
            margin_asset_rela.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected void addEvent() {
        super.addEvent();
//        tabAssetCategory.setxxxx(new OnTabSelectListener() {
//            @Override
//            public void onTabSelect(int position) {
//                showAssetCategoryList(position);
//            }
//
//            @Override
//            public void onTabReselect(int position) {
//
//            }
//        });

        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_IDLE){
                    isScrolling = false;
                }else{
                    isScrolling = true;
                }
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
            }
        });

        viewFinder.find(R.id.depositBtn).setOnClickListener(this);
        viewFinder.find(R.id.withdrawBtn).setOnClickListener(this);
        viewFinder.find(R.id.transferBtn).setOnClickListener(this);
        swipeRefresh.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                refreshLayout.finishRefresh(1000);
                if (UserInfo.isLogin()) {
                    if (!NetWorkStatus.isConnected(getActivity())) {
                        ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
                        return;
                    }
                    AssetUtilsManager.GetInstance().requestAllAsset();
                    AssetUtilsManager.GetInstance().requestAllMarginPosition();

                    getPresenter().getTokens();
                    getPresenter().getMarginTokens();

                    getPresenter().getOptionAssetList();
                    getPresenter().getOptionHoldOrderList();
                    getPresenter().getFuturesAssetList();
                    getPresenter().getCoinPlusAssetList();
                }
            }
        });
        eyeCheckbox.setOnCheckedChangeListener(this);
        isInvisibleCbox.setOnCheckedChangeListener(this);
        searchEditText.addTextChangedListener(this);
//        CheckBox invisibleCB = viewFinder.find(R.id.btn_invisible_other_entrust);
//        new DrawableClickUtil(invisibleCB, new DrawableClickUtil.OnDrawableListener() {
//            @Override
//            public void onLeft(View v, Drawable left) {
//            }
//
//            @Override
//            public void onRight(View v, Drawable right) {
//                ToastUtils.showShort(getString(R.string.string_small_balance_tips));
//            }
//        });
        isMarginInvisibleCbox.setOnCheckedChangeListener(this);
        marginSearchEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                getPresenter().searchMargin(s.toString().trim(),isMarginInvisibleCbox.isChecked());
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.depositBtn:  //充币
                IntentUtils.goTokenList(getActivity(),true);
                break;
            case R.id.withdrawBtn:  //提币
                IntentUtils.goTokenList(getActivity(),false);
                break;
            case R.id.transferBtn:  //划转
                IntentUtils.goAssetTransfer(getActivity(), "");
                break;
        }

    }

    public void SetTab(int tabType){
        currentTab = tabType;
        defaultTab = 0;
        DebugLog.e("MyAssetFragment setTab:" +tabType);
        if(currentTab!=-1){
            if (currentTab==TAB_WALLET) {
                if (walletAdapter != null) {
                    List<View> itemViews = walletAdapter.getItemViews();
                    if (itemViews != null) {
                        for (View itemView : itemViews) {
                            String tag = (String) itemView.getTag();
                            if (tag.equals("BB")) {
                                defaultTab = itemViews.indexOf(itemView);
                                break;
                            }
                        }
                    }
                }
            }else if(currentTab==TAB_FUTURES) {
                if (walletAdapter != null) {
                    List<View> itemViews = walletAdapter.getItemViews();
                    if (itemViews != null) {
                        for (View itemView : itemViews) {
                            String tag = (String) itemView.getTag();
                            if (tag.equals("Futures")) {
                                defaultTab = itemViews.indexOf(itemView);
                                break;
                            }
                        }
                    }
                }
            } else if(currentTab==TAB_OPTION) {
                if (walletAdapter != null) {
                    List<View> itemViews = walletAdapter.getItemViews();
                    if (itemViews != null) {
                        for (View itemView : itemViews) {
                            String tag = (String) itemView.getTag();
                            if (tag.equals("Option")) {
                                defaultTab = itemViews.indexOf(itemView);
                                break;
                            }
                        }
                    }
                }
            }else if(currentTab==TAB_FINANCE) {
                if (walletAdapter != null) {
                    List<View> itemViews = walletAdapter.getItemViews();
                    if (itemViews != null) {
                        for (View itemView : itemViews) {
                            String tag = (String) itemView.getTag();
                            if (tag.equals("CoinPlus")) {
                                defaultTab = itemViews.indexOf(itemView);
                                break;
                            }
                        }
                    }
                }
            }else if(currentTab==TAB_MARGIN) {
                if (walletAdapter != null) {
                    List<View> itemViews = walletAdapter.getItemViews();
                    if (itemViews != null) {
                        for (View itemView : itemViews) {
                            String tag = (String) itemView.getTag();
                            if (tag.equals("Margin")) {
                                defaultTab = itemViews.indexOf(itemView);
                                break;
                            }
                        }
                    }
                }
            }
        }
        if(walletAdapter != null && walletVp != null && defaultTab < walletAdapter.getCount())
            walletVp.setCurrentItem(defaultTab);
    }

    public void AssetChange(){
        AssetUtilsManager.GetInstance().requestAllAsset();
        getPresenter().search(searchEditText.getText().toString().trim(), isInvisibleCbox.isChecked());
        getPresenter().searchMargin(marginSearchEditText.getText().toString().trim(), isMarginInvisibleCbox.isChecked());
    }
    //判断是否是杠杆，如果是杠杆并且未开户的话开户
    private void doOpenMarginAccount() {

        if (walletAdapter != null && walletVp != null && walletVp.getCurrentItem() > -1) {
            List<View> itemViews = walletAdapter.getItemViews();
            if (itemViews != null) {
                View view = itemViews.get(walletVp.getCurrentItem());
                String tag = (String) view.getTag();
                if (tag.equals("Margin")) {
                    if (UserInfo.isLogin() && UserManager.getInstance().getUserInfo() != null && !UserManager.getInstance().getUserInfo().isOpenMargin()) {
                        // 登录返回的值不带openmargin的返回值。所以在这个地方增加用户信息获取。
                        LoginApi.GetUserInfo(new SimpleResponseListener<UserInfoBean>() {
                            @Override
                            public void onSuccess(UserInfoBean response) {
                                super.onSuccess(response);
                                if (CodeUtils.isSuccess(response)) {
                                    UserManager.getInstance().saveUserInfo(response);
                                    if (response != null && !response.isOpenMargin()) {
                                        MarginAgreementDialog.showDialog(getChildFragmentManager(), "", new MarginAgreementDialog.OnClickListenter() {
                                            @Override
                                            public void onCheckClickListener(View view, boolean isCheck) {
                                                MarginApi.RequestOpenMargin(new SimpleResponseListener<ResultResponse>() {
                                                    @Override
                                                    public void onBefore() {
                                                        super.onBefore();
                                                        getUI().showProgressDialog("", "");
                                                    }

                                                    @Override
                                                    public void onFinish() {
                                                        super.onFinish();
                                                        getUI().dismissProgressDialog();
                                                    }

                                                    @Override
                                                    public void onSuccess(ResultResponse data) {
                                                        super.onSuccess(data);
                                                        getPresenter().getUserInfo();
                                                    }
                                                });
                                            }

                                            @Override
                                            public void onCancelClickListener() {

                                            }
                                        }, false);
                                    } else {
                                        getPresenter().getMarginTokens();
                                        AssetUtilsManager.GetInstance().requestAllMarginPosition();
                                    }
                                }
                            }
                        });
                    }

                }

            }
        }
    }


    public void showAssetView() {

        if(mAllAssetResponse == null)
            return;

        setTotalAsset(mAllAssetResponse);
        updateCategoryAsset(walletVp.getCurrentItem());

        if (walletAdapter == null) {

            walletAdapter = new MyAssetMultiplePagerAdapter(getActivity(),assetTitlesArray, mAllAssetResponse, walletVp, isOpenEye);
            walletVp.setAdapter(walletAdapter);
            walletVp.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
                @Override
                public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

                }

                @Override
                public void onPageSelected(int position) {
                    updateCategoryAsset(position);
                    doOpenMarginAccount();
                }

                @Override
                public void onPageScrollStateChanged(int state) {

                }
            });
            if(currentTab!=-1){
                if (currentTab == TAB_WALLET) {
                    List<View> itemViews = walletAdapter.getItemViews();
                    if (itemViews != null) {
                        for (View itemView : itemViews) {
                            String tag = (String) itemView.getTag();
                            if (tag.equals("BB")) {
                                defaultTab = itemViews.indexOf(itemView);
                                break;
                            }
                        }
                    }
                } else if (currentTab == TAB_FUTURES) {
                    List<View> itemViews = walletAdapter.getItemViews();
                    if (itemViews != null) {
                        for (View itemView : itemViews) {
                            String tag = (String) itemView.getTag();
                            if (tag.equals("Futures")) {
                                defaultTab = itemViews.indexOf(itemView);
                                break;
                            }
                        }
                    }
                } else if (currentTab == TAB_OPTION) {
                    List<View> itemViews = walletAdapter.getItemViews();
                    if (itemViews != null) {
                        for (View itemView : itemViews) {
                            String tag = (String) itemView.getTag();
                            if (tag.equals("Option")) {
                                defaultTab = itemViews.indexOf(itemView);
                                break;
                            }
                        }
                    }
                } else if (currentTab == TAB_FINANCE) {
                    List<View> itemViews = walletAdapter.getItemViews();
                    if (itemViews != null) {
                        for (View itemView : itemViews) {
                            String tag = (String) itemView.getTag();
                            if (tag.equals("CoinPlus")) {
                                defaultTab = itemViews.indexOf(itemView);
                                break;
                            }
                        }
                    }
                } else if (currentTab == TAB_MARGIN) {
                    List<View> itemViews = walletAdapter.getItemViews();
                    if (itemViews != null) {
                        for (View itemView : itemViews) {
                            String tag = (String) itemView.getTag();
                            if (tag.equals("Margin")) {
                                defaultTab = itemViews.indexOf(itemView);
                                break;
                            }
                        }
                    }
                }
            }
            if(defaultTab < walletAdapter.getCount())
                walletVp.setCurrentItem(defaultTab);
        } else {
            walletAdapter.ChangeData(assetTitlesArray,mAllAssetResponse, isOpenEye);
            walletAdapter.notifyDataSetChanged();
        }

    }

    private void updateCategoryAsset(int position) {
        if (walletAdapter != null) {
            List<View> itemViews = walletAdapter.getItemViews();
            if (itemViews != null) {
                View view = itemViews.get(position);
                String tag = (String) view.getTag();
                if (tag.equals("BB")) {
                    bb_asset_layout.setVisibility(View.VISIBLE);
                    futures_asset_layout.setVisibility(View.GONE);
                    option_asset_layout.setVisibility(View.GONE);
                    coinplus_asset_layout.setVisibility(View.GONE);

                    categoryTotalAssetTitleTx.setText(getString(R.string.string_asset_bb_about));
                    setCategoryAssetInfo(mAllAssetResponse.coinAsset,mAllAssetResponse.unit);

                    margin_asset_layout.setVisibility(View.GONE);
                    category_asset_rela.setVisibility(View.VISIBLE);
                    margin_asset_rela.setVisibility(View.GONE);
                }else if(tag.equals("Futures")){
                    bb_asset_layout.setVisibility(View.GONE);
                    futures_asset_layout.setVisibility(View.VISIBLE);
                    option_asset_layout.setVisibility(View.GONE);
                    coinplus_asset_layout.setVisibility(View.GONE);

                    categoryTotalAssetTitleTx.setText(getString(R.string.string_asset_futures_about) + "(BTC)");
                    setCategoryAssetInfo(mAllAssetResponse.futuresCoinAsset,mAllAssetResponse.unit);

                    margin_asset_layout.setVisibility(View.GONE);
                    category_asset_rela.setVisibility(View.VISIBLE);
                    margin_asset_rela.setVisibility(View.GONE);
                }else if(tag.equals("Option")){
                    bb_asset_layout.setVisibility(View.GONE);
                    futures_asset_layout.setVisibility(View.GONE);
                    option_asset_layout.setVisibility(View.VISIBLE);
                    coinplus_asset_layout.setVisibility(View.GONE);

                    categoryTotalAssetTitleTx.setText(getString(R.string.string_asset_option_about) + "(BTC)");
                    String optionAssetDouble = NumberUtils.add2(mAllAssetResponse.optionAsset, mAllAssetResponse.optionCoinAsset);
                    setCategoryAssetInfo(optionAssetDouble,mAllAssetResponse.unit);

                    margin_asset_layout.setVisibility(View.GONE);
                    category_asset_rela.setVisibility(View.VISIBLE);
                    margin_asset_rela.setVisibility(View.GONE);
                }else if(tag.equals("CoinPlus")){
                    bb_asset_layout.setVisibility(View.GONE);
                    futures_asset_layout.setVisibility(View.GONE);
                    option_asset_layout.setVisibility(View.GONE);
                    coinplus_asset_layout.setVisibility(View.VISIBLE);

                    categoryTotalAssetTitleTx.setText(getString(R.string.string_asset_coinplus_about));
                    String asset = NumberUtils.add2(mAllAssetResponse.financeAsset,mAllAssetResponse.stakingAsset);
                    setCategoryAssetInfo(asset,mAllAssetResponse.unit);

                    margin_asset_layout.setVisibility(View.GONE);
                    category_asset_rela.setVisibility(View.VISIBLE);
                    margin_asset_rela.setVisibility(View.GONE);
                }else if(tag.equals("Margin")){
                    bb_asset_layout.setVisibility(View.GONE);
                    futures_asset_layout.setVisibility(View.GONE);
                    option_asset_layout.setVisibility(View.GONE);
                    coinplus_asset_layout.setVisibility(View.GONE);

                    setMarginAssetInfo();

                    margin_asset_layout.setVisibility(View.VISIBLE);
                    category_asset_rela.setVisibility(View.GONE);
                    margin_asset_rela.setVisibility(View.VISIBLE);
                }

            }
        }
    }

    /**
     * 设置总资产
     * @param mAllAssetResponse
     */
    private void setTotalAsset(AllAssetResponse mAllAssetResponse) {
        if (mAllAssetResponse != null) {
            totalAsset = NumberUtils.roundFormatDown(RateDataManager.getTokenToBTC(mAllAssetResponse.unit,mAllAssetResponse.totalAsset), AppData.Config.DIGIT_DEFAULT_VALUE);

//        String legalMoney = RateDataManager.CurRatePrice(mAllAssetResponse.unit, mAllAssetResponse.totalAsset);
            String legalMoney = RateDataManager.CurRatePrice(AppData.Config.LEGAL_MONEY_ABOUT_VALUE_CAL_UNIT, totalAsset);
            legalMoney = RateDataManager.getShowLegalMoney(legalMoney, AppData.DIGIT_LEGAL_MONEY);

            totalAssetTitleTx.setText(getString(R.string.string_total_asset_about));
            if (isOpenEye) {
                totalAssetTx.setText(totalAsset);
                totalAssetCurrencyTx.setText("≈"+legalMoney);
            }else{
                totalAssetTx.setText(getString(R.string.string_star_star));
                totalAssetCurrencyTx.setText(getString(R.string.string_star_star));
            }
//        totalAssetTx.setText(totalAsset);
//        totalAssetCurrencyTx.setText("≈"+totalAssetCurrency);
        }
    }

    /**
     * 设置分类总资产
     * @param categoryTotalAsset
     * @param unit
     */
    private void setCategoryAssetInfo(String categoryTotalAsset, String unit) {
        String categoryAsset = NumberUtils.roundFormatDown(RateDataManager.getTokenToBTC(unit,categoryTotalAsset), AppData.Config.DIGIT_DEFAULT_VALUE);

        String legalMoney = RateDataManager.CurRatePrice(AppData.Config.LEGAL_MONEY_ABOUT_VALUE_CAL_UNIT, categoryAsset);
        legalMoney = RateDataManager.getShowLegalMoney(legalMoney, AppData.DIGIT_LEGAL_MONEY);

        if (isOpenEye) {
            categoryTotalAssetTx.setText(categoryAsset);
            categoryTotalAssetCurrencyTx.setText("≈"+legalMoney);
        }else{
            categoryTotalAssetTx.setText(getString(R.string.string_star_star));
            categoryTotalAssetCurrencyTx.setText(getString(R.string.string_star_star));
        }
    }

    private void setMarginAssetInfo() {
        if (mMarginPositionResponse==null) {
            marginTotalAssetTx.setText(getString(R.string.string_placeholder));
            marginTotalAssetCurrencyTx.setText(getString(R.string.string_placeholder));
            marginDepositAssetTx.setText(getString(R.string.string_placeholder));
            marginTotalDepositCurrencyTx.setText(getString(R.string.string_placeholder));
            return;
        }
        String unit = AppData.Config.MARGIN_ASSET_UNIT;
        String categoryAsset = NumberUtils.roundFormatDown(RateDataManager.getTokenToBTC(unit,mMarginPositionResponse.getTotal()), AppData.Config.DIGIT_DEFAULT_VALUE);

        String legalMoney = RateDataManager.CurRatePrice(AppData.Config.LEGAL_MONEY_ABOUT_VALUE_CAL_UNIT, categoryAsset);
        legalMoney = RateDataManager.getShowLegalMoney(legalMoney, AppData.DIGIT_LEGAL_MONEY);

        String totalMarginAmount =NumberUtils.add2(mMarginPositionResponse.getMarginAmount(),mMarginPositionResponse.getOccupyMargin());
        String depositAssetValue = NumberUtils.roundFormatDown(RateDataManager.getTokenToBTC(unit,totalMarginAmount), AppData.Config.DIGIT_DEFAULT_VALUE);

        String depositLegalMoney = RateDataManager.CurRatePrice(AppData.Config.LEGAL_MONEY_ABOUT_VALUE_CAL_UNIT, depositAssetValue);
        depositLegalMoney = RateDataManager.getShowLegalMoney(depositLegalMoney, AppData.DIGIT_LEGAL_MONEY);
        if (isOpenEye) {
            marginTotalAssetTx.setText(categoryAsset);
            marginTotalAssetCurrencyTx.setText("≈"+legalMoney);
            marginDepositAssetTx.setText(depositAssetValue);
            marginTotalDepositCurrencyTx.setText("≈"+depositLegalMoney);
        }else{
            marginTotalAssetTx.setText(getString(R.string.string_star_star));
            marginTotalAssetCurrencyTx.setText(getString(R.string.string_star_star));
            marginDepositAssetTx.setText(getString(R.string.string_star_star));
            marginTotalDepositCurrencyTx.setText(getString(R.string.string_star_star));
        }
    }

    private void refreshAssetList(){
        if (mTokenList != null && !isScrolling) {
            assetListAdapter.setNewData(mTokenList);
        }
    }

    /**
     * 显示资产列表
     * @param tokenList
     */
    @Override
    public void showAssetList(List<AssetListResponse.BalanceBean> tokenList) {
        mTokenList = tokenList;
        if (tokenList != null) {
            assetListAdapter.setNewData(tokenList);
        }
    }

    @Override
    public void showFuturesAssetList(List<FuturesAssetListResponse.FuturesAssetBean> futuresTokenList) {
        mfuturesTokenList = futuresTokenList;
        if (futuresTokenList != null) {
            futuresAssetListAdapter.setNewData(futuresTokenList);
        }
    }

    @Override
    public void showOptionAssetList(List<OptionAssetListResponse.OptionAssetBean> optionTokenList) {
        mOptionTokenList = optionTokenList;
        if (optionTokenList != null) {
            optionAssetListAdapter.setNewData(optionTokenList);
        }
    }

    @Override
    public void showOptionHoldOrders(List<OptionHoldOrderResponse.OptionHoldOrderBean> tokenList) {
        mOptionHoldOrderList = tokenList;
        if (tokenList != null) {
            optionHoldOrdersAdapter.setNewData(tokenList);
        }
    }

    @Override
    public void showStakingAssetsLists(List<StakingAssetsBean> tokenList){
        mStakingAssetsTokenList = tokenList;
        if (tokenList != null) {
            stakingAssetsAdapter.setNewData(tokenList);
        }
    }

    @Override
    public void showMarginLists(List<MarginAccountAssetResponse.DataBean> tokenList){
        mMarginTokenList = tokenList;
        if (tokenList != null) {
            marginAdapter.setNewData(tokenList);
        }
    }

    @Override
    public void updateStakingConfig(StakingConfigResponse configResponse) {
//        if (configResponse!=null&&configResponse.getStakingSettings()!=null) {
//            if (!TextUtils.isEmpty(configResponse.getStakingSettings().getAppTitle())) {
//                if (!stakingTitle.equals(configResponse.getStakingSettings().getAppTitle())) {
//                    if (mTabEntities.size()>0&&mTabEntities.contains(stakingTitle)) {
//                        for (int i=0;i<mTabEntities.size();i++) {
//                            if (!TextUtils.isEmpty(mTabEntities.get(i))&&mTabEntities.get(i).equals(stakingTitle)) {
//                                stakingTitle = configResponse.getStakingSettings().getAppTitle();
//                                mTabEntities.set(i,stakingTitle);
//                                String[] strArray = new String[mTabEntities.size()];
//                                assetTitlesArray = mTabEntities.toArray(strArray);
//                                showAssetView();
//                                break;
//                            }
//                        }
//                    }
//
//                }
//            }
//        }
        if(configResponse==null || configResponse.getStakingSettings()==null){
            return;
        }

        if (TextUtils.isEmpty(configResponse.getStakingSettings().getAppTitle())) {
            return;
        }

        if (stakingTitle.equals(configResponse.getStakingSettings().getAppTitle())) {
            return;
        }

        if (mTabEntities.size()<=0 || !mTabEntities.contains(stakingTitle)) {
            return;
        }

        for (int i=0;i<mTabEntities.size();i++) {

            if (TextUtils.isEmpty(mTabEntities.get(i)) || !mTabEntities.get(i).equals(stakingTitle)) {
                continue;
            }

            stakingTitle = configResponse.getStakingSettings().getAppTitle();
            mTabEntities.set(i, stakingTitle);
            String[] strArray = new String[mTabEntities.size()];
            assetTitlesArray = mTabEntities.toArray(strArray);
            showAssetView();
            break;
        }
    }

    @Override
    public void refreshAssetComplete() {
        swipeRefresh.finishRefresh();
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        switch (buttonView.getId()){
            case R.id.asset_eye:
                switchEye(isChecked);

                break;
            case R.id.btn_invisible_other_entrust:
                String searchContent = searchEditText.getText().toString().trim();
                getPresenter().search(searchContent,isChecked);
                break;

            case R.id.btn_margin_invisible_other_entrust:
                String marginSearchContent = marginSearchEditText.getText().toString().trim();
                getPresenter().searchMargin(marginSearchContent,isChecked);
                break;
        }
    }

    /**
     * 设置开闭眼资产
     * @param isOpenEye
     */
    private void switchEye(boolean isOpenEye) {
        SPEx.set(AppData.SPKEY.ASSET_EYE_SWITCHER,isOpenEye);
        this.isOpenEye = isOpenEye;
        showAssetView();
        showAssetList(mTokenList);
        showFuturesAssetList(mfuturesTokenList);
        showOptionAssetList(mOptionTokenList);
        showOptionHoldOrders(mOptionHoldOrderList);
        showStakingAssetsLists(mStakingAssetsTokenList);
        showMarginLists(mMarginTokenList);
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
        getPresenter().search(s.toString().trim(),isInvisibleCbox.isChecked());
    }

    @Override
    public void afterTextChanged(Editable s) {

    }


    @Override
    public void OnAssetChanged() {
        mAllAssetResponse = AssetUtilsManager.GetInstance().GetAllAsset();
        mMarginPositionResponse = AssetUtilsManager.GetInstance().GetAllMarginAsset();
        showAssetView();
        setMarginAssetInfo();
    }

    private class AssetListAdapter extends BaseQuickAdapter<AssetListResponse.BalanceBean,BaseViewHolder> {


        AssetListAdapter(List<AssetListResponse.BalanceBean> data) {
            super(R.layout.item_asset_list_layout, data);
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final AssetListResponse.BalanceBean itemModel) {
            if(itemModel == null)
                return;
            baseViewHolder.setText(R.id.item_asset_coin_name, itemModel.getTokenName());

            String free = "0";
            if (!TextUtils.isEmpty(itemModel.getFree())) {
                free =itemModel.getFree();
            }
            baseViewHolder.setText(R.id.item_asset_available, NumberUtils.roundFormatDown(free,AppData.Config.DIGIT_DEFAULT_VALUE));

            String locked = "0";
            if (!TextUtils.isEmpty(itemModel.getLocked())) {
                locked =itemModel.getLocked();
            }
            baseViewHolder.setText(R.id.item_asset_frozen, NumberUtils.roundFormatDown(locked,AppData.Config.DIGIT_DEFAULT_VALUE));

//            String total_about = RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(itemModel.getTokenId(),itemModel.getTotal()), AppData.DIGIT_LEGAL_MONEY);
            String total_about = RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(AppData.Config.LEGAL_MONEY_ABOUT_VALUE_CAL_UNIT,itemModel.getBtcValue()), AppData.DIGIT_LEGAL_MONEY);

            baseViewHolder.setText(R.id.item_asset_total_asset_about, "≈"+total_about);
            if (!TextUtils.isEmpty(itemModel.getTotal())&&Double.valueOf(itemModel.getTotal())>0d) {
                baseViewHolder.setTextColor(R.id.item_asset_total_asset_about,getResources().getColor(R.color.blue));
            }else{
                baseViewHolder.setTextColor(R.id.item_asset_total_asset_about,getResources().getColor(CommonUtil.isBlackMode() ? R.color.dark50_night : R.color.dark50));
            }
//            baseViewHolder.setText(R.id.item_asset_curreny_amount, RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(itemModel.getTokenId(),itemModel.getTotal()), AppData.DIGIT_LEGAL_MONEY));
            baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    IntentUtils.goAssetDetail(getActivity(),itemModel);
                }
            });
            if(isOpenEye == false){
                baseViewHolder.setText(R.id.item_asset_available, getString(R.string.string_star_star));
                baseViewHolder.setText(R.id.item_asset_frozen, getString(R.string.string_star_star));
                baseViewHolder.setText(R.id.item_asset_total_asset_about, getString(R.string.string_star_star));

            }
        }

    }

    private class FuturesAssetListAdapter extends BaseQuickAdapter<FuturesAssetListResponse.FuturesAssetBean,BaseViewHolder> {


        FuturesAssetListAdapter(List<FuturesAssetListResponse.FuturesAssetBean> data) {
            super(R.layout.item_futures_asset_list_layout, data);
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final FuturesAssetListResponse.FuturesAssetBean itemModel) {
            if(itemModel == null)
                return;
            baseViewHolder.setText(R.id.item_asset_coin_name, itemModel.tokenName);

            String free = "0";
            if (!TextUtils.isEmpty(itemModel.availableMargin)) {
                free =itemModel.availableMargin;
            }
            baseViewHolder.setText(R.id.item_asset_available, NumberUtils.roundFormatDown(free,AppData.Config.DIGIT_DEFAULT_VALUE));

            String positionMargin = "0";
            if (!TextUtils.isEmpty(itemModel.positionMargin)) {
                positionMargin =itemModel.positionMargin;
            }
            baseViewHolder.setText(R.id.item_asset_position_margin, NumberUtils.roundFormatDown(positionMargin,AppData.Config.DIGIT_DEFAULT_VALUE));

            String orderMargin = "0";
            if (!TextUtils.isEmpty(itemModel.orderMargin)) {
                orderMargin =itemModel.orderMargin;
            }
            baseViewHolder.setText(R.id.item_asset_entrust_margin, NumberUtils.roundFormatDown(orderMargin,AppData.Config.DIGIT_DEFAULT_VALUE));

//            String total_about = RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(itemModel.tokenId,itemModel.total), AppData.DIGIT_LEGAL_MONEY);
//
//            baseViewHolder.setText(R.id.item_asset_total_asset_about, "≈"+total_about);
//            if (!TextUtils.isEmpty(itemModel.total)&&Double.valueOf(itemModel.total)>0d) {
//                baseViewHolder.setTextColor(R.id.item_asset_total_asset_about,getResources().getColor(R.color.blue));
//            }else{
//                baseViewHolder.setTextColor(R.id.item_asset_total_asset_about,getResources().getColor(R.color.dark50));
//            }
//            baseViewHolder.setText(R.id.item_asset_curreny_amount, RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(itemModel.getTokenId(),itemModel.getTotal()), AppData.DIGIT_LEGAL_MONEY));
            baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    IntentUtils.goFuturesAssetDetail(getActivity(),itemModel);
                }
            });
            if(isOpenEye == false){
                baseViewHolder.setText(R.id.item_asset_available, getString(R.string.string_star_star));
                baseViewHolder.setText(R.id.item_asset_position_margin, getString(R.string.string_star_star));
                baseViewHolder.setText(R.id.item_asset_entrust_margin, getString(R.string.string_star_star));

            }
        }

    }

    private class OptionAssetListAdapter extends BaseQuickAdapter<OptionAssetListResponse.OptionAssetBean, BaseViewHolder> {


        OptionAssetListAdapter(List<OptionAssetListResponse.OptionAssetBean> data) {
            super(R.layout.item_option_asset_list_layout, data);
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final OptionAssetListResponse.OptionAssetBean itemModel) {
            if(itemModel == null)
                return;
            baseViewHolder.setText(R.id.item_asset_option_name, itemModel.tokenName);

            List<String> ExploretokenList =  AppConfigManager.GetInstance().getExploreToken();
            if(ExploretokenList!= null && ExploretokenList.size() > 0){
                for (String explore:ExploretokenList) {
                    if(!TextUtils.isEmpty(itemModel.tokenName) && itemModel.tokenName.contains(explore)){

                        baseViewHolder.setText(R.id.item_explore_tip, getString(R.string.string_exploretoken_tip));
                    }
                }
            }

            String free = "0";
            if (!TextUtils.isEmpty(itemModel.available)) {
                free =itemModel.available;
            }
            baseViewHolder.setText(R.id.option_asset_item_available, NumberUtils.roundFormatDown(free,AppData.Config.DIGIT_DEFAULT_VALUE));

            String locked = "0";
            if (!TextUtils.isEmpty(itemModel.locked)) {
                locked =itemModel.locked;
            }
            baseViewHolder.setText(R.id.option_asset_frozen, NumberUtils.roundFormatDown(locked,AppData.Config.DIGIT_DEFAULT_VALUE));


            String margin = "0";
            if (!TextUtils.isEmpty(itemModel.margin)) {
                margin =itemModel.margin;
            }
            baseViewHolder.setText(R.id.option_asset_earnest_money, NumberUtils.roundFormatDown(margin,AppData.Config.DIGIT_DEFAULT_VALUE));
            baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    IntentUtils.goOptionAssetDetail(getActivity(),itemModel);
                }
            });

            if(isOpenEye == false){
                baseViewHolder.setText(R.id.option_asset_item_available, getString(R.string.string_star_star));
                baseViewHolder.setText(R.id.option_asset_frozen, getString(R.string.string_star_star));
                baseViewHolder.setText(R.id.option_asset_earnest_money, getString(R.string.string_star_star));

            }
        }

    }

    public  class OptionHoldOrdersAdapter extends BaseQuickAdapter<OptionHoldOrderResponse.OptionHoldOrderBean, BaseViewHolder> {

        //private Context mContext;

        OptionHoldOrdersAdapter(List<OptionHoldOrderResponse.OptionHoldOrderBean> data) {
            super(R.layout.item_option_hold_order_layout, data);
            //mContext = context;
        }


        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final OptionHoldOrderResponse.OptionHoldOrderBean itemModel) {
            try {
                if(itemModel == null)
                    return;

                String title = "";
                int color = 0;
                if (itemModel.position.startsWith("-")) {
                    title = KlineUtils.getOptionBuyOrSellTxt(mContext, false);
                    color = KlineUtils.getBuyOrSellColor(mContext, false);
                } else {
                    title = KlineUtils.getOptionBuyOrSellTxt(mContext, true);
                    color = KlineUtils.getBuyOrSellColor(mContext, true);
                }
                baseViewHolder.setText(R.id.order_buy_type, title);
                baseViewHolder.setTextColor(R.id.order_buy_type, color);

                baseViewHolder.setText(R.id.order_coin_name, itemModel.symbolName);
                baseViewHolder.setText(R.id.option_indices, itemModel.indices +" " + itemModel.quoteTokenName);
                baseViewHolder.setText(R.id.option_strikePrice, itemModel.strikePrice +" " + itemModel.quoteTokenName);

                baseViewHolder.setGone(R.id.option_indices_title, false);
                baseViewHolder.setGone(R.id.option_indices, false);
                baseViewHolder.setGone(R.id.option_strikePrice_title, false);
                baseViewHolder.setGone(R.id.option_strikePrice, false);

                baseViewHolder.setText(R.id.option_hold_price, itemModel.averagePrice +" " + itemModel.quoteTokenName);
                baseViewHolder.setText(R.id.option_market_price, itemModel.price +" " + itemModel.quoteTokenName);
                baseViewHolder.setText(R.id.option_position, String.valueOf(Math.abs(Double.valueOf(itemModel.position)))+" " + mContext.getString(R.string.string_option_unit));
                baseViewHolder.setText(R.id.option_availPosition, String.valueOf(Math.abs(Double.valueOf(itemModel.availPosition)))+" "  + mContext.getString(R.string.string_option_unit));
                baseViewHolder.setText(R.id.option_changed, itemModel.changed +" " + itemModel.quoteTokenName);
                if(itemModel.position.startsWith("-")){
                    baseViewHolder.setGone(R.id.option_margin_title, true);
                    baseViewHolder.setGone(R.id.option_margin, true);
                }
                else {
                    baseViewHolder.setGone(R.id.option_margin_title, false);
                    baseViewHolder.setGone(R.id.option_margin, false);
                }
                baseViewHolder.setText(R.id.option_margin, itemModel.margin+" " + itemModel.quoteTokenName);
                baseViewHolder.setText(R.id.option_positionRights, itemModel.positionRights+" "  + itemModel.quoteTokenName);
                baseViewHolder.setText(R.id.option_changed_rate, "(" + itemModel.changedRate +  "%)");
                int colorChange = 0;
                if (!TextUtils.isEmpty(itemModel.changed) && itemModel.changed.startsWith("-")) {
                    colorChange = KlineUtils.getBuyOrSellColor(mContext, false);
                } else {
                    colorChange = KlineUtils.getBuyOrSellColor(mContext, true);
                }
                baseViewHolder.setTextColor(R.id.option_changed_rate, colorChange);

                if(isOpenEye == false){
                    baseViewHolder.setText(R.id.option_hold_price, getString(R.string.string_star_star));
                    baseViewHolder.setText(R.id.option_market_price, getString(R.string.string_star_star));
                    baseViewHolder.setText(R.id.option_position, getString(R.string.string_star_star));
                    baseViewHolder.setText(R.id.option_availPosition, getString(R.string.string_star_star));
                    baseViewHolder.setText(R.id.option_changed, getString(R.string.string_star_star));
                    baseViewHolder.setText(R.id.option_margin, getString(R.string.string_star_star));
                    baseViewHolder.setText(R.id.option_positionRights, getString(R.string.string_star_star));
                    baseViewHolder.setText(R.id.option_changed_rate, getString(R.string.string_star_star));

                }

                baseViewHolder.setText(R.id.order_close, getString(R.string.title_trade));
                baseViewHolder.getView(R.id.order_close).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        CoinPairBean coinPairBean = AppConfigManager.GetInstance().getSymbolInfoById(itemModel.symbolId);
                        if(coinPairBean == null){
                            getPresenter().RequestOptionSymbols(itemModel.symbolId);
                            return;
                        }else {
                            coinPairBean.setBuyMode(true);
                            coinPairBean.setNeedSwitchTradeTab(true);
                            //IntentUtils.goTrade(mContext, itemModel);
                            EventBus.getDefault().postSticky(coinPairBean);
                        }
                    }
                });
                baseViewHolder.getView(R.id.order_share).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //TODO 分享
                        IntentUtils.goShareProfit(mContext,itemModel, null);

                    }
                });
            } catch (Exception e) {

            }
        }
    }

    public class StakingAssetMultiAdapter extends MultipleItemRvAdapter<StakingAssetsBean, BaseViewHolder> {

        private final Context myContext;
        // 0=定期 1=定期锁仓 2=活期
        public static final int PRODUCT_CURRENT = 2; //活期
        public static final int PRODUCT_REGULAR = 0; //定期和定期锁仓
        private String mSelectedSymbolId;

        public StakingAssetMultiAdapter(Context context, List<StakingAssetsBean> data) {
            super(data);
            myContext = context;
            finishInitialize();
        }

        @Override
        protected int getViewType(StakingAssetsBean itemModel) {
            int itemShowType = itemModel.getType();
            if (itemShowType== AppData.Config.STAKING_PRODUCE_CURRENT) {
                return PRODUCT_CURRENT;
            }else if(itemShowType==AppData.Config.STAKING_PRODUCE_HOLD||itemShowType==AppData.Config.STAKING_PRODUCE_REGULAR){
                return PRODUCT_REGULAR;
            }
            return itemShowType;
        }

        @Override
        public void registerItemProvider() {
            mProviderDelegate.registerProvider(new StakingCurrentProvider());
            mProviderDelegate.registerProvider(new StakingRegularProvider());
        }

    }

    public class StakingCurrentProvider extends BaseItemProvider<StakingAssetsBean,BaseViewHolder> {
        private static final String TGA = "StakingCurrentProvider";

        public StakingCurrentProvider() {
        }

        @Override
        public int viewType() {
            return StakingAssetMultiAdapter.PRODUCT_CURRENT;
        }

        @Override
        public int layout() {
            return R.layout.item_coinplus_asset;
        }

        @Override
        public void convert(BaseViewHolder baseViewHolder, final StakingAssetsBean itemModel, int position) {
            final Context myContext = mContext;
            if(itemModel == null)
                return;

            try {
                if(itemModel == null)
                    return;

                baseViewHolder.setText(R.id.item_asset_coinplus_name, itemModel.getProductName());

                baseViewHolder.setText(R.id.item_asset_coinplus_title, getString(R.string.string_asset_coinplus_hold) + "(" + itemModel.getTokenName() + ")");
                baseViewHolder.setText(R.id.item_asset_coinplus_value, NumberUtils.roundFormatDown(itemModel.getTotalAmount(),AppData.Config.DIGIT_DEFAULT_VALUE));
                baseViewHolder.setText(R.id.title_totalProfit, getString(R.string.string_asset_coinplus_total_profit) + "(" + itemModel.getTokenName() + ")");
                baseViewHolder.setText(R.id.title_lastProfit, getString(R.string.string_asset_coinplus_last_profit) + "(" + itemModel.getTokenName() + ")");
                baseViewHolder.setText(R.id.title_sevenYearRate, getString(R.string.string_asset_coinplus_seven_year_rate) + "(" + itemModel.getTokenName() + ")");


                baseViewHolder.setText(R.id.value_totalProfit, NumberUtils.roundFormatDown(itemModel.getTotalProfit(),AppData.Config.DIGIT_DEFAULT_VALUE));
                baseViewHolder.setText(R.id.value_lastProfit, NumberUtils.roundFormatDown(itemModel.getLastProfit(),AppData.Config.DIGIT_DEFAULT_VALUE));
                baseViewHolder.setText(R.id.value_sevenYearRate, String.valueOf(NumberUtils.mul(itemModel.getWeeklyApr(), "100")) + "%");
                if(isOpenEye == false){
                    baseViewHolder.setText(R.id.value_totalProfit, getString(R.string.string_star_star));
                    baseViewHolder.setText(R.id.value_lastProfit, getString(R.string.string_star_star));
                    baseViewHolder.setText(R.id.item_asset_coinplus_value, getString(R.string.string_star_star));

                }
                if(itemModel.getAllowPurchase() == false){
                    baseViewHolder.getView(R.id.item_asset_coinplus_buy).setBackgroundColor(getResources().getColor(R.color.color_e1dede));
                    ((TextView)baseViewHolder.getView(R.id.item_asset_coinplus_buy)).setTextColor(getResources().getColor(R.color.grey));
                }
                else {
                    baseViewHolder.getView(R.id.item_asset_coinplus_buy).setBackgroundColor(getResources().getColor(R.color.blue));
                    ((TextView)baseViewHolder.getView(R.id.item_asset_coinplus_buy)).setTextColor(getResources().getColor(R.color.color_white));
                }
                if(itemModel.getAllowRedeem() == false){
                    baseViewHolder.getView(R.id.item_asset_coinplus_sell).setBackgroundColor(getResources().getColor(R.color.color_e1dede));
                    ((TextView)baseViewHolder.getView(R.id.item_asset_coinplus_sell)).setTextColor(getResources().getColor(R.color.grey));
                }
                else {
                    baseViewHolder.getView(R.id.item_asset_coinplus_sell).setBackgroundColor(getResources().getColor(R.color.blue));
                    ((TextView)baseViewHolder.getView(R.id.item_asset_coinplus_sell)).setTextColor(getResources().getColor(R.color.color_white));
                }

                baseViewHolder.getView(R.id.item_asset_coinplus_buy).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        IntentUtils.goFinancePurchase(mContext,itemModel.getProductId());
                    }
                });
                baseViewHolder.getView(R.id.item_asset_coinplus_sell).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        IntentUtils.goFinanceRedemption(mContext,itemModel.getProductId());
                    }
                });
            } catch (Exception e) {

            }
        }
    }

    public class StakingRegularProvider extends BaseItemProvider<StakingAssetsBean,BaseViewHolder> {
        private static final String TGA = "StakingRegularProvider";

        public StakingRegularProvider() {
        }

        @Override
        public int viewType() {
            return StakingAssetMultiAdapter.PRODUCT_REGULAR;
        }

        @Override
        public int layout() {
            return R.layout.item_staking_asset;
        }

        @Override
        public void convert(BaseViewHolder baseViewHolder, final StakingAssetsBean itemModel, int position) {
            final Context myContext = mContext;
            if(itemModel == null)
                return;

            baseViewHolder.setText(R.id.finance_title,itemModel.getProductName());
            int status = itemModel.getStatus();
            if (status==1) {
                baseViewHolder.setText(R.id.tv_status,mContext.getResources().getString(R.string.string_staking_asset_buy_succeed));
                baseViewHolder.setTextColor(R.id.tv_status,mContext.getResources().getColor(R.color.staking_order_interest_accruing));
                baseViewHolder.setImageResource(R.id.iv_status,R.mipmap.icon_staking_order_interest_accruing);
            }else if (status==2) {
                baseViewHolder.setText(R.id.tv_status,mContext.getResources().getString(R.string.string_staking_asset_start_instrest));
                baseViewHolder.setTextColor(R.id.tv_status,mContext.getResources().getColor(R.color.staking_order_start_interest));
                baseViewHolder.setImageResource(R.id.iv_status,R.mipmap.icon_staking_order_start_interest);
            }else if (status==3) {
                baseViewHolder.setText(R.id.tv_status,mContext.getResources().getString(R.string.string_staking_asset_status_finished));
                baseViewHolder.setTextColor(R.id.tv_status,mContext.getResources().getColor(R.color.staking_order_cancelled));
                baseViewHolder.setImageResource(R.id.iv_status,R.mipmap.icon_staking_order_cancelled);
            }else if (status==4) {
                baseViewHolder.setText(R.id.tv_status,mContext.getResources().getString(R.string.string_staking_asset_status_failed));
                baseViewHolder.setTextColor(R.id.tv_status,mContext.getResources().getColor(R.color.staking_order_finished));
                baseViewHolder.setImageResource(R.id.iv_status,R.mipmap.icon_staking_order_finished);
            }

            baseViewHolder.setText(R.id.tv_hold_amount_title,mContext.getResources().getString(R.string.string_staking_hold_amount,itemModel.getTokenName()));
            baseViewHolder.setText(R.id.tv_hold_amount_value,itemModel.getCurrentAmount());
            baseViewHolder.setText(R.id.tv_reference_apr_value,itemModel.getReferenceApr()+"%");
            baseViewHolder.setText(R.id.tv_days_value, itemModel.getTimeLimit());
            baseViewHolder.setText(R.id.tv_end_date,mContext.getResources().getString(R.string.string_end_date)+" "+DateUtils.getSimpleTimeFormat(itemModel.getEndDate(),AppData.Config.TIME_FORMAT4));

            if(isOpenEye == false){
                baseViewHolder.setText(R.id.tv_hold_amount_value, getString(R.string.string_star_star));
            }
        }
    }

    public class CoinplusAdapter extends BaseQuickAdapter<CoinplusAssetResponse.CoinplusItemBean, BaseViewHolder> {

        //private Context mContext;

        CoinplusAdapter(List<CoinplusAssetResponse.CoinplusItemBean> data) {
            super(R.layout.item_coinplus_asset, data);
            //mContext = context;
        }


        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final CoinplusAssetResponse.CoinplusItemBean itemModel) {
            try {
                if(itemModel == null)
                    return;


                baseViewHolder.setText(R.id.item_asset_coinplus_name, itemModel.productName);

                baseViewHolder.setText(R.id.item_asset_coinplus_title, getString(R.string.string_asset_coinplus_hold) + "(" + itemModel.token + ")");
                baseViewHolder.setText(R.id.item_asset_coinplus_value, NumberUtils.roundFormatDown(itemModel.total,AppData.Config.DIGIT_DEFAULT_VALUE));
                baseViewHolder.setText(R.id.title_totalProfit, getString(R.string.string_asset_coinplus_total_profit) + "(" + itemModel.token + ")");
                baseViewHolder.setText(R.id.title_lastProfit, getString(R.string.string_asset_coinplus_last_profit) + "(" + itemModel.token + ")");
                baseViewHolder.setText(R.id.title_sevenYearRate, getString(R.string.string_asset_coinplus_seven_year_rate) + "(" + itemModel.token + ")");


                baseViewHolder.setText(R.id.value_totalProfit, NumberUtils.roundFormatDown(itemModel.totalProfit,AppData.Config.DIGIT_DEFAULT_VALUE));
                baseViewHolder.setText(R.id.value_lastProfit, NumberUtils.roundFormatDown(itemModel.lastProfit,AppData.Config.DIGIT_DEFAULT_VALUE));
                baseViewHolder.setText(R.id.value_sevenYearRate, String.valueOf(NumberUtils.mul(itemModel.sevenYearRate, "100")) + "%");
                if(isOpenEye == false){
                    baseViewHolder.setText(R.id.value_totalProfit, getString(R.string.string_star_star));
                    baseViewHolder.setText(R.id.value_lastProfit, getString(R.string.string_star_star));
                    baseViewHolder.setText(R.id.item_asset_coinplus_value, getString(R.string.string_star_star));

                }
                if(itemModel.allowPurchase == false){
                    baseViewHolder.getView(R.id.item_asset_coinplus_buy).setBackgroundColor(getResources().getColor(R.color.color_e1dede));
                    ((TextView)baseViewHolder.getView(R.id.item_asset_coinplus_buy)).setTextColor(getResources().getColor(R.color.grey));
                }
                else {
                    baseViewHolder.getView(R.id.item_asset_coinplus_buy).setBackgroundColor(getResources().getColor(R.color.blue10));
                    ((TextView)baseViewHolder.getView(R.id.item_asset_coinplus_buy)).setTextColor(getResources().getColor(R.color.blue));
                }
                if(itemModel.allowRedeem == false){
                    baseViewHolder.getView(R.id.item_asset_coinplus_sell).setBackgroundColor(getResources().getColor(R.color.color_e1dede));
                    ((TextView)baseViewHolder.getView(R.id.item_asset_coinplus_sell)).setTextColor(getResources().getColor(R.color.grey));
                }
                else {
                    baseViewHolder.getView(R.id.item_asset_coinplus_sell).setBackgroundColor(getResources().getColor(R.color.blue10));
                    ((TextView)baseViewHolder.getView(R.id.item_asset_coinplus_sell)).setTextColor(getResources().getColor(R.color.blue));
                }

                baseViewHolder.getView(R.id.item_asset_coinplus_buy).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        IntentUtils.goFinancePurchase(mContext,itemModel.productId);
                    }
                });
                baseViewHolder.getView(R.id.item_asset_coinplus_sell).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        IntentUtils.goFinanceRedemption(mContext,itemModel.productId);
                    }
                });
            } catch (Exception e) {

            }
        }
    }

    private class MarginAssetListAdapter extends BaseQuickAdapter<MarginAccountAssetResponse.DataBean,BaseViewHolder> {


        MarginAssetListAdapter(List<MarginAccountAssetResponse.DataBean> data) {
            super(R.layout.item_margin_asset_list_layout, data);
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final MarginAccountAssetResponse.DataBean itemModel) {
            if(itemModel == null)
                return;
            baseViewHolder.setText(R.id.item_asset_coin_name, itemModel.getTokenName());

            String free = "0";
            if (!TextUtils.isEmpty(itemModel.getFree())) {
                free =itemModel.getFree();
            }
            baseViewHolder.setText(R.id.item_asset_available, NumberUtils.roundFormatDown(free,AppData.Config.DIGIT_DEFAULT_VALUE));

            String locked = "0";
            if (!TextUtils.isEmpty(itemModel.getLocked())) {
                locked =itemModel.getLocked();
            }
            baseViewHolder.setText(R.id.item_asset_frozen, NumberUtils.roundFormatDown(locked,AppData.Config.DIGIT_DEFAULT_VALUE));

//            String total_about = RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(itemModel.getTokenId(),itemModel.getTotal()), AppData.DIGIT_LEGAL_MONEY);
            String total_about = RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(AppData.Config.LEGAL_MONEY_ABOUT_VALUE_CAL_UNIT,itemModel.getBtcValue()), AppData.DIGIT_LEGAL_MONEY);

            baseViewHolder.setText(R.id.item_asset_total_asset_about, "≈"+total_about);
//            if (!TextUtils.isEmpty(itemModel.getTotal())&&Double.valueOf(itemModel.getTotal())>0d) {
//                baseViewHolder.setTextColor(R.id.item_asset_total_asset_about,getResources().getColor(R.color.blue));
//            }else{
//                baseViewHolder.setTextColor(R.id.item_asset_total_asset_about,getResources().getColor(CommonUtil.isBlackMode() ? R.color.dark50_night : R.color.dark50));
//            }
            String loaned = "0";
            if (!TextUtils.isEmpty(itemModel.getLoanAmount())) {
                loaned =itemModel.getLoanAmount();
            }
            baseViewHolder.setText(R.id.item_loaned, NumberUtils.roundFormatDown(loaned,AppData.Config.DIGIT_DEFAULT_VALUE));

//            baseViewHolder.setText(R.id.item_asset_curreny_amount, RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(itemModel.getTokenId(),itemModel.getTotal()), AppData.DIGIT_LEGAL_MONEY));
            baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (UserManager.getInstance().getUserInfo()!=null &&UserManager.getInstance().getUserInfo().isOpenMargin()) {
                        IntentUtils.goAssetMarginDetail(getActivity(), itemModel);
                    }
                }
            });
            if(isOpenEye == false){
                baseViewHolder.setText(R.id.item_asset_available, getString(R.string.string_star_star));
                baseViewHolder.setText(R.id.item_asset_frozen, getString(R.string.string_star_star));
                baseViewHolder.setText(R.id.item_loaned, getString(R.string.string_star_star));
                baseViewHolder.setText(R.id.item_asset_total_asset_about, getString(R.string.string_star_star));
            }
        }

    }
}
