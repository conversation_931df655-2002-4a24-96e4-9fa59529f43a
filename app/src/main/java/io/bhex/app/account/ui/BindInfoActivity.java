/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BindInfoActivity.java
 *   @Date: 19-5-21 下午7:37
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.content.Intent;
import android.text.TextUtils;
import android.view.View;

import io.bhex.app.R;
import io.bhex.app.account.presenter.BindInfoPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.view.TopBar;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;

public class BindInfoActivity extends BaseActivity<BindInfoPresenter,BindInfoPresenter.BindInfoUI> implements BindInfoPresenter.BindInfoUI, View.OnClickListener {
    private TopBar topBar;
    private String bindType;

    @Override
    protected int getContentView() {
        return R.layout.activity_bind_info_layout;
    }

    @Override
    protected BindInfoPresenter createPresenter() {
        return new BindInfoPresenter();
    }

    @Override
    protected BindInfoPresenter.BindInfoUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        this.setTopbarNight(topBar);
        Intent intent = getIntent();
        if (intent != null) {
            bindType = intent.getStringExtra("bindType");
            if (!TextUtils.isEmpty(bindType)) {
                if (bindType.equals("email")) {
                    topBar.setTitle(getString(R.string.string_email));
                    viewFinder.textView(R.id.bind_type).setText(getString(R.string.string_email));
                } else if(bindType.equals("mobile")) {
                    topBar.setTitle(getString(R.string.string_mobile_number));
                    viewFinder.textView(R.id.bind_type).setText(getString(R.string.string_mobile_number));
                }else if(bindType.equals("ga")) {
                    topBar.setTitle(getString(R.string.string_google_auth));
                    viewFinder.textView(R.id.bind_type).setText(getString(R.string.string_google_auth));
                }
            }

            String bindInfo = intent.getStringExtra("bindInfo");
            if (!TextUtils.isEmpty(bindInfo)) {
                viewFinder.textView(R.id.bind_info).setText(bindInfo);
            }
        }

        UserInfoBean userInfo = UserManager.getInstance().getUserInfo();
        if (userInfo != null) {
            int registerType = userInfo.getRegisterType();
            if (bindType.equals("mobile") && registerType == 1) {
                viewFinder.find(R.id.btn_change).setVisibility(View.GONE);
            }
            if (bindType.equals("email") && registerType == 2) {
                viewFinder.find(R.id.btn_change).setVisibility(View.GONE);
            }
        }

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.btn_change).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.btn_change:
                if (!TextUtils.isEmpty(bindType)) {
                    if (bindType.equals("email")) {
                        IntentUtils.goChangeEmail(this);
                    } else if(bindType.equals("mobile")) {
                        IntentUtils.goChangeMobile(this);
                    }else if(bindType.equals("ga")) {
                        IntentUtils.goChangeGA(this);
                    }
                    this.finish();
                }
                break;
        }
    }
}
