/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OptionHistoryOrderFragment.java
 *   @Date: 1/25/19 4:30 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.account.ui;

import android.content.Context;
import android.view.View;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.presenter.OptionHistoryOrderFragmentPresenter;
import io.bhex.app.base.BaseListFreshFragment;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.trade.bean.OrderBean;

public class OptionHistoryOrderFragment extends BaseListFreshFragment<OptionHistoryOrderFragmentPresenter, OptionHistoryOrderFragmentPresenter.OptionHistoryOrderFragmentUI> implements OptionHistoryOrderFragmentPresenter.OptionHistoryOrderFragmentUI{
    @Override
    protected OptionHistoryOrderFragmentPresenter.OptionHistoryOrderFragmentUI getUI() {
        return this;
    }

    @Override
    protected OptionHistoryOrderFragmentPresenter createPresenter() {
        return new OptionHistoryOrderFragmentPresenter();
    }

    @Override
    public void showOrders(List<OrderBean> currentOrders) {
        if (adapter == null) {
            adapter = new OptionHistoryOrderAdapter(getActivity(), currentOrders);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this,recyclerView);
            adapter.setEnableLoadMore(true);

            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            recyclerView.setItemAnimator(new DefaultItemAnimator());

            adapter.setEmptyView(emptyView);
            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(currentOrders);
        }
    }

    public static class OptionHistoryOrderAdapter extends BaseQuickAdapter<OrderBean,BaseViewHolder> {

        private Context mContext;
        public OptionHistoryOrderAdapter(Context context, List<OrderBean> data) {
            super(R.layout.item_history_option_order_layout, data);
            mContext = context;
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final OrderBean itemModel) {
            if(itemModel == null)
                return;
            String title = KlineUtils.getOptionBuyOrSellTxt(mContext, itemModel.getSide());
            baseViewHolder.setText(R.id.order_buy_type, title);
            baseViewHolder.setTextColor(R.id.order_buy_type,KlineUtils.getBuyOrSellColor(mContext,itemModel.getSide()));
            baseViewHolder.setText(R.id.order_coin_name,itemModel.getSymbolName());
            baseViewHolder.setText(R.id.order_time, DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getTime()), "HH:mm:ss yyyy/MM/dd"));
            baseViewHolder.setText(R.id.order_price, KlineUtils.getPrice(mContext, itemModel));

            int tokenDigit = AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(itemModel.getSymbolId() + itemModel.getQuoteTokenId());
            baseViewHolder.setText(R.id.order_deal_price, NumberUtils.roundFormatDown(itemModel.getAvgPrice(),tokenDigit)+" " + itemModel.getQuoteTokenName());
            baseViewHolder.setText(R.id.order_entrust_amount, itemModel.getOrigQty() +" "+ mContext.getString(R.string.string_option_unit));
            int baseDigit = AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(itemModel.getSymbolId() + itemModel.getBaseTokenId());
            baseViewHolder.setText(R.id.order_deal_amount, NumberUtils.roundFormatDown(itemModel.getExecutedQty(),baseDigit)+" "+ mContext.getString(R.string.string_option_unit));
            baseViewHolder.setText(R.id.order_deal_status, KlineUtils.getOrderStatus(mContext,itemModel) );
            baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    IntentUtils.goOptionOrderDetail(mContext,itemModel);
                }
            });
        }

    }
}


