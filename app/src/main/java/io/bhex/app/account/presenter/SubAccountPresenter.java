package io.bhex.app.account.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.account.AccountInfoApi;
import io.bhex.sdk.account.bean.SubAccountBean;
import io.bhex.sdk.account.bean.SubAccountListResponse;
import io.bhex.sdk.trade.bean.AssetListResponse;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-10-30
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class SubAccountPresenter extends BasePresenter<SubAccountPresenter.SubAccountUI> {

    public interface SubAccountUI extends AppUI{

        void showAccountList(SubAccountListResponse response);

        void showAccountAsset(SubAccountBean subAccountBean, AssetListResponse response);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, SubAccountUI ui) {
        super.onUIReady(activity, ui);
    }

    @Override
    public void onResume() {
        super.onResume();
        getAccountList();
    }

    public void getAccountList() {
        AccountInfoApi.getAccountList(new SimpleResponseListener<SubAccountListResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(SubAccountListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showAccountList(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }


    /**
     * 获取子账户资产
     * @param subAccountBean
     */
    public void getAccountAsset(final SubAccountBean subAccountBean) {
        if (subAccountBean == null) {
            return;
        }
        AccountInfoApi.RequestAccountAsset(subAccountBean.getAccountType(),subAccountBean.getAccountIndex(),new SimpleResponseListener<AssetListResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(AssetListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showAccountAsset(subAccountBean,response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
