package io.bhex.app.account.ui;

import androidx.annotation.NonNull;
import android.view.View;

import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import io.bhex.app.R;
import io.bhex.app.account.presenter.PlanOrderDetailPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.sdk.trade.bean.OrderBean;
import io.bhex.sdk.trade.bean.PlanOrderBean;

/**
 * ================================================
 * 描   述：计划订单详情
 * ================================================
 */

public class PlanOrderDetailActivity extends BaseActivity<PlanOrderDetailPresenter,PlanOrderDetailPresenter.OrderDetailUI> implements PlanOrderDetailPresenter.OrderDetailUI, OnRefreshListener {
    private SmartRefreshLayout swipeRefresh;
    private OrderBean currentOrder;
    private OrderBean triggerOrder;

    @Override
    protected int getContentView() {
        return R.layout.activity_plan_order_detail_layout;
    }

    @Override
    protected PlanOrderDetailPresenter createPresenter() {
        return new PlanOrderDetailPresenter();
    }

    @Override
    protected PlanOrderDetailPresenter.OrderDetailUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        swipeRefresh.setEnabled(true);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        swipeRefresh.setOnRefreshListener(this);
        viewFinder.find(R.id.copy_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (currentOrder != null) {
                    CommonUtil.copyText(PlanOrderDetailActivity.this,currentOrder.getOrderId());
                }
            }
        });
        viewFinder.find(R.id.trigger_order_id_copy_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (triggerOrder != null) {
                    CommonUtil.copyText(PlanOrderDetailActivity.this,triggerOrder.getOrderId());
                }
            }
        });
    }


    @Override
    public void showHeaderOrders(PlanOrderBean order) {
        currentOrder = order;
        viewFinder.textView(R.id.detail_coinpair).setText(order.getBaseTokenName()+"/"+order.getQuoteTokenName());
        viewFinder.textView(R.id.detail_buymode).setText(KlineUtils.getBuyOrSellTxt(this,order.getSide()));
        viewFinder.textView(R.id.detail_buymode).setTextColor(KlineUtils.getBuyOrSellColor(this,order.getSide()));
        viewFinder.textView(R.id.detail_status).setText(KlineUtils.getPlanOrderStatusTxt(this,order.getStatus()));
        viewFinder.textView(R.id.detail_price_mode).setText(KlineUtils.getPriceModeTxt(this,order.getType()));
        viewFinder.textView(R.id.detail_order_price).setText(KlineUtils.getPrice(this,order));
        viewFinder.textView(R.id.detail_trigger_price).setText(KlineUtils.getPlanOrderTriggerPrice(this,order));
        viewFinder.textView(R.id.detail_create_date).setText(DateUtils.getSimpleTimeFormat(order.getTime()));
        if (!order.getExecutedOrderId().equals("0")) {
            viewFinder.textView(R.id.detail_trigger_date).setText(DateUtils.getSimpleTimeFormat(order.getTriggerTime()));
        }
        viewFinder.textView(R.id.detail_order_id).setText(order.getOrderId());
        viewFinder.textView(R.id.detail_order_entrust_amount_title).setText(KlineUtils.getEntrustTitle(this,order));
        viewFinder.textView(R.id.detail_order_entrust_amount).setText(KlineUtils.getOrderEntrustAndUnit(order));


    }


    @Override
    public void showOrder(OrderBean order) {
        if (order != null) {
            triggerOrder = order;
            viewFinder.find(R.id.trigger_detail_title).setVisibility(View.VISIBLE);
            viewFinder.find(R.id.trigger_detail_ll).setVisibility(View.VISIBLE);
            viewFinder.textView(R.id.trigger_order_date).setText(DateUtils.getSimpleTimeFormat(order.getTime()));
            viewFinder.textView(R.id.trigger_order_type).setText(KlineUtils.getPriceModeTxt(this,order.getType()));
            viewFinder.textView(R.id.trigger_order_status).setText(KlineUtils.getOrderStatus(this,order));

            viewFinder.textView(R.id.trigger_order_price).setText(KlineUtils.getPrice(this,order));
            viewFinder.textView(R.id.trigger_order_deal_average_price).setText(KlineUtils.getAvgPrice(this,order));
            viewFinder.textView(R.id.trigger_order_entrust_amount_title).setText(KlineUtils.getEntrustTitle(this,order));
            viewFinder.textView(R.id.trigger_order_entrust_amount).setText(KlineUtils.getOrderEntrustAndUnit(order));

            viewFinder.textView(R.id.trigger_order_deal_amount).setText(KlineUtils.getDealAmount(this,order));
            viewFinder.textView(R.id.trigger_order_total_deal_money).setText(KlineUtils.getDealMoney(this,order));
            viewFinder.textView(R.id.trigger_order_id).setText(order.getOrderId());
        }
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        getPresenter().getOrderDetail();
        refreshLayout.finishRefresh(1000);
    }

}
