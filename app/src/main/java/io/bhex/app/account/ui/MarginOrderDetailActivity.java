/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OrderDetailActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import io.bhex.app.R;
import io.bhex.app.account.presenter.MarginOrderDetailPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.trade.bean.OrderBean;
import io.bhex.sdk.trade.bean.OrderDealDetailResponse;

/**
 * ================================================
 * 描   述：订单详情
 * ================================================
 */

public class MarginOrderDetailActivity extends BaseActivity<MarginOrderDetailPresenter,MarginOrderDetailPresenter.OrderDetailUI> implements MarginOrderDetailPresenter.OrderDetailUI, BaseQuickAdapter.RequestLoadMoreListener, OnRefreshListener {
    private SmartRefreshLayout swipeRefresh;
    private RecyclerView recyclerView;
    private View headerView;
    private OrderDetailAdapter adapter;
    private TextView coinPair;
    private TextView buyMode;
    private TextView detailStatus;
    private TextView detailOrderPrice;
    private TextView detailOrderDealAveragePrice;
    private TextView detailOrderEntrustAmount;
    private TextView detailOrderDealAmount;
    private TextView detailOrderTotalDealMoney;
    private TextView detailOrderTotalFee;
    private List<OrderDealDetailResponse.DealOrderBean> currentOrderDatas=new ArrayList<>();
    private TextView detailOrderEntrustAmountTitle;
    private TextView priceMode;
    private TextView detailOrderId;
    private OrderBean currentOrder;

    @Override
    protected int getContentView() {
        return R.layout.activity_order_detail_layout;
    }

    @Override
    protected MarginOrderDetailPresenter createPresenter() {
        return new MarginOrderDetailPresenter();
    }

    @Override
    protected MarginOrderDetailPresenter.OrderDetailUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        swipeRefresh.setEnabled(true);
        recyclerView = viewFinder.find(R.id.recyclerView);
        LayoutInflater layoutInflater = LayoutInflater.from(this);
        headerView = layoutInflater.inflate(R.layout.header_order_detail, null);
        coinPair = headerView.findViewById(R.id.detail_coinpair);
        buyMode = headerView.findViewById(R.id.detail_buymode);
        priceMode = headerView.findViewById(R.id.detail_price_mode);
        detailStatus = headerView.findViewById(R.id.detail_status);
        detailOrderPrice = headerView.findViewById(R.id.detail_order_price);
        detailOrderDealAveragePrice = headerView.findViewById(R.id.detail_order_deal_average_price);
        detailOrderEntrustAmountTitle = headerView.findViewById(R.id.detail_order_entrust_amount_title);
        detailOrderEntrustAmount = headerView.findViewById(R.id.detail_order_entrust_amount);
        detailOrderDealAmount = headerView.findViewById(R.id.detail_order_deal_amount);
        detailOrderTotalDealMoney = headerView.findViewById(R.id.detail_order_total_deal_money);
        detailOrderId = headerView.findViewById(R.id.detail_order_id);

        adapter = new OrderDetailAdapter(currentOrderDatas);
//            adapter.openLoadAnimation(BaseQuickAdapter.SCALEIN);
        adapter.isFirstOnly(false);
        adapter.setOnLoadMoreListener(this,recyclerView);
//            swipeRefresh.setColorSchemeColors(Color.RED, Color.BLUE, Color.GREEN);
//            swipeRefresh.setOnRefreshListener(this);
        adapter.addHeaderView(headerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));

        recyclerView.setAdapter(adapter);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        swipeRefresh.setOnRefreshListener(this);
        headerView.findViewById(R.id.copy_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (currentOrder != null) {
                    CommonUtil.copyText(MarginOrderDetailActivity.this,currentOrder.getOrderId());
                }
            }
        });
    }


    @Override
    public void showHeaderOrders(OrderBean order) {
        currentOrder = order;
        coinPair.setText(order.getBaseTokenName()+"/"+order.getQuoteTokenName());
        buyMode.setText(KlineUtils.getBuyOrSellTxt(this,order.getSide()));
        buyMode.setTextColor(KlineUtils.getBuyOrSellColor(this,order.getSide()));
        detailStatus.setText(KlineUtils.getOrderStatus(this,order));
        priceMode.setText(KlineUtils.getPriceModeTxt(this,order.getType()));
        detailOrderPrice.setText(KlineUtils.getPrice(this,order));
        detailOrderDealAveragePrice.setText(KlineUtils.getAvgPrice(this,order));
        detailOrderEntrustAmountTitle.setText(KlineUtils.getEntrustTitle(this,order));
        detailOrderEntrustAmount.setText(KlineUtils.getOrderEntrustAndUnit(order));
        detailOrderDealAmount.setText(KlineUtils.getDealAmount(this,order));
        detailOrderTotalDealMoney.setText(KlineUtils.getDealMoney(this,order));
        detailOrderId.setText(order.getOrderId());
    }

    @Override
    public void loadMoreComplete() {
        if (adapter != null) {
            adapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {

    }

    @Override
    public void loadEnd() {
        if (adapter != null) {
            adapter.loadMoreEnd();
        }
    }

    @Override
    public void showOrders(List<OrderDealDetailResponse.DealOrderBean> datas) {
        if (datas != null) {
            currentOrderDatas = datas;
            adapter.setNewData(datas);
        }
    }

    @Override
    public void onLoadMoreRequested() {
        getPresenter().loadMore();
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        getPresenter().getDealDetail(false);
    }

    private class OrderDetailAdapter extends BaseQuickAdapter<OrderDealDetailResponse.DealOrderBean,BaseViewHolder> {

        OrderDetailAdapter(List<OrderDealDetailResponse.DealOrderBean> data) {
            super(R.layout.item_order_detail__layout, data);
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final OrderDealDetailResponse.DealOrderBean itemModel) {
            baseViewHolder.setText(R.id.detail_order_deal_time, DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getTime()), "HH:mm:ss yyyy/MM/dd"));
            int tokenDigit = AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(itemModel.getSymbolId() + itemModel.getQuoteTokenId());
            int baseDigit = AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(itemModel.getSymbolId() + itemModel.getBaseTokenId());
            int amountDigit = AppConfigManager.GetInstance().getAmountDigitBySymbolIdAndTokenId(itemModel.getSymbolId() + itemModel.getQuoteTokenId());
            baseViewHolder.setText(R.id.detail_order_deal_price,  KlineUtils.roundFormatDown(itemModel.getPrice(),tokenDigit)+" "+itemModel.getQuoteTokenName());
            baseViewHolder.setText(R.id.detail_order_deal_amount, KlineUtils.roundFormatDown(itemModel.getQuantity(),baseDigit)+" "+itemModel.getBaseTokenName());
            baseViewHolder.setText(R.id.detail_order_deal_money, KlineUtils.roundFormatDown(String.valueOf(NumberUtils.mul(itemModel.getQuantity(),itemModel.getPrice())),amountDigit)+" "+itemModel.getQuoteTokenName());
            if (!TextUtils.isEmpty(itemModel.getFee())) {
                if (Double.valueOf(itemModel.getFee())>0) {
                    baseViewHolder.setText(R.id.detail_order_fee, KlineUtils.roundFormatDown(itemModel.getFee(),AppData.Config.DIGIT_DEFAULT_VALUE)+" "+itemModel.getFeeTokenName());
                }else{
                    baseViewHolder.setText(R.id.detail_order_fee, "0");
                }
            }else{
                baseViewHolder.setText(R.id.detail_order_fee, "0");
            }
            baseViewHolder.setText(R.id.detail_order_id, itemModel.getTradeId());
            baseViewHolder.getView(R.id.trade_id_copy_btn).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    CommonUtil.copyText(mContext,itemModel.getTradeId());
                }
            });

        }

    }
}
