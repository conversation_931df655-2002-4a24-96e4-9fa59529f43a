/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AssetRecordActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.os.Bundle;

import androidx.core.util.Pair;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;

import java.util.ArrayList;

import io.bhex.app.R;
import io.bhex.app.account.presenter.AssetRecordPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.baselib.constant.AppData;
import io.bhex.app.utils.CommonUtil;


public class AssetRecordActivity extends BaseActivity<AssetRecordPresenter,AssetRecordPresenter.AssetRecordUI> implements AssetRecordPresenter.AssetRecordUI, ViewPager.OnPageChangeListener {
    private ViewPager viewPager;
    private TabLayout tab;
    private ArrayList<Pair<String, Fragment>> items;
    private RecordAdapter recordAdapter;

    @Override
    protected int getContentView() {
        return R.layout.activity_asset_record_layout;
    }

    @Override
    protected AssetRecordPresenter createPresenter() {
        return new AssetRecordPresenter();
    }

    @Override
    protected AssetRecordPresenter.AssetRecordUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        viewPager = viewFinder.find(R.id.viewPager);
        tab = viewFinder.find(R.id.tab);
        initFragmentTab();
    }

    private void initFragmentTab() {

        items = new ArrayList<>();
        AssetRecordFragment assetRecordRecharge = new AssetRecordFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(AppData.INTENT.KEY_RECORD_TYPE,0);
        assetRecordRecharge.setArguments(bundle);

        AssetRecordFragment assetRecordWithDraw = new AssetRecordFragment();
        Bundle bundle1 = new Bundle();
        bundle1.putInt(AppData.INTENT.KEY_RECORD_TYPE,1);
        assetRecordWithDraw.setArguments(bundle1);

        AssetRecordFragment other = new AssetRecordFragment();
        Bundle bundle2 = new Bundle();
        bundle2.putInt(AppData.INTENT.KEY_RECORD_TYPE,2);
        other.setArguments(bundle2);


        items.add(new Pair<String, Fragment>(getString(R.string.string_recharge_coin),assetRecordRecharge));
        items.add(new Pair<String, Fragment>(getString(R.string.string_withdraw_coin),assetRecordWithDraw));
        items.add(new Pair<String, Fragment>(getString(R.string.string_other),other));
        if (recordAdapter == null) {

            recordAdapter = new RecordAdapter(getSupportFragmentManager());
            viewPager.setAdapter(recordAdapter);
            tab.setupWithViewPager(viewPager);
//        tab.setTabTextColors(getResources().getColor(R.color.color_white),getResources().getColor(R.color.color_black));
            tab.setTabMode(TabLayout.MODE_SCROLLABLE);
            tab.setTabGravity(TabLayout.GRAVITY_CENTER);

            viewPager.addOnPageChangeListener(this);
        }else{
            recordAdapter.notifyDataSetChanged();
        }
        CommonUtil.setUpIndicatorWidthByReflex(tab,15,15);
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {

    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    private class RecordAdapter extends FragmentPagerAdapter {

        public RecordAdapter(FragmentManager fm) {
            super(fm);
        }

        @Override
        public Fragment getItem(int position) {
            return items.get(position).second;
        }

        @Override
        public int getCount() {
            return items.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return items.get(position).first;
        }


    }
}
