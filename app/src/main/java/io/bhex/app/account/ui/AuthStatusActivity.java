/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AuthStatusActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.content.Intent;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.Toolbar;

import com.google.android.material.appbar.CollapsingToolbarLayout;

import io.bhex.app.R;
import io.bhex.app.account.presenter.AuthStatusPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.sdk.account.bean.enums.VERIFY_STATUS;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.view.ShadowDrawable;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.account.bean.kyc.KycLevelBean;

/**
 * ================================================
 * 描   述：认证结果状态
 * ================================================
 */

public class AuthStatusActivity extends BaseActivity<AuthStatusPresenter, AuthStatusPresenter.AuthStatusUI> implements AuthStatusPresenter.AuthStatusUI, View.OnClickListener {
    private Button btnStatus;
    private int verifyStatus;
    private String verifyStatusTips;
    private KycLevelBean levelBean;

    @Override
    protected int getContentView() {
        return R.layout.activity_auth_status_layout;
    }

    @Override
    protected AuthStatusPresenter createPresenter() {
        return new AuthStatusPresenter();
    }

    @Override
    protected AuthStatusPresenter.AuthStatusUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        Toolbar toolbar= findViewById(R.id.toolbar);
        CollapsingToolbarLayout collapsingToolbarLayout= findViewById(R.id.collapsing_toolbar);
        // 硬编码黑白版Toolbar标题栏title字色
        collapsingToolbarLayout.setCollapsedTitleTextColor(SkinColorUtil.getDark(this));
        collapsingToolbarLayout.setExpandedTitleColor(SkinColorUtil.getDark(this));

        //显示返回按钮
        setSupportActionBar(toolbar);
        ActionBar actionBar=getSupportActionBar();
        if (actionBar!=null){
            actionBar.setDisplayHomeAsUpEnabled(true);
        }
        btnStatus = viewFinder.find(R.id.btn_status);
        Intent intent = getIntent();
        if (intent != null) {

            levelBean = (KycLevelBean) intent.getSerializableExtra("levelBean");
            verifyStatus = intent.getIntExtra("verifyStatus", -1);
            verifyStatusTips = intent.getStringExtra("verifyStatusTips");
            if (verifyStatus == VERIFY_STATUS.VERIFY_CHECK_FAILED.getmStatus()) {
                viewFinder.imageView(R.id.auth_status_img).setImageResource(R.mipmap.icon_auth_failed);
                viewFinder.textView(R.id.auth_status_result).setText(getString(R.string.string_verify_check_failed));
                viewFinder.textView(R.id.auth_status_result_tips).setText(verifyStatusTips);
                viewFinder.textView(R.id.auth_status_result_tips).setVisibility(View.VISIBLE);
                btnStatus.setText(getString(R.string.string_reauth));
                btnStatus.setTextColor(SkinColorUtil.getWhite(this));
                btnStatus.setBackgroundResource(R.drawable.btn_corner);
            } else if (verifyStatus == VERIFY_STATUS.VERIFY_CHECKING.getmStatus()) {
                viewFinder.imageView(R.id.auth_status_img).setImageResource(R.mipmap.icon_auth_checking);
                viewFinder.textView(R.id.auth_status_result).setText(getString(R.string.string_auth_checking_tips));
                viewFinder.textView(R.id.auth_status_result_tips).setVisibility(View.GONE);
                btnStatus.setTextColor(SkinColorUtil.getDark(this));
                btnStatus.setText(getString(R.string.string_title_back));

                ShadowDrawable.setShadowDrawable(btnStatus,
//                getResources().getColor(R.color.white),
                        PixelUtils.dp2px(2),
                        SkinColorUtil.getDark(this),
                        PixelUtils.dp2px(2),
                        0,
                        PixelUtils.dp2px(1));
            }
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()){
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.btn_status).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_status:
                if (verifyStatus == VERIFY_STATUS.VERIFY_CHECK_FAILED.getmStatus()) {
                    if (levelBean != null) {
                        int kycLevel = levelBean.getKycLevel();
                        if (kycLevel ==10) {
                            IntentUtils.goAuthLV1(this);
                        }else if (kycLevel == 20 || kycLevel == 25) {
                            IntentUtils.goAuthLV2ComfirmInfo(this,levelBean);
                        }else if (kycLevel ==30) {
                            IntentUtils.goAuthLV3(this,levelBean);
                        }

                    }else{
                        IntentUtils.goIdentityAuth(AuthStatusActivity.this);
                    }
                    finish();
//                    VerifyUtil.is2FA(this, new VerifyUtil.VerifyListener() {
//                        @Override
//                        public void on2FAVerify(boolean isVerify2FA) {
//                            if (isVerify2FA) {
//                                finish();
//                                IntentUtils.goIdentityAuth(AuthStatusActivity.this);
//                            }else{
//                                //自动提示去2FA
//                            }
//                        }
//                    });
                } else if (verifyStatus == VERIFY_STATUS.VERIFY_CHECKING.getmStatus()) {
                    finish();
                }
                break;
        }
    }
}
