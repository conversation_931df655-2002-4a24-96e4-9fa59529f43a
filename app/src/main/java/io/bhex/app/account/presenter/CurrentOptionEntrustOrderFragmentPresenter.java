/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CurrentOptionEntrustOrderFragmentPresenter.java
 *   @Date: 1/25/19 11:31 AM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.account.presenter;

import android.text.TextUtils;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseListFreshPresenter;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.trade.OptionApi;
import io.bhex.sdk.trade.TradeApi;
import io.bhex.sdk.trade.bean.OpenOrderRequest;
import io.bhex.sdk.trade.bean.OpenOrderResponse;
import io.bhex.sdk.trade.bean.OrderBean;

public class CurrentOptionEntrustOrderFragmentPresenter extends BaseListFreshPresenter<CurrentOptionEntrustOrderFragmentPresenter.CurrentOptionEntrustOrderFragmentUI>  {
    private static final String LOGTAG = "CurrentOptionEntrustOrderFragmentPresenter";
    private List<OrderBean> currentOrders = new ArrayList<>();

    public interface CurrentOptionEntrustOrderFragmentUI extends BaseListFreshPresenter.BaseListFreshUI {
        void showOrders(List<OrderBean> currentOrders);
    }


    @Override
    public void getData(final boolean isLoadMore) {
        if (!UserInfo.isLogin()) {
            if (isLoadMore) {
                getUI().loadMoreComplete();
            }
            return;
        }
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }

        if (isLoadMore) {
            if (currentOrders != null) {
                if (!currentOrders.isEmpty()) {
                    mPageId = currentOrders.get(currentOrders.size() - 1).getOrderId();
                }
            }
        }else{
            mPageId ="";
        }

        String pageId = "";

        if (isLoadMore && !TextUtils.isEmpty(mPageId)) {
            //加载更多
            pageId = mPageId;

        }
        OptionApi.RequestOptionOpenOrder("", pageId,new SimpleResponseListener<OpenOrderResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
                if (isLoadMore) {
                    getUI().loadMoreComplete();
                }
            }

            @Override
            public void onSuccess(OpenOrderResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<OrderBean> data = response.getArray();
                    if (data != null) {

                        if (isLoadMore) {
                            if (data != null) {
                                currentOrders.addAll(data);
                            }

                        } else {
                            if (data != null) {
                                currentOrders.clear();
                                currentOrders = data;
                            }
                        }
                        getUI().showOrders(currentOrders);

                        if (data.size()< AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        }else{
                            getUI().loadMoreComplete();
                        }
                    }else{
                        getUI().loadMoreComplete();
                    }

                }else{
                    getUI().loadMoreFailed();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                if (isLoadMore) {
                    getUI().loadMoreFailed();
                }
            }
        });
    }

    /**
     * 取消订单
     *
     * @param orderId
     * @param isBatch
     */
    public void cancelOrder(String orderId, final boolean isBatch) {
        OptionApi.RequestOptionCancelOrder(orderId, new SimpleResponseListener<OrderBean>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(OrderBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    if (!isBatch) {
                        ToastUtils.showShort(getActivity(), getString(R.string.string_revoke_success));
                        getData(false);
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                if (!isBatch) {
                    ToastUtils.showShort(getActivity(), getString(R.string.string_revoke_failed));
                }
            }
        });
    }
}
