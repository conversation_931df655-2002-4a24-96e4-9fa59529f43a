package io.bhex.app.account.ui;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.google.android.material.appbar.CollapsingToolbarLayout;

import java.io.IOException;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.Toolbar;
import io.bhex.app.R;
import io.bhex.app.account.presenter.ChangeGAPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.safe.DeepKnowVerify;
import io.bhex.app.safe.DeepSEListener;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.app.view.InputView;
import io.bhex.app.view.ShadowDrawable;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.security.bean.GAInfoResponse;
import io.bhex.sdk.socket.Base64;

/**
 * *******************************************************************
 *
 * @项目名称: BHEX Android
 * @文件名称: ChangeGAActivity
 * @Date: 2020/10/14 上午10:58
 * @Author: ppzhao
 * @Copyright（C）: 2020 BlueHelix Inc.   All rights reserved.
 * 注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 * *******************************************************************
 **/
public class ChangeGAActivity extends BaseActivity<ChangeGAPresenter, ChangeGAPresenter.ChangeGAUI> implements ChangeGAPresenter.ChangeGAUI, View.OnClickListener {
    private InputView inputAccount;
    private InputView inputVerifyCode;
    private InputView inputOldGACode;
    private InputView inputNewGACode;
    private boolean isEmail;
    private DeepKnowVerify deepKnowVerify;
    private TextView sendVerifyCodeTv;

    @Override
    protected int getContentView() {
        return R.layout.activity_change_ga_layout;
    }

    @Override
    protected ChangeGAPresenter createPresenter() {
        return new ChangeGAPresenter();
    }

    @Override
    protected ChangeGAPresenter.ChangeGAUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        deepKnowVerify = DeepKnowVerify.getInstance(this);
        Toolbar toolbar= findViewById(R.id.toolbar);
        CollapsingToolbarLayout collapsingToolbarLayout= findViewById(R.id.collapsing_toolbar);
        // 硬编码黑白版Toolbar标题栏title字色
        collapsingToolbarLayout.setCollapsedTitleTextColor(SkinColorUtil.getDark(this));
        collapsingToolbarLayout.setExpandedTitleColor(SkinColorUtil.getDark(this));

        //显示返回按钮
        setSupportActionBar(toolbar);
        ActionBar actionBar=getSupportActionBar();
        if (actionBar!=null){
            actionBar.setDisplayHomeAsUpEnabled(true);
        }
        inputOldGACode = viewFinder.find(R.id.oldGA);
        inputNewGACode = viewFinder.find(R.id.newGA);
        inputVerifyCode = viewFinder.find(R.id.verify_code_et);
        inputAccount = viewFinder.find(R.id.account_input);

        inputVerifyCode = viewFinder.find(R.id.verify_code_et);
        inputVerifyCode.setPaddingRight(PixelUtils.dp2px(80));
        sendVerifyCodeTv = viewFinder.textView(R.id.get_verify_code);
        UserInfoBean userInfo = UserManager.getInstance().getUserInfo();
        if (userInfo != null) {
            if (userInfo.getRegisterType()==1) {
                isEmail = false;
                String mobile = userInfo.getMobile();
                inputAccount.setInputString(mobile);
            }else{
                isEmail = true;
                String email = userInfo.getEmail();
                inputAccount.setInputString(email);
            }
        }

        viewFinder.textView(R.id.ga_bind_tips_b).setText(getString(R.string.string_ga_bind_tips_b,getString(R.string.app_name)));
        ShadowDrawable.setShadow(viewFinder.find(R.id.ga_keystore_rela));
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()){
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.ga_key_copy).setOnClickListener(this);
        deepKnowVerify.ignoreDPView(viewFinder.find(R.id.get_verify_code),"bindga");
        viewFinder.find(R.id.btn_sure).setOnClickListener(this);
        viewFinder.find(R.id.get_verify_code).setOnClickListener(this);
        inputOldGACode.addTextWatch(mTextWatcher);
        inputNewGACode.addTextWatch(mTextWatcher);
        inputVerifyCode.addTextWatch(mTextWatcher);

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.btn_sure:
                final String verifyCode = inputVerifyCode.getInputString();
                if (TextUtils.isEmpty(verifyCode)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.input_verify));
                    return;
                } else {
                    inputVerifyCode.setError("");
                }
                final String oldGACode = inputOldGACode.getInputString();
                if (TextUtils.isEmpty(oldGACode)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.input_old_google_verify_code));
                    return;
                } else {
                    inputOldGACode.setError("");
                }
                final String newGACode = inputNewGACode.getInputString();
                if (TextUtils.isEmpty(newGACode)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.input_new_google_verify_code));
                    return;
                } else {
                    inputNewGACode.setError("");
                }
                if (!NetWorkStatus.isConnected(ChangeGAActivity.this)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.hint_network_not_connect));
                    return;
                }
                getPresenter().requestBindGA(verifyCode,oldGACode,newGACode);

                break;
            case R.id.get_verify_code:
                getUI().showProgressDialog("","");
                deepKnowVerify.verify(baseSEListener);
                break;

            case R.id.ga_key_copy:
                CommonUtil.copyText(this, viewFinder.textView(R.id.ga_keystore).getText().toString());
                break;
        }
    }

    private DeepSEListener baseSEListener = new DeepSEListener() {

        /**
         * SDK内部show loading dialog
         */
        @Override
        public void onShowDialog() {
            getUI().dismissProgressDialog();
        }

        @Override
        public void onError(String errorCode, String error) {
//            DebugLog.i(TAG,"onError-->errorCode:"+errorCode+", error: "+error);
//            ToastUtils.showShort(error);
            getUI().dismissProgressDialog();
            ToastUtils.showShort(getString(R.string.string_net_exception)+errorCode);
        }

        /**
         * 验证码Dialog关闭
         * 1：webview的叉按钮关闭
         * 2：点击屏幕外关闭
         * 3：点击回退键关闭
         *
         * @param num
         */
        @Override
        public void onCloseDialog(int num) {
//            DebugLog.i(TAG, "onCloseDialog-->" + num);
        }

        /**
         * show 验证码webview
         */
        @Override
        public void onDialogReady() {
//            DebugLog.i(TAG,"onDialogReady-->SDK show captcha webview dialog! ");
        }

        /**
         * 验证成功
         * @param token
         */
        @Override
        public void onResult(String token) {
//            DebugLog.i(TAG,"onResult: "+token);
//            final String verifyCode = inputVerifyCode.getInputString();
//            if (TextUtils.isEmpty(verifyCode)) {
//                ToastUtils.showShort(getResources().getString(R.string.input_verify));
//                return;
//            } else {
//                inputVerifyCode.setError("");
//            }
//            final String googleVerifyCode = inputGoogleCode.getInputString();
//            if (TextUtils.isEmpty(googleVerifyCode)) {
//                ToastUtils.showShort(getResources().getString(R.string.input_google_verify_code));
//                return;
//            } else {
//                inputGoogleCode.setError("");
//            }
            getUI().dismissProgressDialog();
            if (!NetWorkStatus.isConnected(ChangeGAActivity.this)) {
                ToastUtils.showShort(ChangeGAActivity.this, getResources().getString(R.string.hint_network_not_connect));
                return;
            }
            getPresenter().requestVerifyCodeOfBindGA(isEmail,token);

        }
    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        deepKnowVerify.destroy();
    }

    @Override
    public void setAuthTv(String s) {
        sendVerifyCodeTv.setText(s);
    }

    @Override
    public void setAuthTvStatus(boolean b) {
        sendVerifyCodeTv.setEnabled(b);
        sendVerifyCodeTv.setTextColor(b ? getResources().getColor(R.color.blue) : SkinColorUtil.getDark50(this));
    }

    /**
     * 编辑框监听器
     */
    private TextWatcher mTextWatcher = new TextWatcher() {

        /** 改变前*/
        @Override
        public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
        }

        /** 内容改变*/
        @Override
        public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            if (getPresenter().checkInputContentIsEmpty(inputVerifyCode, inputOldGACode,inputNewGACode)) {
                viewFinder.find(R.id.btn_sure).setEnabled(true);
            } else {
                viewFinder.find(R.id.btn_sure).setEnabled(false);
            }

        }

        @Override
        public void afterTextChanged(Editable s) {

        }
    };

    @Override
    public void showGABindInfo(GAInfoResponse response) {
        viewFinder.textView(R.id.ga_keystore).setText(response.getSecretKey());
        ImageView qrCode = viewFinder.imageView(R.id.ga_bind_qrcode);
        if (!TextUtils.isEmpty(response.getQrcode())) {
            try {
                byte[] qrCodeByte = Base64.decode(response.getQrcode(), Base64.DECODE);
                Glide.with(this).load(qrCodeByte).into(qrCode);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
