/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinancePasswdPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import android.os.CountDownTimer;
import android.text.TextUtils;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.sdk.security.SecurityApi;
import io.bhex.sdk.security.bean.OrderIdParamResponse;
import io.bhex.app.view.InputView;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;

public class FinancePasswdPresenter extends BasePresenter<FinancePasswdPresenter.FinancePasswdUI> {
    private String orderId="";

    public boolean checkInputContentIsEmpty(InputView inputPasswd, InputView inputPasswd2, InputView inputVerify) {
            String passwd = inputPasswd.getInputString();
            String comfirmPasswd = inputPasswd2.getInputString();
            String code = inputVerify.getInputString();

            return !TextUtils.isEmpty(passwd) && !TextUtils.isEmpty(comfirmPasswd) && !TextUtils.isEmpty(code);
    }

    public void sendVerifyCode(boolean isEmail, boolean isBindTradePwd) {
        SecurityApi.sendFundVerifyCode(isEmail,!isBindTradePwd,new SimpleResponseListener<OrderIdParamResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }
            @Override
            public void onSuccess(OrderIdParamResponse data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data,true)) {
                    orderId = data.getOrderId();
                    getUI().setAuthTvStatus(false);
                    timer.start();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    /**
     * 设置资金密码
     * @param isBindTradePwd
     * @param isEmail
     * @param passwdFirst
     * @param passwdSecond
     * @param verifyCode
     */
    public void setFinancePasswd(final boolean isBindTradePwd, boolean isEmail, String passwdFirst, String passwdSecond, String verifyCode) {
        if(TextUtils.isEmpty(orderId)) {
            ToastUtils.showShort(getString(R.string.string_verify_code_invalid));
            return;
        }
        SecurityApi.setFinancePasswd(!isBindTradePwd,passwdFirst,passwdSecond,orderId,verifyCode,new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    ToastUtils.showShort(getString(!isBindTradePwd?R.string.string_set_finance_passwd_success:R.string.string_modify_finance_passwd_success));
                    getActivity().finish();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(getString(!isBindTradePwd?R.string.string_set_finance_passwd_fail:R.string.string_modify_finance_passwd_fail));
            }
        });
    }

    public interface FinancePasswdUI extends AppUI{

        void setAuthTvStatus(boolean b);

        void setAuthTv(String tips);
    }


    /**
     * 倒计时
     */
    private CountDownTimer timer = new CountDownTimer(AppData.DOWN_TIME_CODE, AppData.DOWN_TIME_INTERVAL_CODE) {
        @Override
        public void onTick(long millisUntilFinished) {
            getUI().setAuthTv((millisUntilFinished / 1000)
                    + getActivity().getResources().getString(
                    R.string.after_second));
        }

        @Override
        public void onFinish() {
            getUI().setAuthTvStatus(true);
            getUI().setAuthTv(getResources().getString(
                    R.string.string_get_auth_code));
        }
    };

}
