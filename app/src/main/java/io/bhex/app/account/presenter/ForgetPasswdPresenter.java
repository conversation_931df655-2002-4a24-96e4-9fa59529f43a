/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: ForgetPasswdPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import android.os.CountDownTimer;
import android.text.TextUtils;

import io.bhex.app.BuildConfig;
import io.bhex.app.R;
import io.bhex.sdk.account.AccountInfoApi;
import io.bhex.sdk.account.bean.FindPwdCheckRequest;
import io.bhex.sdk.account.bean.FindPwdCheckResponse;
import io.bhex.sdk.account.bean.OrderParamResponse;
import io.bhex.app.base.AppUI;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.app.view.InputView;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.exception.NetException;
import io.bhex.baselib.network.response.UISafeKeeper;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.bean.VerifyFirstRequest;


public class ForgetPasswdPresenter extends BasePresenter<ForgetPasswdPresenter.ForgetPasswdUI>{
    private String orderIdOfEmail;
    private String orderIdOfMobile;

    public interface ForgetPasswdUI extends AppUI{

        void checkSuccess(boolean isEmail, String account, String nationalCode, FindPwdCheckResponse orderId);

        void setAuthTvStatus(boolean b);

        void setAuthTv(String s);

        boolean isChinaMobile();
    }

    public void verifyFirstRequest(final boolean isEmail, String mobileCode, InputView accountView, String token){
        final String account = accountView.getInputString();
        if (TextUtils.isEmpty(account)) {
            accountView.setError(isEmail?getResources().getString(R.string.input_email):getResources().getString(R.string.input_phone_number));
            return;
        }else{
            accountView.setError("");
        }
        if (isEmail) {
            //手机号校验
//            if (!RegexUtils.checkEmail(account)) {
//                accountView.setError(getResources().getString(R.string.input_right_email_please));
//                return;
//            }else{
//                accountView.setError("");
//            }
        }else{
//            if (!RegexUtils.checkMobile(account)&&!getUI().isChinaMobile()){
//                accountView.setError(getResources().getString(R.string.input_right_mobile_please));
//                return;
//            }else{
//                accountView.setError("");
//            }
        }

        final VerifyFirstRequest requestData = new VerifyFirstRequest();
        requestData.bEmail = isEmail;
        requestData.account = account;
        requestData.token = token;
        requestData.captcha_id = BuildConfig.DEEPKNOW_ID;
        if (isEmail) {
        }else{
            requestData.mobileCode = mobileCode;
        }
        AccountInfoApi.RequestVerifyFirst(requestData, UISafeKeeper.guard(getUI(), new SimpleResponseListener<OrderParamResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("",getString(R.string.string_loading_hint_text));
            }

            @Override
            public void onSuccess(OrderParamResponse data) {
                super.onSuccess(data);

                if (CodeUtils.isSuccess(data,true)) {
                    if (isEmail) {
                        orderIdOfEmail = data.getOrderId();
                    }else{
                        orderIdOfMobile = data.getOrderId();
                    }
                    getUI().setAuthTvStatus(false);
                    timer.start();
                }

            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                if (error instanceof NetException) {
//                    NetException nt = (NetException)error;
//                    ToastUtils.showShort(getActivity(),nt.getCode()+":"+nt.getShowMessage());
                }else{

//                    ToastUtils.showShort(getActivity(),getResources().getString(R.string.server_error));
                }
            }
        }));
    }

    /**
     * 找回密码
     * @param accountEt
     * @param verifyCodeEt
     */
    public void findPasswdCheck(final boolean isEmail,final String nationalCode, InputView accountEt, InputView verifyCodeEt) {
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }
        final String account = accountEt.getInputString();
        if (TextUtils.isEmpty(account)) {
            ToastUtils.showShort(getActivity(), isEmail?getString(R.string.input_email):getString(R.string.input_phone_number));
            return;
        }
        String verifyCode = verifyCodeEt.getInputString();
        if (TextUtils.isEmpty(verifyCode)) {
            ToastUtils.showShort(getActivity(), getString(R.string.input_verify));
            return;
        }

        //校验验证码
        if (isEmail) {
            if(TextUtils.isEmpty(orderIdOfEmail)) {
                verifyCodeEt.setError(getString(R.string.string_verify_code_invalid));
                return;
            }
        }else{
            if(TextUtils.isEmpty(orderIdOfMobile)) {
                verifyCodeEt.setError(getString(R.string.string_verify_code_invalid));
                return;
            }
        }
        FindPwdCheckRequest requestData = new FindPwdCheckRequest();
        requestData.bEmail = isEmail;
        requestData.account = account;
        requestData.verifyCode = verifyCode;
        if (isEmail) {
            requestData.orderId = orderIdOfEmail;
        }else{
            requestData.orderId = orderIdOfMobile;
            requestData.mobileCode = nationalCode;
        }

        AccountInfoApi.FindPwdCheck1(requestData, new SimpleResponseListener<FindPwdCheckResponse>(){
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(FindPwdCheckResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().checkSuccess(isEmail,account,nationalCode,response);

                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }


    /**
     * 倒计时
     */
    private CountDownTimer timer = new CountDownTimer(AppData.DOWN_TIME_CODE, AppData.DOWN_TIME_INTERVAL_CODE) {
        @Override
        public void onTick(long millisUntilFinished) {
            getUI().setAuthTv((millisUntilFinished / 1000)
                    + getActivity().getResources().getString(
                    R.string.after_second));
        }

        @Override
        public void onFinish() {
            getUI().setAuthTvStatus(true);
            getUI().setAuthTv(getResources().getString(
                    R.string.string_get_auth_code));
        }
    };
}
