/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinancePasswdActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.TextView;

import io.bhex.app.R;
import io.bhex.app.account.presenter.SetLoginPasswdPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.app.utils.RegexUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.app.view.InputView;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;

/**
 * ================================================
 * 描   述：资金密码
 * ================================================
 */

public class SetLoginPasswdActivity extends BaseActivity<SetLoginPasswdPresenter,SetLoginPasswdPresenter.SetLoginPasswdUI> implements SetLoginPasswdPresenter.SetLoginPasswdUI, View.OnClickListener {
    private InputView inputPasswd;
    private InputView inputPasswd2;
    private InputView inputAccount;
    private InputView inputVerify;
    private TextView sendVerifyCodeTv;

    @Override
    protected int getContentView() {
        return R.layout.activity_set_login_passwd_layout;
    }

    @Override
    protected SetLoginPasswdPresenter createPresenter() {
        return new SetLoginPasswdPresenter();
    }

    @Override
    protected SetLoginPasswdPresenter.SetLoginPasswdUI getUI() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //设置禁止系统截屏、录制
//        getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
    }

    @Override
    protected void initView() {
        super.initView();
        inputPasswd = viewFinder.find(R.id.pwd_input);
        inputPasswd2 = viewFinder.find(R.id.pwd2_input);
        inputAccount = viewFinder.find(R.id.account_input);
        inputVerify = viewFinder.find(R.id.verify_code_et);

        sendVerifyCodeTv = viewFinder.textView(R.id.get_verify_code);
        inputVerify.setPaddingRight(PixelUtils.dp2px(80));

        UserInfoBean userInfo = UserManager.getInstance().getUserInfo();
        if (userInfo != null) {
            String mobile = userInfo.getMobile();
            inputAccount.setInputString(mobile);

//            if (userInfo.getRegisterType()==1) {
//                String mobile = userInfo.getMobile();
//                inputAccount.setInputString(mobile);
//                isEmail = false;
//            }else{
//                String email = userInfo.getEmail();
//                inputAccount.setInputString(email);
//                isEmail = true;
//            }
        }
//        inputVerify.setInputHint(isEmail?getString(R.string.string_input_email_verify_code):getString(R.string.string_input_mobile_verify_code));
        inputVerify.setInputHint(getString(R.string.string_input_mobile_verify_code));

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.get_verify_code).setOnClickListener(this);
        viewFinder.find(R.id.btn_sure).setOnClickListener(this);
        inputPasswd.addTextWatch(mTextWatcher);
        inputPasswd.addTextWatch(mTextWatcher);
        inputVerify.addTextWatch(mTextWatcher);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.get_verify_code:
                final String passwd = inputPasswd.getInputString();
                final String passwd2 = inputPasswd2.getInputString();
                if (TextUtils.isEmpty(passwd)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.string_input_new_passwd));
                    return;
                } else {
                    inputPasswd.setError("");
                }
                if (!RegexUtils.checkPasswd(passwd)) {
                    inputPasswd.setError(getResources().getString(R.string.input_pwd_reg_no_match));
                    return;
                }else{
                    inputPasswd.setError("");
                }
                if (TextUtils.isEmpty(passwd2)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.input_pwd2));
                    return;
                } else {
                    inputPasswd2.setError("");
                }
                if (!passwd.equals(passwd2)) {
                    inputPasswd2.setError(getString(R.string.string_pwd_no_match));
                    return;
                }else{
                    inputPasswd2.setError("");
                }
                if (!NetWorkStatus.isConnected(this)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.hint_network_not_connect));
                    return;
                }
                getPresenter().sendVerifyCode();
                break;
            case R.id.btn_sure:
                final String passwdFirst = inputPasswd.getInputString();
                final String passwdSecond = inputPasswd2.getInputString();
                final String verifyCode = inputVerify.getInputString();
                if (TextUtils.isEmpty(passwdFirst)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.string_input_new_passwd));
                    return;
                } else {
                    inputPasswd.setError("");
                }
                if (!RegexUtils.checkPasswd(passwdFirst)) {
                    inputPasswd.setError(getResources().getString(R.string.input_pwd_reg_no_match));
                    return;
                }else{
                    inputPasswd.setError("");
                }
                if (TextUtils.isEmpty(passwdSecond)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.input_pwd2));
                    return;
                } else {
                    inputPasswd2.setError("");
                }
                if (!passwdFirst.equals(passwdSecond)) {
                    inputPasswd2.setError(getString(R.string.string_pwd_no_match));
                    return;
                }else{
                    inputPasswd2.setError("");
                }
                if (TextUtils.isEmpty(verifyCode)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.input_verify));
                    return;
                } else {
                    inputVerify.setError("");
                }
                if (!NetWorkStatus.isConnected(this)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.hint_network_not_connect));
                    return;
                }
                getPresenter().setLoginPasswd(passwdFirst,passwdSecond,verifyCode);
                break;
        }
    }

    /**
     * 编辑框监听器
     */
    private TextWatcher mTextWatcher = new TextWatcher() {

        /** 改变前*/
        @Override
        public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
        }

        /** 内容改变*/
        @Override
        public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            if (getPresenter().checkInputContentIsEmpty(inputPasswd,inputPasswd,inputVerify)) {
                viewFinder.find(R.id.btn_sure).setEnabled(true);
            } else {
                viewFinder.find(R.id.btn_sure).setEnabled(false);
            }

        }

        @Override
        public void afterTextChanged(Editable s) {

        }
    };

    @Override
    public void setAuthTvStatus(boolean b) {
        sendVerifyCodeTv.setEnabled(b);
        sendVerifyCodeTv.setTextColor(b ? getResources().getColor(R.color.blue) : SkinColorUtil.getDark50(this));

    }

    @Override
    public void setAuthTv(String tips) {
        sendVerifyCodeTv.setText(tips);
    }
}
