/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MyAssetMultiplePagerAdapter.java
 *   @Date: 1/22/19 11:12 AM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.account.adapter;

import android.content.Context;
import android.graphics.PorterDuff;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.BasicFunctionsUtil;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.config.bean.BasicFunctionsConfig;
import io.bhex.sdk.data_manager.RateDataManager;
import io.bhex.sdk.trade.bean.AllAssetResponse;

public class MyAssetMultiplePagerAdapter extends PagerAdapter {
    private String[] mAssetTitlesArray;
    //private List<CoinPairBean> mList = new ArrayList<CoinPairBean>();
    private List<View> mViewList = new ArrayList<>();
    private LayoutInflater layoutInflater;
    private ViewPager mVP;
    private boolean mbOpenEye;
    private Context mContext;
    public MyAssetMultiplePagerAdapter(Context context, String[] assetTitlesArray, AllAssetResponse response, ViewPager walletVp, boolean bOpenEye) {
        super();
        layoutInflater = LayoutInflater.from(context);
        mContext = context;
        mVP = walletVp;
        mbOpenEye = bOpenEye;
        mAssetTitlesArray = assetTitlesArray;

        setData(response);
    }

    public List<View> getItemViews(){
        return mViewList;
    }

    @Override
    public int getItemPosition(@NonNull Object object) {
        return POSITION_NONE;
    }

    @Override
    public int getCount() {
        return this.mViewList.size();
    }

    @Override
    public boolean isViewFromObject(View view, Object object) {
        return view == object;
    }

    /**
     * 页面宽度所占ViewPager测量宽度的权重比例，默认为1
     */
    @Override
    public float getPageWidth(int position) {
        if(getCount() > 1){
            return (float) 5/6;
        }else{
            return 1;
        }
    }

    @Nullable
    @Override
    public CharSequence getPageTitle(int position) {
        if (mAssetTitlesArray == null) {
            return "";
        }
        if (mAssetTitlesArray.length>position) {
            return mAssetTitlesArray[position];
        }else{
            return "";
        }
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        View view = (View) object;
        container.removeView(view);
    }

    public void ChangeData(String[] assetTitlesArray, AllAssetResponse data, boolean bOpenEye){
        mAssetTitlesArray = assetTitlesArray;
        mbOpenEye = bOpenEye;
        if(mViewList != null && mViewList.size() > 0 && data != null){
            for (View view : mViewList) {
                String tag = (String) view.getTag();
                if (tag.equals("BB")) {
                    {
                        View bbAssetItem = view;
                        ((TextView) bbAssetItem.findViewById(R.id.asset_trade_account)).setText(mContext.getString(R.string.string_asset_bb));
                        ((TextView) bbAssetItem.findViewById(R.id.asset_total_txt)).setText(mContext.getString(R.string.string_asset_bb_about));
                        String tokenToBTCQuantity = RateDataManager.getTokenToBTC(data.unit, data.coinAsset);
                        String bbAsset = NumberUtils.roundFormatDown(tokenToBTCQuantity, AppData.Config.DIGIT_DEFAULT_VALUE);
                        ((TextView) bbAssetItem.findViewById(R.id.asset_total)).setText(bbAsset);
                        String bbAssetCurrency = RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(AppData.Config.LEGAL_MONEY_ABOUT_VALUE_CAL_UNIT, tokenToBTCQuantity), AppData.DIGIT_LEGAL_MONEY);
                        ((TextView) bbAssetItem.findViewById(R.id.asset_total_currency)).setText("≈" + bbAssetCurrency);
                        if (mbOpenEye == false) {
                            ((TextView) bbAssetItem.findViewById(R.id.asset_total)).setText(mContext.getString(R.string.string_star_star));
                            ((TextView) bbAssetItem.findViewById(R.id.asset_total_currency)).setText(mContext.getString(R.string.string_star_star));
                        }
                    }
                }else if(tag.equals("Futures")){
                    {
                        View futuresAssetItem = view;
                        ((TextView) futuresAssetItem.findViewById(R.id.asset_trade_account)).setText(mContext.getString(R.string.string_asset_futures));
                        ((TextView) futuresAssetItem.findViewById(R.id.asset_total_txt)).setText(mContext.getString(R.string.string_asset_futures_about) + "(BTC)");

                        String tokenToBTCQuantity = RateDataManager.getTokenToBTC(data.unit, data.futuresCoinAsset);
                        String futuresAssetBTC = NumberUtils.roundFormatDown(tokenToBTCQuantity, AppData.Config.DIGIT_DEFAULT_VALUE);
                        ((TextView) futuresAssetItem.findViewById(R.id.asset_total)).setText(futuresAssetBTC);
                        String futuresAssetCurrency = RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(AppData.Config.LEGAL_MONEY_ABOUT_VALUE_CAL_UNIT, tokenToBTCQuantity), AppData.DIGIT_LEGAL_MONEY);
                        ((TextView) futuresAssetItem.findViewById(R.id.asset_total_currency)).setText("≈" + futuresAssetCurrency);
                        if (mbOpenEye == false) {
                            ((TextView) futuresAssetItem.findViewById(R.id.asset_total)).setText(mContext.getString(R.string.string_star_star));
                            ((TextView) futuresAssetItem.findViewById(R.id.asset_total_currency)).setText(mContext.getString(R.string.string_star_star));
                        }
                    }
                }else if(tag.equals("Option")){
                    {
                        View optionAssetItem = view;
                        String optionAssetDouble = NumberUtils.add2(data.optionAsset, data.optionCoinAsset);
                        ((TextView) optionAssetItem.findViewById(R.id.asset_trade_account)).setText(mContext.getString(R.string.string_asset_option));
                        ((TextView) optionAssetItem.findViewById(R.id.asset_total_txt)).setText(mContext.getString(R.string.string_asset_option_about) + "(BTC)");

                        String tokenToBTCQuantity = RateDataManager.getTokenToBTC(data.unit, optionAssetDouble);
                        String optionAssetBTC = NumberUtils.roundFormatDown(tokenToBTCQuantity, AppData.Config.DIGIT_DEFAULT_VALUE);
                        ((TextView) optionAssetItem.findViewById(R.id.asset_total)).setText(optionAssetBTC);
                        String optionAssetCurrency = RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(AppData.Config.LEGAL_MONEY_ABOUT_VALUE_CAL_UNIT, tokenToBTCQuantity), AppData.DIGIT_LEGAL_MONEY);
                        ((TextView) optionAssetItem.findViewById(R.id.asset_total_currency)).setText("≈" + optionAssetCurrency);
                        if (mbOpenEye == false) {
                            ((TextView) optionAssetItem.findViewById(R.id.asset_total)).setText(mContext.getString(R.string.string_star_star));
                            ((TextView) optionAssetItem.findViewById(R.id.asset_total_currency)).setText(mContext.getString(R.string.string_star_star));
                        }
                    }
                }else if(tag.equals("CoinPlus")){
                    {
                        View coinplusAssetItem = view;
                        ((TextView) coinplusAssetItem.findViewById(R.id.asset_trade_account)).setText(mContext.getString(R.string.string_asset_coinplus));
                        ((TextView) coinplusAssetItem.findViewById(R.id.asset_total_txt)).setText(mContext.getString(R.string.string_asset_coinplus_about) );
                        String tokenToBTCQuantity = RateDataManager.getTokenToBTC(data.unit, data.financeAsset);
                        String coinplusAsset = NumberUtils.roundFormatDown(tokenToBTCQuantity, AppData.Config.DIGIT_DEFAULT_VALUE);
                        ((TextView) coinplusAssetItem.findViewById(R.id.asset_total)).setText(coinplusAsset);

                        String coinplusAssetCurrency = RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(AppData.Config.LEGAL_MONEY_ABOUT_VALUE_CAL_UNIT, tokenToBTCQuantity), AppData.DIGIT_LEGAL_MONEY);
                        ((TextView) coinplusAssetItem.findViewById(R.id.asset_total_currency)).setText("≈" + coinplusAssetCurrency);
                        if (mbOpenEye == false) {
                            ((TextView) coinplusAssetItem.findViewById(R.id.asset_total)).setText(mContext.getString(R.string.string_star_star));
                            ((TextView) coinplusAssetItem.findViewById(R.id.asset_total_currency)).setText(mContext.getString(R.string.string_star_star));
                        }
                    }
                } else if(tag.equals("Margin")){
                    {
                        View marginAssetItem = view;
                        ((TextView) marginAssetItem.findViewById(R.id.asset_trade_account)).setText(mContext.getString(R.string.string_asset_margin));
                        ((TextView) marginAssetItem.findViewById(R.id.asset_total_txt)).setText(mContext.getString(R.string.string_asset_margin_about) );
                        String tokenToBTCQuantity = RateDataManager.getTokenToBTC(data.unit, data.coinAsset);
                        String marginAsset = NumberUtils.roundFormatDown(tokenToBTCQuantity, AppData.Config.DIGIT_DEFAULT_VALUE);
                        ((TextView) marginAssetItem.findViewById(R.id.asset_total)).setText(marginAsset);

                        String marginAssetCurrency = RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(AppData.Config.LEGAL_MONEY_ABOUT_VALUE_CAL_UNIT, tokenToBTCQuantity), AppData.DIGIT_LEGAL_MONEY);
                        ((TextView) marginAssetItem.findViewById(R.id.asset_total_currency)).setText("≈" + marginAssetCurrency);
                        if (mbOpenEye == false) {
                            ((TextView) marginAssetItem.findViewById(R.id.asset_total)).setText(mContext.getString(R.string.string_star_star));
                            ((TextView) marginAssetItem.findViewById(R.id.asset_total_currency)).setText(mContext.getString(R.string.string_star_star));
                        }
                    }
                }
            }

        }
    }

    public void setData(AllAssetResponse data)
    {
        if (data == null)
            return;
        final List<View> viewList = new ArrayList<>();
        BasicFunctionsConfig basicFunctionsConfig = BasicFunctionsUtil.getBasicFunctionsConfig();

        {
            //钱包默认账户，不能隐藏
            final View bbAssetItem = layoutInflater.inflate(R.layout.asset_item_view, null);
            bbAssetItem.setTag("BB");
            bbAssetItem.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mVP != null)
                        mVP.setCurrentItem(viewList.indexOf(bbAssetItem));
                }
            });
            ((TextView) bbAssetItem.findViewById(R.id.asset_trade_account)).setText(mContext.getString(R.string.string_asset_bb));
            ((TextView) bbAssetItem.findViewById(R.id.asset_total_txt)).setText(mContext.getString(R.string.string_asset_bb_about));
            String bbAsset = NumberUtils.roundFormatDown(RateDataManager.getTokenToBTC(data.unit, data.coinAsset), AppData.Config.DIGIT_DEFAULT_VALUE);
            ((TextView) bbAssetItem.findViewById(R.id.asset_total)).setText(bbAsset);
            String bbAssetCurrency = RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice("USDT", data.coinAsset), AppData.DIGIT_LEGAL_MONEY);
            ((TextView) bbAssetItem.findViewById(R.id.asset_total_currency)).setText("≈" + bbAssetCurrency);
            if (mbOpenEye == false) {
                ((TextView) bbAssetItem.findViewById(R.id.asset_total)).setText(mContext.getString(R.string.string_star_star));
                ((TextView) bbAssetItem.findViewById(R.id.asset_total_currency)).setText(mContext.getString(R.string.string_star_star));
            }
            viewList.add(bbAssetItem);
        }

        {
            if (!basicFunctionsConfig.isFuture()) {

                final View futuresAssetItem = layoutInflater.inflate(R.layout.asset_item_view, null);
                futuresAssetItem.setTag("Futures");
                futuresAssetItem.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (mVP != null)
                            mVP.setCurrentItem(viewList.indexOf(futuresAssetItem));
                    }
                });
                ((TextView) futuresAssetItem.findViewById(R.id.asset_trade_account)).setText(mContext.getString(R.string.string_asset_futures));
                ((TextView) futuresAssetItem.findViewById(R.id.asset_total_txt)).setText(mContext.getString(R.string.string_asset_futures_about)+ "(" + data.unit + ")");
                String futuresAsset = NumberUtils.roundFormatDown(data.futuresCoinAsset, AppData.Config.DIGIT_DEFAULT_VALUE);
                ((TextView) futuresAssetItem.findViewById(R.id.asset_total)).setText(futuresAsset);

                String futuresAssetBTC = NumberUtils.roundFormatDown(RateDataManager.getTokenToBTC(data.unit, data.futuresCoinAsset), AppData.Config.DIGIT_DEFAULT_VALUE);
                String futuresAssetCurrency = RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(data.unit, data.futuresCoinAsset), AppData.DIGIT_LEGAL_MONEY);
                ((TextView) futuresAssetItem.findViewById(R.id.asset_total_currency)).setText("≈" + futuresAssetBTC + "BTC / " + futuresAssetCurrency);
                if (mbOpenEye == false) {
                    ((TextView) futuresAssetItem.findViewById(R.id.asset_total)).setText(mContext.getString(R.string.string_star_star));
                    ((TextView) futuresAssetItem.findViewById(R.id.asset_total_currency)).setText(mContext.getString(R.string.string_star_star));
                }
                viewList.add(futuresAssetItem);
            }
        }

        {
            if (!basicFunctionsConfig.isMargin()) {
                final View marginAssetItem = layoutInflater.inflate(R.layout.asset_item_view, null);
                marginAssetItem.setTag("Margin");
                marginAssetItem.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (mVP != null)
                            mVP.setCurrentItem(viewList.indexOf(marginAssetItem));
                    }
                });
                ((TextView) marginAssetItem.findViewById(R.id.asset_trade_account)).setText(mContext.getString(R.string.string_asset_margin));
                ((TextView) marginAssetItem.findViewById(R.id.asset_total_txt)).setText(mContext.getString(R.string.string_asset_margin_about));
                String coinplusAsset = NumberUtils.roundFormatDown(RateDataManager.getTokenToBTC(data.unit, data.coinAsset), AppData.Config.DIGIT_DEFAULT_VALUE);
                ((TextView) marginAssetItem.findViewById(R.id.asset_total)).setText(coinplusAsset);

                String coinplusAssetCurrency = RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(data.unit, data.coinAsset), AppData.DIGIT_LEGAL_MONEY);
                ((TextView) marginAssetItem.findViewById(R.id.asset_total_currency)).setText("≈" + /*coinplusAssetBTC + "BTC / " +*/ coinplusAssetCurrency);
                if (mbOpenEye == false) {
                    ((TextView) marginAssetItem.findViewById(R.id.asset_total)).setText(mContext.getString(R.string.string_star_star));
                    ((TextView) marginAssetItem.findViewById(R.id.asset_total_currency)).setText(mContext.getString(R.string.string_star_star));
                }
                viewList.add(marginAssetItem);
            }
        }

        {
            if (!basicFunctionsConfig.isOption()) {

                final View optionAssetItem = layoutInflater.inflate(R.layout.asset_item_view, null);
                optionAssetItem.setTag("Option");
                optionAssetItem.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (mVP != null)
                            mVP.setCurrentItem(viewList.indexOf(optionAssetItem));
                    }
                });
                ((TextView) optionAssetItem.findViewById(R.id.asset_trade_account)).setText(mContext.getString(R.string.string_asset_option));
                ((TextView) optionAssetItem.findViewById(R.id.asset_total_txt)).setText(mContext.getString(R.string.string_asset_option_about)+ "(" + data.unit + ")");
                double optionAssetDouble = NumberUtils.add(data.optionAsset, data.optionCoinAsset);
                String optionAsset = NumberUtils.roundFormatDown(optionAssetDouble, AppData.Config.DIGIT_DEFAULT_VALUE);
                ((TextView) optionAssetItem.findViewById(R.id.asset_total)).setText(optionAsset);

                String optionAssetBTC = NumberUtils.roundFormatDown(RateDataManager.getTokenToBTC(data.unit, data.optionAsset), AppData.Config.DIGIT_DEFAULT_VALUE);
                String optionAssetCurrency = RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(data.unit, data.optionAsset), AppData.DIGIT_LEGAL_MONEY);
//                String optionAssetCurrency = RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice("USDT", data.optionAsset), AppData.DIGIT_LEGAL_MONEY);
                ((TextView) optionAssetItem.findViewById(R.id.asset_total_currency)).setText("≈" + optionAssetBTC + "BTC / " + optionAssetCurrency);
                if (mbOpenEye == false) {
                    ((TextView) optionAssetItem.findViewById(R.id.asset_total)).setText(mContext.getString(R.string.string_star_star));
                    ((TextView) optionAssetItem.findViewById(R.id.asset_total_currency)).setText(mContext.getString(R.string.string_star_star));
                }
                viewList.add(optionAssetItem);
            }
        }

        {
            if (!basicFunctionsConfig.isBonus()||!basicFunctionsConfig.isStaking()) {
                final View coinplusAssetItem = layoutInflater.inflate(R.layout.asset_item_view, null);
                coinplusAssetItem.setTag("CoinPlus");
                coinplusAssetItem.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (mVP != null)
                            mVP.setCurrentItem(viewList.indexOf(coinplusAssetItem));
                    }
                });
                ((TextView) coinplusAssetItem.findViewById(R.id.asset_trade_account)).setText(mContext.getString(R.string.string_asset_coinplus));
                ((TextView) coinplusAssetItem.findViewById(R.id.asset_total_txt)).setText(mContext.getString(R.string.string_asset_coinplus_about));
                String coinplusAsset = NumberUtils.roundFormatDown(RateDataManager.getTokenToBTC(data.unit, data.financeAsset), AppData.Config.DIGIT_DEFAULT_VALUE);
                ((TextView) coinplusAssetItem.findViewById(R.id.asset_total)).setText(coinplusAsset);

                String coinplusAssetCurrency = RateDataManager.getShowLegalMoney(RateDataManager.CurRatePrice(data.unit, data.financeAsset), AppData.DIGIT_LEGAL_MONEY);
                ((TextView) coinplusAssetItem.findViewById(R.id.asset_total_currency)).setText("≈" + /*coinplusAssetBTC + "BTC / " +*/ coinplusAssetCurrency);
                if (mbOpenEye == false) {
                    ((TextView) coinplusAssetItem.findViewById(R.id.asset_total)).setText(mContext.getString(R.string.string_star_star));
                    ((TextView) coinplusAssetItem.findViewById(R.id.asset_total_currency)).setText(mContext.getString(R.string.string_star_star));
                }
                viewList.add(coinplusAssetItem);
            }
        }

        mViewList = viewList;
    }


    @Override
    public Object instantiateItem(ViewGroup container, int position) {
        View symbolItem = layoutInflater.inflate(R.layout.asset_item_view, null);
        symbolItem.getBackground().setColorFilter(SkinColorUtil.getWhite(symbolItem.getContext()), PorterDuff.Mode.MULTIPLY);
        if(position < mViewList.size())
            symbolItem = mViewList.get(position);

        container.addView(symbolItem);
        return symbolItem;// 返回填充的View对象
    }
}
