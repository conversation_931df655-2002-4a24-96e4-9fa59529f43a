/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: LoginActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.view.animation.TranslateAnimation;
import android.view.inputmethod.InputMethodManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import io.bhex.app.R;
import io.bhex.app.account.presenter.LoginPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.safe.DeepKnowVerify;
import io.bhex.app.safe.DeepSEListener;
import io.bhex.app.utils.ActivityCache;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.CustomerServiceUtils;
import io.bhex.app.utils.DialogUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KeyBoardUtil;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.app.view.InputView;
import io.bhex.app.view.ObservableScrollView;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.constant.Fields;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.app.view.alertview.AlertView;
import io.bhex.app.view.alertview.OnItemClickListener;
import io.bhex.sdk.account.LoginResultCarrier;
import io.bhex.sdk.account.bean.MobileCodeListBean;
import io.bhex.sdk.data_manager.MMKVManager;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.trade.bean.TwoVerifyBean;


public class LoginActivity extends BaseActivity<LoginPresenter, LoginPresenter.LoginUI> implements LoginPresenter.LoginUI, View.OnClickListener {
    private static final String TAG = "LoginActivity";
    private boolean DEFULAT_ACCOUNT_PASSWD_LOGIN = true;//默认账号密码登录
    private InputView pwdEdt;
    private TopBar topBar;
    private LinearLayout mobileCodeLinear;
    private InputView accountEdit;
    private TextView mobileCode;
    private String currentToken="";
    private ObservableScrollView scrollView;
    private TextView slideTitle;
    private DeepKnowVerify deepKnowVerify;
    private InputView verifyCodeEdt;
    private TextView sendVerifyCodeTv;
    private View verifyCodeRela;
    private int registerOption;//注册入口配置
    private LoginResultCarrier callback;

    @Override
    protected int getContentView() {
        return R.layout.activity_login_layout;
    }

    @Override
    protected LoginPresenter createPresenter() {
        return new LoginPresenter();
    }

    @Override
    protected LoginPresenter.LoginUI getUI() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //设置禁止系统截屏、录制
//        getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
    }

    @SuppressLint("StringFormatInvalid")
    @Override
    protected void initView() {
        super.initView();
        deepKnowVerify = DeepKnowVerify.getInstance(this);
        registerOption = AppConfigManager.GetInstance().getRegisterOption();

        Intent intent = getIntent();
        if (intent != null) {
            callback = (LoginResultCarrier)intent.getParcelableExtra(AppData.INTENT.LOGIN_CALLBACK);
        }
        topBar = viewFinder.find(R.id.topBar);
        topBar.setRightTextAppearance(this,R.style.Body_Blue_Bold);
        topBar.setLeftOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                KeyBoardUtil.closeKeybord(accountEdit.getEditText(), LoginActivity.this);
                if (callback==null) {
                    //ActivityCache.getInstance().finishActivity();
                    //IntentUtils.goMain(LoginActivity.this);
                    finish();
                }else {
                    callback.onLoginFailed();
                    finish();
                }
            }
        });
        topBar.setRightOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //快速注册
                IntentUtils.goRegister(LoginActivity.this,Fields.REQUEST_CODE_REGISTER_FROM_LOGIN,callback);
            }
        });

        //默认邮箱注册
        mobileCodeLinear = viewFinder.find(R.id.mobile_code_linear);
        mobileCode = viewFinder.textView(R.id.mobile_code);
        accountEdit = viewFinder.find(R.id.account_input);
        pwdEdt = viewFinder.find(R.id.login_pwd_input);
        pwdEdt.setInputMode(InputView.PWDMODE);

        verifyCodeRela = viewFinder.find(R.id.verifyCodeRela);
        verifyCodeEdt = viewFinder.find(R.id.verify_code_et);
        verifyCodeEdt.setPaddingRight(PixelUtils.dp2px(80));
        sendVerifyCodeTv = viewFinder.textView(R.id.get_verify_code);
        verifyCodeEdt.setInputHint(getString(R.string.string_input_mobile_verify_code));

        mobileCodeLinear.setVisibility(View.VISIBLE);
        accountEdit.setPaddingLeft(PixelUtils.dp2px(68));
        setDefaultMobileCode();

        //根据本地记录，设置是邮箱登录还是手机登录，并回填账号  默认邮箱登录
//        DEFULAT_ACCOUNT_PASSWD_LOGIN = SPEx.get(AppData.SPKEY.USER_ACCOUNT_MODE_KEY, DEFULAT_ACCOUNT_PASSWD_LOGIN);
//        switchLoginWay(!DEFULAT_ACCOUNT_PASSWD_LOGIN);

        //TODO 测试验收
//        if (registerOption == AppData.REGISTEROPTION.ONLY_EMAIL) {
//            //如果设置了仅邮箱登录，屏蔽手机验证码快捷登录方式 ********
//            viewFinder.find(R.id.tab_layout).setVisibility(View.GONE);
//            DEFULAT_ACCOUNT_PASSWD_LOGIN = true;
//        }else{
//            viewFinder.find(R.id.tab_layout).setVisibility(View.VISIBLE);
//        }

        setTab(DEFULAT_ACCOUNT_PASSWD_LOGIN);

        if (!DEFULAT_ACCOUNT_PASSWD_LOGIN) {
            //切换到手机快捷登录
            DEFULAT_ACCOUNT_PASSWD_LOGIN = false;
            accountEdit.setInputHint(getString(R.string.input_phone_number));
            accountEdit.setPaddingLeft(PixelUtils.dp2px(68));
            mobileCodeLinear.setVisibility(View.VISIBLE);
            pwdEdt.setVisibility(View.GONE);
            verifyCodeRela.setVisibility(View.VISIBLE);

        } else {
            //切换到账号密码登录
            DEFULAT_ACCOUNT_PASSWD_LOGIN = true;
            accountEdit.setInputHint(getString(R.string.string_hint_input_mobile_or_email));
            accountEdit.setPaddingLeft(PixelUtils.dp2px(8));
            mobileCodeLinear.setVisibility(View.GONE);
            pwdEdt.setVisibility(View.VISIBLE);
            verifyCodeRela.setVisibility(View.GONE);
        }

        //填充账号
        getPresenter().setLoginAccount(!DEFULAT_ACCOUNT_PASSWD_LOGIN,accountEdit);
//        accountEdit.setInputString(SPEx.get(AppData.SPKEY.USER_ACCOUNT_KEY+DEFULAT_ACCOUNT_PASSWD_LOGIN, ""));

        slideTitle = viewFinder.textView(R.id.slideTilte);
        scrollView = viewFinder.find(R.id.scrollView);

    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (intent != null) {
            callback = (LoginResultCarrier)intent.getParcelableExtra(AppData.INTENT.LOGIN_CALLBACK);
        }
    }
    private void setDefaultMobileCode() {
        String defaultMobileCode = CommonUtil.getDefaultMobileCode(this);
        if (!TextUtils.isEmpty(defaultMobileCode)) {
            mobileCode.setText("+"+defaultMobileCode);
        }
    }

    private void switchLoginWay(boolean isCurrentEmail) {
        getPresenter().switchSignupWay(isCurrentEmail);

        accountEdit.setInputString("");
        pwdEdt.setInputString("");

        accountEdit.setError("");
        pwdEdt.setError("");
        //快速登录
        if (isCurrentEmail) {
            //切换到手机登录
            DEFULAT_ACCOUNT_PASSWD_LOGIN = false;
//            topBar.setRightText(getString(R.string.string_email_login));
            accountEdit.setInputHint(getString(R.string.input_phone_number));
            accountEdit.setPaddingLeft(PixelUtils.dp2px(68));
            mobileCodeLinear.setVisibility(View.VISIBLE);
            pwdEdt.setVisibility(View.GONE);
            verifyCodeRela.setVisibility(View.VISIBLE);

        } else {
            //切换到邮箱登录
            DEFULAT_ACCOUNT_PASSWD_LOGIN = true;
//            topBar.setRightText(getString(R.string.string_mobile_login));
            accountEdit.setInputHint(getString(R.string.string_hint_input_mobile_or_email));
            accountEdit.setPaddingLeft(PixelUtils.dp2px(8));
            mobileCodeLinear.setVisibility(View.GONE);
            pwdEdt.setVisibility(View.VISIBLE);
            verifyCodeRela.setVisibility(View.GONE);

        }
        getPresenter().setLoginAccount(isCurrentEmail,accountEdit);
//        accountEdit.setInputString(SPEx.get(AppData.SPKEY.USER_ACCOUNT_KEY+!isCurrentEmail, ""));

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.mobile_code_linear).setOnClickListener(this);
        viewFinder.find(R.id.btn_login).setOnClickListener(this);
        viewFinder.find(R.id.get_verify_code).setOnClickListener(this);
        deepKnowVerify.ignoreDPView(viewFinder.find(R.id.btn_login),"LoginActivity");
        viewFinder.find(R.id.btn_forget_pwd).setOnClickListener(this);

        accountEdit.addTextWatch(mTextWatcher);
        pwdEdt.addTextWatch(mTextWatcher);
        verifyCodeEdt.addTextWatch(mTextWatcher);

        viewFinder.find(R.id.tab_a_rela).setOnClickListener(this);
        viewFinder.find(R.id.tab_b_rela).setOnClickListener(this);
//        scrollView.setScrollViewListener(new ObservableScrollView.ScrollViewListener() {
//            @Override
//            public void onScrollChanged(ObservableScrollView scrollView, int x, int y, int oldx, int oldy) {
////                slideTitle.scrollBy(oldy-y,y-oldy);
//                pingYiAmin(slideTitle,(y-oldy)/2,-(y-oldy));
//            }
//        });
    }

    private void pingYiAmin(View view,float xDelta,float yDelta) {
        float x = view.getX();
        float y = view.getY();
        TranslateAnimation animation;
        animation = new TranslateAnimation(x, x+xDelta, y,
                y+yDelta);
//        animation = new TranslateAnimation(0, 180, Animation.RELATIVE_TO_SELF,
//                Animation.RELATIVE_TO_SELF);
        animation.setDuration(50);
        animation.setFillAfter(true);
        view.startAnimation(animation);
    }

    public void updateConfig() {
        registerOption = AppConfigManager.GetInstance().getRegisterOption();
        if (registerOption==AppData.REGISTEROPTION.ONLY_EMAIL) {
            viewFinder.find(R.id.tab_layout).setVisibility(View.GONE);
            viewFinder.find(R.id.mobile_code).setPadding(0,0,0,0);
            //邮箱登录
            setTab(true);
            switchLoginWay(false);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        updateConfig();
    }

    @Override
    public void needPasswdAndGoVerifyPasswd(final String requestId) {
        DialogUtils.showLoginPasswdDialog(this, getString(R.string.string_login_passwd), "", "", true, new DialogUtils.OnLoginListener() {
            @Override
            public void onConfirm(AlertDialog dialog, String passwd) {
                getPresenter().requestQuickLoginComfirm(dialog,requestId,passwd);
            }

            @Override
            public void onCancel() {

            }

            @Override
            public void forgetPasswd() {
                showForgetPwdActions();
            }
        });
    }

    @Override
    public void loginSuccess() {
        CustomerServiceUtils.setZendeskIdentify(this);
        if (callback==null) {
            ActivityCache.getInstance().removeActivityFromCacheMap(this);
            ActivityCache.getInstance().finishActivity();
            IntentUtils.goMain(this);
            finish();
        }else {
            setResult(RESULT_OK);
            callback.onLoginSucceed();
            finish();
        }
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        if (keyCode== KeyEvent.KEYCODE_BACK) {
            KeyBoardUtil.closeKeybord(accountEdit.getEditText(),this);
            if (callback==null) {
                finish();
            }else {
                callback.onLoginFailed();
                finish();
            }
            return false;
        }else{
            return super.onKeyUp(keyCode, event);
        }

    }

    @Override
    public void loginFailed(String reason) {

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.mobile_code_linear:
                //选择国家手机区号
                IntentUtils.goMobileCodeList(this, Fields.REQUEST_CODE_LOGIN, Fields.FIELD_COUNTRY_PARAM_TYPE_AREA_CODE);
                break;
//            //快速注册
//            case R.id.btn_go_register:
//                IntentUtils.goRegister(this,caller);
//                break;
            //忘记密码
            case R.id.btn_forget_pwd:
                KeyBoardUtil.closeKeybord(accountEdit.getEditText(),this);
                showForgetPwdActions();
                break;
            //登录
            case R.id.btn_login:
//                WebActivity.runActivity(this,getString(R.string.string_check_is_people),"file:///android_asset/recaptcha.html");
//                WebActivity.runActivityForResult(this,getString(R.string.string_check_is_people), Urls.H5_URL_RECAPTCHA,VERIFY_REQUEST_CODE);

//                getPresenter().loginVerify(DEFULAT_ACCOUNT_PASSWD_LOGIN,mobileCode.getText().toString().replace("+",""),accountEdit,pwdEdt,currentToken);
                //其中的第一个参数为具体的所要填充的内容,第二个参数为回调。

                if (!NetWorkStatus.isConnected(this)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.hint_network_not_connect));
                    return;
                }
//                getUI().showProgressDialog("","");
//                deepKnowVerify.verify(baseSEListener);

//                BhexRecaptcha.getInstance(this).verifyWithRecaptcha(new BhexRecaptcha.VerifyCallBack() {
//                    @Override
//                    public void onSuccess(String token) {
//                        getPresenter().login(DEFULAT_ACCOUNT_PASSWD_LOGIN,mobileCode.getText().toString().replace("+",""),accountEdit,pwdEdt,token);
//                    }
//
//                    @Override
//                    public void onFailure(@NonNull Exception e) {
//
//                    }
//                });

                final String account = accountEdit.getInputString();
                if (TextUtils.isEmpty(account)) {
                    ToastUtils.showShort(this, DEFULAT_ACCOUNT_PASSWD_LOGIN ? getResources().getString(R.string.string_hint_input_mobile_or_email):getResources().getString(R.string.input_phone_number));
                    return;
                } else {
                    accountEdit.setError("");
                }

                if (DEFULAT_ACCOUNT_PASSWD_LOGIN) {
                    //账号密码登录
                    String passwd = pwdEdt.getInputString();
                    if (TextUtils.isEmpty(passwd)) {
                        ToastUtils.showShort(this, getResources().getString(R.string.input_pwd));
                        return;
                    }
                    getUI().showProgressDialog("","");
                    deepKnowVerify.verify(baseSEListener);
                }else{
                    //快速登录
                    String verifyCode = verifyCodeEdt.getInputString();
                    if (TextUtils.isEmpty(verifyCode)) {
                        ToastUtils.showShort(this, getResources().getString(R.string.input_verify));
                        return;
                    }
                    getPresenter().requestQuickLogin(mobileCode,account,verifyCode);
                }
                getPresenter().saveLoginAccount(!DEFULAT_ACCOUNT_PASSWD_LOGIN,accountEdit.getInputString());
                break;

            case R.id.tab_a_rela:
                //邮箱登录
                setTab(true);
                switchLoginWay(false);
                break;
            case R.id.tab_b_rela:
                //手机登录
                setTab(false);
                switchLoginWay(true);
                break;
            //获取验证码
            case R.id.get_verify_code:

                String mobile = accountEdit.getInputString();
                if (TextUtils.isEmpty(mobile)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.input_phone_number));
                    return;
                } else {
                    accountEdit.setError("");
                }
//                WebActivity.runActivityForResult(this, getString(R.string.string_check_is_people), Urls.H5_URL_RECAPTCHA, VERIFY_REQUEST_CODE);
                if (!NetWorkStatus.isConnected(this)) {
                    ToastUtils.showShort(this,getResources().getString(R.string.hint_network_not_connect));
                    return;
                }
                getUI().showProgressDialog("","");
                deepKnowVerify.verify(verifyCodeSEListener);
                break;
        }
    }

    private void showForgetPwdActions() {
        String[] moreActions = {getString(R.string.string_find_pwd_by_mobile), getString(R.string.string_find_pwd_by_email)};
        AlertView moreAlertView = new AlertView(null, null, getString(R.string.string_cancel), null, moreActions, this, AlertView.Style.ActionSheet, new OnItemClickListener() {
            @Override
            public void onItemClick(Object o, int position) {
                if (position == -1) {
                    return;
                }
                if (position == 0) {
                    //通过注册手机号码找回
                    IntentUtils.goForgetPwd(LoginActivity.this,false);
                } else {
                    //通过注册邮箱账号找回
                    IntentUtils.goForgetPwd(LoginActivity.this,true);
                }
            }
        });

        moreAlertView.show();
    }

    private DeepSEListener baseSEListener = new DeepSEListener() {

        /**
         * SDK内部show loading dialog
         */
        @Override
        public void onShowDialog() {
            DebugLog.i(TAG,"onShowDialog-->SDK show loading dialog！");
            getUI().dismissProgressDialog();
        }

        @Override
        public void onError(String errorCode, String error) {
            DebugLog.i(TAG,"onError-->errorCode:"+errorCode+", error: "+error);
//            ToastUtils.showShort(error);
            getUI().dismissProgressDialog();
            ToastUtils.showShort(getString(R.string.string_net_exception)+errorCode);
        }

        /**
         * 验证码Dialog关闭
         * 1：webview的叉按钮关闭
         * 2：点击屏幕外关闭
         * 3：点击回退键关闭
         *
         * @param num
         */
        @Override
        public void onCloseDialog(int num) {
            DebugLog.i(TAG, "onCloseDialog-->" + num);
        }

        /**
         * show 验证码webview
         */
        @Override
        public void onDialogReady() {
            DebugLog.i(TAG,"onDialogReady-->SDK show captcha webview dialog! ");
            //RateAndLocalManager.GetInstance(LoginActivity.this).SetCurLocalKind(RateAndLocalManager.GetInstance(LoginActivity.this).getCurLocalKind());

        }

        /**
         * 验证成功
         * @param token
         */
        @Override
        public void onResult(String token) {
            DebugLog.i(TAG,"onResult: "+token);
            getUI().dismissProgressDialog();
            //去二次验证
            getPresenter().loginVerify(DEFULAT_ACCOUNT_PASSWD_LOGIN,mobileCode.getText().toString().replace("+",""),accountEdit,pwdEdt,token);
        }
    };

    /**
     * 快速登录-验证码-极验
     */
    private DeepSEListener verifyCodeSEListener = new DeepSEListener() {

        /**
         * SDK内部show loading dialog
         */
        @Override
        public void onShowDialog() {
            DebugLog.i(TAG,"onShowDialog-->SDK show loading dialog！");
            getUI().dismissProgressDialog();
        }

        @Override
        public void onError(String errorCode, String error) {
            DebugLog.i(TAG,"onError-->errorCode:"+errorCode+", error: "+error);
//            ToastUtils.showShort(error);
            getUI().dismissProgressDialog();
            ToastUtils.showShort(getString(R.string.string_net_exception)+errorCode);
        }

        /**
         * 验证码Dialog关闭
         * 1：webview的叉按钮关闭
         * 2：点击屏幕外关闭
         * 3：点击回退键关闭
         *
         * @param num
         */
        @Override
        public void onCloseDialog(int num) {
            DebugLog.i(TAG, "onCloseDialog-->" + num);
        }

        /**
         * show 验证码webview
         */
        @Override
        public void onDialogReady() {
            DebugLog.i(TAG,"onDialogReady-->SDK show captcha webview dialog! ");
            //RateAndLocalManager.GetInstance(LoginActivity.this).SetCurLocalKind(RateAndLocalManager.GetInstance(LoginActivity.this).getCurLocalKind());

        }

        /**
         * 验证成功
         * @param token
         */
        @Override
        public void onResult(String token) {
            DebugLog.i(TAG,"onResult: "+token);
            getUI().dismissProgressDialog();
            //TODO 发送验证码
            getPresenter().quickLoginVerify(mobileCode,accountEdit,token);
        }
    };

    @Override
    public void setAuthTv(String s) {
        sendVerifyCodeTv.setText(s);
    }

    @Override
    public void setAuthTvStatus(boolean b) {
        sendVerifyCodeTv.setEnabled(b);
        sendVerifyCodeTv.setTextColor(b ? getResources().getColor(R.color.blue) : SkinColorUtil.getDark50(this));
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        deepKnowVerify.destroy();
    }

    /**
     * 设置tab
     * @param isSetEmailLoginTab
     */
    private void setTab(boolean isSetEmailLoginTab) {
        if (isSetEmailLoginTab) {
            viewFinder.find(R.id.tab_a_indicator).setVisibility(View.VISIBLE);
            viewFinder.find(R.id.tab_b_indicator).setVisibility(View.GONE);
            viewFinder.textView(R.id.tab_a_name).setTextColor(getResources().getColor(R.color.blue));
            viewFinder.textView(R.id.tab_b_name).setTextColor(getResources().getColor(CommonUtil.isBlackMode()?R.color.dark_night:R.color.dark));
        }else{
            viewFinder.find(R.id.tab_a_indicator).setVisibility(View.GONE);
            viewFinder.find(R.id.tab_b_indicator).setVisibility(View.VISIBLE);
            viewFinder.textView(R.id.tab_a_name).setTextColor(getResources().getColor(CommonUtil.isBlackMode()?R.color.dark_night:R.color.dark));
            viewFinder.textView(R.id.tab_b_name).setTextColor(getResources().getColor(R.color.blue));
        }
    }

    @Override
    public void hideKeyboard() {
        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(accountEdit.getWindowToken(), 0);

    }

    @Override
    public void setAccount(String accountName) {
        accountEdit.setInputString(accountName);
    }

    @Override
    public String getMobileCode() {
        return viewFinder.textView(R.id.mobile_code).getText().toString();
    }

    @Override
    public int getTwoVerifyRequestCode() {
        return Fields.REQUEST_CODE_TWO_VERIFY;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Fields.REQUEST_CODE_LOGIN && resultCode == RESULT_OK) {
            MobileCodeListBean.MobileCodeBean mobileCodeBean = (MobileCodeListBean.MobileCodeBean) data.getSerializableExtra(Fields.INTENT_MOBILE_CODE);
            if (mobileCodeBean != null) {
                String countryCode = mobileCodeBean.getNationalCode();
                if (!TextUtils.isEmpty(countryCode)) {
                    mobileCode.setText("+" + countryCode);
                }
            }
        }else if(requestCode == Fields.REQUEST_CODE_VERIFY && resultCode == RESULT_OK){
            if (data != null) {
                currentToken = data.getStringExtra("token");
                //去二次验证
                getPresenter().loginVerify(DEFULAT_ACCOUNT_PASSWD_LOGIN,mobileCode.getText().toString().replace("+",""),accountEdit,pwdEdt,currentToken);
            }
        }else if(requestCode == Fields.REQUEST_CODE_TWO_VERIFY && resultCode == RESULT_OK){
            if (data != null) {
                TwoVerifyBean twoVerifyBean = (TwoVerifyBean)data.getSerializableExtra("twoVerify");
                //去登录
                getPresenter().login(DEFULAT_ACCOUNT_PASSWD_LOGIN,mobileCode.getText().toString().replace("+",""),accountEdit,pwdEdt,currentToken,twoVerifyBean.getRequest_id(),twoVerifyBean.getAuth_type(),twoVerifyBean.getOrder_id(),twoVerifyBean.getVerify_code());
            }
        }else if(requestCode == Fields.REQUEST_CODE_REGISTER_FROM_LOGIN && resultCode == RESULT_OK){
            //注册成功(会自动登录)-返回-代表自动登录成功
            loginSuccess();
        }
    }

    /**
     * 编辑框监听器
     */
    private TextWatcher mTextWatcher = new TextWatcher() {

        /** 改变前*/
        @Override
        public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
        }

        /** 内容改变*/
        @Override
        public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            if (DEFULAT_ACCOUNT_PASSWD_LOGIN) {
                //账号密码登录 输入框校验-账号和密码
                if(getPresenter().checkInputContentLegality(DEFULAT_ACCOUNT_PASSWD_LOGIN, mobileCode.getText().toString().replace("+", ""),accountEdit, pwdEdt)){
                    viewFinder.find(R.id.btn_login).setEnabled(true);
                }else{
                    viewFinder.find(R.id.btn_login).setEnabled(false);
                }
            }else{
                //快速登录 输入框校验-账号和验证码
                if(getPresenter().checkQuickLoginInputContentLegality(DEFULAT_ACCOUNT_PASSWD_LOGIN, mobileCode.getText().toString().replace("+", ""),accountEdit, verifyCodeEdt)){
                    viewFinder.find(R.id.btn_login).setEnabled(true);
                }else{
                    viewFinder.find(R.id.btn_login).setEnabled(false);
                }
            }

        }

        @Override
        public void afterTextChanged(Editable s) {

        }

    };
}
