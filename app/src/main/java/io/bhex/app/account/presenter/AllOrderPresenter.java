/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AllOrderPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;

public class AllOrderPresenter extends BasePresenter<AllOrderPresenter.AllOrderUI> {
    public interface AllOrderUI extends AppUI{

    }

    @Override
    public void onUIReady(BaseCoreActivity activity, AllOrderUI ui) {
        super.onUIReady(activity, ui);
    }

}
