/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AssetTransferActivity.java
 *   @Date: 1/22/19 5:50 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.account.ui;

import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;

import io.bhex.app.R;
import io.bhex.app.account.presenter.AssetTransferPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.BasicFunctionsUtil;
import io.bhex.app.utils.DialogUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.view.ShadowDrawable;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.app.view.alertview.AlertView;
import io.bhex.app.view.alertview.OnDismissListener;
import io.bhex.app.view.alertview.OnItemClickListener;
import io.bhex.sdk.account.AccountInfoApi;
import io.bhex.sdk.account.bean.SubAccountBean;
import io.bhex.sdk.account.bean.SubAccountListResponse;
import io.bhex.sdk.config.bean.BasicFunctionsConfig;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.enums.ACCOUNT_TYPE;
import io.bhex.sdk.quote.bean.QuoteTokensBean;
import io.bhex.sdk.trade.bean.CanTransferAssetResponse;
import io.bhex.sdk.trade.bean.TransferAssetRequest;
import io.bhex.sdk.trade.margin.MarginApi;
import io.bhex.sdk.trade.margin.bean.MarginAvailWithdrawResponse;
import io.bhex.sdk.trade.margin.bean.MarginTokenConfigResponse;

public class AssetTransferActivity extends BaseActivity<AssetTransferPresenter, AssetTransferPresenter.AssetTransferUI> implements AssetTransferPresenter.AssetTransferUI {

    private TextView selectTokenname;
    private TextView transferAmountTokenUnit;
    private TextView asset_transfer_from;
    private TextView asset_transfer_to;
    private TextView asset_available;
    private EditText token_withdraw_amount_et;
    private List<String> selectTokenList = new ArrayList<>();
    private String currentToken = "";
    private SubAccountBean sourceAssetType;//转出账户
    private SubAccountBean targetAssetType;//接收到账账户
    private SubAccountBean walletAccount;   //钱包主账户
    private SubAccountBean contractAccount; //合约主账号
    private ImageView transferImageFrom;
    private ImageView transferImageTo;
    private boolean isClickFromAccount = false;
    private String tokenIdFrom = "";
    private List<MarginTokenConfigResponse.MarginToken> marginTokens;
    private int defaultTargetAccountType = -1;
    private int defaultSourceAccountType = -1;

    @Override
    protected int getContentView() {
        return R.layout.activity_asset_transfer;
    }

    @Override
    protected AssetTransferPresenter createPresenter() {
        return new AssetTransferPresenter();
    }

    @Override
    protected AssetTransferPresenter.AssetTransferUI getUI() {
        return this;
    }

    //钱包类型Map
    LinkedHashMap<String, SubAccountBean> accountsMap = new LinkedHashMap<>();
    ArrayList<SubAccountBean> assetTypeList = new ArrayList<>();
    ArrayList<String> assetNameList = new ArrayList<>();

    @Override
    protected void initView() {
        super.initView();

        Intent intent = getIntent();
        if (intent != null) {
            tokenIdFrom = intent.getStringExtra("tokenId");
            defaultTargetAccountType = intent.getIntExtra("targetAccountType",-1);  // 默认不选中，如果不为-1，则不判断是不是包含币种
            defaultSourceAccountType = intent.getIntExtra("sourceAccountType",-1);
            if (defaultTargetAccountType!=ACCOUNT_TYPE.ASSET_WALLET.getType()&&defaultSourceAccountType!=ACCOUNT_TYPE.ASSET_WALLET.getType()) {
                if (defaultSourceAccountType != -1 && defaultTargetAccountType != -1) {
                    defaultSourceAccountType = -1;
                } else if (defaultSourceAccountType!=-1) {
                    defaultTargetAccountType = -1;
                } else {
                    defaultSourceAccountType = -1;
                }
            }
            if (!TextUtils.isEmpty(tokenIdFrom)) {
                currentToken = tokenIdFrom;
            }
        }

        asset_transfer_from = viewFinder.find(R.id.asset_transfer_from);
        asset_transfer_to = viewFinder.find(R.id.asset_transfer_to);

        selectTokenname = viewFinder.find(R.id.item_asset_coin_name);
        transferAmountTokenUnit = viewFinder.find(R.id.token_withdraw_amount_unit);

        ShadowDrawable.setShadow(viewFinder.find(R.id.asset_transfer_rela));
        ShadowDrawable.setShadow(viewFinder.find(R.id.asset_transfer_amount_rela));

        asset_available = viewFinder.find(R.id.asset_available);
        token_withdraw_amount_et = viewFinder.find(R.id.token_withdraw_amount_et);

        transferImageFrom = viewFinder.imageView(R.id.transfer_arrow_from);
        transferImageTo = viewFinder.imageView(R.id.transfer_arrow_to);

    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (intent != null) {
            tokenIdFrom = intent.getStringExtra("tokenId");
            defaultTargetAccountType = intent.getIntExtra("targetAccountType",-1);  // 默认不选中，如果不为-1，则不判断是不是包含币种
            defaultSourceAccountType = intent.getIntExtra("sourceAccountType",-1);
            if (defaultTargetAccountType!=ACCOUNT_TYPE.ASSET_WALLET.getType()&&defaultSourceAccountType!=ACCOUNT_TYPE.ASSET_WALLET.getType()) {
                if (defaultSourceAccountType != -1 && defaultTargetAccountType != -1) {
                    defaultSourceAccountType = -1;
                } else if (defaultSourceAccountType!=-1) {
                    defaultTargetAccountType = -1;
                } else {
                    defaultSourceAccountType = -1;
                }
            }
            if (!TextUtils.isEmpty(tokenIdFrom)) {
                currentToken = tokenIdFrom;
            }
        }
        getPresenter().getAccountList();
        getPresenter().getMarginTokens();
    }
    @Override
    public void showAccountList(SubAccountListResponse response) {
        if (response != null) {
            List<SubAccountBean> accountList = response.getArray();
            if (accountList != null) {
                handleCanSupportAccount(accountList);
            }
        }
    }

    @Override
    public void updateMarginTokens(List<MarginTokenConfigResponse.MarginToken> datas) {
        marginTokens =datas;
        if (sourceAssetType!=null && targetAssetType!=null) {
            if (sourceAssetType.getAccountType() == ACCOUNT_TYPE.ASSET_MARGIN.getType() || targetAssetType.getAccountType() == ACCOUNT_TYPE.ASSET_MARGIN.getType()) {
                updateSelectTokenList(false, targetAssetType);
            }
        }
    }

    /**
     * 设置选中的资产类型
     *
     * @param isFromAsset 转出资产
     * @param assetType
     */
    private void setSelectAssetType(boolean isFromAsset, SubAccountBean assetType) {

        if (isFromAsset) {//转出账户
            sourceAssetType = assetType;
            asset_transfer_from.setText(assetType.getAccountName());
            DebugLog.e("TRANSFER", "设置：转出账户" + assetType.getAccountType() + "  " + assetType.getAccountName());
            updateSelectTokenList(isFromAsset, assetType);
        } else {//接收账户
            targetAssetType = assetType;
            asset_transfer_to.setText(assetType.getAccountName());
            DebugLog.e("TRANSFER", "设置：接收账户" + assetType.getAccountType() + "  " + assetType.getAccountName());
            updateSelectTokenList(isFromAsset, targetAssetType);
        }
//        updateSelectAssetType(isFromAsset);
    }

    HashMap<Integer, List<String>> tokenNameListMap = new HashMap<>();
    List<String> bbTokenList = new ArrayList<>();
    List<String> optionCoinToken = new ArrayList<>();
    List<String> futuresCoinToken = new ArrayList<>();
    List<String> marginCoinToken = new ArrayList<>();

    /**
     * 更新作价 TOKEN 列表
     *
     * @param assetType
     */
    private void updateSelectTokenList(boolean isFromAsset, SubAccountBean assetType) {
        selectTokenList.clear();
        tokenNameListMap.clear();
        bbTokenList.clear();
        optionCoinToken.clear();
        futuresCoinToken.clear();
        marginCoinToken.clear();
        if (isFromAsset ? targetAssetType == null : sourceAssetType == null) {
            return;
        }

        List<String> sourceBBCoinList = AppConfigManager.GetInstance().getBBTokensList();
        for (String tokenId : sourceBBCoinList) {
            bbTokenList.add(tokenId);
        }
        List<String> sourceOptionCoinToken = AppConfigManager.GetInstance().getOptionCoinToken();
        for (String tokenId : sourceOptionCoinToken) {
            optionCoinToken.add(tokenId);
        }
        List<String> sourceFuturesCoinToken = AppConfigManager.GetInstance().getFuturesCoinToken();
        for (String tokenId : sourceFuturesCoinToken) {
            futuresCoinToken.add(tokenId);
        }
        if (marginTokens==null) {
            marginTokens = AppConfigManager.GetInstance().getMarginTokens();
        }
        if (marginTokens!=null) {
            // 如果币种不可用则可转出，不可转入
            for (MarginTokenConfigResponse.MarginToken token : marginTokens) {
                if (targetAssetType.getAccountType() == ACCOUNT_TYPE.ASSET_MARGIN.getType()) {
                    if (token.getIsOpen() == 1) {
                        marginCoinToken.add(token.getTokenId());
                    }
                } else {
                    marginCoinToken.add(token.getTokenId());
                }
            }
        }


        tokenNameListMap.put(ACCOUNT_TYPE.ASSET_WALLET.getType(), bbTokenList);
        tokenNameListMap.put(ACCOUNT_TYPE.ASSET_OPTION.getType(), this.optionCoinToken);
        tokenNameListMap.put(ACCOUNT_TYPE.ASSET_FUTURES.getType(), this.futuresCoinToken);
        tokenNameListMap.put(ACCOUNT_TYPE.ASSET_MARGIN.getType(), this.marginCoinToken);

        int targetType = isFromAsset ? targetAssetType.getAccountType() : sourceAssetType.getAccountType();
        List<String> targetTokenList = tokenNameListMap.get(targetType);

        if (assetType.getAccountType() == ACCOUNT_TYPE.ASSET_WALLET.getType()) {
            //币币
            if (bbTokenList != null) {
                bbTokenList.retainAll(targetTokenList);
                selectTokenList.addAll(bbTokenList);
            }

        } else if (assetType.getAccountType() == ACCOUNT_TYPE.ASSET_OPTION.getType()) {
            //期权
            if (this.optionCoinToken != null) {
                this.optionCoinToken.retainAll(targetTokenList);
                selectTokenList.addAll(this.optionCoinToken);
            }
        } else if (assetType.getAccountType() == ACCOUNT_TYPE.ASSET_FUTURES.getType()) {
            //合约
            if (this.futuresCoinToken != null) {
                this.futuresCoinToken.retainAll(targetTokenList);
                selectTokenList.addAll(this.futuresCoinToken);
            }
        } else if (assetType.getAccountType() == ACCOUNT_TYPE.ASSET_MARGIN.getType()) {
            //合约
            if (this.marginCoinToken != null) {
                this.marginCoinToken.retainAll(targetTokenList);
                selectTokenList.addAll(this.marginCoinToken);
            }
        }

        //默认选择第一个token
        if (selectTokenList.size() > 0) {
            if (!TextUtils.isEmpty(currentToken) && selectTokenList.contains(currentToken)) {
                setSelectToken(sourceAssetType, currentToken);
            } else {
                String token = selectTokenList.get(0);
                setSelectToken(sourceAssetType, token);
            }
        } else {
            //没有可默认选择的token
            setSelectToken(sourceAssetType, "");
        }

    }

    /**
     * 设置选择的TOKEN
     *
     * @param accountBean
     * @param selectToken
     */
    private void setSelectToken(SubAccountBean accountBean, String selectToken) {
        currentToken = selectToken;
        if (TextUtils.isEmpty(currentToken)) {
            selectTokenname.setText(getString(R.string.string_placeholder));
            transferAmountTokenUnit.setText(getString(R.string.string_placeholder));
        }else{
            QuoteTokensBean.TokenItem tokenItem = AppConfigManager.GetInstance().getToken(currentToken);
            String tokenName = tokenItem.getTokenName();
            selectTokenname.setText(!TextUtils.isEmpty(tokenName) ? tokenName : currentToken);
            transferAmountTokenUnit.setText(!TextUtils.isEmpty(tokenName) ? tokenName : currentToken);
        }

        clearOldData();
        updateAsset(accountBean, selectToken);
    }

    /**
     * 更新资产
     *
     * @param accountBean
     * @param selectToken
     */
    private void updateAsset(SubAccountBean accountBean, String selectToken) {
        getAsset(accountBean, selectToken);
    }

    /**
     * 清理输入框的划转数量和之前token的可用余额
     */
    private void clearOldData() {
        token_withdraw_amount_et.setText("");
        asset_available.setText(getString(R.string.string_can_transfer) + " " + getString(R.string.string_placeholder) + currentToken);
    }

    /**
     * 初始化资产类型
     *
     * @param accountList
     */
    private void handleCanSupportAccount(List<SubAccountBean> accountList) {
        BasicFunctionsConfig basicFunctionsConfig = BasicFunctionsUtil.getBasicFunctionsConfig();

        if (accountList != null) {
            for (SubAccountBean accountBean : accountList) {
                if (accountBean.getAccountType() == ACCOUNT_TYPE.ASSET_WALLET.getType()) {
                    //币币账户类型
                    //钱包默认账户，不能隐藏
                    accountBean.setRequestParam(ACCOUNT_TYPE.ASSET_WALLET.getRequestParam());
                    if (accountBean.getAccountIndex() == 0) {//主账户
                        accountBean.setAccountName(getString(R.string.string_asset_bb));
                        walletAccount = accountBean;
                    }
                    accountsMap.put(accountBean.getAccountId(), accountBean);
                } else if (accountBean.getAccountType() == ACCOUNT_TYPE.ASSET_FUTURES.getType()) {
                    //合约账户类型
                    if (!basicFunctionsConfig.isFuture()) {
                        //合约
                        if (accountBean.getAccountIndex() == 0) {//主账户
                            accountBean.setAccountName(getString(R.string.string_asset_futures));
                            contractAccount = accountBean;
                        }
                        accountBean.setRequestParam(ACCOUNT_TYPE.ASSET_FUTURES.getRequestParam());
                        accountsMap.put(accountBean.getAccountId(), accountBean);
                    }
                } else if (accountBean.getAccountType() == ACCOUNT_TYPE.ASSET_OPTION.getType()) {
                    //期权账户类型
                    if (!basicFunctionsConfig.isOption()) {
                        //期权
                        if (accountBean.getAccountIndex() == 0) {//主账户
                            accountBean.setAccountName(getString(R.string.string_asset_option));
                        }
                        accountBean.setRequestParam(ACCOUNT_TYPE.ASSET_OPTION.getRequestParam());
                        accountsMap.put(accountBean.getAccountId(), accountBean);
                    }
                } else if (accountBean.getAccountType() == ACCOUNT_TYPE.ASSET_MARGIN.getType()) {
                    //杠杆账户类型
                    if (!basicFunctionsConfig.isMargin()) {
                        //杠杆
                        if (accountBean.getAccountIndex() == 0) {//主账户
                            accountBean.setAccountName(getString(R.string.string_asset_margin));
                        }
                        accountBean.setRequestParam(ACCOUNT_TYPE.ASSET_MARGIN.getRequestParam());
                        accountsMap.put(accountBean.getAccountId(), accountBean);
                    }
                }


            }
        }

        loadCanTransferAccountList();
    }

    /**
     * 加载资产类型到集合
     */
    private void loadCanTransferAccountList() {
        if (assetTypeList != null && assetTypeList.size() > 0) {
            assetTypeList.clear();
        }
        if (assetNameList != null && assetNameList.size() > 0) {
            assetNameList.clear();
        }
        if (accountsMap.size() > 0) {
            Set<String> keys = accountsMap.keySet();
            for (String key : keys) {
                SubAccountBean accountBean = accountsMap.get(key);
                if (walletAccount != null) {
                    if (!accountBean.getAccountId().equals(walletAccount.getAccountId())) {
                        // 排除：钱包主账户
                        assetTypeList.add(accountBean);
                        assetNameList.add(accountBean.getAccountName());
                    }
                }

            }
        }

        checkIsCanTransfer();

        setDefaultTranferAccount();
    }

    private void checkIsCanTransfer() {
        if (accountsMap.size() < 2) {
            DialogUtils.showDialogOneBtn(this, getString(R.string.string_reminder), getString(R.string.string_not_support_transfer_accounts), getString(R.string.string_i_know), false, new DialogUtils.OnButtonEventListener() {
                @Override
                public void onConfirm() {
                    AssetTransferActivity.this.finish();
                }

                @Override
                public void onCancel() {

                }
            });
        }
    }

    /**
     * 设置首次默认的转账账户
     */
    private void setDefaultTranferAccount() {
        if (walletAccount != null) {
            if (defaultSourceAccountType==ACCOUNT_TYPE.ASSET_WALLET.getType()||defaultSourceAccountType==-1) {
                //设置转出账户 - 默认钱包主账户转出
                setSelectAssetType(true, walletAccount);
                boolean isHasDefaultTargetAccount = false;
                if (contractAccount != null && defaultTargetAccountType==-1) {
                    ////设置接收账户 - 默认优先级，优先默认合约主账户，然后默认和转出账号不同
                    setSelectAssetType(false, contractAccount);
                }else{
                    if (assetTypeList.size() > 0) {
                        //设置接收账户 - 默认和转出账号不同
                        for (SubAccountBean assetType : assetTypeList) {
                            if (!assetType.getAccountId().equals(walletAccount.getAccountId())) {
                                if (defaultTargetAccountType==assetType.getAccountType()) {
                                    setSelectAssetType(false, assetType);
                                    isHasDefaultTargetAccount = true;
                                    break;
                                }
                            }
                        }
                        if (!isHasDefaultTargetAccount) {
                            for (SubAccountBean assetType : assetTypeList) {
                                if (!assetType.getAccountId().equals(walletAccount.getAccountId())) {
                                    setSelectAssetType(false, assetType);
                                    break;
                                }
                            }
                        }
                    }
                }
            } else {
                //设置转入账户
                setSelectAssetType(false, walletAccount);

                boolean isHasDefaultSourceAccount = false;
                if (contractAccount != null && defaultSourceAccountType==-1) {
                    ////设置接收账户 - 默认优先级，优先默认合约主账户，然后默认和转出账号不同
                    setSelectAssetType(true, contractAccount);
                }else{
                    if (assetTypeList.size() > 0) {
                        //设置接收账户 - 默认和转出账号不同
                        for (SubAccountBean assetType : assetTypeList) {
                            if (!assetType.getAccountId().equals(walletAccount.getAccountId())) {
                                if (defaultSourceAccountType==assetType.getAccountType()) {
                                    setSelectAssetType(true, assetType);
                                    isHasDefaultSourceAccount = true;
                                    break;
                                }
                            }
                        }
                        if (!isHasDefaultSourceAccount) {
                            for (SubAccountBean assetType : assetTypeList) {
                                if (!assetType.getAccountId().equals(walletAccount.getAccountId())) {
                                    setSelectAssetType(true, assetType);
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }


    }

    /**
     * 检查是否支持当前的token
     *
     * @param accountBean
     */
    private boolean checkSupportCurrentTokenTransfer(SubAccountBean accountBean) {
        if (!TextUtils.isEmpty(currentToken)) {
            List<String> sourceBBCoinList = AppConfigManager.GetInstance().getBBTokensList();
            List<String> sourceOptionCoinToken = AppConfigManager.GetInstance().getOptionCoinToken();
            List<String> sourceFuturesCoinToken = AppConfigManager.GetInstance().getFuturesCoinToken();

            if (accountBean.getAccountType() == ACCOUNT_TYPE.ASSET_WALLET.getType()) {
                return sourceBBCoinList.contains(currentToken);
            } else if (accountBean.getAccountType() == ACCOUNT_TYPE.ASSET_OPTION.getType()) {
                return sourceOptionCoinToken.contains(currentToken);
            } else if (accountBean.getAccountType() == ACCOUNT_TYPE.ASSET_FUTURES.getType()) {
                return sourceFuturesCoinToken.contains(currentToken);
            } else if (accountBean.getAccountType() == ACCOUNT_TYPE.ASSET_MARGIN.getType()) {
                return marginCoinToken!=null&&marginCoinToken.contains(currentToken);
            } else {
                return false;
            }
        } else {
            //无默认token,默认返回支持划转
            return true;
        }
    }

    @Override
    protected void addEvent() {

        //选择TOKEN
        viewFinder.find(R.id.select_token_rela).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showSelectToken();
            }
        });

        //转出账户
        viewFinder.find(R.id.asset_transfer_from).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isClickFromAccount) {
                    showSelectAsset(0);
                }
            }
        });

        //转入账户
        viewFinder.find(R.id.asset_transfer_to).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!isClickFromAccount) {
                    showSelectAsset(1);
                }
            }
        });

        //切换
        viewFinder.find(R.id.asset_transfer_image).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                switchAssetAccount();
            }
        });

        //确认
        viewFinder.find(R.id.btn_sure).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                submitTransfer();
            }
        });
        viewFinder.find(R.id.asset_all).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (sourceAssetType == null || TextUtils.isEmpty(currentToken)) {
                    return;
                }
                String availableAsset = assetMap.get(sourceAssetType.getAccountId() + ":" + currentToken);
                if (!TextUtils.isEmpty(availableAsset)) {
                    String showValue = NumberUtils.roundFormatDown(availableAsset, AppData.Config.DIGIT_DEFAULT_VALUE);
                    token_withdraw_amount_et.setText(showValue);
                } else {
                    token_withdraw_amount_et.setText("");
                }
            }
        });

    }

    /**
     * 切换资产账户
     */
    private void switchAssetAccount() {
        SubAccountBean middleAccount = sourceAssetType;
        sourceAssetType = targetAssetType;
        targetAssetType = middleAccount;
        setSelectAssetType(true, sourceAssetType);
        setSelectAssetType(false, targetAssetType);

        if (walletAccount != null) {

            if (sourceAssetType.getAccountId().equals(walletAccount.getAccountId())) {
                transferImageFrom.setVisibility(View.GONE);
                transferImageTo.setVisibility(View.VISIBLE);
                isClickFromAccount = false;
            }

            if (targetAssetType.getAccountId().equals(walletAccount.getAccountId())) {
                transferImageFrom.setVisibility(View.VISIBLE);
                transferImageTo.setVisibility(View.GONE);
                isClickFromAccount = true;
            }
        }
    }

    /**
     * 资产选择
     *
     * @param left
     */
    private void showSelectAsset(final int left) {
        List<String> selectArr = new ArrayList<>();
        String selectAccount ="";
        if (left == 0) {
            selectAccount = sourceAssetType==null?"":sourceAssetType.getAccountName();
        } else {
            selectAccount = targetAssetType==null?"":targetAssetType.getAccountName();
        }
        selectArr.add(selectAccount);
        AlertView selectPhotoAlert = new AlertView(null, null, getString(R.string.string_cancel), selectArr, assetNameList, this, AlertView.Style.ActionSheet, new OnItemClickListener() {
            @Override
            public void onItemClick(Object o, int position) {
                if (position == -1) {
                    return;
                }

                setSelectAssetType(left == 0, assetTypeList.get(position));
            }
        });
        selectPhotoAlert.setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(Object o) {

            }
        });
        selectPhotoAlert.show();
    }

    /**
     * token选择
     */
    private void showSelectToken() {

        AlertView selectPhotoAlert = new AlertView(getString(R.string.string_select_token), null, getString(R.string.string_cancel), new String[]{currentToken}, selectTokenList.toArray(new String[selectTokenList.size()]), this, AlertView.Style.ActionSheet, new OnItemClickListener() {
            @Override
            public void onItemClick(Object o, int position) {
                if (position == -1) {
                    return;
                }
                if (position < selectTokenList.size()) {
                    currentToken = selectTokenList.get(position);
                    setSelectToken(sourceAssetType, currentToken);
                }
            }
        });
        selectPhotoAlert.show();
    }

    /**
     * 获取 币币资产
     *
     * @param accountBean
     * @param tokenId
     */
    protected void getAsset(final SubAccountBean accountBean, final String tokenId) {
        if (accountBean == null) {
            return;
        }
        // 只限制融币主账户
        if (sourceAssetType.getAccountType() == ACCOUNT_TYPE.ASSET_MARGIN.getType() && accountBean.getAccountIndex()==0) {
            MarginApi.RequestMarginAvailWithdraw(tokenId,  new SimpleResponseListener<MarginAvailWithdrawResponse>() {
                @Override
                public void onBefore() {
                    super.onBefore();
                    getUI().showProgressDialog("", "");
                }

                @Override
                public void onFinish() {
                    super.onFinish();
                    getUI().dismissProgressDialog();
                }

                @Override
                public void onSuccess(MarginAvailWithdrawResponse response) {
                    super.onSuccess(response);
                    setAllAvailableAsset(accountBean, tokenId, "0");
                    if (CodeUtils.isSuccess(response, true)) {
                        String amount = response.getAvailWithdrawAmount();
                        if (!TextUtils.isEmpty(amount)) {
                            setAllAvailableAsset(accountBean, tokenId, amount);
                        }
                    }
                }

                @Override
                public void onError(Throwable error) {
                    super.onError(error);
                }
            });
        } else {
            AccountInfoApi.RequestCanTransferAsset(tokenId, accountBean.getAccountType(), accountBean.getAccountIndex(), new SimpleResponseListener<CanTransferAssetResponse>() {
                @Override
                public void onBefore() {
                    super.onBefore();
                    getUI().showProgressDialog("", "");
                }

                @Override
                public void onFinish() {
                    super.onFinish();
                    getUI().dismissProgressDialog();
                }

                @Override
                public void onSuccess(CanTransferAssetResponse response) {
                    super.onSuccess(response);
                    setAllAvailableAsset(accountBean, tokenId, "0");
                    if (CodeUtils.isSuccess(response, true)) {
                        String amount = response.getAmount();
                        if (!TextUtils.isEmpty(amount)) {
                            setAllAvailableAsset(accountBean, tokenId, amount);
                        }
                    }
                }

                @Override
                public void onError(Throwable error) {
                    super.onError(error);
                }
            });
        }
    }

    private HashMap<String, String> assetMap = new HashMap<>();//存储资产的map

    private void setAllAvailableAsset(SubAccountBean accountBean, String assetToken, String value) {
        if (accountBean != null) {
            assetMap.put(accountBean.getAccountId() + ":" + assetToken, value);
            if (sourceAssetType == null || TextUtils.isEmpty(currentToken) || TextUtils.isEmpty(assetToken)) {
                return;
            }
            if (accountBean.getAccountId().equals(sourceAssetType.getAccountId()) && currentToken.equals(assetToken)) {
                String showValue = NumberUtils.roundFormatDown(value, AppData.Config.DIGIT_DEFAULT_VALUE);
                asset_available.setText(getString(R.string.string_available) + " " + showValue + currentToken);
            }
        }
    }

    private void submitTransfer() {
        if (sourceAssetType == null) {
            ToastUtils.showShort(getString(R.string.string_select_transfer_from_account));
            return;
        }
        if (targetAssetType == null) {
            ToastUtils.showShort(getString(R.string.string_select_transfer_to_account));
            return;
        }
        if (sourceAssetType.getAccountId().equals(targetAssetType.getAccountId())) {
            ToastUtils.showShort(getString(R.string.string_dont_transfer_at_same_account));
            return;
        }
        if (TextUtils.isEmpty(currentToken) || currentToken.equalsIgnoreCase(getString(R.string.string_placeholder))) {
            ToastUtils.showShort(getString(R.string.string_select_transfer_token));
            return;
        }
        String amount = token_withdraw_amount_et.getText().toString().trim();
        if (TextUtils.isEmpty(amount)) {
            ToastUtils.showShort(getString(R.string.string_asset_transfer_input_amount));
            return;
        }
        if (NumberUtils.sub(amount, "0") <= 0) {
            ToastUtils.showShort(getString(R.string.string_asset_transfer_input_amount));
            return;
        }

        TransferAssetRequest request = new TransferAssetRequest();
        request.token_id = currentToken;
        request.quantity = token_withdraw_amount_et.getText().toString().trim();
        request.source_type = sourceAssetType.getRequestParam();
        request.target_type = targetAssetType.getRequestParam();

        AccountInfoApi.subAccountTransfer(currentToken, amount, sourceAssetType.getAccountType(), sourceAssetType.getAccountIndex(), targetAssetType.getAccountType(), targetAssetType.getAccountIndex(), new SimpleResponseListener<ResultResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("", "");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    ToastUtils.showShort(AssetTransferActivity.this, getString(R.string.string_asset_transfer_success));
                    //划转成功，重新刷新一遍数据
                    onBackPressed();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(AssetTransferActivity.this, getResources().getString(R.string.server_error));
            }
        });
    }

}
