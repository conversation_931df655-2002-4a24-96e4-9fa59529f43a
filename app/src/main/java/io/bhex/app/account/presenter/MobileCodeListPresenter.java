/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MobileCodeListPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import android.content.Intent;
import android.text.TextUtils;

import java.util.ArrayList;
import java.util.List;

import io.bhex.baselib.constant.Fields;
import io.bhex.sdk.account.AccountInfoApi;
import io.bhex.sdk.account.bean.MobileCodeListBean;
import io.bhex.app.base.AppUI;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;


public class MobileCodeListPresenter extends BasePresenter<MobileCodeListPresenter.MobileCodeListUI> {

    private List<MobileCodeListBean.MobileCodeBean> mDatas;
    private String reqParamType="";

    public interface MobileCodeListUI extends AppUI{

        void showCodeList(List<MobileCodeListBean.MobileCodeBean> response);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, MobileCodeListUI ui) {
        super.onUIReady(activity, ui);
        Intent intent = getActivity().getIntent();
        reqParamType = intent.getStringExtra("reqParamType");
        getMobileCodeList(reqParamType,"");
    }

    /**
     * 获取国家手机区号列表
     * @param reqParamType
     * @param searchContent
     */
    public void getMobileCodeList(String reqParamType, final String searchContent) {
        AccountInfoApi.RequestGetMobileCodeList(reqParamType,new SimpleResponseListener<MobileCodeListBean>(){
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(MobileCodeListBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    List<MobileCodeListBean.MobileCodeBean> datas = response.getArray();
                    if (datas != null) {
                        getUI().showCodeList(datas);
                        if (!datas.isEmpty()) {
                            mDatas = datas;
                            filterData(mDatas,searchContent);
                        }
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    List<MobileCodeListBean.MobileCodeBean> datasSearchResult = new ArrayList<>();
    /**
     * 搜索
     * @param searchContent
     */
    public void search(String searchContent) {
        if (mDatas != null) {
            filterData(mDatas,searchContent);
        }else{
            getMobileCodeList(reqParamType, searchContent);
        }

    }

    private void filterData(List<MobileCodeListBean.MobileCodeBean> mDatas, String searchContent) {
        datasSearchResult.clear();
        for (MobileCodeListBean.MobileCodeBean mData : mDatas) {
            String nationalCode = mData.getNationalCode();
            String countryName = mData.getCountryName();
            String countryShortName = mData.getShortName();
            String countrySearchName = TextUtils.isEmpty(mData.getIndexName())?"":mData.getIndexName();
            searchContent = searchContent.toUpperCase();
            if (nationalCode.contains(searchContent)||countryName.toUpperCase().contains(searchContent)||countrySearchName.toUpperCase().contains(searchContent)||countryShortName.toUpperCase().contains(searchContent)) {
                datasSearchResult.add(mData);
            }
        }
        getUI().showCodeList(datasSearchResult);
    }
}
