/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: UserDetailActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.Toolbar;

import com.google.android.material.appbar.CollapsingToolbarLayout;

import io.bhex.app.R;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.account.bean.UserVerifyInfo;
import io.bhex.sdk.account.bean.enums.VERIFY_STATUS;
import io.bhex.app.account.presenter.UserDetailPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.DateUtils;



public class UserDetailActivity extends BaseActivity<UserDetailPresenter, UserDetailPresenter.UserDetailUI> implements UserDetailPresenter.UserDetailUI, View.OnClickListener {
    private boolean isShowVerifyUserInfo;

    @Override
    protected int getContentView() {
        return R.layout.activity_user_detail_layout;
    }

    @Override
    protected UserDetailPresenter createPresenter() {
        return new UserDetailPresenter();
    }

    @Override
    protected UserDetailPresenter.UserDetailUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        Toolbar toolbar = findViewById(R.id.toolbar);
        toolbar.setTitleTextColor(SkinColorUtil.getDark(this));
        CollapsingToolbarLayout collapsingToolbarLayout = findViewById(R.id.collapsing_toolbar);
        // 硬编码黑白版Toolbar标题栏title字色
        collapsingToolbarLayout.setCollapsedTitleTextColor(SkinColorUtil.getDark(this));
        collapsingToolbarLayout.setExpandedTitleColor(SkinColorUtil.getDark(this));

        //显示返回按钮
        setSupportActionBar(toolbar);
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
//            actionBar.setDisplayHomeAsUpEnabled(true);
//            actionBar.setDisplayShowTitleEnabled(false);
        }
//        collapsingToolbarLayout.setTitle(getString(R.string.string_title_userinfo));
        viewFinder.textView(R.id.account_not_verify).setText(getString(R.string.string_pc_identify,getString(R.string.app_name)));


    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.account_uid).setOnClickListener(this);
        viewFinder.find(R.id.uid_copy).setOnClickListener(this);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void showUserVerifyInfo(UserVerifyInfo data) {
        if (data != null&&isShowVerifyUserInfo) {
            viewFinder.find(R.id.account_not_verify).setVisibility(View.GONE);
            viewFinder.find(R.id.account_id_verify_status).setVisibility(View.VISIBLE);
            viewFinder.find(R.id.account_id_verify_status_icon).setVisibility(View.VISIBLE);
            viewFinder.find(R.id.account_verify_info_rela).setVisibility(View.VISIBLE);
            viewFinder.textView(R.id.account_id_verify_status).setText(getString(VERIFY_STATUS.getDescByStatus(data.getVerifyStatus())));

            viewFinder.imageView(R.id.account_id_verify_status_icon).setVisibility(data.getVerifyStatus() == VERIFY_STATUS.VERIFY_CHECKED.getmStatus() ? View.VISIBLE : View.GONE);
            viewFinder.imageView(R.id.account_id_verify_status_icon).setBackgroundResource(R.mipmap.icon_verfiled);
            viewFinder.textView(R.id.account_real_name).setText(data.getFirstName()+" " + data.getSecondName());
            viewFinder.textView(R.id.account_country).setText(data.getNationality());
            viewFinder.textView(R.id.account_id_number).setText(data.getCardNo());
            viewFinder.textView(R.id.account_gender).setText(data.getGender() == 1 ? getString(R.string.string_male) : getString(R.string.string_female));
        }
    }

    @Override
    public void showUserInfo(UserInfoBean userInfo) {
        if (userInfo != null) {
            int userType = userInfo.getUserType();
            if (userType==1) {
                isShowVerifyUserInfo = true;
                viewFinder.find(R.id.account_type).setVisibility(View.GONE);
                viewFinder.find(R.id.account_type_title).setVisibility(View.GONE);
                viewFinder.find(R.id.verify_info_title).setVisibility(View.VISIBLE);
                viewFinder.find(R.id.verify_info_title_divider).setVisibility(View.VISIBLE);
                viewFinder.find(R.id.account_verify_info_rela).setVisibility(View.VISIBLE);

            }else if(userType==2){
                isShowVerifyUserInfo = false;
                viewFinder.find(R.id.account_type).setVisibility(View.VISIBLE);
                viewFinder.find(R.id.account_type_title).setVisibility(View.VISIBLE);
                viewFinder.find(R.id.verify_info_title).setVisibility(View.GONE);
                viewFinder.find(R.id.verify_info_title_divider).setVisibility(View.GONE);
                viewFinder.find(R.id.account_verify_info_rela).setVisibility(View.GONE);
            }

            int registerType = userInfo.getRegisterType();
            if (registerType == 1) {
                viewFinder.textView(R.id.account_name).setText(userInfo.getMobile());
            } else {
                viewFinder.textView(R.id.account_name).setText(userInfo.getEmail());
            }
            viewFinder.textView(R.id.account_uid).setText(userInfo.getUserId());
            viewFinder.textView(R.id.account_uid).setTextIsSelectable(true);
            viewFinder.textView(R.id.account_register_date).setText(DateUtils.getSimpleTimeFormatS(Long.valueOf(userInfo.getRegisterDate()), "HH:mm:ss yyyy/MM/dd"));
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.account_uid:
            case R.id.uid_copy:
                TextView uidTx = viewFinder.textView(R.id.account_uid);
                CommonUtil.copyText(this,uidTx.getText().toString());
                break;
        }
    }
}
