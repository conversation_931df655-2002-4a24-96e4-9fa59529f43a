/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: RateSelectPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.sdk.data_manager.RateAndLocalManager;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;

public class RateSelectPresenter extends BasePresenter<RateSelectPresenter.RatesListUI> {

    public RateAndLocalManager.RateKind[] mRates;
    public RateAndLocalManager.RateKind mSelectRate;

    public void saveSelect() {
        RateAndLocalManager.GetInstance(this.getActivity()).setCurRateKind(mSelectRate);
    }

    public void setSelect(RateAndLocalManager.RateKind rateKind) {
        mSelectRate = rateKind;
    }

    public interface RatesListUI extends AppUI {

        void showRateList(RateAndLocalManager.RateKind[] rates);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, RateSelectPresenter.RatesListUI ui) {
        super.onUIReady(activity, ui);
        mRates = RateAndLocalManager.RateKind.values();
        mSelectRate = RateAndLocalManager.GetInstance(this.getActivity()).getCurRateKind();
        getUI().showRateList(mRates);
    }

}

