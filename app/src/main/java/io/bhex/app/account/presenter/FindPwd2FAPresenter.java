package io.bhex.app.account.presenter;

import android.os.CountDownTimer;
import android.text.TextUtils;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.app.view.InputView;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.exception.NetException;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.network.response.UISafeKeeper;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.AccountInfoApi;
import io.bhex.sdk.account.LoginApi;
import io.bhex.sdk.account.bean.FindPwdSend2FAVerifyCodeResponse;
import io.bhex.sdk.account.bean.OrderParamResponse;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-10-22
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class FindPwd2FAPresenter extends BasePresenter<FindPwd2FAPresenter.FindPwd2FAUI> {
    public interface FindPwd2FAUI extends AppUI{
        void setAuthTv(String s);

        void setAuthTvStatus(boolean b);

        void updateOrderId(String orderId);

        void goFindPwd2ConfirmStep();
    }

    private String orderId="";

    /**
     * 已登录状态验证
     * @param requestId
     */
    public void FindPwdSend2FAVerifyCode(String requestId) {
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }

        AccountInfoApi.FindPwdSend2FAVerifyCode(requestId, UISafeKeeper.guard(getUI(), new SimpleResponseListener<FindPwdSend2FAVerifyCodeResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("",getString(R.string.string_loading_hint_text));
            }

            @Override
            public void onSuccess(FindPwdSend2FAVerifyCodeResponse data) {
                super.onSuccess(data);

                if (CodeUtils.isSuccess(data,true)) {
                    orderId = data.getOrderId();
                    getUI().updateOrderId(orderId);
                    getUI().setAuthTvStatus(false);
                    timer.start();
                }

            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(getActivity(),getResources().getString(R.string.string_net_exception));
            }
        }));
    }

    /**
     * 提交验证结果
     * @param verifyEt
     */
    public void submit(boolean isNeedVerifyCode, String requestId, String currentOrderId, InputView verifyEt) {
        if (isNeedVerifyCode && TextUtils.isEmpty(currentOrderId)) {
            ToastUtils.showShort(getActivity(), getString(R.string.string_verify_code_invalid));
            return;
        }

        String verifyCode = verifyEt.getInputString();
        if (TextUtils.isEmpty(verifyCode)) {
            ToastUtils.showShort(getActivity(), getString(R.string.input_verify));
            return;
        }
        AccountInfoApi.FindPwdCheck2FAVerifyCode(requestId,currentOrderId,verifyCode,new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    if (response.isSuccess()) {

                        getUI().goFindPwd2ConfirmStep();
                    }else{
                        ToastUtils.showShort(getActivity().getResources().getString(R.string.string_submit_failed));
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(getActivity(),getResources().getString(R.string.string_net_exception));
            }
        });

    }

    /**
     * 倒计时
     */
    private CountDownTimer timer = new CountDownTimer(AppData.DOWN_TIME_CODE, AppData.DOWN_TIME_INTERVAL_CODE) {
        @Override
        public void onTick(long millisUntilFinished) {
            getUI().setAuthTv((millisUntilFinished / 1000)
                    + getActivity().getResources().getString(
                    R.string.after_second));
        }

        @Override
        public void onFinish() {
            getUI().setAuthTvStatus(true);
            getUI().setAuthTv(getResources().getString(
                    R.string.string_get_auth_code));
        }
    };
}
