/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: ResetPasswdActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;

import io.bhex.app.R;
import io.bhex.app.account.presenter.ResetPasswdPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.view.InputView;
import io.bhex.app.view.TopBar;


public class ResetPasswdActivity extends BaseActivity<ResetPasswdPresenter, ResetPasswdPresenter.ResetPasswdUI> implements ResetPasswdPresenter.ResetPasswdUI, View.OnClickListener, TextWatcher {
    private TopBar topBar;
    private Button btnSure;
    private InputView passwdNew;
    private InputView passwdNew2;
    private boolean isEmail;
    private String account = "";
    private String orderId = "";
    private String nationalCode = "";

    @Override
    protected int getContentView() {
        return R.layout.activity_reset_passwd_layout;
    }

    @Override
    protected ResetPasswdPresenter createPresenter() {
        return new ResetPasswdPresenter();
    }

    @Override
    protected ResetPasswdPresenter.ResetPasswdUI getUI() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //设置禁止系统截屏、录制
//        getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        topBar.setLeftImg(R.mipmap.btn_head_back);
        topBar.setLeftOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        Intent intent = getIntent();
        if (intent != null) {
            isEmail = intent.getBooleanExtra("isEmail", true);
            account = intent.getStringExtra("account");
            nationalCode = intent.getStringExtra("nationalCode");
            orderId = intent.getStringExtra("orderId");
        }

        btnSure = viewFinder.find(R.id.btn_sure);
        passwdNew = viewFinder.find(R.id.passwd_new);
        passwdNew.setInputHint(getString(R.string.string_passwd_new));
        passwdNew.setInputMode(InputView.PWDMODE);
        passwdNew2 = viewFinder.find(R.id.passwd_new2);
        passwdNew2.setInputHint(getString(R.string.string_passwd_new2));
        passwdNew2.setInputMode(InputView.PWDMODE);


    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.btn_sure).setOnClickListener(this);
        passwdNew.addTextWatch(this);
        passwdNew2.addTextWatch(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_sure:
                getPresenter().resetPasswd(isEmail, account, nationalCode, orderId, passwdNew, passwdNew2);
                break;
        }
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
        checkEditText();
    }

    @Override
    public void afterTextChanged(Editable s) {

    }

    private void checkEditText() {
        String newPasswd = passwdNew.getInputString();
        String newPasswd2 = passwdNew2.getInputString();
        if (TextUtils.isEmpty(newPasswd) || TextUtils.isEmpty(newPasswd2)) {
            btnSure.setEnabled(false);
        } else {
            btnSure.setEnabled(true);
        }
    }
}
