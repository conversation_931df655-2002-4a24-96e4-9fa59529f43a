/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: InvitationRewardPresenter.java
 *   @Date: 18-12-24 下午7:53
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.invite.InviteApi;
import io.bhex.sdk.invite.bean.InviteResponse;

public class InvitationRewardPresenter extends BasePresenter<InvitationRewardPresenter.InvitationRewardUI> {

    public interface InvitationRewardUI extends AppUI {

        void showShareInfo(InviteResponse response);
    }


    @Override
    public void onUIReady(BaseCoreActivity activity, InvitationRewardUI ui) {
        super.onUIReady(activity, ui);
        getShareInfo();
    }

    public void getShareInfo(){
        InviteApi.inviteShareInfo(new SimpleResponseListener<InviteResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(InviteResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showShareInfo(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
