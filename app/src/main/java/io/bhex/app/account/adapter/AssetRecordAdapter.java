/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AssetRecordAdapter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.adapter;

import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.sdk.trade.bean.AssetRecordResponse;
import io.bhex.app.utils.DateUtils;

/**
 * ================================================
 * 描   述：单个币对的转账记录
 * ================================================
 */

public class AssetRecordAdapter extends BaseQuickAdapter<AssetRecordResponse.RecordBean, BaseViewHolder> {


    public AssetRecordAdapter(List<AssetRecordResponse.RecordBean> data) {
        super(R.layout.item_asset_token_record_layout, data);
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final AssetRecordResponse.RecordBean itemModel) {
        //默认充币
        boolean isDeposit = true;
        String change = itemModel.getChange();
        if (!TextUtils.isEmpty(change)) {
            if (change.contains("-")) {
                isDeposit = false;
            }
        }

        baseViewHolder.setVisible(R.id.item_divider, baseViewHolder.getAdapterPosition() != mData.size());

//        baseViewHolder.setBackgroundRes(R.id.item_asset_record_type_icon,isDeposit?R.mipmap.icon_deposit_item:R.mipmap.icon_withdraw_item);
//        baseViewHolder.setText(R.id.item_asset_record_type, itemModel.getToken());

        baseViewHolder.setText(R.id.item_asset_record_amount, change);
        baseViewHolder.setText(R.id.item_asset_record_type, itemModel.getFlowType());
//        baseViewHolder.setText(R.id.item_asset_record_status, mContext.getString(R.string.string_success));
        baseViewHolder.setText(R.id.item_asset_record_time, DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getCreated()), "HH:mm:ss yyyy/MM/dd"));
    }

}