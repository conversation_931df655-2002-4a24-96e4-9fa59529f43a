/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FuturesHistoryDealRecordFragmentPresenter.java
 *   @Date: 19-7-26 下午6:37
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import android.text.TextUtils;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseListFreshPresenter;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.trade.futures.FuturesApi;
import io.bhex.sdk.trade.futures.bean.DeliveryOrder;
import io.bhex.sdk.trade.futures.bean.DeliveryOrderResponse;

public class FuturesHistoryDealRecordFragmentPresenter extends BaseListFreshPresenter<FuturesHistoryDealRecordFragmentPresenter.FuturesHistoryDealRecordFragmentUI>  {
private static final String LOGTAG = "FuturesHistoryDealRecordFragmentPresenter";
private List<DeliveryOrder> currentOrders = new ArrayList<>();

public interface FuturesHistoryDealRecordFragmentUI extends BaseListFreshPresenter.BaseListFreshUI {
    void showOrders(List<DeliveryOrder> currentOrders);
}


    @Override
    public void getData(final boolean isLoadMore) {
        if (!UserInfo.isLogin()) {
            if (isLoadMore) {
                getUI().loadMoreComplete();
            }
            return;
        }
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }

        if (isLoadMore) {
            if (currentOrders != null) {
                if (!currentOrders.isEmpty()) {
                    mPageId = currentOrders.get(currentOrders.size() - 1).getTradeId();
                }
            }
        }else{
            mPageId ="";
        }

        String pageId = "";

        if (isLoadMore && !TextUtils.isEmpty(mPageId)) {
            //加载更多
            pageId = mPageId;

        }
        FuturesApi.RequestHistoryDeliveryOrders("","",pageId,"",AppData.Config.PAGE_LIMIT,"", new SimpleResponseListener<DeliveryOrderResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
                if (isLoadMore) {
                    getUI().loadMoreComplete();
                }
            }

            @Override
            public void onSuccess(DeliveryOrderResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<DeliveryOrder> data = response.getArray();
                    if (data != null) {

                        if (isLoadMore) {
                            if (data != null) {
                                for (DeliveryOrder datum : data) {
                                    if (!KlineUtils.isFuturesForceCLoseTradeOrder(getActivity(),datum)) {
                                        currentOrders.add(datum);
                                    }
                                }
                            }

                        } else {
                            if (data != null) {
                                currentOrders.clear();
                                for (DeliveryOrder datum : data) {
                                    if (!KlineUtils.isFuturesForceCLoseTradeOrder(getActivity(),datum)) {
                                        currentOrders.add(datum);
                                    }
                                }
                            }
                        }
                        getUI().showOrders(currentOrders);

                        if (data.size()< AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        }else{
                            getUI().loadMoreComplete();
                        }
                    }else{
                        getUI().loadMoreComplete();
                    }

                }else{
                    getUI().loadMoreFailed();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                if (isLoadMore) {
                    getUI().loadMoreFailed();
                }
            }
        });
    }

}