package io.bhex.app.account.presenter;

import android.os.CountDownTimer;
import android.text.TextUtils;

import io.bhex.app.BuildConfig;
import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.view.InputView;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.security.SecurityApi;
import io.bhex.sdk.security.bean.OrderIdParamResponse;

/**
 * *******************************************************************
 *
 * @项目名称: BHEX Android
 * @文件名称: ChangeMobilePresenter
 * @Date: 2020/10/14 下午2:36
 * @Author: ppzhao
 * @Copyright（C）: 2020 BlueHelix Inc.   All rights reserved.
 * 注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 * *******************************************************************
 **/
public class ChangeMobilePresenter extends BasePresenter<ChangeMobilePresenter.ChangeMobileUI> {
    public interface ChangeMobileUI extends AppUI{

        void setAuthTvStatus(boolean b, boolean b1);

        void setAuthTv(boolean b, String s);

        void setAuthTv(String s);

        void setAuthTvStatus(boolean b);
    }

    private String orderIdOfEmail="";
    private String orderIdOfMobile="";
    private String orderIdOfNewMobile="";

    /**
     * 新手机验证码
     * @param nationalCode
     * @param mobile
     * @param token
     */
    public void requestMobileCode(String nationalCode, String mobile, String token) {
        SecurityApi.requestNewMobileVerifyCodeOfChangeMobile(mobile,token, BuildConfig.DEEPKNOW_ID,nationalCode,new SimpleResponseListener<OrderIdParamResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("",getString(R.string.string_loading_hint_text));
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(OrderIdParamResponse data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data,true)) {
                    orderIdOfNewMobile = data.getOrderId();
                    getUI().setAuthTvStatus(false);
                    timerOfNewMobile.start();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);

            }
        });
    }

    /**
     * 旧手机验证码
     */
    public void requestOldMobileCode() {
        SecurityApi.requestMobileVerifyCodeOfChangeMobile(new SimpleResponseListener<OrderIdParamResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("",getString(R.string.string_loading_hint_text));
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(OrderIdParamResponse data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data,true)) {
                    orderIdOfMobile = data.getOrderId();
                    getUI().setAuthTvStatus(false,false);
                    timerOfMobile.start();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);

            }
        });
    }

    public void sendEmailCode() {
        SecurityApi.requestEmailVerifyCodeOfChangeMobile(new SimpleResponseListener<OrderIdParamResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("",getString(R.string.string_loading_hint_text));
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(OrderIdParamResponse data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data,true)) {
                    orderIdOfEmail = data.getOrderId();
                    getUI().setAuthTvStatus(true,false);
                    timerOfEmail.start();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);

            }
        });
    }

    /**
     * 请求绑定手机
     * @param newNationCode
     * @param oldMobileCode
     * @param newMobile
     * @param newMobileCode
     * @param emailCode
     */
    public void requestChangeMobile(String newNationCode, String oldMobileCode, String newMobile, String newMobileCode, String emailCode) {
        if(TextUtils.isEmpty(orderIdOfNewMobile)) {
            ToastUtils.showShort(getActivity(),getString(R.string.string_verify_code_invalid));
            return;
        }
        if(TextUtils.isEmpty(orderIdOfEmail)) {
            ToastUtils.showShort(getActivity(),getString(R.string.string_verify_code_invalid));
            return;
        }
        if(TextUtils.isEmpty(orderIdOfMobile)) {
            ToastUtils.showShort(getActivity(),getString(R.string.string_verify_code_invalid));
            return;
        }
        SecurityApi.changeMobile(orderIdOfMobile,oldMobileCode,newNationCode,newMobile,orderIdOfNewMobile,newMobileCode,orderIdOfEmail,emailCode,new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    ToastUtils.showShort(getActivity(), getString(R.string.string_bind_mobile_success));
                    getActivity().finish();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                ToastUtils.showShort(getActivity(), getString(R.string.string_bind_mobile_error));
            }
        });
    }

    public boolean checkInputContentIsEmpty(InputView inputMobile, InputView inputVerifyEmail, InputView inputVerifyMobile) {
        String mobile = inputMobile.getInputString();
        String codeOfemail = inputVerifyEmail.getInputString();
        String codeOfMobile = inputVerifyMobile.getInputString();

        return !TextUtils.isEmpty(mobile) && !TextUtils.isEmpty(codeOfemail) && !TextUtils.isEmpty(codeOfMobile);
    }

    /**
     * 倒计时
     */
    private CountDownTimer timerOfEmail = new CountDownTimer(AppData.DOWN_TIME_CODE, AppData.DOWN_TIME_INTERVAL_CODE) {
        @Override
        public void onTick(long millisUntilFinished) {
            getUI().setAuthTv(true,(millisUntilFinished / 1000)
                    + getActivity().getResources().getString(
                    R.string.after_second));
        }

        @Override
        public void onFinish() {
            getUI().setAuthTvStatus(true,true);
            getUI().setAuthTv(true,getResources().getString(
                    R.string.string_get_auth_code));
        }
    };

    /**
     * 倒计时
     */
    private CountDownTimer timerOfMobile = new CountDownTimer(AppData.DOWN_TIME_CODE, AppData.DOWN_TIME_INTERVAL_CODE) {
        @Override
        public void onTick(long millisUntilFinished) {
            getUI().setAuthTv(false,(millisUntilFinished / 1000)
                    + getActivity().getResources().getString(
                    R.string.after_second));
        }

        @Override
        public void onFinish() {
            getUI().setAuthTvStatus(false,true);
            getUI().setAuthTv(false,getResources().getString(
                    R.string.string_get_auth_code));
        }
    };

    /**
     * 倒计时
     */
    private CountDownTimer timerOfNewMobile = new CountDownTimer(AppData.DOWN_TIME_CODE, AppData.DOWN_TIME_INTERVAL_CODE) {
        @Override
        public void onTick(long millisUntilFinished) {
            getUI().setAuthTv((millisUntilFinished / 1000)
                    + getActivity().getResources().getString(
                    R.string.after_second));
        }

        @Override
        public void onFinish() {
            getUI().setAuthTvStatus(true);
            getUI().setAuthTv(getResources().getString(
                    R.string.string_get_auth_code));
        }
    };
}
