/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FuturesHoldOrderFragmentPresenter.java
 *   @Date: 19-7-26 下午2:23
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import android.text.TextUtils;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseListFreshPresenter;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.trade.OptionApi;
import io.bhex.sdk.trade.bean.FuturesHoldOrderSocketResponse;
import io.bhex.sdk.trade.bean.OptionHoldOrderResponse;
import io.bhex.sdk.trade.futures.FuturesApi;
import io.bhex.sdk.trade.futures.bean.FuturesPositionOrder;
import io.bhex.sdk.trade.futures.bean.FuturesPositionOrderResponse;

public class FuturesHoldOrderFragmentPresenter extends BaseListFreshPresenter<FuturesHoldOrderFragmentPresenter.HoldOrderFragmentUI>  {
    private static final String LOGTAG = "CurrentOptionHoldOrderFragmentPresenter";
    private List<FuturesPositionOrder> currentOrders = new ArrayList<>();

    public interface HoldOrderFragmentUI extends BaseListFreshPresenter.BaseListFreshUI {
        void showOrders(List<FuturesPositionOrder> currentOrders);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, HoldOrderFragmentUI ui) {
        super.onUIReady(activity, ui);
//        subPositionOrder();
    }

    @Override
    public void onVisibleChanged(boolean visible) {
        super.onVisibleChanged(visible);
        if (visible) {
            getData(false);
        }
    }

    @Override
    public void getData(final boolean isLoadMore) {
        if (!UserInfo.isLogin()) {
            if (isLoadMore) {
                getUI().loadMoreComplete();
            }
            return;
        }
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }

        if (isLoadMore) {
            if (currentOrders != null) {
                if (!currentOrders.isEmpty()) {
                    mPageId = currentOrders.get(currentOrders.size() - 1).getPositionId();
                }
            }
        }else{
            mPageId ="";
        }

        String pageId = "";

        if (isLoadMore && !TextUtils.isEmpty(mPageId)) {
            //加载更多
            pageId = mPageId;

        }
        FuturesApi.RequestHoldOrders("","", pageId,"",AppData.Config.PAGE_LIMIT,"", new SimpleResponseListener<FuturesPositionOrderResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
                if (isLoadMore) {
                    getUI().loadMoreComplete();
                }
            }

            @Override
            public void onSuccess(FuturesPositionOrderResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<FuturesPositionOrder> data = response.getArray();
                    if (data != null) {

                        if (isLoadMore) {
                            if (data != null) {
                                currentOrders.addAll(data);
                            }

                        } else {
                            if (data != null) {
                                currentOrders.clear();
                                currentOrders = data;
                            }
                        }
                        getUI().showOrders(currentOrders);

                        if (data.size()< AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        }else{
                            getUI().loadMoreComplete();
                        }
                    }else{
                        getUI().loadMoreComplete();
                    }

                }else{
                    getUI().loadMoreFailed();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                if (isLoadMore) {
                    getUI().loadMoreFailed();
                }
            }
        });
    }

    /**
     * 订阅持仓单
     *
     */
    private void subPositionOrder() {
        if (!UserInfo.isLogin()) {
            //未登录状态不请求
            return;
        }

        FuturesApi.SubFuturesHoldOrderChange(new SimpleResponseListener<FuturesPositionOrderResponse>() {
            @Override
            public void onSuccess(FuturesPositionOrderResponse response) {
                super.onSuccess(response);
                try {
                    if (CodeUtils.isSuccess(response, true)) {
                        List<FuturesPositionOrder> list = new ArrayList<>();
                        for (int i = 0; i < response.getArray().size(); i++) {
                            FuturesPositionOrder orderBean = response.getArray().get(i);
                            if (orderBean != null) {
                                list.add(orderBean);
                            }
                        }
                        getUI().showOrders(list);

                    }
                } catch (Exception e) {
                }
            }
        });

    }

}