/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FuturesHistoryOrderFragmentPresenter.java
 *   @Date: 19-7-26 下午6:09
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import android.text.TextUtils;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseListFreshPresenter;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.enums.ORDER_ENTRUST_TYPE;
import io.bhex.sdk.trade.futures.FuturesApi;
import io.bhex.sdk.trade.futures.bean.EntrustOrderResponse;
import io.bhex.sdk.trade.futures.bean.FuturesOrderResponse;

public class FuturesHistoryOrderFragmentPresenter extends BaseListFreshPresenter<FuturesHistoryOrderFragmentPresenter.HistoryOrderFragmentUI> {
    private static final String LOGTAG = "FuturesHistoryOrderFragmentPresenter";
    private List<FuturesOrderResponse> currentOrders = new ArrayList<>();
    private List<FuturesOrderResponse> currentOrdersOfPlanning = new ArrayList<>();
    private List<FuturesOrderResponse> stopProfitLossOrders = new ArrayList<>();
    private String mOrdinaryPageId = "";
    private String mPlanningPageId = "";
    private String mPageIdOfStopProfitLoss = "";
    //默认是普通委托类型
    private String currentOrderType = ORDER_ENTRUST_TYPE.LIMIT.getEntrustType();

    public interface HistoryOrderFragmentUI extends BaseListFreshPresenter.BaseListFreshUI {
        void showOrders(String entrustType, List<FuturesOrderResponse> currentOrders);

    }

    @Override
    public void onUIReady(BaseCoreActivity activity, HistoryOrderFragmentUI ui) {
        super.onUIReady(activity, ui);
        getOrdinaryHistoryOrder(false);
    }

    @Override
    public void getData(boolean isLoadMore) {
        if (currentOrderType.equals(ORDER_ENTRUST_TYPE.LIMIT.getEntrustType())) {
            getOrdinaryHistoryOrder(isLoadMore);
        }else if(currentOrderType.equals(ORDER_ENTRUST_TYPE.STOP.getEntrustType())){
            getPlanningHistoryOrder(isLoadMore);
        }else{
            getStopProfitLossData(isLoadMore);
        }
    }

    public void getOrdinaryHistoryOrder(final boolean isLoadMore) {
        currentOrderType = ORDER_ENTRUST_TYPE.LIMIT.getEntrustType();
        if (!UserInfo.isLogin()) {
            if (isLoadMore) {
                getUI().loadMoreComplete();
            }
            return;
        }
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }

        if (isLoadMore) {
            if (currentOrders != null) {
                if (!currentOrders.isEmpty()) {
                    mOrdinaryPageId = currentOrders.get(currentOrders.size() - 1).getOrderId();
                }
            }
        } else {
            mOrdinaryPageId = "";
        }

        String pageId = "";

        if (isLoadMore && !TextUtils.isEmpty(mOrdinaryPageId)) {
            //加载更多
            pageId = mOrdinaryPageId;

        }
        FuturesApi.RequestHistoryEntrustOrders("", "", pageId, "", AppData.Config.PAGE_LIMIT, "", currentOrderType, new SimpleResponseListener<EntrustOrderResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
                if (isLoadMore) {
                    getUI().loadMoreComplete();
                }
            }

            @Override
            public void onSuccess(EntrustOrderResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<FuturesOrderResponse> data = response.getArray();
                    if (data != null) {

                        if (isLoadMore) {
                            if (data != null) {
                                currentOrders.addAll(data);
                            }

                        } else {
                            if (data != null) {
                                currentOrders.clear();
                                currentOrders = data;
                            }
                        }
                        getUI().showOrders(ORDER_ENTRUST_TYPE.LIMIT.getEntrustType(),currentOrders);

                        if (data.size() < AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        } else {
                            getUI().loadMoreComplete();
                        }
                    } else {
                        getUI().loadMoreComplete();
                    }

                } else {
                    getUI().loadMoreFailed();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                if (isLoadMore) {
                    getUI().loadMoreFailed();
                }
            }
        });
    }

    /**
     * 计划委托
     *
     * @param isLoadMore
     */
    public void getPlanningHistoryOrder(final boolean isLoadMore) {
        currentOrderType = ORDER_ENTRUST_TYPE.STOP.getEntrustType();
        if (!UserInfo.isLogin()) {
            if (isLoadMore) {
                getUI().loadMoreComplete();
            }
            return;
        }
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }

        if (isLoadMore) {
            if (currentOrdersOfPlanning != null) {
                if (!currentOrdersOfPlanning.isEmpty()) {
                    mPlanningPageId = currentOrdersOfPlanning.get(currentOrdersOfPlanning.size() - 1).getOrderId();
                }
            }
        } else {
            mPlanningPageId = "";
        }

        String pageId = "";

        if (isLoadMore && !TextUtils.isEmpty(mPlanningPageId)) {
            //加载更多
            pageId = mPlanningPageId;

        }
        FuturesApi.RequestHistoryEntrustOrders("", "", pageId, "", AppData.Config.PAGE_LIMIT, "", currentOrderType, new SimpleResponseListener<EntrustOrderResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
                if (isLoadMore) {
                    getUI().loadMoreComplete();
                }
            }

            @Override
            public void onSuccess(EntrustOrderResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<FuturesOrderResponse> data = response.getArray();
                    if (data != null) {

                        if (isLoadMore) {
                            if (data != null) {
                                currentOrdersOfPlanning.addAll(data);
                            }

                        } else {
                            if (data != null) {
                                currentOrdersOfPlanning.clear();
                                currentOrdersOfPlanning = data;
                            }
                        }
                        getUI().showOrders(ORDER_ENTRUST_TYPE.STOP.getEntrustType(),currentOrdersOfPlanning);

                        if (data.size() < AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        } else {
                            getUI().loadMoreComplete();
                        }
                    } else {
                        getUI().loadMoreComplete();
                    }

                } else {
                    getUI().loadMoreFailed();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                if (isLoadMore) {
                    getUI().loadMoreFailed();
                }
            }
        });
    }


    /**
     * 查询止盈止损-当前委托单
     * @param isLoadMore
     */
    public void getStopProfitLossData(final boolean isLoadMore) {
        currentOrderType = ORDER_ENTRUST_TYPE.STOP_PROFIT_OR_LOSS.getEntrustType();
        if (!UserInfo.isLogin()) {
            if (isLoadMore) {
                getUI().loadMoreComplete();
            }
            return;
        }
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }

        if (isLoadMore) {
            if (stopProfitLossOrders != null) {
                if (!stopProfitLossOrders.isEmpty()) {
                    mPageIdOfStopProfitLoss = stopProfitLossOrders.get(stopProfitLossOrders.size() - 1).getOrderId();
                }
            }
        } else {
            mPageIdOfStopProfitLoss = "";
        }

        String pageId = "";

        if (isLoadMore && !TextUtils.isEmpty(mPageIdOfStopProfitLoss)) {
            //加载更多
            pageId = mPageIdOfStopProfitLoss;

        }
        FuturesApi.getHistoryStopProfitLossData("", mPageIdOfStopProfitLoss, "", AppData.Config.PAGE_LIMIT, "", new SimpleResponseListener<EntrustOrderResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
                if (isLoadMore) {
                    getUI().loadMoreComplete();
                }
            }

            @Override
            public void onSuccess(EntrustOrderResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<FuturesOrderResponse> data = response.getArray();
                    if (data != null) {

                        if (isLoadMore) {
                            if (data != null) {
                                stopProfitLossOrders.addAll(data);
                            }

                        } else {
                            if (data != null) {
                                stopProfitLossOrders.clear();
                                stopProfitLossOrders = data;
                            }
                        }
                        getUI().showOrders(ORDER_ENTRUST_TYPE.STOP_PROFIT_OR_LOSS.getEntrustType(), stopProfitLossOrders);

                        if (data.size() < AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        } else {
                            getUI().loadMoreComplete();
                        }
                    } else {
                        getUI().loadMoreComplete();
                    }

                } else {
                    getUI().loadMoreFailed();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                if (isLoadMore) {
                    getUI().loadMoreFailed();
                }
            }
        });
    }

}