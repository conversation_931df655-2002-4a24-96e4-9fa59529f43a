/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AllOptionOrderPresenter.java
 *   @Date: 1/11/19 4:46 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.account.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;

public class AllOptionOrderPresenter  extends BasePresenter<AllOptionOrderPresenter.AllOptionOrderUI> {
    public interface AllOptionOrderUI extends AppUI {

    }

    @Override
    public void onUIReady(BaseCoreActivity activity, AllOptionOrderUI ui) {
        super.onUIReady(activity, ui);
    }

}