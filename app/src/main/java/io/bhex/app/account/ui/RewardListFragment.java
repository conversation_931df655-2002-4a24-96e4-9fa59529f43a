/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: RewardListFragment.java
 *   @Date: 19-8-26 下午3:48
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.adapter.InviteRewardListAdapter;
import io.bhex.app.account.presenter.RewardListPresenter;
import io.bhex.app.base.BaseFragment;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.invite.bean.InviteRewardListResponse;

/**
 * 邀请明细
 */
public class RewardListFragment extends BaseFragment<RewardListPresenter,RewardListPresenter.RewardListUI> implements RewardListPresenter.RewardListUI, OnRefreshListener, BaseQuickAdapter.RequestLoadMoreListener {
    private SmartRefreshLayout swipeRefresh;
    private RecyclerView recyclerView;
    private View emptyView;
    private InviteRewardListAdapter adapter;

    @Override
    protected RewardListPresenter.RewardListUI getUI() {
        return this;
    }

    @Override
    protected RewardListPresenter createPresenter() {
        return new RewardListPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_reward_list_layout, null, false);
    }

    @Override
    protected void initViews() {
        super.initViews();
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        swipeRefresh.setOnRefreshListener(this);
        recyclerView = viewFinder.find(R.id.recyclerView);
        LayoutInflater layoutInflater = LayoutInflater.from(getActivity());
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
        emptyView.setLayoutParams(layoutParams);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.closeBtn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                viewFinder.find(R.id.topTips).setVisibility(View.GONE);
            }
        });
    }

    @Override
    public void showRewardList(List<InviteRewardListResponse.RewardBean> rewardList) {
        if (rewardList != null) {
            if (adapter == null) {
                adapter = new InviteRewardListAdapter(rewardList);
                adapter.isFirstOnly(false);
                adapter.setOnLoadMoreListener(this, recyclerView);
                adapter.setEnableLoadMore(true);

                recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
                recyclerView.setItemAnimator(new DefaultItemAnimator());

                adapter.setEmptyView(emptyView);
                recyclerView.setAdapter(adapter);
            } else {
                adapter.setNewData(rewardList);
            }
        }
    }

    @Override
    public void loadMoreComplete() {
        if (adapter != null) {
            adapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {
        if (adapter != null) {
            adapter.loadMoreFail();
        }
    }

    @Override
    public void loadEnd() {
        if (adapter != null) {
            adapter.loadMoreEnd();
        }
    }

    @Override
    public void onLoadMoreRequested() {
        getPresenter().getRewardList(true);
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        getPresenter().getRewardList(false);
        refreshLayout.finishRefresh(1000);
    }
}
