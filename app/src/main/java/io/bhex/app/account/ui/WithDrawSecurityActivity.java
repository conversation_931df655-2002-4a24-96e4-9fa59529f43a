/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: WithDrawSecurityActivity.java
 *   @Date: 19-5-22 下午5:00
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.content.Intent;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.Toolbar;

import com.google.android.material.appbar.CollapsingToolbarLayout;

import io.bhex.app.R;
import io.bhex.app.account.presenter.WithDrawSecurityPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.account.bean.enums.VERIFY_STATUS;

/**
 * ================================================
 * 描   述：安全中心
 * ================================================
 */

public class WithDrawSecurityActivity extends BaseActivity<WithDrawSecurityPresenter,WithDrawSecurityPresenter.WithDrawSecurityUI> implements WithDrawSecurityPresenter.WithDrawSecurityUI, View.OnClickListener {
    private UserInfoBean userInfo;
    private boolean bindGA;
    private String email;
    private String mobile;
    private String tokenId;

    @Override
    protected int getContentView() {
        return R.layout.activity_withdraw_security_layout;
    }

    @Override
    protected WithDrawSecurityPresenter createPresenter() {
        return new WithDrawSecurityPresenter();
    }

    @Override
    protected WithDrawSecurityPresenter.WithDrawSecurityUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        Toolbar toolbar= findViewById(R.id.toolbar);
        CollapsingToolbarLayout collapsingToolbarLayout= findViewById(R.id.collapsing_toolbar);
        // 硬编码黑白版Toolbar标题栏title字色
        collapsingToolbarLayout.setCollapsedTitleTextColor(SkinColorUtil.getDark(this));
        collapsingToolbarLayout.setExpandedTitleColor(SkinColorUtil.getDark(this));

        //显示返回按钮
        setSupportActionBar(toolbar);
        ActionBar actionBar=getSupportActionBar();
        if (actionBar!=null){
            actionBar.setDisplayHomeAsUpEnabled(true);
        }

        Intent intent = getIntent();
        if (intent != null) {
            tokenId = intent.getStringExtra("tokenId");
        }
        userInfo = UserManager.getInstance().getUserInfo();
        setUserInfoStatus(userInfo);

    }

    private void setUserInfoStatus(UserInfoBean userInfo) {
        if (userInfo != null) {
            if (userInfo.getRegisterType()==1) {
                //手机注册
                viewFinder.find(R.id.msm_rela).setVisibility(View.GONE);
                viewFinder.find(R.id.email_rela).setVisibility(View.VISIBLE);
            }else{
                viewFinder.find(R.id.msm_rela).setVisibility(View.VISIBLE);
                viewFinder.find(R.id.email_rela).setVisibility(View.GONE);
            }
            bindGA = userInfo.isBindGA();
            if (bindGA) {
                viewFinder.textView(R.id.auth_ga_status).setTextColor(SkinColorUtil.getDark50(this));
                viewFinder.textView(R.id.auth_ga_status).setText(getString(R.string.string_binded));
            }else{
                viewFinder.textView(R.id.auth_ga_status).setText(getString(R.string.string_not_bind));
            }
            email = userInfo.getEmail();
            if (!TextUtils.isEmpty(email)) {
                viewFinder.textView(R.id.auth_email_status).setTextColor(SkinColorUtil.getDark50(this));
                viewFinder.textView(R.id.auth_email_status).setText(getString(R.string.string_binded));
            }else{
                viewFinder.textView(R.id.auth_email_status).setText(getString(R.string.string_not_bind));
            }
            mobile = userInfo.getMobile();
            if (!TextUtils.isEmpty(mobile)) {
                viewFinder.textView(R.id.auth_msm_status).setTextColor(SkinColorUtil.getDark50(this));
                viewFinder.textView(R.id.auth_msm_status).setText(getString(R.string.string_binded));
            }else{
                viewFinder.textView(R.id.auth_msm_status).setText(getString(R.string.string_not_bind));
            }
            if (userInfo.isBindTradePwd()) {
                viewFinder.textView(R.id.finance_passed_oporate).setTextColor(SkinColorUtil.getDark50(this));
                viewFinder.textView(R.id.finance_passed_oporate).setText(getString(R.string.string_modify));
            }else{
                viewFinder.textView(R.id.finance_passed_oporate).setText(getString(R.string.string_set));
            }

            if (userInfo.getVerifyStatus()==VERIFY_STATUS.VERIFY_CHECKED.getmStatus()) {
//                viewFinder.textView(R.id.auth_kyc_status).setText(getString(R.string.string_had_auth));
                viewFinder.textView(R.id.auth_kyc_status).setTextColor(SkinColorUtil.getDark50(this));
                viewFinder.textView(R.id.auth_kyc_status).setText(getString(R.string.string_binded));
                viewFinder.find(R.id.kyc_rela).setClickable(false);
            }else{
                viewFinder.find(R.id.btnWithdraw).setVisibility(View.GONE);
            }
            boolean isBindMobile = !TextUtils.isEmpty(mobile);
            boolean isBindEmail = !TextUtils.isEmpty(email);
            /**判断是否设置了资金密码 是否2FA(至少两个绑定) ***********/
            if (userInfo.isBindTradePwd() && ((isBindEmail&&isBindMobile)||(bindGA&&isBindEmail)||(bindGA&&isBindMobile))) {
                viewFinder.find(R.id.btnWithdraw).setVisibility(View.VISIBLE);
            }
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()){
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }
    @Override
    protected void onResume() {
        super.onResume();
        if(UserInfo.isLogin())
            getPresenter().getUserInfo();
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.finance_passwd_rela).setOnClickListener(this);
        viewFinder.find(R.id.ga_rela).setOnClickListener(this);
        viewFinder.find(R.id.msm_rela).setOnClickListener(this);
        viewFinder.find(R.id.email_rela).setOnClickListener(this);
        viewFinder.find(R.id.kyc_rela).setOnClickListener(this);
        viewFinder.find(R.id.btnWithdraw).setOnClickListener(this);

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.finance_passwd_rela:
                if (userInfo != null) {
                    IntentUtils.goFinancePasswd(this,userInfo.isBindTradePwd());
                }else{
                    getPresenter().getUserInfo();
                }
                break;
            case R.id.ga_rela:

                if (!bindGA) {
                    IntentUtils.goBindGAHelp(this);
                }
                break;
            case R.id.msm_rela:
                if (TextUtils.isEmpty(mobile)) {
                    IntentUtils.goBindMobile(this);
                }else{
                    IntentUtils.goBindInfo(this,"mobile",mobile);
                }
                break;
            case R.id.email_rela:
                if (TextUtils.isEmpty(email)) {
                    IntentUtils.goBindEmail(this);
                }else{
                    IntentUtils.goBindInfo(this,"email",email);
                }
                break;
            case R.id.kyc_rela:
                if (userInfo != null) {
                    //设置资金密码提示
                    if (!userInfo.isBindTradePwd()) {
                        ToastUtils.showShort(getString(R.string.string_input_trade_passwd_please));
                        return;
                    }
                }

                IntentUtils.goIdentityAuth(this);
                break;

            case R.id.btnWithdraw:

                IntentUtils.goMyAssetTokenWithdraw(tokenId,this);
                break;
        }
    }

    @Override
    public void getUserInfoSuccess(UserInfoBean data) {
        if (data != null) {
            userInfo = data;
            setUserInfoStatus(userInfo);
        }
    }

}
