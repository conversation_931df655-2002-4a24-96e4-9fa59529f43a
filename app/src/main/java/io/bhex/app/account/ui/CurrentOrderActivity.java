/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CurrentOrderActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import io.bhex.app.account.presenter.CurrentOrderPresenter;
import io.bhex.app.base.BaseActivity;

public class CurrentOrderActivity extends BaseActivity<CurrentOrderPresenter,CurrentOrderPresenter.CurrentOrderUI> implements CurrentOrderPresenter.CurrentOrderUI {

    @Override
    protected int getContentView() {
        return 0;
    }

    @Override
    protected CurrentOrderPresenter createPresenter() {
        return new CurrentOrderPresenter();
    }

    @Override
    protected CurrentOrderPresenter.CurrentOrderUI getUI() {
        return this;
    }
}
