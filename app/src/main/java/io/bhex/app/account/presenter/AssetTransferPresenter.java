/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AssetTransferPresenter.java
 *   @Date: 1/22/19 5:50 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.account.presenter;

import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.account.AccountInfoApi;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.SubAccountListResponse;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.trade.margin.MarginApi;
import io.bhex.sdk.trade.margin.bean.MarginTokenConfigResponse;

public class AssetTransferPresenter extends BasePresenter<AssetTransferPresenter.AssetTransferUI> {
    public interface AssetTransferUI extends AppUI {

        void showAccountList(SubAccountListResponse response);

        void updateMarginTokens(List<MarginTokenConfigResponse.MarginToken> datas);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, AssetTransferUI ui) {
        super.onUIReady(activity, ui);
        getAccountList();
        getMarginTokens();
    }

    public void getAccountList() {
        AccountInfoApi.getAccountList(new SimpleResponseListener<SubAccountListResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(SubAccountListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showAccountList(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public void getMarginTokens() {
        if(UserManager.getInstance().getUserInfo()==null ||!UserManager.getInstance().getUserInfo().isOpenMargin()){
            return;
        }
        MarginApi.getMarginTokenConfig("", new SimpleResponseListener<MarginTokenConfigResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(MarginTokenConfigResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    List<MarginTokenConfigResponse.MarginToken> datas = response.getArray();
                    AppConfigManager.GetInstance().loadMarginTokenItemToMap(response,true);
                    getUI().updateMarginTokens(datas);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
