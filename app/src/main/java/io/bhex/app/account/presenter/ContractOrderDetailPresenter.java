/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OrderDetailPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import android.content.Intent;
import android.text.TextUtils;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.app.utils.KlineUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.trade.TradeApi;
import io.bhex.sdk.trade.bean.OrderBean;
import io.bhex.sdk.trade.bean.OrderDealDetailResponse;
import io.bhex.sdk.trade.futures.FuturesApi;
import io.bhex.sdk.trade.futures.bean.DeliveryOrder;
import io.bhex.sdk.trade.futures.bean.DeliveryOrderResponse;
import io.bhex.sdk.trade.futures.bean.FuturesOrderResponse;

/**
 * ================================================
 * 描   述：订单详情
 * ================================================
 */

public class ContractOrderDetailPresenter extends BasePresenter<ContractOrderDetailPresenter.OrderDetailUI> {
    private List<DeliveryOrder> currentOrders = new ArrayList<>();
    private String orderPageId;
    private String orderId="";
    private FuturesOrderResponse order;

    public void loadMore() {
        getDealDetail(true);
    }

    public interface OrderDetailUI extends AppUI{

        void loadMoreComplete();

        void showOrders(List<DeliveryOrder> currentOrders);

        void loadMoreFailed();

        void showHeaderOrders(FuturesOrderResponse order);

        void loadEnd();
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, OrderDetailUI ui) {
        super.onUIReady(activity, ui);
        Intent intent = getActivity().getIntent();
        if (intent != null) {
            order = (FuturesOrderResponse) intent.getSerializableExtra(AppData.INTENT.KEY_ORDER);
            if (order != null) {
                if (KlineUtils.isStopProfitOrLossOrder(order)) {
                    orderId = order.getExecutedOrderId();   //止盈止损委托-使用执行订单号拉取成交详情
                    getExecutedOrderDetail(orderId);
                }else{
                    orderId = order.getOrderId();
                }
                getUI().showHeaderOrders(order);
                getDealDetail(false);
            }
        }
    }

    /**
     * 获取执行委托单详情
     * @param orderId
     */
    private void getExecutedOrderDetail(String orderId) {
        FuturesApi.RequestOrderDetailByOrderId(orderId,new SimpleResponseListener<FuturesOrderResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(FuturesOrderResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    getUI().showHeaderOrders(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });

    }

    /**
     * 获取成交详情
     *
     * @param isLoadMore
     */
    public void getDealDetail(final boolean isLoadMore) {
        if (isLoadMore) {
            if (currentOrders != null) {
                if (!currentOrders.isEmpty()) {
                    orderPageId = currentOrders.get(currentOrders.size() - 1).getTradeId();
                }
            }
        }
        String reqeustOrderPageId = "";
        if (isLoadMore && !TextUtils.isEmpty(orderPageId)) {
            //加载更多
            reqeustOrderPageId= orderPageId;

        }
        FuturesApi.RequestHistoryDeliveryOrdersDetail(orderId, "",AppData.Config.PAGE_LIMIT,new SimpleResponseListener<DeliveryOrderResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
                if (isLoadMore) {
                    getUI().loadMoreComplete();
                }
            }

            @Override
            public void onSuccess(DeliveryOrderResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<DeliveryOrder> data = response.getArray();
                    if (data != null) {
                        if (isLoadMore) {
                            if (data != null) {
                                currentOrders.addAll(data);
                            }

                        } else {
                            if (data != null) {
                                currentOrders.clear();
                                currentOrders = data;
                            }
                        }
                        getUI().showOrders(currentOrders);
                        if (data.size()< AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        }else{
                            getUI().loadMoreComplete();
                        }
                    }else{
                        getUI().loadMoreComplete();
                    }

                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                if (isLoadMore) {
                    getUI().loadMoreFailed();
                }
            }
        });
    }
}
