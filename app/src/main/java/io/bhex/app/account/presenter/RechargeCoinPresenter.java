/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: RechargeCoinPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import io.bhex.baselib.network.response.UISafeKeeper;
import io.bhex.sdk.trade.AssetApi;
import io.bhex.sdk.trade.bean.CoinAddressBean;
import io.bhex.app.base.AppUI;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;


public class RechargeCoinPresenter extends BasePresenter<RechargeCoinPresenter.RechargeCoinUI> {
    public interface RechargeCoinUI extends AppUI {

        void showCoinAddress(CoinAddressBean response);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, RechargeCoinUI ui) {
        super.onUIReady(activity, ui);
    }

    /**
     * 获取充币地址
     */
    public void getRechargeAddress(String token,String chainType) {
        AssetApi.RequestRechargeAddress(token,chainType, UISafeKeeper.guard(getUI(), new SimpleResponseListener<CoinAddressBean>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(CoinAddressBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    getUI().showCoinAddress(response);
                }
            }

            @Override
            public void onError(Throwable error){
                super.onError(error);
            }
        }));
    }
}
