/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: GABindHelpPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.sdk.security.SecurityApi;
import io.bhex.sdk.security.bean.GAInfoResponse;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;


public class GABindHelpPresenter extends BasePresenter<GABindHelpPresenter.GABindHelpUI> {
    public interface GABindHelpUI extends AppUI{

        void showGABindInfo(GAInfoResponse response);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, GABindHelpUI ui) {
        super.onUIReady(activity, ui);
        SecurityApi.getBindGAInfo(new SimpleResponseListener<GAInfoResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(GAInfoResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showGABindInfo(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
