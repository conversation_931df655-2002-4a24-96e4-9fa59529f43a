/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: LocalLangSelectActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.content.Intent;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.Arrays;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.presenter.LocalLangSelectPresenter;
import io.bhex.app.app.BHexApplication;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.main.ui.MainActivity;
import io.bhex.app.view.TopBar;
import io.bhex.sdk.data_manager.RateAndLocalManager;
import io.bhex.sdk.data_manager.AppConfigManager;

public class LocalLangSelectActivity extends BaseActivity<LocalLangSelectPresenter,
        LocalLangSelectPresenter.LocalListUI> implements LocalLangSelectPresenter.LocalListUI {
    private RecyclerView recyclerView;
    private LocalLangSelectActivity.LocalsListAdapter adapter;
    private TopBar topBar;

    @Override
    protected int getContentView() {
        return R.layout.activity_local_select_layout;
    }

    @Override
    protected LocalLangSelectPresenter createPresenter() {
        return new LocalLangSelectPresenter();
    }

    @Override
    protected LocalLangSelectPresenter.LocalListUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        recyclerView = viewFinder.find(R.id.recyclerView);
        topBar = viewFinder.find(R.id.topBar);
        topBar.setLeftImg(R.mipmap.btn_head_back);
        topBar.setTitle(RateAndLocalManager.getStringById((R.string.string_local_language)));
        topBar.setTitle(getString(R.string.string_local_language));
        topBar.setLeftOnClickListener(view -> {
            finish();
        });
        /*topBar.setRightOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getPresenter().saveSelect();
                *//*Intent intent = new Intent(LocalLangSelectActivity.this, MainActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(intent);
                finish();*//*
                EventBus.getDefault().post(new LocaleChangeEvent());
                finish();
            }
        });*/
    }

    @Override
    protected void addEvent() {
        super.addEvent();

    }

    @Override
    public void showLocalList(final RateAndLocalManager.LocalKind[] locals) {
        if (locals == null) {
            return;
        }
        List<RateAndLocalManager.LocalKind> datas = Arrays.asList(locals);


        if (adapter == null) {
            adapter = new LocalsListAdapter(datas, this);

            adapter.isFirstOnly(false);
            recyclerView.setLayoutManager(new LinearLayoutManager(this));
            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(datas);
        }

        adapter.setOnItemClickListener((adapter1, view, position) -> {
            if(adapter1.getData()!=null && adapter1.getData().size()>0){
                RateAndLocalManager.LocalKind itemModel = (RateAndLocalManager.LocalKind)adapter1.getData().get(position);
                //mSelectActivity.getPresenter().setSelect(itemModel);
                changeLanguaeAction(itemModel);
            }

        });
    }

    public static class LocalsListAdapter extends BaseQuickAdapter<RateAndLocalManager.LocalKind,BaseViewHolder> {
        LocalLangSelectActivity mSelectActivity;

        LocalsListAdapter(List<RateAndLocalManager.LocalKind> data, LocalLangSelectActivity selectActivity) {
            super(R.layout.item_rate_list_layout, data);
            mSelectActivity = selectActivity;
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final RateAndLocalManager.LocalKind itemModel) {

            baseViewHolder.setText(R.id.item_value, itemModel.name);

            if (itemModel.name.equalsIgnoreCase(mSelectActivity.getPresenter().mSelectLocal.name))
                baseViewHolder.getConvertView().findViewById(R.id.item_selected).setBackgroundResource(R.mipmap.icon_checkbox_checked);
            else
                baseViewHolder.getConvertView().findViewById(R.id.item_selected).setBackgroundResource(R.mipmap.icon_checkbox_normal);
        }


    }

    //语言切换
    private  void changeLanguaeAction(RateAndLocalManager.LocalKind itemModel){
        showProgressDialog("","");
        getPresenter().setSelect(itemModel);
        adapter.notifyDataSetChanged();
        getPresenter().saveSelectLanguage();
        AppConfigManager.GetInstance().getAppConfig(null);   //切换语言刷新一下Config接口币对信息
        //EventBus.getDefault().post(new LocaleChangeEvent());
        BHexApplication.getMainHandler().postDelayed(()->{
            //finish();
            Intent intent = new Intent(LocalLangSelectActivity.this, MainActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
            finish();
            dismissProgressDialog();
        },500);

    }

}
