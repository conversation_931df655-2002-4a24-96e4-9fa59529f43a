package io.bhex.app.account.utils;

import android.content.Context;

import io.bhex.app.R;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-10-31
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class AccountUtil {

    /**
     * 获取账户名称
     * @param context
     * @param accountType
     * @return
     */
    public static String getAccountTypeName(Context context, Integer accountType) {
        String accountName = "";
        if (accountType == 1) {
            accountName = context.getResources().getString(R.string.string_account_bb);
        }else if(accountType == 2) {
            accountName = context.getResources().getString(R.string.string_account_option);
        }else if(accountType == 3) {
            accountName = context.getResources().getString(R.string.string_account_contract);
        }else if(accountType == 27) {
            accountName = context.getResources().getString(R.string.string_account_margin);
        }
        return accountName;
    }
}
