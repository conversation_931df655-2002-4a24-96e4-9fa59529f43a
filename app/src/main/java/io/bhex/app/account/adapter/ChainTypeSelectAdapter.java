/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: ChainTypeSelectAdapter.java
 *   @Date: 19-8-9 下午2:31
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.adapter;

import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.sdk.quote.bean.ChainType;

public class ChainTypeSelectAdapter extends BaseQuickAdapter<ChainType, BaseViewHolder> {

    public ChainTypeSelectAdapter(@Nullable List<ChainType> data) {
        super(R.layout.item_chain_type_list_layout, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, ChainType item) {
        String name = item.getChainType();
        if (!TextUtils.isEmpty(name)) {
            helper.setText(R.id.item_name, name);
        }
        helper.addOnClickListener(R.id.item_name);
        helper.setChecked(R.id.item_name,item.isSelect());
    }
}
