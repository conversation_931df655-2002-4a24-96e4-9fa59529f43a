/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AuthenticateSubmitPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import android.os.Handler;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.account.LoginApi;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.config.ConfigApi;
import io.bhex.sdk.config.bean.KycInfoConfigResponse;

public class AuthenticateSubmitPresenter extends BasePresenter<AuthenticateSubmitPresenter.AuthenticateSubmitUI> {
    public void getUserInfo() {
        LoginApi.GetUserInfo(new SimpleResponseListener<UserInfoBean>(){
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(UserInfoBean data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data,true)) {
                    //保存用户数据
                    UserManager.getInstance().saveUserInfo(data);
                    getUI().requestUserInfoSuccess(data);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public interface AuthenticateSubmitUI extends AppUI{

        void requestUserInfoSuccess(UserInfoBean data);

        void updateKycConfig(KycInfoConfigResponse response);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, AuthenticateSubmitUI ui) {
        super.onUIReady(activity, ui);
        getKYCConfigInfo();
    }

    private void getKYCConfigInfo() {
        ConfigApi.getCustomKV("cust.kycSettings",new SimpleResponseListener<KycInfoConfigResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(KycInfoConfigResponse response) {
                super.onSuccess(response);
                if (response != null) {
                    getUI().updateKycConfig(response);
                }
            }

            @Override
            public KycInfoConfigResponse parserResponse(Handler uiHandler, String response, Class<KycInfoConfigResponse> clazz) {
                response = response.replace("cust.kycSettings","kycSettings");
//                KycInfoConfigResponse kycInfoConfigResponse = Convert.fromJson(response, clazz);
                return super.parserResponse(uiHandler, response, clazz);
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });

    }
}
