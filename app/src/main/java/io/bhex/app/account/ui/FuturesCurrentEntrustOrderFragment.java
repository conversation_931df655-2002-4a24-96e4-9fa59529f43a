/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FuturesCurrentEntrustOrderFragment.java
 *   @Date: 19-6-25 上午11:12
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.view.View;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.flyco.tablayout.SegmentTabLayout;
import com.flyco.tablayout.listener.OnTabSelectListener;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.presenter.CurrentFuturesEntrustOrderFragmentPresenter;
import io.bhex.app.base.BaseListFreshFragment;
import io.bhex.app.trade.adapter.FuturesEntrustOrderAdapter;
import io.bhex.app.utils.KlineUtils;
import io.bhex.sdk.enums.ORDER_ENTRUST_TYPE;
import io.bhex.sdk.trade.futures.bean.FuturesOrderResponse;

public class FuturesCurrentEntrustOrderFragment extends BaseListFreshFragment<CurrentFuturesEntrustOrderFragmentPresenter, CurrentFuturesEntrustOrderFragmentPresenter.CurrentFuturesEntrustOrderFragmentUI> implements CurrentFuturesEntrustOrderFragmentPresenter.CurrentFuturesEntrustOrderFragmentUI {
    private SegmentTabLayout entrustTab;

    @Override
    protected CurrentFuturesEntrustOrderFragmentPresenter.CurrentFuturesEntrustOrderFragmentUI getUI() {
        return this;
    }

    @Override
    protected CurrentFuturesEntrustOrderFragmentPresenter createPresenter() {
        return new CurrentFuturesEntrustOrderFragmentPresenter();
    }

    @Override
    protected void initViews() {
        super.initViews();
        viewFinder.find(R.id.tabLinear).setVisibility(View.VISIBLE);
        entrustTab = viewFinder.find(R.id.tabEntrust);
        entrustTab.setTabData(new String[]{getString(R.string.string_ordinary_entrument),getString(R.string.string_planning_entrument),getString(R.string.string_stop_profit_stop_loss)});

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        entrustTab.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                if (position ==0) {
                    //普通委托
                    getPresenter().getOrdinaryData(false);
                }else if(position ==1){
                    //计划委托
                    getPresenter().getPlanningData(false);
                }else if(position ==2){
                    //止盈止损
                    getPresenter().getStopProfitLossData(false);
                }
            }

            @Override
            public void onTabReselect(int position) {

            }
        });
    }

    @Override
    public void showOrders(String entrustType, List<FuturesOrderResponse> currentOrders) {
        if (entrustType.equals(ORDER_ENTRUST_TYPE.LIMIT.getEntrustType())) {
            if (entrustTab.getCurrentTab()!=0) {
                //如果订单为普通委托，但是当前tab不在委托列表，不展示
                return;
            }
        }
        if (entrustType.equals(ORDER_ENTRUST_TYPE.STOP.getEntrustType())) {
            if (entrustTab.getCurrentTab()!=1) {
                //如果订单为计划委托，但是当前tab不在计划列表，不展示
                return;
            }
        }
        if (entrustType.equals(ORDER_ENTRUST_TYPE.STOP_PROFIT_OR_LOSS.getEntrustType())) {
            if (entrustTab.getCurrentTab()!=2) {
                //如果订单为止盈止损，但是当前tab不在止盈止损，不展示
                return;
            }
        }
        if (adapter == null) {
            adapter = new FuturesEntrustOrderAdapter(getActivity(), currentOrders);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this, recyclerView);
            adapter.setEnableLoadMore(true);

            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            recyclerView.setItemAnimator(new DefaultItemAnimator());

            adapter.setEmptyView(emptyView);
            recyclerView.setAdapter(adapter);
            adapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                    if (view.getId() == R.id.revoke_order) {
                        FuturesOrderResponse itemModel = (FuturesOrderResponse) adapter.getData().get(position);
                        if (KlineUtils.isStopCommonOrder(itemModel)) {
                            getPresenter().cancelOrder(System.currentTimeMillis() + "", itemModel.getOrderId(), itemModel.getType(), false);
                        }else{
                            getPresenter().cancelStopProfitLoss(System.currentTimeMillis() + "", itemModel);
                        }
                    }
                }
            });
        } else {
            adapter.setNewData(currentOrders);
        }
    }

}
