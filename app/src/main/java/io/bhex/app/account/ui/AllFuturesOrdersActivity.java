/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AllFuturesOrdersActivity.java
 *   @Date: 19-6-25 上午11:20
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.view.View;
import android.widget.EditText;
import android.widget.RadioGroup;

import androidx.core.util.Pair;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;

import java.util.ArrayList;

import io.bhex.app.R;
import io.bhex.app.account.presenter.AllFuturesOrderPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.event.OrderFilterEvent;
import io.bhex.app.skin.view.SkinTabLayout;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.view.TopBar;

public class AllFuturesOrdersActivity extends BaseActivity<AllFuturesOrderPresenter, AllFuturesOrderPresenter.AllFuturesOrderUI> implements AllFuturesOrderPresenter.AllFuturesOrderUI, View.OnClickListener, ViewPager.OnPageChangeListener {
    private SkinTabLayout tabLayout;
    private ViewPager viewPager;
    private ArrayList<Pair<String, Fragment>> items;
    private EntrustAdapter entrustAdapter;
    private TopBar topBar;
    private View revokeAllOrders;
    private View filterLayout;
    private boolean isShowFilter = false;
    private EditText baseTokenEt;
    private EditText quoteTokenEt;
    private RadioGroup orderStatusRadio;
    private RadioGroup priceModeRadio;
    private OrderFilterEvent filterEventCurrent = new OrderFilterEvent();
    private OrderFilterEvent filterEventHistory = new OrderFilterEvent();
    //默认是当前委托单列表
    private boolean isOpenOrders=true;
    //当前筛选条件
    private OrderFilterEvent currentFilterEvent;

    @Override
    protected int getContentView() {
        return R.layout.activity_all_futures_orders_layout;
    }

    @Override
    protected AllFuturesOrderPresenter createPresenter() {
        return new AllFuturesOrderPresenter();
    }

    @Override
    protected AllFuturesOrderPresenter.AllFuturesOrderUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        topBar.setLeftImg(R.mipmap.btn_head_back);
        topBar.setLeftOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        tabLayout = viewFinder.find(R.id.tabLayout);
        viewPager = viewFinder.find(R.id.viewPager);
        initTabs();
    }

    private void initTabs() {
        items = new ArrayList<>();

        items.add(new Pair<String, Fragment>(getString(R.string.string_current_entrust), new FuturesCurrentEntrustOrderFragment()));
        items.add(new Pair<String, Fragment>(getString(R.string.string_current_hold), new FuturesHoldOrderFragment()));
        items.add(new Pair<String, Fragment>(getString(R.string.string_history_entrust), new FuturesHistoryOrderFragment()));
        items.add(new Pair<String, Fragment>(getString(R.string.string_history_deal), new FuturesHistoryDealRecordFragment()));

        entrustAdapter = new AllFuturesOrdersActivity.EntrustAdapter(getSupportFragmentManager());
        viewPager.setAdapter(entrustAdapter);
        tabLayout.setupWithViewPager(viewPager);
//        tab.setTabTextColors(getResources().getColor(R.color.color_white),getResources().getColor(R.color.color_black));
        tabLayout.setTabMode(TabLayout.MODE_SCROLLABLE);
        tabLayout.setTabGravity(TabLayout.GRAVITY_CENTER);

        viewPager.addOnPageChangeListener(this);
        CommonUtil.setUpIndicatorWidthByReflex(tabLayout,15,15);
    }


    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {

    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    @Override
    public void onClick(View v) {

    }

    private class EntrustAdapter extends FragmentPagerAdapter {

        public EntrustAdapter(FragmentManager fm) {
            super(fm);
        }

        @Override
        public Fragment getItem(int position) {
            return items.get(position).second;
        }

        @Override
        public int getCount() {
            return items.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return items.get(position).first;
        }

    }
}
