package io.bhex.app.account.ui;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.TextView;

import io.bhex.app.R;
import io.bhex.app.account.presenter.ChangeEmailPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.safe.DeepKnowVerify;
import io.bhex.app.safe.DeepSEListener;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.app.view.InputView;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;

/**
 * *******************************************************************
 *
 * @项目名称: BHEX Android
 * @文件名称: ChangeEmailActivity
 * @Date: 2020/10/14 下午6:00
 * @Author: ppzhao
 * @Copyright（C）: 2020 BlueHelix Inc.   All rights reserved.
 * 注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 * *******************************************************************
 **/
public class ChangeEmailActivity extends BaseActivity<ChangeEmailPresenter, ChangeEmailPresenter.ChangeEmailUI> implements ChangeEmailPresenter.ChangeEmailUI, View.OnClickListener {
    private InputView inputVerifyMobile;
    private InputView inputVerifyEmail;
    private InputView inputAccount;
    private InputView inputEmail;
    private DeepKnowVerify deepKnowVerify;
    private TextView sendVerifyCodeTvOfEmail;
    private TextView sendVerifyCodeTvOfMobile;
    private InputView inputOldEmail;
    private InputView inputOldVerifyEmail;
    private TextView sendVerifyCodeTvOfOldEmail;

    @Override
    protected int getContentView() {
        return R.layout.activity_layout_change_email;
    }

    @Override
    protected ChangeEmailPresenter createPresenter() {
        return new ChangeEmailPresenter();
    }

    @Override
    protected ChangeEmailPresenter.ChangeEmailUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        deepKnowVerify = DeepKnowVerify.getInstance(this);
        inputOldEmail = viewFinder.find(R.id.old_email_input);
        inputEmail = viewFinder.find(R.id.email_input);
        inputAccount = viewFinder.find(R.id.account_input);
        inputVerifyMobile = viewFinder.find(R.id.mobile_verify_code_et);
        inputVerifyMobile.setPaddingRight(PixelUtils.dp2px(80));
        inputVerifyEmail = viewFinder.find(R.id.email_verify_code_et);
        inputVerifyEmail.setPaddingRight(PixelUtils.dp2px(80));
        inputOldVerifyEmail = viewFinder.find(R.id.old_email_verify_code_et);
        inputOldVerifyEmail.setPaddingRight(PixelUtils.dp2px(80));
        sendVerifyCodeTvOfOldEmail = viewFinder.textView(R.id.get_old_email_verify_code);
        sendVerifyCodeTvOfEmail = viewFinder.textView(R.id.get_email_verify_code);
        sendVerifyCodeTvOfMobile = viewFinder.textView(R.id.get_mobile_verify_code);

        UserInfoBean userInfo = UserManager.getInstance().getUserInfo();
        if (userInfo != null) {
            String oldEmaile = userInfo.getEmail();
            String mobile = userInfo.getMobile();
            inputOldEmail.setInputString(oldEmaile);
            inputAccount.setInputString(mobile);
        }
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        deepKnowVerify.ignoreDPView(viewFinder.find(R.id.get_email_verify_code),"bindemail");
        viewFinder.find(R.id.get_old_email_verify_code).setOnClickListener(this);
        viewFinder.find(R.id.get_email_verify_code).setOnClickListener(this);
        viewFinder.find(R.id.get_mobile_verify_code).setOnClickListener(this);
        viewFinder.find(R.id.btn_sure).setOnClickListener(this);

        inputEmail.addTextWatch(mTextWatcher);
        inputVerifyEmail.addTextWatch(mTextWatcher);
        inputVerifyMobile.addTextWatch(mTextWatcher);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.get_old_email_verify_code:
                getPresenter().sendOldEmailCode();

                break;
            case R.id.get_mobile_verify_code:
                getPresenter().sendMobileCode();

                break;
            case R.id.get_email_verify_code:
                String email = inputEmail.getInputString();
                if (TextUtils.isEmpty(email)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.input_email));
                    return;
                } else {
                    inputEmail.setError("");
                }
                if (!NetWorkStatus.isConnected(this)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.hint_network_not_connect));
                    return;
                }
                getUI().showProgressDialog("","");
                deepKnowVerify.verify(baseSEListener);
                break;
            case R.id.btn_sure:

                String oldEmailCode = inputOldVerifyEmail.getInputString();
                if (TextUtils.isEmpty(oldEmailCode)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.string_input_old_email_verify_code));
                    return;
                } else {
                    inputOldVerifyEmail.setError("");
                }

                String email2 = inputEmail.getInputString();
                if (TextUtils.isEmpty(email2)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.input_email));
                    return;
                } else {
                    inputEmail.setError("");
                }
                String emailCode = inputVerifyEmail.getInputString();
                if (TextUtils.isEmpty(emailCode)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.string_input_email_verify_code));
                    return;
                } else {
                    inputVerifyEmail.setError("");
                }
                String mobileCode = inputVerifyMobile.getInputString();
                if (TextUtils.isEmpty(mobileCode)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.string_input_mobile_verify_code));
                    return;
                } else {
                    inputVerifyMobile.setError("");
                }
                if (!NetWorkStatus.isConnected(this)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.hint_network_not_connect));
                    return;
                }
                getPresenter().requestChangeEmail(oldEmailCode,email2,emailCode,mobileCode);
                break;
        }
    }

    private DeepSEListener baseSEListener = new DeepSEListener() {

        /**
         * SDK内部show loading dialog
         */
        @Override
        public void onShowDialog() {
            getUI().dismissProgressDialog();
        }

        @Override
        public void onError(String errorCode, String error) {
//            DebugLog.i(TAG,"onError-->errorCode:"+errorCode+", error: "+error);
//            ToastUtils.showShort(error);
            getUI().dismissProgressDialog();
            ToastUtils.showShort(getString(R.string.string_net_exception)+errorCode);
        }

        /**
         * 验证码Dialog关闭
         * 1：webview的叉按钮关闭
         * 2：点击屏幕外关闭
         * 3：点击回退键关闭
         *
         * @param num
         */
        @Override
        public void onCloseDialog(int num) {
//            DebugLog.i(TAG, "onCloseDialog-->" + num);
        }

        /**
         * show 验证码webview
         */
        @Override
        public void onDialogReady() {
//            DebugLog.i(TAG,"onDialogReady-->SDK show captcha webview dialog! ");
        }

        /**
         * 验证成功
         * @param token
         */
        @Override
        public void onResult(String token) {
//            DebugLog.i(TAG,"onResult: "+token);
            getUI().dismissProgressDialog();
            final String email = inputEmail.getInputString();
            if (TextUtils.isEmpty(email)) {
                ToastUtils.showShort(ChangeEmailActivity.this, getResources().getString(R.string.input_email));
                return;
            } else {
                inputEmail.setError("");
            }
            if (!NetWorkStatus.isConnected(ChangeEmailActivity.this)) {
                ToastUtils.showShort(ChangeEmailActivity.this, getResources().getString(R.string.hint_network_not_connect));
                return;
            }
            getPresenter().requestEmailVerifyCodeOfBindEmail(email,token);

        }
    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        deepKnowVerify.destroy();
    }

    @Override
    public void setAuthTv(boolean isEmail,String s) {
        if (isEmail) {
            sendVerifyCodeTvOfOldEmail.setText(s);
        }else{
            sendVerifyCodeTvOfMobile.setText(s);
        }
    }

    @Override
    public void setAuthTvStatus(boolean isEmail,boolean b) {
        if (isEmail) {
            sendVerifyCodeTvOfOldEmail.setEnabled(b);
            sendVerifyCodeTvOfOldEmail.setTextColor(b ? getResources().getColor(R.color.blue) : SkinColorUtil.getDark50(this));
        }else{
            sendVerifyCodeTvOfMobile.setEnabled(b);
            sendVerifyCodeTvOfMobile.setTextColor(b ? getResources().getColor(R.color.blue) : SkinColorUtil.getDark50(this));
        }
    }

    @Override
    public void setAuthTv(String s) {
        sendVerifyCodeTvOfEmail.setText(s);
    }

    @Override
    public void setAuthTvStatus(boolean b) {
        sendVerifyCodeTvOfEmail.setEnabled(b);
        sendVerifyCodeTvOfEmail.setTextColor(b ? getResources().getColor(R.color.blue) : SkinColorUtil.getDark50(this));
    }

    /**
     * 编辑框监听器
     */
    private TextWatcher mTextWatcher = new TextWatcher() {

        /** 改变前*/
        @Override
        public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
        }

        /** 内容改变*/
        @Override
        public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            if (getPresenter().checkInputContentIsEmpty(inputEmail,inputVerifyEmail,inputVerifyMobile)) {
                viewFinder.find(R.id.btn_sure).setEnabled(true);
            } else {
                viewFinder.find(R.id.btn_sure).setEnabled(false);
            }

        }

        @Override
        public void afterTextChanged(Editable s) {

        }
    };
}
