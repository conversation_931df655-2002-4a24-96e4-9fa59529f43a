/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AssetFlowFragment.java
 *   @Date: 12/17/18 2:21 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.account.ui;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.adapter.AssetRecordAdapter;
import io.bhex.app.account.presenter.AssetFlowFragmentPresenter;
import io.bhex.app.base.BaseFragment;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.trade.bean.AssetRecordResponse;

public class AssetFlowFragment extends BaseFragment<AssetFlowFragmentPresenter,AssetFlowFragmentPresenter.AssetFlowFragmentUI> implements AssetFlowFragmentPresenter.AssetFlowFragmentUI, BaseQuickAdapter.RequestLoadMoreListener,BaseFragment.NotifyActivity {

    private RecyclerView recyclerView;
    private int tabType;
    private AssetRecordAdapter adapter;
    private View emptyView;
    private String tokenId;
    public static String KEY_ASSET_TOKENID = "tokenId";

    @Override
    protected AssetFlowFragmentPresenter.AssetFlowFragmentUI getUI() {
        return this;
    }

    @Override
    protected AssetFlowFragmentPresenter createPresenter() {
        return new AssetFlowFragmentPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.acitivity_asset_record_fragment,null,false);
    }

    @Override
    protected void initViews() {
        super.initViews();
        Bundle arguments = getArguments();
        if (arguments != null) {
            tabType = arguments.getInt(AppData.INTENT.KEY_RECORD_TYPE, 0);
            tokenId = arguments.getString(KEY_ASSET_TOKENID);
            getPresenter().getRecord(tabType,tokenId,false);
        }
        LinearLayout rootView = viewFinder.find(R.id.rootView);
        LayoutInflater layoutInflater = LayoutInflater.from(getActivity());
        emptyView = layoutInflater.inflate(R.layout.empty_layout, rootView, false);
        recyclerView = viewFinder.find(R.id.recyclerView);
    }

    @Override
    public void showRecordList(List<AssetRecordResponse.RecordBean> datas) {
        if(datas == null)
            return;

        if (adapter == null) {

            adapter = new AssetRecordAdapter(datas);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this,recyclerView);
            adapter.setEnableLoadMore(true);

//            swipeRefresh.setColorSchemeColors(Color.RED, Color.BLUE, Color.GREEN);
//            swipeRefresh.setOnRefreshListener(this);

            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            adapter.setEmptyView(emptyView);
            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(datas);
        }
    }

    @Override
    public void onLoadMoreRequested() {
        getPresenter().loadMore(tabType, tokenId,true);
    }
    @Override
    public void loadComplete() {
        if (adapter != null) {
            adapter.loadMoreComplete();
        }
    }

    @Override
    public void loadEnd() {
        if (adapter != null) {
            adapter.loadMoreEnd();
        }
    }

    @Override
    public void loadFailed() {
        if (adapter != null) {
            adapter.loadMoreFail();
        }
    }


    @Override
    public void onNotifyActivity() {
        if(getPresenter() != null)
            getPresenter().getRecord(tabType,tokenId,false);
    }
}
