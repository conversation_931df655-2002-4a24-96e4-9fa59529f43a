/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: WithDrawResultActivity.java
 *   @Date: 19-8-27 上午11:45
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.content.Intent;
import android.text.TextUtils;
import android.view.View;

import io.bhex.app.R;
import io.bhex.app.account.presenter.WithDrawResultPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.CommonUtil;
import io.bhex.sdk.trade.bean.AssetListResponse;

public class WithDrawResultActivity extends BaseActivity<WithDrawResultPresenter, WithDrawResultPresenter.WithDrawResultUI> implements WithDrawResultPresenter.WithDrawResultUI {
    @Override
    protected int getContentView() {
        return R.layout.activity_withdraw_result_layout;
    }

    @Override
    protected WithDrawResultPresenter createPresenter() {
        return new WithDrawResultPresenter();
    }

    @Override
    protected WithDrawResultPresenter.WithDrawResultUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        Intent intent = getIntent();
        if (intent != null) {
            AssetListResponse.BalanceBean balanceBean = (AssetListResponse.BalanceBean) intent.getSerializableExtra("assetItem");
            String withDrawAmount = intent.getStringExtra("withDrawAmount");
            String arrivalAmount = intent.getStringExtra("arrivalAmount");
            String address = intent.getStringExtra("address");
            String tag = intent.getStringExtra("tag");
            String fee = intent.getStringExtra("fee");
            String feeUnit = intent.getStringExtra("feeUnit");
            if (balanceBean != null) {
                String tokenName = balanceBean.getTokenName();
                viewFinder.textView(R.id.wd_b_name).setText(tokenName);
                viewFinder.textView(R.id.wd_arrival_amount).setText(arrivalAmount + " " + tokenName);
            }
            viewFinder.textView(R.id.wd_b_address).setText(address);
            if (!TextUtils.isEmpty(tag)) {
                viewFinder.find(R.id.tagRela).setVisibility(View.VISIBLE);
                viewFinder.textView(R.id.wd_b_tag).setText(tag);
            } else {
                viewFinder.find(R.id.tagRela).setVisibility(View.GONE);
            }
            viewFinder.textView(R.id.wd_b_fee).setText(fee + " " + feeUnit);

        }
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.wd_b_address).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CommonUtil.copyText(WithDrawResultActivity.this, viewFinder.textView(R.id.wd_b_address).getText().toString());
            }
        });
        viewFinder.find(R.id.wd_b_tag).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CommonUtil.copyText(WithDrawResultActivity.this, viewFinder.textView(R.id.wd_b_tag).getText().toString());
            }
        });

        viewFinder.find(R.id.btnBack).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                finish();
            }
        });
    }
}
