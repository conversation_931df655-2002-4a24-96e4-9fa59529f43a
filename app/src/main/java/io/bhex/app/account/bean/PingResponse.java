package io.bhex.app.account.bean;

import io.bhex.baselib.network.response.BaseResponse;

/**
 * *******************************************************************
 *
 * @项目名称: BHEX Android
 * @文件名称: PingResponse
 * @Date: 2020/9/10 上午12:39
 * @Author: ppzhao
 * @Copyright（C）: 2020 BlueHelix Inc.   All rights reserved.
 * 注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 * *******************************************************************
 **/
public class PingResponse extends BaseResponse {

    /**
     * pong : *************
     * sendTime : 0
     */

    private String pong;
    private long sendTime;

    public String getPong() {
        return pong;
    }

    public void setPong(String pong) {
        this.pong = pong;
    }

    public long getSendTime() {
        return sendTime;
    }

    public void setSendTime(long sendTime) {
        this.sendTime = sendTime;
    }
}
