/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: TwoVerificaionPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import android.os.CountDownTimer;
import android.text.TextUtils;

import io.bhex.app.R;
import io.bhex.sdk.account.LoginApi;
import io.bhex.sdk.account.bean.OrderParamResponse;
import io.bhex.app.base.AppUI;
import io.bhex.sdk.Urls;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.network.BParamsBuilder;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.app.view.InputView;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.HttpUtils;
import io.bhex.baselib.network.exception.NetException;
import io.bhex.baselib.network.params.IParams;
import io.bhex.baselib.network.response.UISafeKeeper;
import io.bhex.baselib.utils.ToastUtils;



public class TwoVerificaionPresenter extends BasePresenter<TwoVerificaionPresenter.TwoVerificaionUI> {
    private String orderIdOfEmail="";
    private String orderIdOfMobile="";

    /**
     * 未登录状态验证
     * @param isVerifyEmail
     * @param requestId
     */
    public void verifyBeforLogin(final boolean isVerifyEmail, String requestId) {
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }
        LoginApi.RequestVerifyBeforeLogin(isVerifyEmail, requestId, UISafeKeeper.guard(getUI(), new SimpleResponseListener<OrderParamResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("",getString(R.string.string_loading_hint_text));
            }

            @Override
            public void onSuccess(OrderParamResponse data) {
                super.onSuccess(data);

                if (CodeUtils.isSuccess(data,true)) {
                    if (isVerifyEmail) {
                        orderIdOfEmail = data.getOrderId();
                        getUI().updateOrderId(orderIdOfEmail);
                    }else{
                        orderIdOfMobile = data.getOrderId();
                        getUI().updateOrderId(orderIdOfMobile);
                    }
                    getUI().setAuthTvStatus(false);
                    timer.start();
                }

            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                if (error instanceof NetException) {
                    NetException nt = (NetException)error;
//                    ToastUtils.showShort(getActivity(),nt.getCode()+":"+nt.getShowMessage());
                }else{

//                    ToastUtils.showShort(getActivity(),getResources().getString(R.string.server_error));
                }
            }
        }));
    }

    /**
     * 已登录状态验证
     * @param isVerifyEmail
     * @param token
     */
    public void verifyAfterLogin(final boolean isVerifyEmail, String token, String type) {
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }

        LoginApi.RequestVerifyAfterLogin(isVerifyEmail,type, UISafeKeeper.guard(getUI(), new SimpleResponseListener<OrderParamResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("",getString(R.string.string_loading_hint_text));
            }

            @Override
            public void onSuccess(OrderParamResponse data) {
                super.onSuccess(data);

                if (CodeUtils.isSuccess(data,true)) {
                    if (isVerifyEmail) {
                        orderIdOfEmail = data.getOrderId();
                        getUI().updateOrderId(orderIdOfEmail);
                    }else{
                        orderIdOfMobile = data.getOrderId();
                        getUI().updateOrderId(orderIdOfMobile);
                    }
                    getUI().setAuthTvStatus(false);
                    timer.start();
                }

            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                if (error instanceof NetException) {
                    NetException nt = (NetException)error;
//                    ToastUtils.showShort(getActivity(),nt.getCode()+":"+nt.getShowMessage());
                }else{

//                    ToastUtils.showShort(getActivity(),getResources().getString(R.string.server_error));
                }
            }
        }));
    }

    /**
     * 提交验证结果
     * @param isSelectGAVerify
     * @param isVerifyEmail
     * @param verifyEt
     */
    public void submit(boolean isSelectGAVerify, boolean isVerifyEmail, InputView verifyEt, String orderId) {
        if (!isSelectGAVerify&&TextUtils.isEmpty(orderId)) {
            ToastUtils.showShort(getActivity(), getString(R.string.string_verify_code_invalid));
            return;
        }

        String verifyCode = verifyEt.getInputString();
        if (TextUtils.isEmpty(verifyCode)) {
            ToastUtils.showShort(getActivity(), getString(R.string.input_verify));
            return;
        }



    }

    public interface TwoVerificaionUI extends AppUI{

        void setAuthTv(String s);

        void setAuthTvStatus(boolean b);

        void updateOrderId(String orderIdOfEmail);
    }



    /**
     * 倒计时
     */
    private CountDownTimer timer = new CountDownTimer(AppData.DOWN_TIME_CODE, AppData.DOWN_TIME_INTERVAL_CODE) {
        @Override
        public void onTick(long millisUntilFinished) {
            getUI().setAuthTv((millisUntilFinished / 1000)
                    + getActivity().getResources().getString(
                    R.string.after_second));
        }

        @Override
        public void onFinish() {
            getUI().setAuthTvStatus(true);
            getUI().setAuthTv(getResources().getString(
                    R.string.string_get_auth_code));
        }
    };
}
