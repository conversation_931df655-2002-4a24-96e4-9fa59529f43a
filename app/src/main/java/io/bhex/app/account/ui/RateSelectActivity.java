/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: RateSelectActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.Arrays;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.presenter.RateSelectPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.sdk.data_manager.RateAndLocalManager;
import io.bhex.app.view.TopBar;

public class RateSelectActivity extends BaseActivity<RateSelectPresenter,
        RateSelectPresenter.RatesListUI> implements RateSelectPresenter.RatesListUI {
    private RecyclerView recyclerView;
    private RatesListAdapter adapter;
    private TopBar topBar;

    @Override
    protected int getContentView() {
        return R.layout.activity_rate_select_layout;
    }

    @Override
    protected RateSelectPresenter createPresenter() {
        return new RateSelectPresenter();
    }

    @Override
    protected RateSelectPresenter.RatesListUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        recyclerView = viewFinder.find(R.id.recyclerView);
        topBar = viewFinder.find(R.id.topBar);
        topBar.setLeftImg(R.mipmap.btn_head_back);
        //topBar.setTitle(RateAndLocalManager.GetInstance(this).getStringById(R.string.string_rate));
        topBar.setTitle(getString(R.string.string_rate));
        topBar.setLeftOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        topBar.setRightOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getPresenter().saveSelect();
                finish();
            }
        });
    }

    @Override
    protected void addEvent() {
        super.addEvent();

    }

    @Override
    public void showRateList(final RateAndLocalManager.RateKind[] rates) {
        if (rates == null) {
            return;
        }
        List<RateAndLocalManager.RateKind> datas = Arrays.asList(rates);


        if (adapter == null) {
            adapter = new RatesListAdapter(datas , this);

            adapter.isFirstOnly(false);
            recyclerView.setLayoutManager(new LinearLayoutManager(this));
            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(datas);
        }
    }

    public static class RatesListAdapter extends BaseQuickAdapter<RateAndLocalManager.RateKind,BaseViewHolder> {
        RateSelectActivity mRateSelectActivity;

        RatesListAdapter(List<RateAndLocalManager.RateKind> data, RateSelectActivity rateSelectActivity) {
            super(R.layout.item_rate_list_layout, data);
            mRateSelectActivity = rateSelectActivity;
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final RateAndLocalManager.RateKind itemModel) {

            baseViewHolder.setText(R.id.item_value,itemModel.name);

            if(!RateAndLocalManager.RateKind.USD.name.equalsIgnoreCase(itemModel.name)) {
                baseViewHolder.getConvertView().findViewById(R.id.item_tip).setVisibility(View.VISIBLE);
                baseViewHolder.setText(R.id.item_tip, "1 " + RateAndLocalManager.RateKind.USD.symbol + " ≈ " + itemModel.priceToUSD + " " + itemModel.symbol);
            }
            if(itemModel.name.equalsIgnoreCase(mRateSelectActivity.getPresenter().mSelectRate.name))
                baseViewHolder.getConvertView().findViewById(R.id.item_selected).setBackgroundResource(R.mipmap.icon_checkbox_checked);
            else
                baseViewHolder.getConvertView().findViewById(R.id.item_selected).setBackgroundResource(R.mipmap.icon_checkbox_normal);

            baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mRateSelectActivity.getPresenter().setSelect(itemModel);
                    notifyDataSetChanged();
                }

            });
        }
    }
}
