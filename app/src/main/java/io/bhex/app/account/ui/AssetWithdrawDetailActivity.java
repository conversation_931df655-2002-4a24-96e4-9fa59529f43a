/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AssetWithdrawDetailActivity.java
 *   @Date: 12/17/18 3:39 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.account.ui;

import android.content.Intent;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.Toolbar;

import com.google.android.material.appbar.CollapsingToolbarLayout;

import io.bhex.app.R;
import io.bhex.app.account.presenter.AssetWithdrawDetailPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.sdk.trade.bean.RecordBeanResponse;

public class AssetWithdrawDetailActivity extends BaseActivity<AssetWithdrawDetailPresenter, AssetWithdrawDetailPresenter.AssetWithdrawDetailUI> implements AssetWithdrawDetailPresenter.AssetWithdrawDetailUI, View.OnClickListener {


    private RecordBeanResponse.RecordItem mData;
    private int mType;
    @Override
    protected int getContentView() {
        return R.layout.activity_asset_withdraw_detail_layout;
    }

    @Override
    protected AssetWithdrawDetailPresenter createPresenter() {
        return new AssetWithdrawDetailPresenter();
    }

    @Override
    protected AssetWithdrawDetailPresenter.AssetWithdrawDetailUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();


        Toolbar toolbar = findViewById(R.id.toolbar);
        toolbar.setTitleTextColor(SkinColorUtil.getDark(this));
        CollapsingToolbarLayout collapsingToolbarLayout= findViewById(R.id.collapsing_toolbar);
        // 硬编码黑白版Toolbar标题栏title字色
        collapsingToolbarLayout.setCollapsedTitleTextColor(SkinColorUtil.getDark(this));
        collapsingToolbarLayout.setExpandedTitleColor(SkinColorUtil.getDark(this));

        //显示返回按钮
        setSupportActionBar(toolbar);
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setDisplayHomeAsUpEnabled(true);
//            actionBar.setDisplayShowTitleEnabled(false);
        }

        Intent intent = getIntent();

        if (intent != null) {
            mData = (RecordBeanResponse.RecordItem) intent.getSerializableExtra("RecordItem");
            mType = intent.getIntExtra("type", 0);
            if (mData != null) {
                viewFinder.textView(R.id.status_value).setText(mData.getStatusDesc());
                viewFinder.textView(R.id.fee_value).setText(mData.fee + " " + mData.feeTokenName);
                viewFinder.textView(R.id.date_value).setText(DateUtils.getSimpleTimeFormat(Long.valueOf(mData.getTime()), "HH:mm:ss yyyy/MM/dd"));

                viewFinder.textView(R.id.address_value).setText(mData.getAddress());
                viewFinder.textView(R.id.address_value).setTextIsSelectable(true);
                viewFinder.textView(R.id.tid_value).setText(mData.txid);
                viewFinder.textView(R.id.tid_value).setTextIsSelectable(true);
                if(TextUtils.isEmpty(mData.txid) && mData.isInternalTransfer == true) {
                    if (mData.isSameBroker) {
                        // 目前只是充币能判断，提币的txid直接显示--
                        viewFinder.textView(R.id.tid_value).setText(getString(R.string.string_wallet_internal_transfer));
                    } else {
                        viewFinder.textView(R.id.tid_value).setText(getString(R.string.string_placeholder));
                    }
                }
                viewFinder.textView(R.id.progress_value).setText(mData.confirmNum + "/" + mData.requiredConfirmNum);

                viewFinder.textView(R.id.deal_date_value).setText(DateUtils.getSimpleTimeFormat(Long.valueOf(mData.walletHandleTime), "HH:mm:ss yyyy/MM/dd"));
                if(TextUtils.isEmpty(mData.addressExt)){
                    viewFinder.find(R.id.tag_value).setVisibility(View.GONE);
                    viewFinder.find(R.id.tag).setVisibility(View.GONE);
                }
                else {
                    viewFinder.textView(R.id.tag_value).setText(mData.addressExt);
                    viewFinder.textView(R.id.tag_value).setTextIsSelectable(true);
                }

                viewFinder.textView(R.id.kernel_id_value).setTextIsSelectable(true);

            }
            if(mType == 0){
                collapsingToolbarLayout.setTitle("+ " + mData.getQuantity() + mData.tokenName);
                viewFinder.textView(R.id.type_value).setText(getString(R.string.string_recharge_coin));
                viewFinder.find(R.id.fee_value).setVisibility(View.GONE);
                viewFinder.find(R.id.fee).setVisibility(View.GONE);
                viewFinder.find(R.id.kernel_id).setVisibility(View.GONE);
                viewFinder.find(R.id.kernel_id_value).setVisibility(View.GONE);
                viewFinder.find(R.id.account_id).setVisibility(mData.isInternalTransfer ? View.VISIBLE : View.GONE);
                viewFinder.find(R.id.account_id_value).setVisibility(mData.isInternalTransfer ? View.VISIBLE : View.GONE);
                String fromAddressAccountId = mData.getFromAddress();
                viewFinder.textView(R.id.account_id_value).setText(fromAddressAccountId);
                //viewFinder.find(R.id.tid_value).setVisibility(View.GONE);
                //viewFinder.find(R.id.tid).setVisibility(View.GONE);
                //viewFinder.find(R.id.deal_date_value).setVisibility(View.GONE);
                //viewFinder.find(R.id.deal_date).setVisibility(View.GONE);
            }
            else {
                viewFinder.find(R.id.progress).setVisibility(View.GONE);
                viewFinder.find(R.id.progress_value).setVisibility(View.GONE);
                viewFinder.textView(R.id.type_value).setText(getString(R.string.string_withdraw_coin));
                collapsingToolbarLayout.setTitle("- " + mData.getQuantity() + mData.tokenName);
                final String url = mData.kernelId;
                if(TextUtils.isEmpty(url)){
                    viewFinder.find(R.id.kernel_id).setVisibility(View.GONE);
                    viewFinder.find(R.id.kernel_id_value).setVisibility(View.GONE);
                }
                else {
                    viewFinder.find(R.id.kernel_id).setVisibility(View.VISIBLE);
                    viewFinder.find(R.id.kernel_id_value).setVisibility(View.VISIBLE);
                    viewFinder.textView(R.id.kernel_id_value).setText(url);
                }
                viewFinder.find(R.id.account_id).setVisibility(View.GONE);
                viewFinder.find(R.id.account_id_value).setVisibility(View.GONE);

                if (mType == 1) {//提币
                    //提币备注
                    String remarks = mData.getRemarks();
                    if (!TextUtils.isEmpty(remarks)) {
                        viewFinder.find(R.id.remark).setVisibility(View.VISIBLE);
                        viewFinder.find(R.id.remark_content).setVisibility(View.VISIBLE);
                        viewFinder.textView(R.id.remark_content).setText(remarks);
                    }
                    //提币拒绝原因
                    String failedReasonDesc = mData.getFailedReasonDesc();
                    if (!TextUtils.isEmpty(failedReasonDesc)) {
                        viewFinder.find(R.id.refuseReasonRela).setVisibility(View.VISIBLE);
                        viewFinder.textView(R.id.refuseReason).setText(mData.getFailedReasonDesc());
                    }
                }
            }
        }

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.address_value).setOnClickListener(this);
        viewFinder.find(R.id.tag_value).setOnClickListener(this);
        viewFinder.find(R.id.tid_value).setOnClickListener(this);
        viewFinder.find(R.id.kernel_id_value).setOnClickListener(this);
        viewFinder.find(R.id.account_id_value).setOnClickListener(this);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()){
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.address_value:
                CommonUtil.copyText(this,viewFinder.textView(R.id.address_value).getText().toString());
                break;
            case R.id.tag_value:
                CommonUtil.copyText(this,viewFinder.textView(R.id.tag_value).getText().toString());
                break;
            case R.id.tid_value:
                CommonUtil.copyText(this,viewFinder.textView(R.id.tid_value).getText().toString());
                break;
            case R.id.kernel_id_value:
                CommonUtil.copyText(this,viewFinder.textView(R.id.kernel_id_value).getText().toString());
                break;
            case R.id.account_id_value:
                CommonUtil.copyText(this,viewFinder.textView(R.id.account_id_value).getText().toString());
                break;
        }
    }
}
