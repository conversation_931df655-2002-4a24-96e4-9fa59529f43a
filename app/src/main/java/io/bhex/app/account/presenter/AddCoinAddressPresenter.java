/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AddCoinAddressPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.presenter;

import android.text.TextUtils;
import android.widget.EditText;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.network.response.UISafeKeeper;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.LoginApi;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.trade.AssetApi;
import io.bhex.sdk.trade.bean.AddWithdrawAddressRequest;
import io.bhex.sdk.trade.bean.TwoVerifyBean;

public class AddCoinAddressPresenter extends BasePresenter<AddCoinAddressPresenter.AddCoinAddressUI> {
    public void getUserInfo() {
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }
        LoginApi.GetUserInfo(new SimpleResponseListener<UserInfoBean>(){
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(UserInfoBean data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data,true)) {
                    //保存用户数据
                    UserManager.getInstance().saveUserInfo(data);
                    getUI().requestUserInfoSuccess(data);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    /**
     * 添加地址
     * @param isNeedChainSelect
     * @param tokenId
     * @param selectChainType
     * @param addressEt
     * @param addressRemarkEt
     * @param verifyBean
     */
    public void addAddress(boolean isNeedChainSelect, String tokenId, String selectChainType, EditText addressEt, EditText addressRemarkEt, EditText addressExtEt, TwoVerifyBean verifyBean) {
        String address = addressEt.getText().toString().trim();
        if (TextUtils.isEmpty(address)) {
            ToastUtils.showShort(getActivity(), getString(R.string.input_address));
            return;
        }
        String remark = addressRemarkEt.getText().toString().trim();
        if (TextUtils.isEmpty(remark)) {
            ToastUtils.showShort(getActivity(), getString(R.string.input_remark));
            return;
        }
        if (isNeedChainSelect) {
            if (TextUtils.isEmpty(selectChainType)) {
                ToastUtils.showShort(getActivity(),getString(R.string.hint_select_chain_please));
                return;
            }
        }
        AddWithdrawAddressRequest requestData = new AddWithdrawAddressRequest();
        requestData.tokenId = tokenId;
        requestData.address = address;
        requestData.addressRemark = remark;
        if(!TextUtils.isEmpty(addressExtEt.getText().toString()))
            requestData.address_ext = addressExtEt.getText().toString();
        requestData.verifyBean = new TwoVerifyBean();
        requestData.verifyBean.setAuth_type(verifyBean.getAuth_type());
        requestData.verifyBean.setOrder_id(verifyBean.getOrder_id());
        requestData.verifyBean.setVerify_code(verifyBean.getVerify_code());
        requestData.chainType = selectChainType;

        AssetApi.RequestAddWithdrawAddress(requestData, UISafeKeeper.guard(getUI(), new SimpleResponseListener<ResultResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    ToastUtils.showShort(getActivity(), getString(R.string.string_add_address_success));
                    getActivity().finish();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        }));

    }

    public interface AddCoinAddressUI extends AppUI{

        void requestUserInfoSuccess(UserInfoBean data);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, AddCoinAddressUI ui) {
        super.onUIReady(activity, ui);
    }
}
