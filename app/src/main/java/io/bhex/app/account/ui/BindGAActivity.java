/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BindGAActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.account.ui;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.Toolbar;

import com.google.android.material.appbar.CollapsingToolbarLayout;

import io.bhex.app.R;
import io.bhex.app.account.presenter.BindGAPresenter;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.app.safe.DeepKnowVerify;
import io.bhex.app.safe.DeepSEListener;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.app.view.InputView;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;

/**
 * ================================================
 * 描   述：绑定GA
 * ================================================
 */

public class BindGAActivity extends BaseActivity<BindGAPresenter,BindGAPresenter.BindGAUI> implements BindGAPresenter.BindGAUI, View.OnClickListener {

    private InputView inputAccount;
    private InputView inputVerifyCode;
    private InputView inputGoogleCode;
    private boolean isEmail;
    private DeepKnowVerify deepKnowVerify;
    private TextView sendVerifyCodeTv;

    @Override
    protected int getContentView() {
        return R.layout.activity_bind_ga_layout;
    }

    @Override
    protected BindGAPresenter createPresenter() {
        return new BindGAPresenter();
    }

    @Override
    protected BindGAPresenter.BindGAUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        deepKnowVerify = DeepKnowVerify.getInstance(this);
        Toolbar toolbar= findViewById(R.id.toolbar);
        CollapsingToolbarLayout collapsingToolbarLayout= findViewById(R.id.collapsing_toolbar);
        // 硬编码黑白版Toolbar标题栏title字色
        collapsingToolbarLayout.setCollapsedTitleTextColor(SkinColorUtil.getDark(this));
        collapsingToolbarLayout.setExpandedTitleColor(SkinColorUtil.getDark(this));

        //显示返回按钮
        setSupportActionBar(toolbar);
        ActionBar actionBar=getSupportActionBar();
        if (actionBar!=null){
            actionBar.setDisplayHomeAsUpEnabled(true);
        }
        inputGoogleCode = viewFinder.find(R.id.google_input);
        inputVerifyCode = viewFinder.find(R.id.verify_code_et);
        inputAccount = viewFinder.find(R.id.account_input);

        inputVerifyCode = viewFinder.find(R.id.verify_code_et);
        inputVerifyCode.setPaddingRight(PixelUtils.dp2px(80));
        sendVerifyCodeTv = viewFinder.textView(R.id.get_verify_code);
        UserInfoBean userInfo = UserManager.getInstance().getUserInfo();
        if (userInfo != null) {
            if (userInfo.getRegisterType()==1) {
                isEmail = false;
                String mobile = userInfo.getMobile();
                inputAccount.setInputString(mobile);
            }else{
                isEmail = true;
                String email = userInfo.getEmail();
                inputAccount.setInputString(email);
            }
        }

    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()){
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        deepKnowVerify.ignoreDPView(viewFinder.find(R.id.get_verify_code),"bindga");
        viewFinder.find(R.id.btn_sure).setOnClickListener(this);
        viewFinder.find(R.id.get_verify_code).setOnClickListener(this);
        inputGoogleCode.addTextWatch(mTextWatcher);
        inputVerifyCode.addTextWatch(mTextWatcher);

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.btn_sure:
                final String verifyCode = inputVerifyCode.getInputString();
                if (TextUtils.isEmpty(verifyCode)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.input_verify));
                    return;
                } else {
                    inputVerifyCode.setError("");
                }
                final String googleVerifyCode = inputGoogleCode.getInputString();
                if (TextUtils.isEmpty(googleVerifyCode)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.input_google_verify_code));
                    return;
                } else {
                    inputGoogleCode.setError("");
                }
                if (!NetWorkStatus.isConnected(BindGAActivity.this)) {
                    ToastUtils.showShort(this, getResources().getString(R.string.hint_network_not_connect));
                    return;
                }
                getPresenter().requestBindGA(verifyCode,googleVerifyCode);

                break;
            case R.id.get_verify_code:
                getUI().showProgressDialog("","");
                deepKnowVerify.verify(baseSEListener);
                break;
        }
    }

    private DeepSEListener baseSEListener = new DeepSEListener() {

        /**
         * SDK内部show loading dialog
         */
        @Override
        public void onShowDialog() {
            getUI().dismissProgressDialog();
        }

        @Override
        public void onError(String errorCode, String error) {
//            DebugLog.i(TAG,"onError-->errorCode:"+errorCode+", error: "+error);
//            ToastUtils.showShort(error);
            getUI().dismissProgressDialog();
            ToastUtils.showShort(getString(R.string.string_net_exception)+errorCode);
        }

        /**
         * 验证码Dialog关闭
         * 1：webview的叉按钮关闭
         * 2：点击屏幕外关闭
         * 3：点击回退键关闭
         *
         * @param num
         */
        @Override
        public void onCloseDialog(int num) {
//            DebugLog.i(TAG, "onCloseDialog-->" + num);
        }

        /**
         * show 验证码webview
         */
        @Override
        public void onDialogReady() {
//            DebugLog.i(TAG,"onDialogReady-->SDK show captcha webview dialog! ");
        }

        /**
         * 验证成功
         * @param token
         */
        @Override
        public void onResult(String token) {
//            DebugLog.i(TAG,"onResult: "+token);
//            final String verifyCode = inputVerifyCode.getInputString();
//            if (TextUtils.isEmpty(verifyCode)) {
//                ToastUtils.showShort(getResources().getString(R.string.input_verify));
//                return;
//            } else {
//                inputVerifyCode.setError("");
//            }
//            final String googleVerifyCode = inputGoogleCode.getInputString();
//            if (TextUtils.isEmpty(googleVerifyCode)) {
//                ToastUtils.showShort(getResources().getString(R.string.input_google_verify_code));
//                return;
//            } else {
//                inputGoogleCode.setError("");
//            }
            getUI().dismissProgressDialog();
            if (!NetWorkStatus.isConnected(BindGAActivity.this)) {
                ToastUtils.showShort(BindGAActivity.this, getResources().getString(R.string.hint_network_not_connect));
                return;
            }
            getPresenter().requestVerifyCodeOfBindGA(isEmail,token);

        }
    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        deepKnowVerify.destroy();
    }

    @Override
    public void setAuthTv(String s) {
        sendVerifyCodeTv.setText(s);
    }

    @Override
    public void setAuthTvStatus(boolean b) {
        sendVerifyCodeTv.setEnabled(b);
        sendVerifyCodeTv.setTextColor(b ? getResources().getColor(R.color.blue) : SkinColorUtil.getDark50(this));
    }

    /**
     * 编辑框监听器
     */
    private TextWatcher mTextWatcher = new TextWatcher() {

        /** 改变前*/
        @Override
        public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
        }

        /** 内容改变*/
        @Override
        public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            if (getPresenter().checkInputContentIsEmpty(inputVerifyCode,inputGoogleCode)) {
                viewFinder.find(R.id.btn_sure).setEnabled(true);
            } else {
                viewFinder.find(R.id.btn_sure).setEnabled(false);
            }

        }

        @Override
        public void afterTextChanged(Editable s) {

        }
    };
}
