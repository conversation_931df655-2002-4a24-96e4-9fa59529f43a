/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OptionAssetDetailActivity.java
 *   @Date: 2/15/19 11:29 AM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.account.ui;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.view.TopBar;
import io.bhex.app.web.presenter.NullPresenter;
import io.bhex.sdk.trade.bean.OptionAssetListResponse;

import static io.bhex.baselib.constant.AppData.INTENT.KEY_ASSET;

public class OptionAssetDetailActivity extends BaseActivity<NullPresenter,NullPresenter.NullUI> implements NullPresenter.NullUI {
    private OptionAssetDetailFragment mOptionAssetDetailFragment;
    private OptionAssetListResponse.OptionAssetBean assetItemBean;
    private TopBar topBar;

    @Override
    protected int getContentView() {
        return R.layout.activity_fragment_container_layout;
    }

    @Override
    protected NullPresenter createPresenter() {
        return new NullPresenter();
    }

    @Override
    protected NullPresenter.NullUI getUI() {
        return this;
    }



    @Override
    protected void initView() {
        super.initView();
        topBar = this.findViewById(R.id.topBar);
        topBar.setLeftOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        mOptionAssetDetailFragment = new OptionAssetDetailFragment();
        Intent intent = getIntent();
        if (intent != null) {
            assetItemBean = (OptionAssetListResponse.OptionAssetBean) intent.getSerializableExtra(KEY_ASSET);
            if (assetItemBean != null) {
                topBar.setTitle(assetItemBean.tokenName);

                Bundle bundle = new Bundle();
                bundle.putSerializable(KEY_ASSET,assetItemBean);
                mOptionAssetDetailFragment.setArguments(bundle);
                getSupportFragmentManager()    //
                        .beginTransaction()
                        .add(R.id.fragment_container, mOptionAssetDetailFragment)   // 此处的R.id.fragment_container是要盛放fragment的父容器
                        .commit();
            }
        }




    }

}

