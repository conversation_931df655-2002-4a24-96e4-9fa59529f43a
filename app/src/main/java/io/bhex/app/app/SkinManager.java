package io.bhex.app.app;

import android.content.res.Configuration;

import androidx.appcompat.app.AppCompatDelegate;

import io.bhex.app.skin.support.load.NightDrawableLoader;
import io.bhex.app.utils.CommonUtil;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.core.SPEx;
import io.bhex.baselib.utils.DebugLog;
import skin.support.SkinCompatManager;
import skin.support.app.SkinAppCompatViewInflater;
import skin.support.app.SkinCardViewInflater;
import skin.support.constraint.app.SkinConstraintViewInflater;
import skin.support.design.app.SkinMaterialViewInflater;
import skin.support.flycotablayout.app.SkinFlycoTabLayoutInflater;
import skin.support.utils.Slog;

/**
 * created by gong<PERSON><PERSON>
 * on 2020/2/28
 */
public class SkinManager {

    private static volatile SkinManager _instance;
    private boolean isBlack = false;
    private SkinManager(){

    }

    public static SkinManager getInstance(){
        if(_instance==null){
            synchronized (SkinManager.class){
                if(_instance==null){
                    _instance = new SkinManager();
                }
            }
        }

        return _instance;
    }

    /**
     * 换肤配置
     */
    public void initConfig(){

        //        SkinCompatManager.withoutActivity(this)                         // 基础控件换肤初始化
//                .addInflater(new SkinMaterialViewInflater())            // material design 控件换肤初始化[可选]
//                .addInflater(new SkinConstraintViewInflater())          // ConstraintLayout 控件换肤初始化[可选]
//                .addInflater(new SkinCardViewInflater())                // CardView v7 控件换肤初始化[可选]
//                .setSkinStatusBarColorEnable(false)                     // 关闭状态栏换肤，默认打开[可选]
//                .setSkinWindowBackgroundEnable(false)                   // 关闭windowBackground换肤，默认打开[可选]
//                .loadSkin(loadSkin);


        //        SkinCircleImageViewManager.init(this);
//        SkinMaterialManager.init(this);
//        SkinConstraintManager.init(this);
//        SkinCardViewManager.init(this);
//        SkinFlycoTabLayoutManager.init(this);
//        SkinCompatManager.init(this).loadSkin();
//        SkinCompatManager.init(this)
        // 框架换肤日志打印
        Slog.DEBUG = true;
        SkinCompatManager.withoutActivity(BHexApplication.getInstance())
//                .addStrategy(new CustomSDCardLoader())          // 自定义加载策略，指定SDCard路径
//                .addStrategy(new ZipSDCardLoader())             // 自定义加载策略，获取zip包中的资源
                .addInflater(new SkinAppCompatViewInflater())   // 基础控件换肤
                .addInflater(new SkinMaterialViewInflater())    // material design
                .addInflater(new SkinConstraintViewInflater())  // ConstraintLayout
                .addInflater(new SkinCardViewInflater())        // CardView v7
//                .addInflater(new SkinCircleImageViewInflater()) // hdodenhof/CircleImageView
                .addInflater(new SkinFlycoTabLayoutInflater()) // *********/FlycoTabLayout
                .setSkinStatusBarColorEnable(false)              // 关闭状态栏换肤
//                .setSkinWindowBackgroundEnable(false)           // 关闭windowBackground换肤
//                .setSkinAllActivityEnable(false)                // true: 默认所有的Activity都换肤; false: 只有实现SkinCompatSupportable接口的Activity换肤
                .loadSkin();
        AppCompatDelegate.setCompatVectorFromResourcesEnabled(true);
        BHexApplication instance = BHexApplication.getInstance();

        isBlack = SPEx.get(AppData.SPKEY.SKIN_IS_BLACK_MODE,isBlack);

/*
        instance.getResources().getDrawable(R.mipmap.tab_home).setColorFilter(SkinColorUtil.getDark(instance), PorterDuff.Mode.SRC_ATOP);
        instance.getResources().getDrawable(R.mipmap.tab_hangqing).setColorFilter(SkinColorUtil.getDark(instance), PorterDuff.Mode.SRC_ATOP);
        instance.getResources().getDrawable(R.mipmap.tab_trade).setColorFilter(SkinColorUtil.getDark(instance), PorterDuff.Mode.SRC_ATOP);
        instance.getResources().getDrawable(R.mipmap.tab_contract).setColorFilter(SkinColorUtil.getDark(instance), PorterDuff.Mode.SRC_ATOP);
        instance.getResources().getDrawable(R.mipmap.tab_asset).setColorFilter(SkinColorUtil.getDark(instance), PorterDuff.Mode.SRC_ATOP);
*/
//        setBlackSkin();

        SkinCompatManager.getInstance().addStrategy(NightDrawableLoader.getInstance());
        boolean isHasSetBlackMode = SPEx.get(AppData.SPKEY.SKIN_IS_SET_BLACK_MODE, false);
        if (isHasSetBlackMode) {
            //手动设置过
            if (CommonUtil.isBlackMode()) {
                CommonUtil.setBlackSkin();
            }
        }else{
            //没有手动设置过，根据系统模式设置
            int currentNightMode = instance.getResources().getConfiguration().uiMode & Configuration.UI_MODE_NIGHT_MASK;
            DebugLog.e("Night Mode = "+currentNightMode);
            switch (currentNightMode) {
                case Configuration.UI_MODE_NIGHT_NO:
                    // Night mode is not active, we're using the light theme
                    CommonUtil.clearBlackSkin();
                    break;
                case Configuration.UI_MODE_NIGHT_YES:
                    // Night mode is active, we're using dark theme
                    CommonUtil.setBlackSkin();
                    break;
            }
        }
    }

    public boolean isBlack() {
        return isBlack;
    }

    public void setBlack(boolean black) {
        isBlack = black;
        SPEx.set(AppData.SPKEY.SKIN_IS_BLACK_MODE,isBlack);
    }

}
