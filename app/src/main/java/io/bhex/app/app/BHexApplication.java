/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BHexApplication.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.app;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.StrictMode;
import android.text.TextUtils;

import com.bhex.pushlib.PushManager;
import com.facebook.stetho.Stetho;
import com.github.moduth.blockcanary.BlockCanary;

import java.util.List;

import androidx.multidex.MultiDex;
import io.bhex.app.BuildConfig;
import io.bhex.app.account.utils.LocalManageUtil;
import io.bhex.app.safe.SafeUilts;
import io.bhex.app.utils.IntentUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.core.CApplication;
import io.bhex.baselib.network.Utils.CookieUtils;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.LogToFile;
import io.bhex.sdk.BhexSdk;
import io.bhex.sdk.account.LoginResultCarrier;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.app.AppStatusConstant;
import io.bhex.sdk.app.AppStatusManager;
import io.bhex.sdk.data_manager.RateAndLocalManager;
import xcrash.XCrash;

public class BHexApplication extends CApplication {
    private int count=0;
    private Handler mHandler;
    private static final int TIMEOUT_MESSAGE = 0X1;
    public static BHexApplication getInstance() {
        return (BHexApplication) CApplication.getInstance();
    }

    @Override
    public void onCreate() {
        super.onCreate();

        AppStatusManager.getInstance().setAppStatus(AppStatusConstant.STATUS_NORMAL); //进入应用初始化正常状态-设置成正常(未回收)状态

        // google必须要在application初始化，否则在google接收到通知的时候无法显示，
        // 猜测:google接收到通知启动了对应的app进程，如果fcm在此处不初始化的话，google就无法找到对应的通道从而导致通知推送显示失败
        PushManager.initFirebaseApp(this, BuildConfig.FCM_PROJECT_ID,BuildConfig.FCM_APP_ID,BuildConfig.FCM_API_KEY);

        UserManager.getInstance().updateFingerAuthStatus(false);

        //APP生命周期监听
        registerActivityLife();

        //压后台处理
        backHome();

        //APP配置初始化
        initAppConfig();

        //设置Cookie监听
        setCookieListener();

//        if(BuildConfig.DEBUG){
//            BlockCanary.install(this, new AppBlockCanaryContext()).start();
//            Stetho.initializeWithDefaults(this);
//            //initStrictMode();
//        }
        // TODO 实现插件化之后使用插件化方法
        BhexSdk.SetSendPushTokenCallback(new BhexSdk.SendPushTokenCallback() {
            @Override
            public void sendPushToken() {
                PushManager.sendRegTokenToServer();
            }
        });
        //long t2 = SystemClock.currentThreadTimeMillis();
        /*Log.d("AppConfig:","onCreate 用时:"+(t2-t1));
        Log.d("BHexApplication","BlockCanary==");*/

        if (BuildConfig.DEBUG) {
            //初始化写日志
            LogToFile.init(this);
        }
    }

    private void initStrictMode() {
        StrictMode.setThreadPolicy(
                new StrictMode.ThreadPolicy.Builder()
                        .detectDiskReads()
                        .detectDiskWrites()
                        .detectNetwork().penaltyLog().build());

        StrictMode.setVmPolicy(
                new StrictMode.VmPolicy.Builder().
                        detectLeakedSqlLiteObjects()
                        .detectLeakedClosableObjects()
                        .penaltyLog().penaltyDeath().build());
    }

    /**
     * 设置Cookie监听
     */
    private void setCookieListener() {
        //需要去登录监听
        BhexSdk.SetNoCookieListener(new BhexSdk.OnNoCookieListener() {
            @Override
            public void OnNoCookie(Context activity, LoginResultCarrier callback) {

                IntentUtils.goLogin(activity, callback);
            }

        });
    }

    /**
     * APP 配置初始化
     */
    private void initAppConfig() {
        String processName = getProcessName(this);
        DebugLog.d(processName);

        boolean isInMainProcess = !TextUtils.isEmpty(processName) && processName.equals(BuildConfig.APPLICATION_ID);
        if (isInMainProcess) {
            APPConfig.getInstance().initConfig();
        }
    }



    /**
     * 压入后台
     */
    private void backHome() {

        mHandler = new Handler(){
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                switch (msg.what){
                    case TIMEOUT_MESSAGE:
                        if(AppData.isHome == true){
                            UserManager.getInstance().updateFingerAuthStatus(false);
                            if (!UserManager.getInstance().isLogin()) {
                                CookieUtils.getInstance().clearWebCookie(BHexApplication.getInstance());
                            }
                        }
                        break;
                }
            }
        };
    }

    /**
     * 生命周期监听
     */
    private void registerActivityLife() {
        registerActivityLifecycleCallbacks(new ActivityLifecycleCallbacks() {

            @Override
            public void onActivityStopped(Activity activity) {
                DebugLog.v("Activity", activity + "onActivityStopped");
                count--;
                if (count == 0) {
                    DebugLog.v("Activity", ">>>>>>>>>>>>>>>>>>>切到后台  lifecycle");
                    if (SafeUilts.isOpenSageVerify()) {
                        AppData.isHome = true;
                        AppData.HOME_TIME = System.currentTimeMillis();
                        if(expiredTime() >0 &&  expiredTime() - System.currentTimeMillis() >0){
                            mHandler.sendEmptyMessageDelayed(TIMEOUT_MESSAGE, expiredTime() - System.currentTimeMillis());
//                            mHandler.sendEmptyMessageDelayed(TIMEOUT_MESSAGE, 30 * 1000);
                        }
                    }

                    BhexSdk.Stop();
                }
            }

            @Override
            public void onActivityStarted(Activity activity) {
                DebugLog.v("BHActivity", activity + "onActivityStarted");
                if (count == 0) {
                    DebugLog.v("Activity", ">>>>>>>>>>>>>>>>>>>切到前台  lifecycle");
                    if(expiredTime() >0 &&  expiredTime() < System.currentTimeMillis() && System.currentTimeMillis() - AppData.HOME_TIME > AppData.HOME_ALLOW_TIME){
                        //ActivityCache.getInstance().finishActivity();
                        UserManager.getInstance().updateFingerAuthStatus(false);
                        if (!UserManager.getInstance().isLogin()) {
                            CookieUtils.getInstance().clearWebCookie(BHexApplication.getInstance());
                        }

                    }
                    BhexSdk.Start();
                }
                count++;
                //RateAndLocalManager.GetInstance(activity).SetCurLocalKind(activity, RateAndLocalManager.GetInstance(activity).getCurLocalKind());
            }

            @Override
            public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
                DebugLog.v("Activity", activity + "切onActivitySaveInstanceState");
            }

            @Override
            public void onActivityResumed(Activity activity) {
                DebugLog.v("Activity", activity + "切onActivityResumed");
                io.bhex.app.app.ActivityManager.getInstance().setCurrentActivity(activity);
            }

            @Override
            public void onActivityPaused(Activity activity) {
                DebugLog.v("Activity", activity + "切onActivityPaused");
            }

            @Override
            public void onActivityDestroyed(Activity activity) {
                DebugLog.v("Activity", activity + "切onActivityDestroyed");
                io.bhex.app.app.ActivityManager.getInstance().removeActivity(activity);
            }

            @Override
            public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
                DebugLog.v("Activity", activity + "切onActivityCreated");
                io.bhex.app.app.ActivityManager.getInstance().addActivity(activity);
            }
        });

    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        if (!BuildConfig.DEBUG) {
            CrashHandler.uninstall();
        }
    }

    @Override
    protected void attachBaseContext(Context base) {
        BHexApplication.setMBaseContext(base);
        super.attachBaseContext(
                LocalManageUtil.createConfigurationResources(base,
                LocalManageUtil.getSetLanguageLocale(base).getLanguage()));
        RateAndLocalManager.GetInstance(base).initRateKind(base);
        //super.attachBaseContext(base);
        MultiDex.install(this);
        // 临时禁用xcrash初始化，解决AndroidX迁移后的native库兼容性问题
        // if(BuildConfig.DEBUG){
        //     XCrash.init(this,new XCrash.InitParameters().setLogDir(getExternalFilesDir("tombstone").getAbsolutePath()));
        // }
    }

    private String getProcessName(Context context) {
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> runningApps = am.getRunningAppProcesses();
        if (runningApps == null) {
            return null;
        }
        for (ActivityManager.RunningAppProcessInfo proInfo : runningApps) {
            if (proInfo.pid == android.os.Process.myPid()) {
                if (proInfo.processName != null) {
                    return proInfo.processName;
                }
            }
        }
        return null;
    }

}
