package io.bhex.app.kline.ui;

import android.graphics.Typeface;
import android.os.Build;
import android.util.TypedValue;
import android.view.View;

import com.bhex.kline.indicator.IndicatorManager;
import com.bhex.kline.model.ChartIndicatorSetting;

import java.util.ArrayList;
import java.util.List;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.base.SimplePresenter;
import io.bhex.app.kline.adapter.IndicatorListAdapter;
import io.bhex.app.view.TopBar;

/**
 * <AUTHOR>
 * 指标设置
 * 2020-9-8 17:25:12
 */
public class KlineSettingActivity extends BaseActivity<SimplePresenter, SimplePresenter.SimpleUI>
        implements SimplePresenter.SimpleUI {

    private TopBar mTopBar;

    private RecyclerView mRcvMain;

    private RecyclerView mRcvSub;

    private List<ChartIndicatorSetting> mIndexMainItemList = new ArrayList();
    private List<ChartIndicatorSetting> mIndexSubItemList = new ArrayList();

    private IndicatorListAdapter mIndicatorMainAdapter;
    private IndicatorListAdapter mIndicatorSubAdapter;

    @Override
    protected int getContentView() {
        return R.layout.activity_kline_setting;
    }

    @Override
    protected SimplePresenter createPresenter() {
        return new SimplePresenter();
    }

    @Override
    protected SimplePresenter.SimpleUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        mTopBar = viewFinder.find(R.id.topBar);
        mTopBar.setTitle(getString(R.string.k_index_setting));
        mTopBar.getTitleView().setTextSize(TypedValue.COMPLEX_UNIT_SP,17);
        mTopBar.getTitleView().setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
        mTopBar.setLeftImg(R.mipmap.white_back);

        //主图指标
        mIndexMainItemList = IndicatorManager.getInstance().loadMainIndicator(this);
        mRcvMain = findViewById(R.id.rcv_main);
        LinearLayoutManager llm = new LinearLayoutManager(this);
        llm.setOrientation(LinearLayoutManager.VERTICAL);
        mRcvMain.setLayoutManager(llm);
        mIndicatorMainAdapter = new IndicatorListAdapter(this,mIndexMainItemList);
        mRcvMain.setAdapter(mIndicatorMainAdapter);
        mRcvMain.setNestedScrollingEnabled(false);

        //附图指标
        mIndexSubItemList = IndicatorManager.getInstance().loadSubIndicator(this);
        mRcvSub = findViewById(R.id.rcv_sub);
        LinearLayoutManager subLlm = new LinearLayoutManager(this);
        subLlm.setOrientation(LinearLayoutManager.VERTICAL);
        mRcvSub.setLayoutManager(subLlm);
        mIndicatorSubAdapter = new IndicatorListAdapter(this,mIndexSubItemList);
        mRcvSub.setAdapter(mIndicatorSubAdapter);
        mRcvSub.setNestedScrollingEnabled(false);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            //实现状态栏图标和文字颜色为浅色
            getWindow().getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
            getWindow().getDecorView().findViewById(android.R.id.content).setPadding(0, 0, 0, 0);
        }
    }

    @Override
    public int getColorPrimary() {
        return ContextCompat.getColor(this,R.color.color_bg_2_night);
    }


}