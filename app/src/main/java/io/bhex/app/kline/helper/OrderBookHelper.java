package io.bhex.app.kline.helper;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.ProgressBar;
import android.widget.TextView;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.kline.bean.BuySellBean;
import io.bhex.app.kline.ui.BookListFragment;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.utils.SkinColorUtil;

/**
 * Created by BHEX.
 * User: gdy
 * Date: 2020/3/6
 * Time: 23:20
 */
public class OrderBookHelper {

    public  List<LinearLayout> listViews = new ArrayList<>();
    private boolean isShowCumulativeVolume;

    //动态添加布局
    public  void updateOrderBookItem(Activity context, ViewGroup parentView, List<BuySellBean> list, String basePrecision, float average, BookListFragment.ShowModeLisenter showModeLisenter){
        if(list==null || list.size()==0){
            return;
        }
        if (showModeLisenter != null) {
            isShowCumulativeVolume = showModeLisenter.isShowQuantityMode();
        }

        if(listViews.size()==0){
            for(int i = 0;i<list.size();i++){
                LinearLayout linearLayout = (LinearLayout)View.inflate(context,R.layout.item_book_list_layout,null);
                parentView.addView(linearLayout);
                listViews.add(linearLayout);

            }
        }else{
            if(listViews.size()<list.size()){
                for(int i = listViews.size();i<list.size();i++){
                    LinearLayout linearLayout = (LinearLayout)View.inflate(context,R.layout.item_book_list_layout,null);
                    parentView.addView(linearLayout);
                    listViews.add(linearLayout);
                }
            }
        }
        for(int i = 0;i<list.size();i++){
            if(i<listViews.size()){
                BuySellBean itemModel = list.get(i);
                View itemView = listViews.get(i);
                setViewData(itemView,context,itemModel,basePrecision,average);
            }
        }
    }

    public  void setViewData(View view, Activity context,BuySellBean itemModel,String basePrecision, float average){
        int digitOfVolume = NumberUtils.calNumerCount(context, basePrecision);
        String str_1 =  TextUtils.isEmpty(itemModel.buyPrice) ? context.getString(R.string.string_placeholder) : io.bhex.baselib.utils.NumberUtils.handVolumeLength(isShowCumulativeVolume ? itemModel.dealBuy+"" : itemModel.buyAmount+"",digitOfVolume);
        TextView textView_1 = view.findViewById(R.id.item_amount_buy);
        textView_1.setText(str_1);

        String str_2 =  TextUtils.isEmpty(itemModel.sellPrice) ? context.getString(R.string.string_placeholder) : io.bhex.baselib.utils.NumberUtils.handVolumeLength(isShowCumulativeVolume ? itemModel.dealSell+"" : itemModel.sellAmount+"",digitOfVolume);
        TextView textView_2 = view.findViewById(R.id.item_amount_sell);
        textView_2.setText(str_2);

        String str_3 = TextUtils.isEmpty(itemModel.buyPrice) ? context.getString(R.string.string_placeholder) : itemModel.buyPrice;
        TextView textView_3 = view.findViewById(R.id.item_price_buy);
        textView_3.setText(str_3);
        textView_3.setTextColor(SkinColorUtil.getGreen(context));

        String str_4 = TextUtils.isEmpty(itemModel.sellPrice) ? context.getString(R.string.string_placeholder) : itemModel.sellPrice;
        TextView textView_4 = view.findViewById(R.id.item_price_sell);
        textView_4.setText(str_4);
        textView_4.setTextColor(SkinColorUtil.getRed(context));

        float pB = itemModel.dealBuy / average * 100;
        float pS = itemModel.dealSell / average * 100;
        pB = Double.isInfinite(pB) || Double.isNaN(pB) ? 0f : pB;
        pS = Double.isInfinite(pS) || Double.isNaN(pS) ? 0f : pS;
        pB = pB > 100 ? 100 : pB;
        pS = pS > 100 ? 100 : pS;

        int int_1 = 100 - new BigDecimal(pB).intValue();
        ProgressBar progressBar_1 = view.findViewById(R.id.progress_buy);
        progressBar_1.setProgress(int_1);
        progressBar_1.setProgressDrawable(context.getResources().getDrawable(SkinColorUtil.getBuyProgressDrawable(context)));

        int int_2 = new BigDecimal(pS).intValue();
        ProgressBar progressBar_2 = view.findViewById(R.id.progress_sell);
        progressBar_2.setProgress(int_2);
        progressBar_2.setProgressDrawable(context.getResources().getDrawable(SkinColorUtil.getSellProgressDrawable(context)));

        //baseViewHolder.setProgress(R.id.progress_buy, 100 - new BigDecimal(pB).intValue());
        //baseViewHolder.setProgress(R.id.progress_sell, new BigDecimal(pS).intValue());
    }
}
