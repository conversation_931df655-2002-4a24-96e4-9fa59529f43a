/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MinuteLineEntity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.kline.bean.chart;


import java.util.Date;


/**
 * 分时图实体
 */

/*
public class MinuteLineEntity implements IMinuteLine {
    */
/**
     * time : 09:30
     * price : 3.53
     * avg : 3.5206
     * vol : 9251
     *//*


    public Date time;
    public float price;
    public float avg;
    public float volume;

    @Override
    public float getAvgPrice() {
        return avg;
    }

    @Override
    public float getPrice() {
        return price;
    }

    @Override
    public Date getDate() {
        return time;
    }

    @Override
    public float getVolume() {
        return volume;
    }


}
*/
