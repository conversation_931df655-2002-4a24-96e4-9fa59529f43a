package io.bhex.app.kline.enums;

import com.bhex.kline.draw.Status;

/**
 * <AUTHOR>
 * @Date 2020-05-01 15:30:17
 * K线枚举类
 */
public enum  KLINE_BUSI_TYPE {
    K线_15分钟("kind_t_15m","15m"),
    主图指标("kind_m_close","2"),BOLL("kind_m_boll","1"),主图MACD("kind_m_ma","0"),
    附图指标("kind_s_close","-1"),MACD("kind_s_macd","0"),KDJ("kind_s_kdj","1"),
    RSI("kind_s_rsi","2"),WR("kind_s_wr","3");

    //K线的类型
    public String tag;
    //类型标识对应的值
    public String value;

    KLINE_BUSI_TYPE(String tag,String value){
        this.tag = tag;
        this.value = value;
    }


    public static KLINE_BUSI_TYPE getValue(String tag) {
        KLINE_BUSI_TYPE[] klineTypes = values();
        for (KLINE_BUSI_TYPE item : klineTypes) {
            if (item.tag.equals(tag)) {
                return item;
            }
        }

        return null;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

}