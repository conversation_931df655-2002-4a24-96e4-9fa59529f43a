package io.bhex.app.kline.adapter;


import androidx.core.util.Pair;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import java.util.List;

/**
 * Created by BHEX.
 * User: gdy
 * Date: 2020/3/27
 * Time: 11:13
 */
public class KlineFragmentAdapter extends FragmentPagerAdapter {

    private List<Pair<String, Fragment>> items;

    public KlineFragmentAdapter(FragmentManager fm, List<Pair<String, Fragment>> items) {
        super(fm);
        this.items = items;
    }

    @Override
    public Fragment getItem(int position) {
        return items.get(position).second;
    }

    @Override
    public int getCount() {
        return items.size();
    }

    @Override
    public CharSequence getPageTitle(int position) {
        return items.get(position).first;
    }
}
