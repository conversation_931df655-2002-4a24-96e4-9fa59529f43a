package io.bhex.app.kline.ui;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseFragment;
import io.bhex.app.kline.presenter.ContractIntroductionPresenter;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.app.view.WrapContentHeightViewPager;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.FuturensBaseToken;
import io.bhex.sdk.quote.bean.RiskLimitBean;
import io.bhex.sdk.quote.bean.TickerBean;
import io.bhex.sdk.trade.bean.FuturesFundingRate;
import io.bhex.sdk.trade.bean.FuturesFundingRatesResponse;
import io.bhex.sdk.trade.bean.IndicesBean;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-04-14
 * 邮   箱：
 * 描   述：合约简介
 * ================================================
 */

public class ContractIntroductionFragment extends BaseFragment<ContractIntroductionPresenter, ContractIntroductionPresenter.ContractIntroductionUI> implements ContractIntroductionPresenter.ContractIntroductionUI {
    private CoinPairBean mCoinPairBean;
    private String quoteToken ="";

    private WrapContentHeightViewPager vp;
    public ContractIntroductionFragment() {
    }

    @SuppressLint("ValidFragment")
    public ContractIntroductionFragment(WrapContentHeightViewPager vp) {
        this.vp = vp;
    }

    @Override
    protected ContractIntroductionPresenter.ContractIntroductionUI getUI() {
        return this;
    }

    @Override
    protected ContractIntroductionPresenter createPresenter() {
        return new ContractIntroductionPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View rootView = inflater.inflate(R.layout.fragment_contract_brief_introduction_layout, null, false);
        vp.setObjectForPosition(rootView,2);
        return rootView;
    }

    @Override
    protected void initViews() {
        super.initViews();


    }

    @Override
    public void showIndices(CoinPairBean coinPairBean, IndicesBean indicesBean) {
        mCoinPairBean = coinPairBean;
        setIndex(mCoinPairBean,indicesBean);
    }

    private void setIndex(CoinPairBean coinPairBean, IndicesBean indicesBean) {
        if (coinPairBean != null) {
            FuturensBaseToken baseTokenFutures = coinPairBean.baseTokenFutures;
            if (baseTokenFutures != null) {
                if (!TextUtils.isEmpty(coinPairBean.getQuoteTokenName())) {
                    quoteToken = coinPairBean.getQuoteTokenName();
                }
                if (indicesBean != null) {

                }
                String indexData = NumberUtils.roundFormatDown(indicesBean.getIndex(), AppConfigManager.GetInstance().getTokenDigitBySymbolIdAndTokenId(coinPairBean.getSymbolId() + quoteToken));
                viewFinder.textView(R.id.value1).setText(indexData +" "+quoteToken);
                List<RiskLimitBean> riskLimits = baseTokenFutures.getRiskLimits();
                if (riskLimits != null && riskLimits.size()>0) {
                    RiskLimitBean riskLimitBean = riskLimits.get(0);
                    if (riskLimitBean != null) {
                        viewFinder.textView(R.id.value5).setText(NumberUtils.roundFormat(NumberUtils.mul(riskLimitBean.getInitialMargin(), "100"), 2) + "%");
                        viewFinder.textView(R.id.value6).setText(NumberUtils.roundFormat(NumberUtils.mul(riskLimitBean.getMaintainMargin(), "100"), 2) + "%");
                    }
                }

                viewFinder.textView(R.id.value9).setText(coinPairBean.getMinPricePrecision() + " " + baseTokenFutures.getDisplayTokenId());
                viewFinder.textView(R.id.value12).setText(coinPairBean.getMinTradeQuantity() + " " + getResources().getString(R.string.string_futures_unit));
            }


        }
    }

    @Override
    public void showFundingRates(FuturesFundingRatesResponse response) {
        if (response != null) {
            if (mCoinPairBean != null) {
                String symbolId = mCoinPairBean.getSymbolId();
                if (!TextUtils.isEmpty(symbolId)) {
                    List<FuturesFundingRate> datas = response.getArray();
                    if (datas != null) {
                        for (FuturesFundingRate futuresFundingRate : datas) {
                            if (futuresFundingRate.getTokenId().equals(symbolId)) {
                                if (futuresFundingRate != null) {
                                    String actualFundingRate = futuresFundingRate.getFundingRate();
                                    String fundingRate = NumberUtils.roundFormat(NumberUtils.mul("100", actualFundingRate), AppData.DIGIT_FUNDING_FEE_RATE) + "%";
                                    viewFinder.textView(R.id.value4).setText(fundingRate);
                                    if (NumberUtils.sub(actualFundingRate,"0") >= 0) {
                                        viewFinder.textView(R.id.value4).setTextColor(SkinColorUtil.getGreen(getActivity()));
                                    }else{
                                        viewFinder.textView(R.id.value4).setTextColor(SkinColorUtil.getRed(getActivity()));
                                    }
                                    String actualSettleRate = futuresFundingRate.getSettleRate();
                                    String settleRate = NumberUtils.roundFormat(NumberUtils.mul("100", actualSettleRate), AppData.DIGIT_FUNDING_FEE_RATE) + "%";
                                    viewFinder.textView(R.id.value2).setText(settleRate);
                                    if (NumberUtils.sub(actualFundingRate,"0") >= 0) {
                                        viewFinder.textView(R.id.value2).setTextColor(SkinColorUtil.getGreen(getActivity()));
                                    }else{
                                        viewFinder.textView(R.id.value2).setTextColor(SkinColorUtil.getRed(getActivity()));
                                    }
                                    viewFinder.textView(R.id.value3).setText(DateUtils.getSimpleTimeFormat(futuresFundingRate.getLastSettleTime()));

                                }
                            }
                        }
                    }

                }
            }
        }
    }

    @Override
    public void showTickerInfo(TickerBean tickerBean) {
        String tradeAmount = NumberUtils.roundFormatUp(tickerBean.getQv(), 2);
        viewFinder.textView(R.id.value7).setText(tradeAmount + " " + quoteToken);
        viewFinder.textView(R.id.value8).setText(tickerBean.getV() + " " +getResources().getString(R.string.string_futures_unit));
    }
}
