package io.bhex.app.kline.ui.holder;

import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.core.widget.NestedScrollView;

import com.bhex.kline.widget.util.PixelUtils;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.view.NoScrollViewPager;
import io.bhex.app.view.TopBar;

/**
 * Created by BHEX.
 * User: gdy
 * Date: 2020/3/27
 * Time: 15:14
 */
public class PortViewHolder{

    private BaseActivity mContext;

    public TopBar topBar;

    public LinearLayout buysell_linear;

    public LinearLayout top_market_data;

    public LinearLayout top_option_data;

    public RelativeLayout layout_port_kind;

    public View layout_port_kind_divider;

    public RelativeLayout layout_kline_content;

    public View layout_line_1;

    public View market_divider;

    public RelativeLayout layout_port_info_tab;

    public LinearLayout entrust_header;

    public LinearLayout latest_header;

    public NoScrollViewPager viewPager;

    //public ScrollView preView;

    public NestedScrollView layout_main;

    public NoScrollViewPager viewPagerKline;

    public View include_latest_market_header_land_layout;

    public RelativeLayout rootKlineView;

    public PortViewHolder(BaseActivity context){
        this.mContext = context;
    }

    public void hideAllView(){
        topBar.setVisibility(View.GONE);
        buysell_linear.setVisibility(View.GONE);
        top_market_data.setVisibility(View.GONE);
        top_option_data.setVisibility(View.GONE);
        layout_port_kind.setVisibility(View.GONE);
        layout_port_kind_divider.setVisibility(View.GONE);
        //layout_kline_content.setVisibility(View.GONE);

        layout_line_1.setVisibility(View.GONE);
        layout_port_info_tab.setVisibility(View.GONE);
        //entrust_header.setVisibility(View.GONE);
        //latest_header.setVisibility(View.GONE);

        viewPager.setVisibility(View.GONE);
        //preView.setVisibility(View.GONE);

//        market_divider.setVisibility(View.GONE);

    }


    public void showAllView(){
        topBar.setVisibility(View.VISIBLE);
        buysell_linear.setVisibility(View.VISIBLE);
        top_market_data.setVisibility(View.VISIBLE);
        top_option_data.setVisibility(View.VISIBLE);
        layout_port_kind.setVisibility(View.VISIBLE);
        layout_port_kind_divider.setVisibility(View.VISIBLE);
        //layout_kline_content.setVisibility(View.VISIBLE);

        layout_line_1.setVisibility(View.VISIBLE);
        layout_port_info_tab.setVisibility(View.VISIBLE);
        //entrust_header.setVisibility(View.VISIBLE);
        //latest_header.setVisibility(View.VISIBLE);

        viewPager.setVisibility(View.VISIBLE);
        //preView.setVisibility(View.VISIBLE);

//        market_divider.setVisibility(View.VISIBLE);

        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);

        lp.addRule(RelativeLayout.BELOW,R.id.topBar);
        lp.addRule(RelativeLayout.ALIGN_PARENT_END);
        lp.addRule(RelativeLayout.ABOVE,R.id.buysell_linear);
        layout_main.setLayoutParams(lp);

        RelativeLayout.LayoutParams lp0 = (RelativeLayout.LayoutParams)viewPagerKline.getLayoutParams();
        lp0.height= PixelUtils.dp2px(mContext,400);
        viewPagerKline.setLayoutParams(lp0);

    }

    public void initView() {
        rootKlineView = mContext.findViewById(R.id.rootKlineView);

        topBar = mContext.findViewById(R.id.topBar);
        buysell_linear = mContext.findViewById(R.id.buysell_linear);
        top_market_data = mContext.findViewById(R.id.top_market_data);
        top_option_data = mContext.findViewById(R.id.top_option_data);
        layout_port_kind = mContext.findViewById(R.id.layout_port_kind);
        layout_port_kind_divider = mContext.findViewById(R.id.layout_port_kind_divider);
        layout_kline_content = mContext.findViewById(R.id.layout_kline_content);
        layout_line_1 = mContext.findViewById(R.id.layout_line_1);
        layout_port_info_tab = mContext.findViewById(R.id.layout_port_info_tab);
        entrust_header = mContext.findViewById(R.id.entrust_header);
        latest_header = mContext.findViewById(R.id.latest_header);
        viewPager = mContext.findViewById(R.id.viewPager);
        //preView = mContext.findViewById(R.id.preView);
        market_divider = mContext.findViewById(R.id.market_divider);

        layout_main = mContext.findViewById(R.id.layout_main);

        viewPagerKline = mContext.findViewById(R.id.viewPagerKline);
    }
}
