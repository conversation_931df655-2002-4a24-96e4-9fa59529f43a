package io.bhex.app.kline.ui;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.LinearLayout;

import com.bhex.kline.indicator.Indicator;
import com.bhex.kline.indicator.IndicatorManager;
import com.bhex.kline.indicator.IndicatorParameter;
import com.bhex.kline.model.ChartIndicatorSetting;
import com.bhex.kline.widget.util.PixelUtils;
import com.bhex.util.ShapeUtil;
import com.google.android.flexbox.FlexboxLayout;

import java.util.List;

import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.ContextCompat;
import io.bhex.app.R;
import io.bhex.app.kline.callback.SimpleTextWatcher;


/**
 * <AUTHOR>
 * 2020-9-10 00:12:33
 */
public class ChartIndicatorDetailsPanel implements View.OnClickListener{

    private Context mContext;
    private ChartIndicatorSetting indicatorSetting;
    private OnIndicatorListener mOnIndicatorListener;
    private ViewGroup mPanel;
    private AppCompatTextView tv_introduction;
    private FlexboxLayout gl_indicator;
    private AppCompatTextView btn_confrim;
    private AppCompatTextView btn_reset;
    public ChartIndicatorDetailsPanel(Context context){
        mContext = context;

        mPanel = (ViewGroup) View.inflate(context, R.layout.layout_chart_indicator_detail, null);
        tv_introduction = mPanel.findViewById(R.id.tv_introduction);
        gl_indicator = mPanel.findViewById(R.id.gl_indicator);
        btn_confrim = mPanel.findViewById(R.id.tv_confrim);
        btn_reset = mPanel.findViewById(R.id.tv_reset);
        btn_confrim.setOnClickListener(this);
        btn_reset.setOnClickListener(this);
        mPanel.setVisibility(View.GONE);
    }

    public void setIndicatorSetting(ChartIndicatorSetting setting){
        indicatorSetting = setting;
        tv_introduction.setText(setting.indexIntroduction);
        initIndicatorChoices(setting.indexSetList);
    }

    private void initIndicatorChoices(List<IndicatorParameter> list){
        if(list==null || list.size()==0) {
            return;
        }

        for(int i=0;i<list.size();i++){
            IndicatorParameter item = list.get(i);
            View view = LayoutInflater.from(mContext).inflate(R.layout.item_chart_indictor_detail, gl_indicator, false);

            AppCompatTextView tv_indicator_name = view.findViewById(R.id.tv_indicator_name);
            tv_indicator_name.setText(item.name);

            AppCompatEditText et_indicator_value =  view.findViewById(R.id.et_indicator_value);
            if(item.curValue>0){
                et_indicator_value.setText(String.valueOf(item.curValue));
            }

            CheckBox checkBox = view.findViewById(R.id.cb_can_show);
            checkBox.setChecked(item.isChecked);

            LinearLayout ll_indictor = view.findViewById(R.id.ll_indictor);
            LinearLayout ll_ck_show = view.findViewById(R.id.ll_ck_show);

            FlexboxLayout.LayoutParams layoutParams = new FlexboxLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    PixelUtils.dp2px(mContext,45));

            if(!item.isCanHide){
                ll_ck_show.setVisibility(View.GONE);
                layoutParams.setMargins(PixelUtils.dp2px(mContext,15),0,0,0);
                ll_indictor.getLayoutParams().width = PixelUtils.dp2px(mContext,100);
            }else{
                ll_ck_show.setVisibility(View.VISIBLE);
                layoutParams.setMargins(PixelUtils.dp2px(mContext,15),0,PixelUtils.dp2px(mContext,20),0);
                ll_indictor.getLayoutParams().width = PixelUtils.dp2px(mContext,115);
            }
            et_indicator_value.addTextChangedListener(new SimpleTextWatcher() {
                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {
                    String indicator_value = et_indicator_value.getText().toString().trim();

                    if(indicator_value.matches("^0")){
                        et_indicator_value.setText("");
                    }

                    if(TextUtils.isEmpty(indicator_value)){
                        checkBox.setChecked(false);
                        item.curValue = 0;
                    }else{
                        item.curValue = Integer.valueOf(indicator_value);
                        checkBox.setChecked(true);
                    }

                    updateParameterExprs();
                }
            });
            //et_indicator_value.setInput
            if(checkBox.isChecked()){
                checkBox.setBackground(ShapeUtil.getRoundRectDrawable(1, item.borderColor));
                if(item.isCanHide){
                    et_indicator_value.setTextColor(item.borderColor);
                }else{
                    et_indicator_value.setTextColor(ContextCompat.getColor(mContext,R.color.chart_text_color));
                }
                ll_indictor.setBackground(ShapeUtil.getRoundRectStokeDrawable(1, 1,item.borderColor));
            }else{
                checkBox.setBackground(ShapeUtil.getRoundRectDrawable(1, ContextCompat.getColor(mContext,R.color.chart_default_border)));
                et_indicator_value.setTextColor(ContextCompat.getColor(mContext,R.color.chart_text_color));
                ll_indictor.setBackground(ShapeUtil.getRoundRectStokeDrawable(1, 1,ContextCompat.getColor(mContext,R.color.chart_default_border)));
            }

            checkBox.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if(isChecked){
                    checkBox.setBackground(ShapeUtil.getRoundRectDrawable(1, item.borderColor));
                    et_indicator_value.setTextColor(item.borderColor);
                    ll_indictor.setBackground(ShapeUtil.getRoundRectStokeDrawable(1, 1,item.borderColor));
                }else{
                    checkBox.setBackground(ShapeUtil.getRoundRectDrawable(1, ContextCompat.getColor(mContext,R.color.chart_default_border)));
                    et_indicator_value.setTextColor(ContextCompat.getColor(mContext,R.color.chart_default_border));
                    ll_indictor.setBackground(ShapeUtil.getRoundRectStokeDrawable(1, 1,ContextCompat.getColor(mContext,R.color.chart_default_border)));
                }
                item.isChecked = isChecked;
                updateParameterExprs();

            });


            ll_ck_show.setOnClickListener(v -> {
                checkBox.setChecked(!checkBox.isChecked());
            });

            gl_indicator.addView(view, layoutParams);
            btn_reset.setTag(indicatorSetting.indexName);
        }
    }

    public ViewGroup getRootView() { return this.mPanel; }

    public void changeHidden(AppCompatImageView iv){
        if(mPanel.getVisibility()==View.VISIBLE){
            mPanel.setVisibility(View.GONE);
            iv.setImageResource(R.mipmap.ic_index_arrow_down);
        }else{
            mPanel.setVisibility(View.VISIBLE);
            iv.setImageResource(R.mipmap.ic_index_arrow_up);
        }
    }

    @Override
    public void onClick(View v) {
        if(v.getId()==R.id.tv_reset){
            resetParameterExprs();
        }else if(v.getId()==R.id.tv_confrim){
            updateParameterExprs();
        }
    }

    /**
     * 更新参数
     */
    private void updateParameterExprs(){
        List<IndicatorParameter> list = indicatorSetting.indexSetList;
        String result = "";
        for (int i = 0; i < list.size(); i++) {
            IndicatorParameter item = list.get(i);
            if(!item.isChecked){
                continue;
            }
            result = result.concat(item.name).concat("-").concat(String.valueOf(item.curValue)).concat(" ");
        }
        if(mOnIndicatorListener!=null){
            mOnIndicatorListener.OnListener(result);
        }
        //保存记录
        IndicatorManager.getInstance().saveParameterByIndicator(indicatorSetting.indexName,list);
    }

    private void resetParameterExprs(){
        String tag = (String)btn_reset.getTag();
        Indicator indicator = IndicatorManager.getInstance().getMap().get(tag);
        gl_indicator.removeAllViews();
        initIndicatorChoices(indicator.defaultParams());
        String result = "";
        for (int i = 0; i < indicator.defaultParams().size(); i++) {
            IndicatorParameter item = indicator.defaultParams().get(i);
            if(!item.isChecked){
                continue;
            }
            result = result.concat(item.name).concat("-").concat(item.defaultValue+"").concat(" ");
        }
        if(mOnIndicatorListener!=null){
            mOnIndicatorListener.OnListener(result);
        }
        //保存记录
        IndicatorManager.getInstance().saveParameterByIndicator(indicatorSetting.indexName,indicator.defaultParams());
    }

    public static interface OnIndicatorListener {
        void OnListener(String result);
    }

    public void setOnIndicatorListener(OnIndicatorListener onIndicatorListener) {
        this.mOnIndicatorListener = onIndicatorListener;
    }
}
