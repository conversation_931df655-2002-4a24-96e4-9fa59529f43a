package io.bhex.app.kline.ui;

import android.os.Bundle;

import androidx.appcompat.app.AppCompatActivity;

import com.bhex.kline.widget.TabButtonItem;
import com.bhex.kline.widget.tab.KLineTabLayout;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import io.bhex.app.R;

public class KlineTestActivity extends AppCompatActivity {
    private String[] tabStrs = new String[]{"15分", "1小时", "4小时", "1天", "更多",  "深度"};

    private KLineTabLayout mTabKline;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_kline_test);

        mTabKline = findViewById(R.id.tab_kline);

        ArrayList<TabButtonItem> tabs = new ArrayList<>();
        TabButtonItem item_15 = new TabButtonItem(tabStrs[0],1,"0");
        tabs.add(item_15);

        TabButtonItem item_1h = new TabButtonItem(tabStrs[1],1,"0");
        tabs.add(item_1h);

        TabButtonItem item_4h = new TabButtonItem(tabStrs[2],1,"0");
        tabs.add(item_4h);

        TabButtonItem item_1d = new TabButtonItem(tabStrs[3],1,"0");
        tabs.add(item_1d);

        TabButtonItem item_more = new TabButtonItem(tabStrs[4],2,"0");
        tabs.add(item_more);

        TabButtonItem item_depth = new TabButtonItem(tabStrs[5],1,"0");
        tabs.add(item_depth);

        mTabKline.setTabList(tabs);
        //mTabKline.changeSegment(0);
        //mTabKline.requestLayout();
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        mTabKline.changeSegment(0);
    }
}
