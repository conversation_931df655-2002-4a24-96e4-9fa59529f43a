package io.bhex.app.kline.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.bhex.kline.model.ChartIndicatorSetting;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.RecyclerView;
import io.bhex.app.R;
import io.bhex.app.kline.ui.ChartIndicatorDetailsPanel;

/**
 * <AUTHOR>
 * 2020-9-10 11:58:02
 */
public class IndicatorListAdapter extends RecyclerView.Adapter<IndicatorListAdapter.IndicatorListVH> {
    private List<ChartIndicatorSetting> mData;
    private Context mContext;

    public IndicatorListAdapter(Context context,List<ChartIndicatorSetting> data) {
        this.mContext = context;
        this.mData = data;
    }

    @NonNull
    @Override
    public IndicatorListVH onCreateViewHolder(@NonNull ViewGroup viewGroup, int position) {
        View itemView = LayoutInflater.from(mContext).inflate(R.layout.item_indicator,viewGroup,false);
        IndicatorListVH viewHodler = new IndicatorListVH(itemView);
        viewHodler.addChartIndicatorDetailsPanel(new ChartIndicatorDetailsPanel(mContext));
        viewHodler.rl_title.setOnClickListener(v -> {
            viewHodler.mPanel.changeHidden(viewHodler.iv_more);
        });
        return viewHodler;
    }

    @Override
    public void onBindViewHolder(@NonNull IndicatorListVH indicatorListVH, int position) {
        ChartIndicatorSetting item = mData.get(position);
        indicatorListVH.tv_indicator_title.setText(item.indexName);
        indicatorListVH.mPanel.setIndicatorSetting(item);
        indicatorListVH.mPanel.setOnIndicatorListener(result -> {
            indicatorListVH.tv_indicator_set_value.setText(result);
        });
        indicatorListVH.tv_indicator_set_value.setText(item.indexSetValue);
    }

    @Override
    public int getItemCount() {
        return mData!=null?mData.size():0;
    }

    public class IndicatorListVH extends RecyclerView.ViewHolder{

        public LinearLayout mRootView;

        public RelativeLayout rl_title;

        public AppCompatTextView tv_indicator_title;
        public AppCompatTextView tv_indicator_set_value;
        public AppCompatImageView iv_more;
        public ChartIndicatorDetailsPanel mPanel;
        public IndicatorListVH(@NonNull View itemView) {
            super(itemView);
            mRootView = itemView.findViewById(R.id.rootView);
            rl_title  = itemView.findViewById(R.id.rl_title);
            tv_indicator_title = itemView.findViewById(R.id.tv_indicator_title);
            tv_indicator_set_value = itemView.findViewById(R.id.tv_indicator_set_value);
            iv_more = itemView.findViewById(R.id.iv_more);
        }

        public void addChartIndicatorDetailsPanel(ChartIndicatorDetailsPanel panel){
            mPanel = panel;
            mRootView.addView(panel.getRootView());
        }
    }
}
