/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BuySellBean.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.kline.bean;

import io.bhex.baselib.network.response.BaseResponse;

public class BuySellBean extends BaseResponse {
    public float buyAmount;
    public String buyPrice;
    public String sellPrice;
    public float sellAmount;
    public float progressBuy;
    public float progressSell;
    public float dealBuy;//买累积成交量
    public float dealSell;//卖累积的成交量

    public BuySellBean() {
    }

    public BuySellBean(String buyPrice,float buyAmount,String sellPrice,float sellAmount,float progressBuy,float progressSell) {
        this.buyAmount = buyAmount;
        this.buyPrice = buyPrice;
        this.sellPrice = sellPrice;
        this.sellAmount = sellAmount;
        this.progressBuy = progressBuy;
        this.progressSell = progressSell;
    }
}
