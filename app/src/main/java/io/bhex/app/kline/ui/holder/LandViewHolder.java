package io.bhex.app.kline.ui.holder;

import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.core.widget.NestedScrollView;

import com.bhex.kline.KlineKind;
import com.bhex.kline.widget.tab.TabTextView;
import com.bhex.kline.widget.util.PixelUtils;

import io.bhex.app.R;
import io.bhex.app.kline.ui.BaseKlineActivity;
import io.bhex.app.view.NoScrollViewPager;
import io.bhex.sdk.PhoneUtil;
import io.bhex.sdk.data_manager.MMKVManager;

/**
 * Created by BHEX.
 * User: gdy
 * Date: 2020/3/27
 * Time: 15:07
 */
public class LandViewHolder  {

    private BaseKlineActivity mContext;

    public RelativeLayout layout_land_top;

    public LinearLayout layout_land_index;

    public RelativeLayout layout_land_tab_kind;

    public NestedScrollView layout_main;

    public NoScrollViewPager viewPagerKline;

    public View include_latest_market_header_land_layout;

    public RelativeLayout rootKlineView;


    public LandViewHolder(BaseKlineActivity context,View topMarketViewLand){
        this.mContext = context;
        include_latest_market_header_land_layout = topMarketViewLand;
    }

    public LandViewHolder(RelativeLayout layout_land_top, LinearLayout layout_land_index, RelativeLayout layout_land_tab_kind) {
        //this.layout_land_top = layout_land_top;
        this.layout_land_index = layout_land_index;
        this.layout_land_tab_kind = layout_land_tab_kind;
    }


    public void hideAllView(){
        //this.layout_land_top.setVisibility(View.GONE);
        this.layout_land_index.setVisibility(View.GONE);
        this.layout_land_tab_kind.setVisibility(View.GONE);
        rootKlineView.removeViewAt(0);
    }

    public void showAllView(){
        //this.layout_land_top.setVisibility(View.VISIBLE);
        this.layout_land_index.setVisibility(View.VISIBLE);
        this.layout_land_tab_kind.setVisibility(View.VISIBLE);



        //include_latest_market_header_land_layout = (RelativeLayout) LayoutInflater.from(mContext).inflate(R.layout.include_latest_market_header_land_layout, rootKlineView, false);
        rootKlineView.addView(include_latest_market_header_land_layout, 0);

        RelativeLayout.LayoutParams lp1 = (RelativeLayout.LayoutParams)layout_land_index.getLayoutParams();
        //lp1.addRule(RelativeLayout.BELOW,R.id.layout_land_top);
        lp1.addRule(RelativeLayout.CENTER_VERTICAL);
        layout_land_index.setLayoutParams(lp1);

        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        //lp.addRule(RelativeLayout.BELOW,R.id.layout_land_top);
        lp.addRule(RelativeLayout.LEFT_OF,R.id.layout_land_index);
        lp.addRule(RelativeLayout.ABOVE,R.id.layout_land_tab_kind);
        layout_main.setLayoutParams(lp);

        RelativeLayout.LayoutParams lp0 = (RelativeLayout.LayoutParams)viewPagerKline.getLayoutParams();
        lp0.height= PixelUtils.dp2px(mContext, PhoneUtil.getInstance().widthDip-90);
        lp0.addRule(RelativeLayout.BELOW,R.id.layout_land_ticker);
        viewPagerKline.setLayoutParams(lp0);

        //
        RelativeLayout.LayoutParams lp2 = (RelativeLayout.LayoutParams)layout_land_index.getLayoutParams();
        lp2.addRule(RelativeLayout.BELOW,R.id.layout_land_ticker);
        lp2.addRule(RelativeLayout.ABOVE,R.id.layout_land_tab_kind);
        //lp2.addRule(RelativeLayout.CENTER_VERTICAL);
        layout_land_index.setLayoutParams(lp2);

        initListener();
    }

    /**
     * 初始化View
     */

    public void initView() {

        rootKlineView = mContext.findViewById(R.id.rootKlineView);

        //layout_land_top = mContext.findViewById(R.id.layout_land_top);
        layout_land_index = mContext.findViewById(R.id.layout_land_index);
        layout_land_tab_kind = mContext.findViewById(R.id.layout_land_tab_kind);

        layout_main = mContext.findViewById(R.id.layout_main);

        viewPagerKline = mContext.findViewById(R.id.viewPagerKline);

        //

        initListener();

    }

    public void initListener(){
        //LinearLayout ll = (LinearLayout) ((HorizontalScrollView)layout_land_tab_kind.getChildAt(0)).getChildAt(0);
        LinearLayout ll = (LinearLayout) (layout_land_tab_kind.getChildAt(0));
        for ( int i = 0; i < ll.getChildCount(); i++) {
            TabTextView ttv = (TabTextView)ll.getChildAt(i);
            //ttv.setTag(1,i);
            if(ttv.getTag()==null){
                continue;
            }
            String tag = ttv.getTag().toString();
            ttv.setOnClickListener(new ViewOnClickListener(tag,i));
            if(tag.equals(KlineKind.KIND_T)){
                ttv.setSelected(true);
            }else{
                ttv.setSelected(false);
            }
        }
        for ( int i = 0; i < layout_land_index.getChildCount(); i++) {
            View view = layout_land_index.getChildAt(i);
            if(view.getTag()==null){
                continue;
            }
            String k_tag = view.getTag().toString();
            if(k_tag.startsWith("kind_m_")){
                view.setOnClickListener(v->{
                    switchDraw(2,view.getTag().toString());
                });
                if(k_tag.equals(mContext.getKlineFragment().MAIN_DRAW)){
                    view.setSelected(true);
                }else{
                    view.setSelected(false);
                }
            }else if (view.getTag().toString().startsWith("kind_s_")){
                view.setOnClickListener(v->{
                    switchDraw(3,view.getTag().toString());
                });
                if(k_tag.equals(mContext.getKlineFragment().SUB_DRAW)){
                    view.setSelected(true);
                }else{
                    view.setSelected(false);
                }
            }


        }
    }

    public class ViewOnClickListener implements View.OnClickListener{
        public int index;
        public String tag;
        public ViewOnClickListener(String tag,int index) {
            this.tag = tag;
            this.index = index;
        }

        @Override
        public void onClick(View v) {
            switchKlineType(tag,index);
        }
    }


    private void switchKlineType(String tag, int index) {
        //int index = Integer.valueOf(tv.getTag(1).toString());
        KlineKind.KIND_T = tag;
        LinearLayout ll = (LinearLayout) layout_land_tab_kind.getChildAt(0);
        //ToastUtils.showShort("index=="+index);
        for (int i = 0; i < ll.getChildCount(); i++) {
            TabTextView ttv = (TabTextView)ll.getChildAt(i);
            if(i==index){
                ttv.setSelected(true);
            }else{
                ttv.setSelected(false);
            }
        }
        if(tag.equals("kind_t_1fenshi")){
            mContext.getKlineFragment().chartView.justShowLoading();

            viewPagerKline.setCurrentItem(1);
            mContext.getKlineFragment().switchTime("1m");
        }else if(tag.startsWith("kind_t_")){
            mContext.getKlineFragment().chartView.justShowLoading();
            viewPagerKline.setCurrentItem(1);
            int index_s = tag.lastIndexOf("_");
            String t_ = tag.substring(index_s+1,tag.length());
            mContext.getKlineFragment().switchKlineTypeByString(t_);
        }
    }

    private void switchDraw(int type,String tag) {
        for ( int i = 0; i < layout_land_index.getChildCount(); i++) {
            View view = layout_land_index.getChildAt(i);
            if(view.getTag()==null){
                continue;
            }
            if(type==2 && tag.startsWith("kind_m_")){
                view.setSelected(false);
            }else if (type==3 && tag.startsWith("kind_s_")){
                view.setSelected(false);
            }

            if(tag.equals(view.getTag().toString())){
                view.setSelected(true);
            }
        }

        MMKVManager.getInstance().saveKind_T_Tag(tag);

        mContext.getKlineFragment().switchKlineDraw(type,tag);
    }
}
