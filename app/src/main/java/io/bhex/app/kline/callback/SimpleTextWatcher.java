package io.bhex.app.kline.callback;

import android.text.Editable;
import android.text.TextWatcher;

/**
 * <AUTHOR>
 * 2020-9-18 11:47:43
 */
public abstract class SimpleTextWatcher implements TextWatcher {
    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {

    }
}
