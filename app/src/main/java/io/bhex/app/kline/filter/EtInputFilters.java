package io.bhex.app.kline.filter;

import android.text.InputFilter;
import android.text.Spanned;

/**
 * <AUTHOR>
 * EditText的过滤器
 */
public class EtInputFilters implements InputFilter {

    public static final int TYPE_MINNUMBER = 4;

    private int mType = 0;

    public EtInputFilters(int type) {
        this.mType = type;
    }

    @Override
    public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
        return null;
    }
}
