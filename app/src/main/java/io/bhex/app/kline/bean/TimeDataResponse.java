/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: TimeDataResponse.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.kline.bean;

import java.util.List;

import io.bhex.baselib.network.response.BaseResponse;


public class TimeDataResponse extends BaseResponse {
    private int volHandUnit;
    private List<DayTimeData> data;

    public int getVolHandUnit() {
        return volHandUnit;
    }

    public void setVolHandUnit(int volHandUnit) {
        this.volHandUnit = volHandUnit;
    }

    public List<DayTimeData> getData() {
        return data;
    }

    public void setData(List<DayTimeData> data) {
        this.data = data;
    }
}
