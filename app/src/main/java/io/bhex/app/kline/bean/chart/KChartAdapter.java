/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: KChartAdapter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.kline.bean.chart;


import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 数据适配器
 */

/*public class KChartAdapter extends BaseKChartAdapter {

    private List<KLineEntity> datas = new ArrayList<>();

    public KChartAdapter() {

    }

    @Override
    public int getCount() {
        return datas.size();
    }

    @Override
    public Object getItem(int position) {
        return datas.get(position);
    }

    @Override
    public Date getDate(int position) {
        try {
            Date date = new Date(datas.get(position).Date);
//            Date date = new Date();
//            String s = datas.get(position).Date;
////            String[] split = s.split("/");
////            date.setYear(Integer.parseInt(split[0]) - 1900);
////            date.setMonth(Integer.parseInt(split[1]) - 1);
////            date.setDate(Integer.parseInt(split[2]));
//            if (s.contains(":")){
//                if (s.contains(" ")) {
//                    String[] dateStr = s.split(" ")[0].split("/");
//                    date.setYear(Integer.parseInt(dateStr[0]) - 1900);
//                    date.setMonth(Integer.parseInt(dateStr[1]) - 1);
//                    date.setDate(Integer.parseInt(dateStr[2]));
//
//                    String[] time = s.split(" ")[1].split(":");
//                    date.setHours(Integer.parseInt(time[0]));
//                    date.setMinutes(Integer.parseInt(time[1]));
//                }else{
//                    String[] time = s.split(":");
//                    date.setHours(Integer.parseInt(time[0]));
//                    date.setMinutes(Integer.parseInt(time[1]));
//                }
//            }else{
//                String[] split = s.split("/");
//                date.setYear(Integer.parseInt(split[0]) - 1900);
//                date.setMonth(Integer.parseInt(split[1]) - 1);
//                date.setDate(Integer.parseInt(split[2]));
//            }
            return date;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    *//**
     * 向头部添加数据
     *//*
    public void addHeaderData(KLineEntity item) {
        if (item != null) {
            if (datas.size()>0) {
                if (item.Date.equals(datas.get(datas.size()-1).Date)){
                    datas.remove(datas.size()-1);
                }
            }
            datas.add(item);
            DataHelper.calculate(datas);
            notifyDataSetChanged();
        }
    }

    *//**
     * 向尾部添加数据
     *//*
    public void addFooterData(KLineEntity item) {
        if (item != null) {
            if (datas.size()>0) {
                if (item.Date.equals(datas.get(0).Date)){
                    datas.remove(0);
                }
            }
            datas.add(0, item);
            DataHelper.calculate(datas);
            notifyDataSetChanged();
        }
    }

    *//**
     * 向头部添加数据
     *//*
    public void addHeaderData(List<KLineEntity> data) {
        if (data != null && !data.isEmpty()) {
            datas.addAll(data);
            DataHelper.calculate(datas);
            notifyDataSetChanged();
        }
    }

    *//**
     * 向尾部添加数据
     *//*
    public void addFooterData(List<KLineEntity> data) {
        if (data != null && !data.isEmpty()) {
            datas.addAll(0, data);
            DataHelper.calculate(datas);
            notifyDataSetChanged();
        }
    }

    public void setDatas(List<KLineEntity> data){
        if (data != null && !data.isEmpty()) {
            datas.clear();
            datas.addAll(0, data);
            DataHelper.calculate(datas);
            notifyDataSetChanged();
        }
    }

    *//**
     * 改变某个点的值
     * @param position 索引值
     *//*
    public void changeItem(int position,KLineEntity data)
    {
        datas.set(position,data);
        notifyDataSetChanged();
    }

    *//**
     * 清空Kline数据
     *//*
    public void clearDatas(){
        datas.clear();
        notifyDataSetChanged();
    }

}*/
