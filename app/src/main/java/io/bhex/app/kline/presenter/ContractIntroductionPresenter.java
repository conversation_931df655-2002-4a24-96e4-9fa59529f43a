package io.bhex.app.kline.presenter;

import android.os.Bundle;
import android.text.TextUtils;

import java.util.List;
import java.util.Map;

import io.bhex.app.base.AppUI;
import io.bhex.app.utils.KlineUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BaseFragmentPresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.quote.QuoteApi;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.FuturensBaseToken;
import io.bhex.sdk.quote.bean.IndicesSocketResponse;
import io.bhex.sdk.quote.bean.TickerBean;
import io.bhex.sdk.quote.bean.TickerListBean;
import io.bhex.sdk.socket.NetWorkObserver;
import io.bhex.sdk.trade.bean.FuturesFundingRatesResponse;
import io.bhex.sdk.trade.bean.IndicesBean;
import io.bhex.sdk.trade.bean.IndicesResponse;
import io.bhex.sdk.trade.futures.FuturesApi;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-04-14
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class ContractIntroductionPresenter extends BaseFragmentPresenter<ContractIntroductionPresenter.ContractIntroductionUI> {
    private CoinPairBean coinPairBean;

    public interface ContractIntroductionUI extends AppUI{

        void showFundingRates(FuturesFundingRatesResponse response);

        void showTickerInfo(TickerBean tickerBean);

        void showIndices(CoinPairBean coinPairBean, IndicesBean data);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, ContractIntroductionUI ui) {
        super.onUIReady(activity, ui);
        Bundle arguments = getFragment().getArguments();
        if (arguments != null) {
            coinPairBean = (CoinPairBean) arguments.getSerializable(AppData.INTENT.SYMBOLS);
            if (coinPairBean != null) {
                getTicker(coinPairBean.getExchangeId(),coinPairBean.getSymbolId());
            }
        }

        getSettleStatus();

    }

    @Override
    public void onVisibleChanged(boolean visible) {
        super.onVisibleChanged(visible);
        if (visible) {
            if (coinPairBean != null) {
                getTicker(coinPairBean.getExchangeId(),coinPairBean.getSymbolId());
            }
            getSettleStatus();
            subIndices();
        }else{
            QuoteApi.UnSubIndices();
        }
    }

    private void getTicker(String exchangeId, String symbolId) {
        QuoteApi.RequestTicker(exchangeId,symbolId,new SimpleResponseListener<TickerListBean>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog();
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(TickerListBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    List<TickerBean> data = response.getData();
                    if (data != null && data.size()>0) {
                        TickerBean tickerBean = data.get(0);
                        if (tickerBean != null) {
                            getUI().showTickerInfo(tickerBean);
                        }
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    /**
     * 获取结算时间、资金费率
     */
    public void getSettleStatus() {
        FuturesApi.getFundingRates(new SimpleResponseListener<FuturesFundingRatesResponse>() {
            @Override
            public void onSuccess(FuturesFundingRatesResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    getUI().showFundingRates(response);
                }
            }
        });

    }

    /**
     * 订阅指数
     */
    public void subIndices(){
        String indicesSymbolId = "";
        if (coinPairBean == null) {
            return;
        }
        if(KlineUtils.isSymbolOfFutures(coinPairBean.getCoinType())){
            FuturensBaseToken baseTokenFutures = coinPairBean.baseTokenFutures;
            if (baseTokenFutures == null) {
                return;
            }
            indicesSymbolId = baseTokenFutures.getDisplayIndexToken();
        }else{
            return;
        }

        if (TextUtils.isEmpty(indicesSymbolId)) {
            return;
        }

        String finalIndicesSymbolId = indicesSymbolId;
        QuoteApi.SubIndexs(indicesSymbolId, new NetWorkObserver<IndicesSocketResponse>() {
            @Override
            public void onShowUI(IndicesSocketResponse response) {
                if (getUI() == null || !getUI().isAlive() || response == null)
                    return;
                List<IndicesBean> datas = response.getData();
                if (datas != null) {
                    for (IndicesBean data : datas) {
                        String symbol = data.getSymbol();
                        if (!TextUtils.isEmpty(symbol)) {
                            if (symbol.equals(finalIndicesSymbolId)) {
                                getUI().showIndices(coinPairBean,data);
                            }
                        }
                    }
                }

            }

            @Override
            public void onError(String response) {

            }
        }, new NetWorkObserver<IndicesResponse>() {
            @Override
            public void onShowUI(IndicesResponse response) {
                if (getUI() == null || !getUI().isAlive() || response == null)
                    return;

                Map<String, IndicesBean> indicesMap = response.data;
                if (indicesMap != null && !indicesMap.isEmpty()) {
                    IndicesBean indicesBean = indicesMap.get(finalIndicesSymbolId);
                    if (indicesBean != null) {
                        getUI().showIndices(coinPairBean,indicesBean);
                    }
                }

            }

            @Override
            public void onError(String response) {

            }
        });
    }
}
