package io.bhex.app.kline.ui;

import android.content.Intent;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;

import com.bhex.kline.KlineKind;
import com.bhex.kline.widget.TabButtonItem;
import com.bhex.kline.widget.tab.KLineTabLayout;
import com.google.android.material.tabs.TabLayout;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.kline.presenter.KlinePresenter;
import io.bhex.app.kline.ui.holder.LandViewHolder;
import io.bhex.app.kline.ui.holder.PortViewHolder;
import io.bhex.app.view.NoScrollViewPager;
import io.bhex.app.view.TopBar;
import io.bhex.app.view.WrapContentHeightViewPager;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.mvp.BaseUI;
import io.bhex.sdk.data_manager.MMKVManager;

/**
 * Created by BHEX.
 * User: gdy
 * Date: 2020/3/24
 * Time: 23:54
 */
public abstract class BaseKlineActivity<P extends BasePresenter<V>, V extends AppUI & BaseUI>  extends BaseActivity<P, V>
        implements KLineTabLayout.CheckTabListener,KlineTabPopWindow.SelectKTabListener {

    //protected List<Pair<String, Fragment>> itemsKline;

    protected KLineTabLayout tab_kline;

    protected KlineTabPopWindow kline_kind_pw;

    protected TopBar topBar;
    protected NoScrollViewPager viewPagerKline;
    protected WrapContentHeightViewPager viewPager;
    protected TabLayout tab;

    protected KlineFragment klineFragment;
    protected View topMarketView;
    protected View topOptionView;
    protected View topMarketViewLand;
    protected View buysellLinear;
    //protected LinearLayout entrustHeader;
    //protected LinearLayout latestHeader;
    //protected AppBarLayout appBar;
    public RelativeLayout rootKlineView;

    protected LandViewHolder landViewHolder;
    protected PortViewHolder portViewHolder;

    public static final int REQEUST_KLINE_SETTING = 100;

    @Override
    protected int getContentView() {
        return R.layout.activity_kline_layout;
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            //实现状态栏图标和文字颜色为浅色
            getWindow().getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
            getWindow().getDecorView().findViewById(android.R.id.content).setPadding(0, 0, 0, 0);
        }
    }

    @Override
    protected void initView() {
        super.initView();

        KlinePresenter.initKind(this);
        topBar = findViewById(R.id.topBar);

        topBar.setLeftImg(R.mipmap.white_back);
        topBar.setRightImg(R.mipmap.ic_favorite_white);

        viewPagerKline = viewFinder.find(R.id.viewPagerKline);
        viewPagerKline.setScanScroll(false);
        //RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) viewPagerKline.getLayoutParams();
        //int heightDipx = PixelUtils.dp2px(this,115+160);
        //DebugLog.d("BaseKlineActivity===>:","heightDip=="+heightDipx);
        //lp.height = PhoneUtil.getInstance().heightPixel-heightDipx;
        rootKlineView = findViewById(R.id.rootKlineView);

        klineFragment = new KlineFragment();

        topMarketViewLand = LayoutInflater.from(this).inflate(R.layout.include_latest_market_header_land_layout,
                rootKlineView, false);

        landViewHolder = new LandViewHolder(this,topMarketViewLand);
        portViewHolder = new PortViewHolder(this);
        landViewHolder.initView();
        portViewHolder.initView();
        kline_kind_pw = new KlineTabPopWindow(this,this);
        //kline_kind_pw.setSelectKTabListener(this);
    }

    /**
     * KLineTabLayout.CheckTabListener
     * @param postion
     * @param item
     */
    @Override
    public void changeCheckItem(int postion, TabButtonItem item) {
        if(item.itemType==1){
            //保存K线指标
            MMKVManager.getInstance().saveKind_T_Tag(item.ktag);
            klineFragment.chartView.justShowLoading();
            viewPagerKline.setCurrentItem(1);
            int index = item.ktag.lastIndexOf("_");
            String t_ = item.ktag.substring(index+1);
            klineFragment.switchKlineTypeByString(t_);
            tab_kline.changeTextByIndex(4,getString(R.string.string_more));
        }else if(item.itemType==2){
            kline_kind_pw.show(tab_kline);
        }else if(item.itemType==3){
            viewPagerKline.setCurrentItem(0);
            tab_kline.changeTextByIndex(4,getString(R.string.string_more));
            tab_kline.setMoreDefaultStyle(getString(R.string.string_more));
        }
    }

    //更多指标切换事件
    @Override
    public void selectItemTab(int type, String tag,String title) {
        MMKVManager.getInstance().saveKind_T_Tag(tag);

        if(type==0){
            klineFragment.chartView.justShowLoading();
            viewPagerKline.setCurrentItem(1);
            klineFragment.switchTime("1m");
            tab_kline.changeIndexSelect(4,true);
        }else if(type==1){
            //tab_kline.getChildAt(4).setSelected(true);
            //选中第四个tab
            tab_kline.changeIndexSelect(4,true);
            klineFragment.chartView.justShowLoading();
            viewPagerKline.setCurrentItem(1);
            int index = tag.lastIndexOf("_");
            String t_ = tag.substring(index+1);
            klineFragment.switchKlineTypeByString(t_);
        } else if(type==2 ||type==3){
            if(!tab_kline.selectIsLastIndex()){
                viewPagerKline.setCurrentItem(1);
                klineFragment.switchKlineDraw(type,tag);
            }else{
                tab_kline.setMoreDefaultStyle(getString(R.string.string_more));
            }
        }else if(type==4){
            startActivityForResult(new Intent(this, KlineSettingActivity.class),REQEUST_KLINE_SETTING);
        }

        if(type==0||type==1){
            tab_kline.changeTextByIndex(4,title);
        }else if(type==2){
            klineFragment.MAIN_DRAW = tag;
        }else if(type==3){
            klineFragment.SUB_DRAW = tag;
        }
    }

   /* @Override
    public void onBackPressed() {
        super.onBackPressed();
        if(kline_kind_pw!=null&&kline_kind_pw.isShowing()){
            kline_kind_pw.dismiss();
        }
        finish();
    }*/

    protected void showLandView(){
        portViewHolder.hideAllView();
        landViewHolder.showAllView();
    }

    protected void showPortView(){
        portViewHolder.showAllView();
        landViewHolder.hideAllView();
    }

    public KlineFragment getKlineFragment() {
        return klineFragment;
    }


    /**
     * 横屏到竖屏类型同步
     */
    public void synchronizedKindTab(){
        //ToastUtils.showShort("==synchronizedKindTab==");
        //Log.d("BaseKlineActivity==>:","KIND_T=="+KlineKind.KIND_T);

        if(KlineKind.KIND_T.equals("kind_t_15m")){
            tab_kline.changeIndexSelect(0,true);
        }else if(KlineKind.KIND_T.equals("kind_t_1h")){
            tab_kline.changeIndexSelect(1,true);
        }else if(KlineKind.KIND_T.equals("kind_t_4h")){
            tab_kline.changeIndexSelect(2,true);
        }else if(KlineKind.KIND_T.equals("kind_t_1d")){
            tab_kline.changeIndexSelect(3,true);
        }else if(KlineKind.KIND_T.startsWith("kind_t_")){
            tab_kline.changeIndexSelect(4,true);
            tab_kline.changeTextByIndex(4,KlineKind.kind_t_map.get(KlineKind.KIND_T));
        }
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(requestCode==REQEUST_KLINE_SETTING){
            klineFragment.reloadChart();
        }

    }

}
