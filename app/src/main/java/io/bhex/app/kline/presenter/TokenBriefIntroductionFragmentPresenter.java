package io.bhex.app.kline.presenter;

import android.os.Bundle;
import android.text.TextUtils;

import java.io.Serializable;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BaseFragmentPresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.config.ConfigApi;
import io.bhex.sdk.config.bean.TokenBriefIntroductionBean;
import io.bhex.sdk.quote.bean.CoinPairBean;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-10-08
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class TokenBriefIntroductionFragmentPresenter extends BaseFragmentPresenter<TokenBriefIntroductionFragmentPresenter.TokenBriefIntroductionUI> {
    public interface TokenBriefIntroductionUI extends AppUI{

        void showTokenInfo(TokenBriefIntroductionBean response);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, TokenBriefIntroductionUI ui) {
        super.onUIReady(activity, ui);
        Bundle arguments = getFragment().getArguments();
        if (arguments != null) {
            CoinPairBean coinPairBean = (CoinPairBean) arguments.getSerializable(AppData.INTENT.SYMBOLS);
            String baseTokenId = coinPairBean.getBaseTokenId();
            if (!TextUtils.isEmpty(baseTokenId)) {
                getTokenBriefIntroduction(baseTokenId);
            }

        }
    }

    /**
     * 获取币种介绍
     * @param baseTokenId
     */
    private void getTokenBriefIntroduction(String baseTokenId) {
        ConfigApi.getTokenInfo(baseTokenId,new SimpleResponseListener<TokenBriefIntroductionBean>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(TokenBriefIntroductionBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showTokenInfo(response);
                }
            }
        });
    }
}
