package io.bhex.app.kline.ui;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;

import com.bhex.kline.KlineKind;
import com.bhex.kline.widget.tab.TabTextView;

import java.util.ArrayList;
import java.util.List;

import androidx.appcompat.widget.AppCompatTextView;
import io.bhex.app.R;

/**
 * Created by BHEX.
 * User: gdy
 * Date: 2020/3/25
 * Time: 23:49
 */
public class KlineTabPopWindow extends PopupWindow {


    public ViewGroup mRootView;

    public List<TabTextView> mListView;

    private BaseKlineActivity mContext;

    public KlineTabPopWindow(BaseKlineActivity context,SelectKTabListener listener) {
        super(context);
        mContext = context;
        mSelectKTabListener = listener;
        init(context);
    }

    public KlineTabPopWindow(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public KlineTabPopWindow(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    private void init(Activity context) {
        mListView = new ArrayList<>();

        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        mRootView = (RelativeLayout)inflater.inflate(R.layout.fragment_kline_tab, null);

        setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        //setBackgroundDrawable(new BitmapDrawable());
        this.setOutsideTouchable(false);
        this.setContentView(mRootView);

        DisplayMetrics dm = new DisplayMetrics();
        context.getWindowManager().getDefaultDisplay().getMetrics(dm);
        this.setHeight(RelativeLayout.LayoutParams.WRAP_CONTENT);
        //this.setWidth((int)(dm.widthPixels - PixelUtils.dp2px(context,20)));
        this.setWidth(dm.widthPixels);
        // 设置弹出窗体可点击
        this.setFocusable(true);
        initListener();

        /*mRootView.setOnKeyListener((v, keyCode, event) -> {
            if(event.getAction()== KeyEvent.ACTION_DOWN && keyCode == KeyEvent.KEYCODE_BACK){
                dismiss();
                return true;
            }
            return false;
        });*/
    }

    @Override
    public void dismiss() {
        super.dismiss();
        //mListView.get(4).setTextColor(ContextCompat.getColor(mContext,R.color.kline_tab_normal));
    }

    public void initListener(){
        //
        int count = mRootView.getChildCount();

        for (int i = 0; i < count; i++) {
            //AppCompatTextView tv = (AppCompatTextView) mRootView.getChildAt(i);
            View childView = mRootView.getChildAt(i);
            if(childView.getTag()==null){
                continue;
            }
            String tag = childView.getTag().toString();
            if(TextUtils.isEmpty(tag)){
                continue;
            }

            if(tag.equals("kline_setting")){
                childView.setOnClickListener(v->{
                    if(mSelectKTabListener==null){
                        return;
                    }
                    mSelectKTabListener.selectItemTab(4,tag,"");
                    dismiss();
                });
                continue;
            }

            AppCompatTextView tv = (AppCompatTextView)childView;

            if(tag.equals("kind_t_1fenshi")){
                if(tag.equals(KlineKind.KIND_T)){
                    tv.setSelected(true);
                }else{
                    tv.setSelected(false);
                }

                tv.setOnClickListener(v->{
                    if(mSelectKTabListener==null){
                        return;
                    }
                    KlineKind.KIND_T = tag;
                    mSelectKTabListener.selectItemTab(0,tag,tv.getText().toString());
                    dismiss();
                });
            }

            if(tag.startsWith("kind_t_") && !tag.equals("kind_t_1fenshi")){
                if(tag.equals(KlineKind.KIND_T)){
                    tv.setSelected(true);
                }else{
                    tv.setSelected(false);
                }

                tv.setOnClickListener(v->{
                    if(mSelectKTabListener==null){
                        return;
                    }
                    KlineKind.KIND_T = tag;
                    mSelectKTabListener.selectItemTab(1,tag,tv.getText().toString());
                    dismiss();
                });
            }


            if(tag.startsWith("kind_m_")){
                if(tag.equals(mContext.getKlineFragment().MAIN_DRAW)){
                    tv.setSelected(true);
                }else{
                    tv.setSelected(false);
                }
                tv.setOnClickListener(v->{
                    if(mSelectKTabListener==null){
                        return;
                    }
                    mContext.getKlineFragment().MAIN_DRAW = tag;
                    mSelectKTabListener.selectItemTab(2,tag,tv.getText().toString());
                    dismiss();
                });
            }


            if(tag.startsWith("kind_s_")){
                if(tag.equals(mContext.getKlineFragment().SUB_DRAW)){
                    tv.setSelected(true);
                }else{
                    tv.setSelected(false);
                }

                tv.setOnClickListener(v->{
                    if(mSelectKTabListener==null){
                        return;
                    }
                    mContext.getKlineFragment().SUB_DRAW = tag;
                    mSelectKTabListener.selectItemTab(3,tag,tv.getText().toString());
                    dismiss();
                });
            }
        }
    }


    public interface SelectKTabListener{

        public void selectItemTab(int type,String tag,String title);
    }

    private SelectKTabListener mSelectKTabListener;

    public void setSelectKTabListener(SelectKTabListener mSelectKTabListener) {
        this.mSelectKTabListener = mSelectKTabListener;
    }

    public void show(View target){
        initListener();
        showAsDropDown(target, Gravity.CENTER,0,0);
    }



}
