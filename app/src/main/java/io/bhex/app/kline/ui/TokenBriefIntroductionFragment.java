package io.bhex.app.kline.ui;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import io.bhex.app.R;
import io.bhex.app.base.BaseFragment;
import io.bhex.app.kline.presenter.TokenBriefIntroductionFragmentPresenter;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.view.WrapContentHeightViewPager;
import io.bhex.sdk.config.bean.TokenBriefIntroductionBean;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-10-08
 * 邮   箱：
 * 描   述：币种介绍
 * ================================================
 */

public class TokenBriefIntroductionFragment extends BaseFragment<TokenBriefIntroductionFragmentPresenter, TokenBriefIntroductionFragmentPresenter.TokenBriefIntroductionUI> implements TokenBriefIntroductionFragmentPresenter.TokenBriefIntroductionUI, View.OnClickListener {
    private TokenBriefIntroductionBean currentTokenInfo;
    private WrapContentHeightViewPager vp;
    public TokenBriefIntroductionFragment() {
    }

    @SuppressLint("ValidFragment")
    public TokenBriefIntroductionFragment(WrapContentHeightViewPager vp) {
        this.vp = vp;
    }

    @Override
    protected TokenBriefIntroductionFragmentPresenter.TokenBriefIntroductionUI getUI() {
        return this;
    }

    @Override
    protected TokenBriefIntroductionFragmentPresenter createPresenter() {
        return new TokenBriefIntroductionFragmentPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View rootView = inflater.inflate(R.layout.fragment_token_brief_introduction_layout, null, false);
        vp.setObjectForPosition(rootView,2);
        return rootView;
    }

    @Override
    protected void initViews() {
        super.initViews();

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.textView(R.id.value4).setOnClickListener(this);
        viewFinder.textView(R.id.value5).setOnClickListener(this);
        viewFinder.textView(R.id.value6).setOnClickListener(this);
    }

    @Override
    public void showTokenInfo(TokenBriefIntroductionBean response) {
        currentTokenInfo = response;
        viewFinder.textView(R.id.tokenName).setText(TextUtils.isEmpty(response.getTokenName()) ? getActivity().getResources().getString(R.string.string_placeholder) : response.getTokenName());
        viewFinder.textView(R.id.value1).setText(TextUtils.isEmpty(response.getPublishTime()) ? getActivity().getResources().getString(R.string.string_placeholder) : response.getPublishTime());
        viewFinder.textView(R.id.value2).setText(TextUtils.isEmpty(response.getMaxQuantitySupplied()) ? getActivity().getResources().getString(R.string.string_placeholder) : response.getMaxQuantitySupplied());
        viewFinder.textView(R.id.value3).setText(TextUtils.isEmpty(response.getCurrentTurnover()) ? getActivity().getResources().getString(R.string.string_placeholder) : response.getCurrentTurnover());
        viewFinder.textView(R.id.value4).setText(TextUtils.isEmpty(response.getWhitePaperUrl()) ? getActivity().getResources().getString(R.string.string_placeholder) : response.getWhitePaperUrl());
        viewFinder.textView(R.id.value5).setText(TextUtils.isEmpty(response.getOfficialWebsiteUrl()) ? getActivity().getResources().getString(R.string.string_placeholder) : response.getOfficialWebsiteUrl());
        viewFinder.textView(R.id.value6).setText(TextUtils.isEmpty(response.getExploreUrl()) ? getActivity().getResources().getString(R.string.string_placeholder) : response.getExploreUrl());
        viewFinder.textView(R.id.tokenBriefIntroductionContent).setText(response.getDescription());
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.value4:
                if (currentTokenInfo != null) {
                    if (!TextUtils.isEmpty(currentTokenInfo.getWhitePaperUrl())) {
                        CommonUtil.copyText(getActivity(), currentTokenInfo.getWhitePaperUrl());
                    }
                }
                break;
            case R.id.value5:
                if (currentTokenInfo != null) {
                    if (!TextUtils.isEmpty(currentTokenInfo.getOfficialWebsiteUrl())) {
                        CommonUtil.copyText(getActivity(), currentTokenInfo.getOfficialWebsiteUrl());
                    }
                }
                break;
            case R.id.value6:
                if (currentTokenInfo != null) {
                    if (!TextUtils.isEmpty(currentTokenInfo.getExploreUrl())) {
                        CommonUtil.copyText(getActivity(), currentTokenInfo.getExploreUrl());
                    }
                }
                break;
        }
    }
}
