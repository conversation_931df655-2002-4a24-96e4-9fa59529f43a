/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PriceDigits.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.kline.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import io.bhex.baselib.network.response.BaseResponse;

/**
 * ================================================
 * 描   述：价格单位
 * ================================================
 */

public class PriceDigits extends BaseResponse {


    private List<DigitsItem> array;

    public List<DigitsItem> getArray() {
        return array;
    }

    public void setArray(ArrayList<DigitsItem> array) {
        this.array = array;
    }

    public static class DigitsItem implements Serializable{
        //小数1位  2位  保留到10  百  千
        private String digitsName;
        //位数 0.1  0.01  1  10  100 等等
        private String digits;

        public String getDigitsName() {
            return digitsName;
        }

        public void setDigitsName(String digitsName) {
            this.digitsName = digitsName;
        }

        public String getDigits() {
            return digits;
        }

        public void setDigits(String digits) {
            this.digits = digits;
        }
    }
}
