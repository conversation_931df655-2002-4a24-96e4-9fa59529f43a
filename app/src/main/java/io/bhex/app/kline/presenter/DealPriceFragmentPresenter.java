/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: DealPriceFragmentPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.kline.presenter;


import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.sdk.quote.QuoteApi;
import io.bhex.sdk.quote.bean.DealPriceItem;
import io.bhex.sdk.socket.NetWorkObserver;
import io.bhex.sdk.quote.bean.DealPriceBean;
import io.bhex.sdk.quote.bean.DealPriceSocketResponse;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BaseFragmentPresenter;


public class DealPriceFragmentPresenter extends BaseFragmentPresenter<DealPriceFragmentPresenter.DealPriceUI>{
    private static final int DATE_MAX_SIZE = 20;//最多20条数据
    private List<DealPriceItem> currentDatas=new ArrayList<>();

    @Override
    public void onUIReady(BaseCoreActivity activity, DealPriceUI ui) {
        super.onUIReady(activity, ui);
    }

    @Override
    public void onResume() {
        super.onResume();
//        getLatestDeal();
    }

    @Override
    public void onPause() {
        super.onPause();
//        NetWorkApiManager.getQuoteInstance().UnSubRequestNetWork("trade");
    }

    @Override
    public void onVisibleChanged(boolean visible) {
        super.onVisibleChanged(visible);
        if (visible) {
            currentDatas.clear();
            getLatestDeal();
        }else{
            QuoteApi.UnSubTradeData();
        }
    }

    public void getLatestDeal(){

        QuoteApi.SubTradeData(getUI().getExchangeId(), getUI().getSymbols(), new NetWorkObserver<DealPriceSocketResponse>() {
            @Override
            public void onShowUI(DealPriceSocketResponse response) {
                if (getUI() == null || !getUI() .isAlive() || response == null)
                    return;

                List<DealPriceItem> datas = response.getData();
                if (datas != null) {
                    //接口返回数据顺序相反，数据反转一次
                    Collections.reverse(datas);
                    currentDatas.addAll(0,datas);
                    if(currentDatas.size() > DATE_MAX_SIZE)
                        currentDatas = currentDatas.subList(0, DATE_MAX_SIZE  );
                    getUI().showLatestDeal(currentDatas);
                }
            }
            @Override
            public void onError(String error) {
            }
        });
    }


    public interface DealPriceUI extends AppUI{

        void showLatestDeal(DealPriceBean response);

        void showLatestDeal(List<DealPriceItem> datas);

        String getExchangeId();

        String getSymbols();
    }

}
