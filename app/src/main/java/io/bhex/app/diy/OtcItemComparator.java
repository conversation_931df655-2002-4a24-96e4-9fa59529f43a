/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcItemComparator.java
 *   @Date: 19-8-16 下午7:09
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.diy;

import java.util.Comparator;

import io.bhex.sdk.otc.bean.OtcListResponse;

public class OtcItemComparator implements Comparator<OtcListResponse.OtcItemBean> {

    @Override
    public int compare(OtcListResponse.OtcItemBean o1, OtcListResponse.OtcItemBean o2) {
        if (o1 == null || o2 == null) {
            return 0;
        } else {
            if (o1.getSortId() > o2.getSortId()) {
                return 1;
            } else if (o1.getSortId() == o2.getSortId()) {
                return 0;
            } else {
                return -1;
            }
        }
    }
}
