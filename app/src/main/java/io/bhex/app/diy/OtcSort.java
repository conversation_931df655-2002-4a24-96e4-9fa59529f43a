/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcSortY.java
 *   @Date: 19-8-16 下午2:55
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.diy;

import android.content.Context;

import java.util.Collections;
import java.util.List;

import io.bhex.app.utils.CommonUtil;
import io.bhex.sdk.otc.bean.OtcListResponse;

public class OtcSort {
    /**
     * OTC广告列表排序
     * @param items
     * @return
     */
    public synchronized static List<OtcListResponse.OtcItemBean> sortOTCList(Context context, List<OtcListResponse.OtcItemBean> items){
        if (CommonUtil.getPackageName(context).equals("com.yooex.broker.android")||CommonUtil.getPackageName(context).equals("com.yooex.broker.android.debug")){
            String first = "399307222563264768";
            String second = "409232854579419904";

            if (items != null) {
                for (int i = 0; i < items.size(); i++) {
                    OtcListResponse.OtcItemBean otcItemBean = items.get(i);
                    if (otcItemBean.getAccountId().equals(first)) {
                        otcItemBean.setSortId(1);
                    }else if(otcItemBean.getAccountId().equals(second)){
                        otcItemBean.setSortId(2);
                    }else{
                        otcItemBean.setSortId(i+3);
                    }

                }
                Collections.sort(items,new OtcItemComparator());

            }
        }
        return items;
    }

}
