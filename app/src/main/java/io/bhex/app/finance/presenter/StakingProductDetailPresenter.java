/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinanceDetailPresenter.java
 *   @Date: 19-3-8 下午5:15
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.BaseResponse;
import io.bhex.sdk.finance.FinanceApi;
import io.bhex.sdk.finance.bean.FinanceBean;
import io.bhex.sdk.finance.bean.ProductsBean;

public class StakingProductDetailPresenter extends BasePresenter<StakingProductDetailPresenter.StakingProductDetailUI> {
    public interface StakingProductDetailUI extends AppUI{
        void showFinanceDetail(ProductsBean response);
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    public void getFinanceDetail(String id) {
        FinanceApi.getStakingProductDetail(id,new SimpleResponseListener<ProductsBean>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ProductsBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showFinanceDetail(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
