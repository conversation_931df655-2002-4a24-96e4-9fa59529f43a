/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CurrentEntrustFragmentPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.presenter;

import android.text.TextUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.base.BaseListFreshPresenter;
import io.bhex.app.event.OrderFilterEvent;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BaseFragmentPresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.finance.FinanceApi;
import io.bhex.sdk.finance.bean.FinanceOrderListResponse;
import io.bhex.sdk.finance.bean.StakingOrderListResponse;
import io.bhex.sdk.trade.TradeApi;
import io.bhex.sdk.trade.bean.OpenOrderRequest;
import io.bhex.sdk.trade.bean.OpenOrderResponse;
import io.bhex.sdk.trade.bean.OrderBean;

/**
 * ================================================
 * 描   述：持有中
 * ================================================
 */

public class CurrentStakingFragmentPresenter extends BaseListFreshPresenter<CurrentStakingFragmentPresenter.CurrentStakingOrderUI> {

    private List<StakingOrderListResponse.ArrayBean> currentOrders = new ArrayList<>();
    private String orderPageId = "";

    public interface CurrentStakingOrderUI extends BaseListFreshPresenter.BaseListFreshUI {

        void showOrders(List<StakingOrderListResponse.ArrayBean> currentOrders);
    }

    @Override
    public void onVisibleChanged(boolean visible) {
        super.onVisibleChanged(visible);
    }

    /**
     * 获取进行中订单
     *
     * @param isLoadMore
     */
    public void getData(final boolean isLoadMore) {
        if (!UserInfo.isLogin()) {
            if (isLoadMore) {
                getUI().loadMoreComplete();
            }
            return;
        }
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }

        if (isLoadMore) {
            if (currentOrders != null) {
                if (!currentOrders.isEmpty()) {
                    orderPageId = currentOrders.get(currentOrders.size() - 1).getOrderId();
                }
            }
        }else{
            orderPageId ="";
        }

        FinanceApi.getStakingOrderList(orderPageId, new SimpleResponseListener<StakingOrderListResponse>() {
            @Override
            public void onFinish() {
                super.onFinish();
                if (isLoadMore) {
                    getUI().loadMoreComplete();
                }
            }

            @Override
            public void onSuccess(StakingOrderListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<StakingOrderListResponse.ArrayBean> data = response.getArray();
                    if (data != null) {

                        if (isLoadMore) {
                            if (data != null) {
                                currentOrders.addAll(data);
                            }

                        } else {
                            if (data != null) {
                                currentOrders.clear();
                                currentOrders = data;
                            }
                        }
                        getUI().showOrders(currentOrders);

                        if (data.size()< AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        }else{
                            getUI().loadMoreComplete();
                        }
                    }else{
                        getUI().loadMoreComplete();
                    }

                }else{
                    getUI().loadMoreFailed();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                if (isLoadMore) {
                    getUI().loadMoreFailed();
                }
            }
        });
    }

}
