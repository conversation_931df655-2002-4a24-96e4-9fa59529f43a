/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinanceListFragmentPresenter.java
 *   @Date: 19-4-24 下午3:34
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.presenter;

import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BaseFragmentPresenter;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.finance.FinanceApi;
import io.bhex.sdk.finance.bean.FinanceBean;
import io.bhex.sdk.finance.bean.FinanceListResponse;

public class FinanceListFragmentPresenter extends BaseFragmentPresenter<FinanceListFragmentPresenter.FinanceListUI> {
    public interface FinanceListUI extends AppUI{
        void showFinanceList(List<FinanceBean> data);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, FinanceListUI ui) {
        super.onUIReady(activity, ui);
    }

    @Override
    public void onResume() {
        super.onResume();
        getFinanceList();
    }

    public void getFinanceList() {
        FinanceApi.getFinanceList(new SimpleResponseListener<FinanceListResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(FinanceListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    List<FinanceBean> data = response.getArray();
                    if (data != null) {
                        getUI().showFinanceList(data);
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
