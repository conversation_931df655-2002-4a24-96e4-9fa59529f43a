/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CoinPlusOrdersPresenter.java
 *   @Date: 3/14/19 6:10 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.finance.presenter;

import android.text.TextUtils;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseListFreshPresenter;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.finance.FinanceApi;
import io.bhex.sdk.finance.bean.FinanceOrderListResponse;

public class CoinPlusOrdersPresenter extends BaseListFreshPresenter<CoinPlusOrdersPresenter.CoinPlusOrdersUI> {
    private static final String LOGTAG = "CoinPlusOrdersPresenter";
    private List<FinanceOrderListResponse.FinanceOrderBean> currentOrders = new ArrayList<>();

    public interface CoinPlusOrdersUI extends BaseListFreshPresenter.BaseListFreshUI {
        void showOrders(List<FinanceOrderListResponse.FinanceOrderBean> currentOrders);
    }


    @Override
    public void getData(final boolean isLoadMore) {
        if (!UserInfo.isLogin()) {
            if (isLoadMore) {
                getUI().loadMoreComplete();
            }
            return;
        }
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }

        if (isLoadMore) {
            if (currentOrders != null) {
                if (!currentOrders.isEmpty()) {
                    mPageId = currentOrders.get(currentOrders.size() - 1).id;
                }
            }
        }else{
            mPageId ="";
        }

        String pageId = "";

        if (isLoadMore && !TextUtils.isEmpty(mPageId)) {
            //加载更多
            pageId = mPageId;

        }
        FinanceApi.getFinanceOrderList(pageId, new SimpleResponseListener<FinanceOrderListResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                if (isLoadMore == false)
                    getUI().showProgressDialog("", "");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                if (isLoadMore) {
                    getUI().loadMoreComplete();
                }
                else
                    getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(FinanceOrderListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<FinanceOrderListResponse.FinanceOrderBean> data = response.array;
                    if (data != null) {

                        if (isLoadMore) {
                            if (data != null) {
                                currentOrders.addAll(data);
                            }

                        } else {
                            if (data != null) {
                                currentOrders.clear();
                                currentOrders = data;
                            }
                        }
                        getUI().showOrders(currentOrders);

                        if (data.size()< AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        }else{
                            getUI().loadMoreComplete();
                        }
                    }else{
                        getUI().loadMoreComplete();
                    }

                }else{
                    getUI().loadMoreFailed();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                if (isLoadMore) {
                    getUI().loadMoreFailed();
                }
            }
        });
    }

}

