/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinancePurchasePresenter.java
 *   @Date: 19-3-11 下午4:44
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.presenter;

import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.finance.FinanceApi;
import io.bhex.sdk.finance.bean.RepaymentScheduleResponse;
import io.bhex.sdk.finance.bean.StakingPurchaseResultResponse;

public class StakingOrderDetailPresenter extends BasePresenter<StakingOrderDetailPresenter.StakingOrderDetailUI> {

    public interface StakingOrderDetailUI extends AppUI{

        void updateRepaymentSchedule(List<RepaymentScheduleResponse.SchedulesBean> schedules);
    }

    /**
     *
     * @param orderId
     * @param productId
     */
    public void getRepaymentSchedule(String productId,String orderId) {
        FinanceApi.getRepaymentSchedule(productId,orderId,new SimpleResponseListener<RepaymentScheduleResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(RepaymentScheduleResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().updateRepaymentSchedule(response.getSchedules());
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

}
