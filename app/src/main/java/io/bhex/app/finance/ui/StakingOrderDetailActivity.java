/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinancePurchaseActivity.java
 *   @Date: 19-3-11 下午4:44
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.ui;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.bhex.util.NumberUtil;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.account.ui.OrderDetailActivity;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.finance.adapter.RepaymentScheduleAdapter;
import io.bhex.app.finance.presenter.StakingOrderDetailPresenter;
import io.bhex.app.finance.presenter.StakingPurchaseResultPresenter;
import io.bhex.app.utils.ActivityCache;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.finance.bean.RepaymentScheduleResponse;
import io.bhex.sdk.finance.bean.StakingOrderListResponse;
import io.bhex.sdk.finance.bean.StakingPurchaseResultResponse;

public class StakingOrderDetailActivity extends BaseActivity<StakingOrderDetailPresenter, StakingOrderDetailPresenter.StakingOrderDetailUI> implements StakingOrderDetailPresenter.StakingOrderDetailUI, View.OnClickListener, OnRefreshListener {
    private TopBar topBar;
    private StakingOrderListResponse.ArrayBean stakingOrder;
    private RecyclerView recyclerView;
    private RepaymentScheduleAdapter adapter;
    private SmartRefreshLayout swipeRefresh;
    private View emptyView;

    @Override
    protected int getContentView() {
        return R.layout.activity_staking_order_detail_layout;
    }

    @Override
    protected StakingOrderDetailPresenter createPresenter() {
        return new StakingOrderDetailPresenter();
    }

    @Override
    protected StakingOrderDetailPresenter.StakingOrderDetailUI getUI() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActivityCache.getInstance().addFinanceActivity(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        ActivityCache.getInstance().removeFinanceActivity(this);
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        this.setTopbarNight(topBar);
        topBar.setTitleGravity();
        Intent intent = getIntent();
        if (intent != null) {
            stakingOrder = (StakingOrderListResponse.ArrayBean)intent.getSerializableExtra(AppData.INTENT.KEY_ORDER);
        }
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        recyclerView = viewFinder.find(R.id.recyclerView);
        LayoutInflater layoutInflater = LayoutInflater.from(StakingOrderDetailActivity.this);
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
        emptyView.setLayoutParams(layoutParams);
        if (stakingOrder!=null) {
            viewFinder.textView(R.id.detail_order_id).setText(stakingOrder.getOrderId());
            viewFinder.textView(R.id.detail_product_id).setText(stakingOrder.getProductId());
            viewFinder.textView(R.id.tv_token).setText(stakingOrder.getTokenName());
            viewFinder.textView(R.id.tv_amount).setText(NumberUtils.subZeroAndDot(NumberUtils.roundFormat(stakingOrder.getPayAmount(), AppData.Config.DIGIT_DEFAULT_VALUE)));
            viewFinder.textView(R.id.tv_create_date).setText(DateUtils.getSimpleTimeFormat(stakingOrder.getCreatedAt(),AppData.Config.TIME_FORMAT2));
        }
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        swipeRefresh.setOnRefreshListener(this);
        viewFinder.find(R.id.copy_btn).setOnClickListener(this);
        viewFinder.find(R.id.copy_product_id_btn).setOnClickListener(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
        viewFinder.find(R.id.nestedScrollView).setBackgroundResource(CommonUtil.isBlackMode()?R.color.purchase_result_bg_night:R.color.purchase_result_bg);
        if (stakingOrder!=null) {
            getPresenter().getRepaymentSchedule(stakingOrder.getProductId(),stakingOrder.getOrderId());
        }
    }
    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        if (stakingOrder!=null) {
            getPresenter().getRepaymentSchedule(stakingOrder.getProductId(),stakingOrder.getOrderId());
        }
        refreshLayout.finishRefresh(1000);

    }
    @Override
    protected boolean isNeedLogin() {
        return true;
    }

    @Override
    public void updateRepaymentSchedule(List<RepaymentScheduleResponse.SchedulesBean> schedules) {
        if (adapter == null) {
            adapter = new RepaymentScheduleAdapter(StakingOrderDetailActivity.this, schedules);
            adapter.isFirstOnly(false);
            adapter.setEmptyView(emptyView);
            adapter.setEnableLoadMore(false);
            recyclerView.setLayoutManager(new LinearLayoutManager(StakingOrderDetailActivity.this));

            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(schedules);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.copy_btn:
                if (stakingOrder!=null) {
                    CommonUtil.copyText(StakingOrderDetailActivity.this, stakingOrder.getOrderId());
                }
                break;
            case R.id.copy_product_id_btn:
                if (stakingOrder!=null) {
                    CommonUtil.copyText(StakingOrderDetailActivity.this, stakingOrder.getProductId());
                }
                break;
        }
    }

}
