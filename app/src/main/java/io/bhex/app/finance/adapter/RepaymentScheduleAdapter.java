package io.bhex.app.finance.adapter;

import android.content.Context;
import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.DateUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.finance.bean.RepaymentScheduleResponse;
import io.bhex.sdk.trade.margin.bean.QueryRepayRecordResponse;

public class RepaymentScheduleAdapter extends BaseQuickAdapter<RepaymentScheduleResponse.SchedulesBean, BaseViewHolder> {

    private Context mContext;

    public RepaymentScheduleAdapter(Context context, List<RepaymentScheduleResponse.SchedulesBean> data) {
        super(R.layout.item_repayment_schedule_layout, data);
        mContext = context;
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final RepaymentScheduleResponse.SchedulesBean itemModel) {
        baseViewHolder.setText(R.id.tv_schedule_name, mContext.getResources().getString(R.string.string_schedule_name,itemModel.getSort()+"",getData().size()+""));
        if (TextUtils.isEmpty(itemModel.getRebateAmount())||itemModel.getRebateAmount().equals("0")) {
            baseViewHolder.setText(R.id.tv_repay_amount_value, mContext.getResources().getString(R.string.string_staking_floating_repayment));
        } else {
            baseViewHolder.setText(R.id.tv_repay_amount_value, itemModel.getRebateAmount() + itemModel.getTokenId());
        }
        if (itemModel.getStatus()==1) {
            baseViewHolder.setText(R.id.tv_repay_status, mContext.getResources().getString(R.string.string_repay_already));
            baseViewHolder.setBackgroundRes(R.id.rootView,R.color.done_schedule_bg);
        } else {
            baseViewHolder.setText(R.id.tv_repay_status, mContext.getResources().getString(R.string.string_repay_not_start));
            baseViewHolder.setBackgroundRes(R.id.rootView,R.color.white);
        }
        baseViewHolder.setText(R.id.tv_repay_date, mContext.getResources().getString(R.string.string_schedule_repay_date, DateUtils.getSimpleTimeFormat(itemModel.getRebateDate(), AppData.Config.TIME_FORMAT5)));
    }

}
