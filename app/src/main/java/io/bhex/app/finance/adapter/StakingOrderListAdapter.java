/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinanceListAdapter.java
 *   @Date: 19-3-8 下午4:26
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.adapter;

import android.content.Context;
import androidx.annotation.Nullable;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.finance.bean.StakingOrderListResponse;

public class StakingOrderListAdapter extends BaseQuickAdapter<StakingOrderListResponse.ArrayBean, BaseViewHolder> {

    public StakingOrderListAdapter(Context context, @Nullable List<StakingOrderListResponse.ArrayBean> data) {
        super(R.layout.item_staking_order_list_layout, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, StakingOrderListResponse.ArrayBean item) {
        helper.setText(R.id.finance_title,item.getProductName());
        int status = item.getStatus();
        if (status==0) {
            helper.setText(R.id.tv_status,mContext.getResources().getString(R.string.string_staking_order_status_selling));
            helper.setTextColor(R.id.tv_status,mContext.getResources().getColor(R.color.staking_order_subscribing));
            helper.setImageResource(R.id.iv_status,R.mipmap.icon_staking_order_subscribing);
        }else if (status==1) {
            helper.setText(R.id.tv_status,mContext.getResources().getString(R.string.string_staking_order_buy_succeed));
            helper.setTextColor(R.id.tv_status,mContext.getResources().getColor(R.color.staking_order_interest_accruing));
            helper.setImageResource(R.id.iv_status,R.mipmap.icon_staking_order_interest_accruing);
        }else if (status==2) {
            helper.setText(R.id.tv_status,mContext.getResources().getString(R.string.string_staking_order_buy_failed));
            helper.setTextColor(R.id.tv_status,mContext.getResources().getColor(R.color.staking_order_cancelled));
            helper.setImageResource(R.id.iv_status,R.mipmap.icon_staking_order_cancelled);
        }else if (status==3) {
            helper.setText(R.id.tv_status,mContext.getResources().getString(R.string.string_staking_order_start_instrest));
            helper.setTextColor(R.id.tv_status,mContext.getResources().getColor(R.color.staking_order_start_interest));
            helper.setImageResource(R.id.iv_status,R.mipmap.icon_staking_order_start_interest);
        }else if (status==4) {
            helper.setText(R.id.tv_status,mContext.getResources().getString(R.string.string_staking_order_status_finished));
            helper.setTextColor(R.id.tv_status,mContext.getResources().getColor(R.color.staking_order_finished));
            helper.setImageResource(R.id.iv_status,R.mipmap.icon_staking_order_finished);
        }

        helper.setText(R.id.tv_hold_amount_title,mContext.getResources().getString(R.string.string_staking_hold_amount,item.getTokenName()));
        helper.setText(R.id.tv_hold_amount_value,item.getPayAmount());
        helper.setText(R.id.tv_reference_apr_value,item.getReferenceApr()+"%");
        helper.setText(R.id.tv_days_value, item.getTimeLimit()+"");
        helper.setText(R.id.tv_end_date,mContext.getResources().getString(R.string.string_end_date)+" "+DateUtils.getSimpleTimeFormat(item.getProductEndDate(),AppData.Config.TIME_FORMAT4));
        helper.getConvertView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IntentUtils.goStakingOrderDetail(mContext,item);
            }
        });
    }

}
