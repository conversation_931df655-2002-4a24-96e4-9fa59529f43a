/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinanceListPresenter.java
 *   @Date: 19-3-8 下午2:18
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.presenter;

import android.os.Handler;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.BasicFunctionsUtil;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.BaseResponse;
import io.bhex.sdk.config.ConfigApi;
import io.bhex.sdk.config.bean.BasicFunctionsConfig;
import io.bhex.sdk.config.bean.KycInfoConfigResponse;
import io.bhex.sdk.finance.FinanceApi;
import io.bhex.sdk.finance.bean.FinanceBean;
import io.bhex.sdk.finance.bean.FinanceListResponse;
import io.bhex.sdk.finance.bean.StakingConfigResponse;
import io.bhex.sdk.finance.bean.StakingProductListResponse;
import io.bhex.sdk.utils.CacheUtils;
import io.bhex.sdk.utils.bean.BannerResponse;

public class FinanceListPresenter extends BasePresenter<FinanceListPresenter.FinanceListUI> {
    public interface FinanceListUI extends AppUI{
        void showFinanceList(List<FinanceBean> data);

        void showStakingProduct(List<StakingProductListResponse.ArrayBean> data);

        void updateStakingConfig(StakingConfigResponse response);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, FinanceListUI ui) {
        super.onUIReady(activity, ui);
        loadStakingConfigCache();
    }

    @Override
    public void onResume() {
        super.onResume();
        BasicFunctionsConfig basicFunctionsConfig = BasicFunctionsUtil.getBasicFunctionsConfig();
        if (!basicFunctionsConfig.isBonus()) {
            getFinanceList();
        }
        if (!basicFunctionsConfig.isStaking()) {
            getStakingProductList();
        }
        getStakingConfigInfo();
    }
    /**
     * 缓存-加载staking配置
     */
    private void loadStakingConfigCache() {

        StakingConfigResponse configResponse = CacheUtils.getCacheObject("cust.stakingSettings",StakingConfigResponse.class);

        if (configResponse != null) {
            getUI().updateStakingConfig(configResponse);
        }

    }
    public void getStakingConfigInfo() {
        ConfigApi.getCustomKVStakingConfig("cust.stakingSettings",new SimpleResponseListener<StakingConfigResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(StakingConfigResponse response) {
                super.onSuccess(response);
                if (response != null) {
                    getUI().updateStakingConfig(response);
                    CacheUtils.saveCache("cust.stakingSettings",response);
                }
            }

            @Override
            public StakingConfigResponse parserResponse(Handler uiHandler, String response, Class<StakingConfigResponse> clazz) {
                response = response.replace("cust.stakingSettings","stakingSettings");
//                KycInfoConfigResponse kycInfoConfigResponse = Convert.fromJson(response, clazz);
                return super.parserResponse(uiHandler, response, clazz);
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }


    public void getStakingProductList() {
        FinanceApi.getStakingProductList(new SimpleResponseListener<StakingProductListResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(StakingProductListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    List<StakingProductListResponse.ArrayBean> data = response.getArray();
                    if (data != null) {
                        getUI().showStakingProduct(data);
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public void getFinanceList() {
        FinanceApi.getFinanceList(new SimpleResponseListener<FinanceListResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(FinanceListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    List<FinanceBean> data = response.getArray();
                    if (data != null) {
                        getUI().showFinanceList(data);
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
