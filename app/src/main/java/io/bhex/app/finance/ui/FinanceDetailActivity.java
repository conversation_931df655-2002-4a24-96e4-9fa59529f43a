/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinanceDetailActivity.java
 *   @Date: 19-3-8 下午5:16
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.ui;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.google.android.material.tabs.TabLayout;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.finance.presenter.FinanceDetailPresenter;
import io.bhex.app.utils.ActivityCache;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.account.LoginResultCallback;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.finance.bean.FinanceBean;

public class FinanceDetailActivity extends BaseActivity<FinanceDetailPresenter,FinanceDetailPresenter.FinanceDetailUI> implements FinanceDetailPresenter.FinanceDetailUI, View.OnClickListener {

    private FinanceBean financeBean;
    private TopBar topBar;
    private TabLayout tabLayout;
    private TextView tipsContentTx;
    private TextView returnRateTx;
    private TextView surplusAmountTx;
    private String securityTipsContent;

    @Override
    protected int getContentView() {
        return R.layout.activity_finance_detail_layout;
    }

    @Override
    protected FinanceDetailPresenter createPresenter() {
        return new FinanceDetailPresenter();
    }

    @Override
    protected FinanceDetailPresenter.FinanceDetailUI getUI() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActivityCache.getInstance().addFinanceActivity(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        ActivityCache.getInstance().removeFinanceActivity(this);
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        Intent intent = getIntent();
        if (intent != null) {
           financeBean = (FinanceBean) intent.getSerializableExtra("detail");
            if (financeBean != null) {
                topBar.setTitle(financeBean.getProductName());
            }
        }
        tabLayout = viewFinder.find(R.id.tabLayout);
        TabLayout.Tab tab = tabLayout.newTab();
        tab.setText(getResources().getString(R.string.string_finance_product_rules));
        TabLayout.Tab tab1 = tabLayout.newTab();
        tab1.setText(getResources().getString(R.string.string_finance_qa));
        tabLayout.addTab(tab);
        tabLayout.addTab(tab1);
        CommonUtil.setUpIndicatorWidthByReflex(tabLayout,15,15);
        tab.select();
        returnRateTx = viewFinder.textView(R.id.returnRate);
        surplusAmountTx = viewFinder.textView(R.id.surplusAmount);
        tipsContentTx = viewFinder.textView(R.id.finance_trade_tips_content);
        securityTipsContent = getString(R.string.string_finance_security_rules);
        if (financeBean != null) {
            int tradeScale = NumberUtils.calNumerCount(this, financeBean.getTradeScale());
//            String perIncrease = NumberUtils.roundFormatDown(String.valueOf(Math.pow(0.1, tradeScale)),tradeScale);
            securityTipsContent = String.format(getString(R.string.string_finance_security_rules),financeBean.getUserLimit(),financeBean.getToken(),financeBean.getTradeMinAmount(),financeBean.getToken(),financeBean.getTradeScale(),financeBean.getToken());
            tipsContentTx.setText(securityTipsContent);
        }

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                if (tab.getPosition()==0) {
                    tipsContentTx.setText(securityTipsContent);
                }else{
                    tipsContentTx.setText(getString(R.string.string_finance_help));
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {

            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });
        viewFinder.find(R.id.btn_purchase).setOnClickListener(this);
    }

    @Override
    protected int setStatusBarColor() {
        return getResources().getColor(R.color.blue);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (financeBean != null) {
            getPresenter().getFinanceDetail(financeBean.getId());
        }
    }

    @Override
    public void showFinanceDetail(FinanceBean response) {
        if (response != null) {
            financeBean=response;
            int tradeScale = NumberUtils.calNumerCount(this, financeBean.getTradeScale());
//            String perIncrease = NumberUtils.roundFormatDown(String.valueOf(Math.pow(0.1, tradeScale)),tradeScale);
            securityTipsContent = String.format(getString(R.string.string_finance_security_rules),NumberUtils.roundFormatDown(financeBean.getUserLimit(),tradeScale),financeBean.getToken(),NumberUtils.roundFormatDown(financeBean.getTradeMinAmount(),tradeScale),financeBean.getToken(),financeBean.getTradeScale(),financeBean.getToken());
            surplusAmountTx.setText(getString(R.string.string_finance_surplus_amount)+" "+NumberUtils.roundFormatDown(financeBean.getDailyLastLimit(),tradeScale)+financeBean.getToken());
            returnRateTx.setText(NumberUtils.roundFormatDown(NumberUtils.mul(financeBean.getSevenYearRate(),"100"),2)+"%");
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.btn_purchase:
                UserInfo.LoginAndGoin(this, new LoginResultCallback() {
                    @Override
                    public void onLoginSucceed() {
                        super.onLoginSucceed();
                        if (financeBean != null) {
                            IntentUtils.goFinancePurchase(FinanceDetailActivity.this,financeBean.getId());
                        }
                    }
                });
                break;
        }
    }
}
