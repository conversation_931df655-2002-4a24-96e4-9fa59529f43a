/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinancePurchaseActivity.java
 *   @Date: 19-3-11 下午4:44
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.ui;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.TextView;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.finance.presenter.StakingPurchasePresenter;
import io.bhex.app.utils.ActivityCache;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.view.PointLengthFilter;
import io.bhex.app.view.TopBar;
import io.bhex.app.web.ui.WebActivity;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.LoginResultCallback;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.finance.bean.ProductsBean;

public class StakingPurchaseActivity extends BaseActivity<StakingPurchasePresenter, StakingPurchasePresenter.StakingPurchaseUI> implements StakingPurchasePresenter.StakingPurchaseUI, View.OnClickListener {
    private TopBar topBar;
    private ProductsBean financeBean;
    private EditText purchaseAamountEt;
    private CheckBox agreeCb;
    private View agreeFinance;
    private Button btnPurchase;
    private PointLengthFilter amountPointFilter;
    private String productId;
    private View btnClear;
    private String available;
    private TextView errorText;

    @Override
    protected int getContentView() {
        return R.layout.activity_staking_purchase_layout;
    }

    @Override
    protected StakingPurchasePresenter createPresenter() {
        return new StakingPurchasePresenter();
    }

    @Override
    protected StakingPurchasePresenter.StakingPurchaseUI getUI() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActivityCache.getInstance().addFinanceActivity(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        ActivityCache.getInstance().removeFinanceActivity(this);
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        this.setTopbarNight(topBar);
        Intent intent = getIntent();
        if (intent != null) {
            productId = intent.getStringExtra("productId");
        }
        topBar.setTitleGravity();
        purchaseAamountEt = viewFinder.find(R.id.purchase_amount_et);
        btnClear = viewFinder.find(R.id.input_clear);
        agreeCb = viewFinder.find(R.id.agreement);
        agreeFinance = viewFinder.find(R.id.agreement_finance);
        btnPurchase = viewFinder.find(R.id.btn_purchase);
        errorText = viewFinder.find(R.id.error_text);
        amountPointFilter = new PointLengthFilter();
        amountPointFilter.setDecimalLength(2);
        purchaseAamountEt.setFilters(new InputFilter[]{amountPointFilter});
        amountPointFilter.setDecimalLength(0);

        viewFinder.textView(R.id.tv_price_value).setText(getString(R.string.string_purchase_price, getString(R.string.string_placeholder),""));
        viewFinder.textView(R.id.tv_purchase_amount_hint).setText(getString(R.string.string_purchase_input_amount_format,getString(R.string.string_placeholder)));
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.agreement_finance).setOnClickListener(this);
        viewFinder.find(R.id.btn_recharge).setOnClickListener(this);
        viewFinder.find(R.id.btn_purchase).setOnClickListener(this);
        viewFinder.find(R.id.input_clear).setOnClickListener(this);
        viewFinder.find(R.id.amount_all).setOnClickListener(this);
        purchaseAamountEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                errorText.setVisibility(View.GONE);
                if (TextUtils.isEmpty(s)) {
                    btnPurchase.setEnabled(false);
                    btnClear.setVisibility(View.GONE);
                    viewFinder.find(R.id.tv_purchase_amount_hint).setVisibility(View.VISIBLE);
                } else {
                    btnPurchase.setEnabled(true);
                    btnClear.setVisibility(View.VISIBLE);
                    viewFinder.find(R.id.tv_purchase_amount_hint).setVisibility(View.GONE);
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (!TextUtils.isEmpty(productId)) {
            getPresenter().getFinanceDetail(productId);
        }
    }

    @Override
    protected boolean isNeedLogin() {
        return true;
    }

    @Override
    public void showFinanceDetail(ProductsBean response) {
        if (response != null) {
            financeBean = response;
            viewFinder.textView(R.id.tv_remain_amount_unit).setText(response.getTokenId());
            double remainCont = NumberUtils.sub(response.getUpLimitLots(), response.getSoldLots());
            double remainAmount = NumberUtils.mul(response.getPerLotAmount(), remainCont + "");
            viewFinder.textView(R.id.tv_purchase_amount_hint).setText(getString(R.string.string_purchase_input_amount_format,response.getPerUsrLowLots()));
            viewFinder.textView(R.id.tv_remain_amount_value).setText(NumberUtils.subZeroAndDot(NumberUtils.roundFormatDown(remainAmount, AppData.Config.DIGIT_DEFAULT_VALUE)));
            viewFinder.textView(R.id.tv_remain_avail_amount_value).setText(NumberUtils.subZeroAndDot(NumberUtils.sub(response.getPerUsrUpLots(), response.getUsrSubscribeLots()) + ""));
            viewFinder.textView(R.id.tv_price_value).setText(getString(R.string.string_purchase_price, response.getPerLotAmount(), response.getTokenId()));
        }
    }

    @Override
    public void updateAssettByToken(String tokenId, String free) {
        if (financeBean != null) {
            if (financeBean.getTokenId().equals(tokenId)) {
                available = free;
                viewFinder.textView(R.id.avaliable_amount).setText(getString(R.string.string_finance_avaliable_amount) + " " + NumberUtils.roundFormatDown(TextUtils.isEmpty(free) ? "0.00" : free, AppData.Config.DIGIT_DEFAULT_VALUE) + " " + tokenId);
            }
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.input_clear:
                purchaseAamountEt.setText("");
                break;
            case R.id.amount_all:
                if (TextUtils.isEmpty(available) || financeBean == null) {
                    purchaseAamountEt.setText("");
                } else {
                    double remainCont = NumberUtils.sub(financeBean.getUpLimitLots(), financeBean.getSoldLots());
                    double userRemainCont = NumberUtils.sub(financeBean.getPerUsrUpLots(), financeBean.getUsrSubscribeLots());
                    String assetAvail = NumberUtils.roundFormatDown(NumberUtils.div(financeBean.getPerLotAmount(), available), 0);
                    if (NumberUtils.sub(assetAvail, userRemainCont + "") > 0 && NumberUtils.sub(userRemainCont, remainCont) > 0) {
                        purchaseAamountEt.setText(NumberUtils.subZeroAndDot(NumberUtils.roundFormatDown(remainCont, 0)));
                    } else if (NumberUtils.sub(assetAvail, userRemainCont + "") > 0) {
                        purchaseAamountEt.setText(NumberUtils.subZeroAndDot(NumberUtils.roundFormatDown(userRemainCont, 0)));
                    } else if (NumberUtils.sub(assetAvail, remainCont + "") > 0) {
                        purchaseAamountEt.setText(NumberUtils.subZeroAndDot(NumberUtils.roundFormatDown(remainCont, 0)));
                    } else {
                        purchaseAamountEt.setText(NumberUtils.subZeroAndDot(NumberUtils.roundFormatDown(assetAvail, 0)));
                    }
                }
                break;
            case R.id.btn_recharge:
                UserInfo.LoginAndGoin(this, new LoginResultCallback() {
                    @Override
                    public void onLoginSucceed() {
                        super.onLoginSucceed();

                        if (financeBean != null)
                            IntentUtils.goMyAssetTokenDeposit(financeBean.getTokenId(), StakingPurchaseActivity.this);
                    }
                });
                break;
            case R.id.agreement_finance:
                if (financeBean != null&&!TextUtils.isEmpty(financeBean.getProtocolUrl())) {
                    WebActivity.runActivity(this, getResources().getString(R.string.string_staking_purchase_agreement), financeBean.getProtocolUrl());
                }
                break;

            case R.id.btn_purchase:
                UserInfo.LoginAndGoin(this, new LoginResultCallback() {
                    @Override
                    public void onLoginSucceed() {
                        super.onLoginSucceed();
                        if (financeBean != null) {
                            String amount = purchaseAamountEt.getText().toString().trim();
                            if (TextUtils.isEmpty(amount)) {
                                errorText.setVisibility(View.VISIBLE);
                                errorText.setText(getString(R.string.string_input_please)+getString(R.string.string_subscription_amount));
                                return;
                            }
                            String tradeMinAmount = financeBean.getPerUsrLowLots();
                            if (NumberUtils.sub(amount,tradeMinAmount)<0) {
                                errorText.setVisibility(View.VISIBLE);
                                errorText.setText(getString(R.string.string_staking_min_amount_limit,tradeMinAmount));
                                return;
                            }
                            double remainCont = NumberUtils.sub(financeBean.getUpLimitLots(), financeBean.getSoldLots());
                            double userRemainCont = NumberUtils.sub(financeBean.getPerUsrUpLots(), financeBean.getUsrSubscribeLots());
                            String assetAvail = NumberUtils.roundFormatDown(NumberUtils.div(financeBean.getPerLotAmount(), available), 0);

                            if (NumberUtils.sub(assetAvail,amount)<0) {
                                errorText.setVisibility(View.VISIBLE);
                                errorText.setText(getString(R.string.string_insufficient_balance));
                                return;
                            }

                            if (NumberUtils.sub(userRemainCont+"",amount)<0) {
                                errorText.setVisibility(View.VISIBLE);
                                errorText.setText(getString(R.string.string_staking_user_limit_amount_invalid,financeBean.getPerUsrUpLots(),financeBean.getUsrSubscribeLots(),NumberUtils.subZeroAndDot(NumberUtils.roundFormatDown(userRemainCont, 0))));
                                return;
                            }
                            if (NumberUtils.sub(remainCont+"",amount)<0) {
                                errorText.setVisibility(View.VISIBLE);
                                errorText.setText(getString(R.string.string_staking_total_remain_amount_invalid,NumberUtils.subZeroAndDot(NumberUtils.roundFormatDown(remainCont, 0))));
                                return;
                            }
                            double amountValue =Double.parseDouble(amount);
                            if (Math.round(amountValue) != amountValue) {
                                errorText.setVisibility(View.VISIBLE);
                                errorText.setError(getString(R.string.string_input_integer));
                                return;
                            }

                            if (!agreeCb.isChecked()) {
                                ToastUtils.showLong(StakingPurchaseActivity.this,getString(R.string.string_read_agree_staking_agreement_please)+getString(R.string.string_staking_purchase_agreement));
                                return;
                            }
                            getPresenter().purchase(financeBean,amount);
                        }else{
                            ToastUtils.showLong(StakingPurchaseActivity.this,getString(R.string.string_data_exception));
                        }
                    }
                });
                break;
        }
    }
}
