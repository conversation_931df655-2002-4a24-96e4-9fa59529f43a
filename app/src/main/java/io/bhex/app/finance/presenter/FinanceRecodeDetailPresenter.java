/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinanceRecodeDetailPresenter.java
 *   @Date: 19-3-12 下午4:40
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.presenter;

import android.content.Intent;
import android.text.TextUtils;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.finance.FinanceApi;
import io.bhex.sdk.finance.bean.RecordBeanResponse;

public class FinanceRecodeDetailPresenter extends BasePresenter<FinanceRecodeDetailPresenter.FinanceRecodeDetailUI> {
    private String recordId="";

    public interface FinanceRecodeDetailUI extends AppUI{
        void showRecordDetail(RecordBeanResponse response);

        void showUIView(boolean isFromOrderPage);

        void showSevenYearRate(String sevenYearRate);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, FinanceRecodeDetailUI ui) {
        super.onUIReady(activity, ui);
        Intent intent = getActivity().getIntent();
        if (intent != null) {
            boolean isFromOrderPage = intent.getBooleanExtra("isFromOrderPage",true);
            getUI().showUIView(isFromOrderPage);
            if (!isFromOrderPage) {
               String sevenYearRate = intent.getStringExtra("sevenYearRate");
               getUI().showSevenYearRate(sevenYearRate);
            }
            recordId = intent.getStringExtra("recordId");
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!TextUtils.isEmpty(recordId)) {
            getRecordDetail(recordId);
        }
    }

    /**
     * 获取记录详情
     * @param recordId
     */
    private void getRecordDetail(String recordId) {
        if (!UserInfo.isLogin()) {
            return;
        }
        FinanceApi.financeRecordDetail(recordId,new SimpleResponseListener<RecordBeanResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(RecordBeanResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showRecordDetail(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
