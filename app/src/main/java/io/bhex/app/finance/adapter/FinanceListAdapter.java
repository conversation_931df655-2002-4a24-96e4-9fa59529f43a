/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinanceListAdapter.java
 *   @Date: 19-3-8 下午4:26
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.adapter;

import android.content.Context;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.NumberUtils;
import io.bhex.sdk.finance.bean.FinanceBean;

public class FinanceListAdapter extends BaseQuickAdapter<FinanceBean, BaseViewHolder> {

    public FinanceListAdapter(Context context, @Nullable List<FinanceBean> data) {
        super(R.layout.item_finance_list_item_layout, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, FinanceBean item) {
        helper.setText(R.id.finance_title,item.getProductName());
        int status = item.getStatus();
        if (status==0) {
            helper.setBackgroundRes(R.id.finance_label,R.drawable.bg_staking_product_status_comming);
            helper.setText(R.id.finance_label,mContext.getResources().getString(R.string.string_finance_status_go_on_sale));
            helper.setVisible(R.id.finance_label,true);
            helper.setTextColor(R.id.finance_label, mContext.getResources().getColor(R.color.blue));
        }else if (status==3||status==4) {
            helper.setBackgroundRes(R.id.finance_label,R.drawable.bg_staking_product_finished);
            helper.setTextColor(R.id.finance_label, mContext.getResources().getColor(CommonUtil.isBlackMode()?R.color.gray_300_night:R.color.gray));
            helper.setText(R.id.finance_label,mContext.getResources().getString(R.string.string_finance_status_sold_out_today));
            helper.setVisible(R.id.finance_label,true);
        } else {
            helper.setBackgroundRes(R.id.finance_label,R.drawable.bg_staking_product_status_normal);
            helper.setText(R.id.finance_label,mContext.getResources().getString(R.string.string_finance_status_selling));
            helper.setVisible(R.id.finance_label,true);
            helper.setTextColor(R.id.finance_label, mContext.getResources().getColor(R.color.color_white));
        }
        helper.setText(R.id.finance_rate_of_return,NumberUtils.roundFormatDown(NumberUtils.mul(item.getSevenYearRate(),"100"),2)+"%");
        helper.setText(R.id.finance_amount,NumberUtils.roundFormatDown(item.getDailyLastLimit(),NumberUtils.calNumerCount(mContext,item.getTradeScale())));
        helper.setText(R.id.finance_amount_title,mContext.getResources().getString(R.string.string_finance_surplus_amount)+"("+item.getToken()+")");
        helper.addOnClickListener(R.id.itemView);
    }

}
