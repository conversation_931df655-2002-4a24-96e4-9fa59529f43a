/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MarginAdjustDialog.java
 *   @Date: 19-7-29 下午7:33
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.ui;

import android.app.Activity;
import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Build;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import java.util.Timer;
import java.util.TimerTask;

import io.bhex.app.R;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.finance.FinanceApi;
import io.bhex.sdk.finance.bean.StakingPurchaseResultResponse;

public class WaitingForPurchaseResultDialog {

    private Dialog bottomDialog;
    private Activity mContext;
    private View mContentView;
    int waitingSec = 6;
    String mProductId;
    String mTransferId;


    private final WaitingForPurchaseResultDialog.OnDialogObserver mDialogObserver;
    private TextView tv_count_down;
    private Handler mHandler;


    public interface OnDialogObserver {
        void onWaitingFinished();
        void onReceiveResult(StakingPurchaseResultResponse resultResponse);
    }

    public WaitingForPurchaseResultDialog(Activity context,String productId,String transferId, WaitingForPurchaseResultDialog.OnDialogObserver dialogObserver) {
        mContext = context;
        mDialogObserver = dialogObserver;
        mProductId = productId;
        mTransferId =transferId;
    }

    public void ShowDialog() {
        bottomDialog = new Dialog(mContext, R.style.TopDialog);
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.dialog_waiting_for_result_layout, null);
        bottomDialog.setContentView(mContentView);
        initView();

        int screenWidth = PixelUtils.getScreenWidth();
        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) mContentView.getLayoutParams();
        params.width = screenWidth - PixelUtils.dp2px(40);
        params.height = ViewGroup.LayoutParams.WRAP_CONTENT;
        mContentView.setLayoutParams(params);
        bottomDialog.setCanceledOnTouchOutside(false);
        bottomDialog.setCancelable(false);
        bottomDialog.getWindow().setGravity(Gravity.CENTER);
        bottomDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                stopTimer();
            }
        });
        bottomDialog.show();
        startTimer();
    }
    private Timer timer;
    private TimerTask timerTask;

    public static final long TIMER_DELAY = 0;
    public static final long TIMER_PERIOD = 1000l;
    private void startTimer() {
        mHandler = new Handler() {
            @Override
            public void handleMessage(Message msg) {
                switch (msg.what) {
                    case 1:
                        tv_count_down.setText(mContext.getString(R.string.string_second_formate, msg.obj.toString()));
                        break;
                    case 2:
                        if (mDialogObserver!=null) {
                            mDialogObserver.onWaitingFinished();
                        }
                        bottomDialog.dismiss();
                        break;
                    default:
                }

            }
        };
        timer = new Timer();
        timerTask = new TimerTask() {
            @Override
            public void run() {
                if (waitingSec>0) {
                    Message message = mHandler.obtainMessage(1, 0, 0, waitingSec+"");
                    mHandler.sendMessage(message);
                } else {
                    DebugLog.e("Waiting task ended");
                    Message message = mHandler.obtainMessage(2, 0, 0, "");
                    mHandler.sendMessage(message);
                }
                if (waitingSec%2!=0) {
                    getSubscribeResult();
                }
                waitingSec--;
            }
        };
        timer.schedule(timerTask, TIMER_DELAY, TIMER_PERIOD);
    }


    private void stopTimer() {
        if (timerTask!=null){
            timerTask.cancel();
        }
        if (timer != null) {
            timer.cancel();
        }
    }

    private void initView() {
        initSystemBarTint();

        try {
            tv_count_down= mContentView.findViewById(R.id.tv_count_down);
            tv_count_down.setText(mContext.getString(R.string.string_second_formate,waitingSec+""));
        } catch (Exception e) {
        }
    }

    /**
     * 设置状态栏颜色
     */
    protected void initSystemBarTint() {
        Window window = bottomDialog.getWindow();
        // 沉浸式状态栏
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            //5.0以上使用原生方法
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(mContext.getResources().getColor(R.color.white));
        }
    }

    public void getSubscribeResult() {
        if (!UserInfo.isLogin()) {
            return;
        }
        if (TextUtils.isEmpty(mProductId)||TextUtils.isEmpty(mTransferId)) {
            return;
        }
        FinanceApi.getSubscribeResult(mProductId,mTransferId,new SimpleResponseListener<StakingPurchaseResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(StakingPurchaseResultResponse response) {
                super.onSuccess(response);
                // 不是这里粗暴处理，即使是其他状态也等待，防止出现请求的错误导致等待终止，53800处理中
                if(CodeUtils.isSuccess(response)) {

                    if (response != null) {
                        DebugLog.e("FinanceApi.getSubscribeResult result");
                        if (mDialogObserver != null) {
                            mDialogObserver.onReceiveResult(response);
                        }
                        bottomDialog.dismiss();
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });

    }
}
