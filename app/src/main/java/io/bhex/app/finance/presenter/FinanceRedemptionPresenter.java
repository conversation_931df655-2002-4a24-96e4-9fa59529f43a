/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinanceRedemptionPresenter.java
 *   @Date: 19-3-11 下午4:47
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.presenter;

import android.text.TextUtils;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.IntentUtils;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.finance.FinanceApi;
import io.bhex.sdk.finance.bean.FinanceAssetDetailResponse;
import io.bhex.sdk.finance.bean.FinanceBean;
import io.bhex.sdk.finance.bean.FinanceRecordBeanResponse;

public class FinanceRedemptionPresenter extends BasePresenter<FinanceRedemptionPresenter.FinanceRedemptionUI> {
    public interface FinanceRedemptionUI extends AppUI{

        void showAsset(FinanceAssetDetailResponse response);
    }

    /**
     * 获取资产详情
     * @param productId
     */
    public void getAsset(final String productId) {
        if (!UserInfo.isLogin()) {
            return;
        }
        FinanceApi.financeAssetDetail(productId,new SimpleResponseListener<FinanceAssetDetailResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(FinanceAssetDetailResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    getUI().showAsset(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    /**
     * 赎回
     * @param financeBean
     * @param amount
     */
    public void redeem(final FinanceBean financeBean, String amount) {
        FinanceApi.financeRedeem(financeBean.getId(),amount,new SimpleResponseListener<FinanceRecordBeanResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(FinanceRecordBeanResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    String recordId = response.getRecordId();
                    if (!TextUtils.isEmpty(recordId)) {
                        ToastUtils.showShort(getActivity(),getString(R.string.string_finance_redeem_success));
                        IntentUtils.goFinanceResult(getActivity(),false,financeBean.getSevenYearRate(),response.getRecordId());
                    }else{
                        ToastUtils.showShort(getActivity(),getString(R.string.string_data_exception));
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
