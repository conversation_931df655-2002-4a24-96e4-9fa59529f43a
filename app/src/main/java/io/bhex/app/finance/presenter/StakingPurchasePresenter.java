/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinancePurchasePresenter.java
 *   @Date: 19-3-11 下午4:44
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.presenter;

import android.text.TextUtils;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.finance.ui.WaitingForPurchaseResultDialog;
import io.bhex.app.utils.IntentUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.finance.FinanceApi;
import io.bhex.sdk.finance.bean.ProductsBean;
import io.bhex.sdk.finance.bean.StakingPurchaseResponse;
import io.bhex.sdk.finance.bean.StakingPurchaseResultResponse;
import io.bhex.sdk.trade.AssetApi;
import io.bhex.sdk.trade.bean.AssetDataResponse;

public class StakingPurchasePresenter extends BasePresenter<StakingPurchasePresenter.StakingPurchaseUI> {

    public interface StakingPurchaseUI extends AppUI {

        void showFinanceDetail(ProductsBean response);

        void updateAssettByToken(String tokenId, String free);
    }


    public void getFinanceDetail(String productId) {
        if (!UserInfo.isLogin()) {
            return;
        }

        FinanceApi.getStakingProductDetail(productId, new SimpleResponseListener<ProductsBean>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("", "");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ProductsBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    if (response != null) {
                        getUI().showFinanceDetail(response);
                        getAsset(response.getTokenId());
                    }

                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });

    }

    /**
     * 获取资产
     *
     * @param tokenId
     */
    protected void getAsset(final String tokenId) {
        if (!UserInfo.isLogin()) {
            return;
        }
        AssetApi.RequestTokenIdAsset(tokenId, new SimpleResponseListener<AssetDataResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("", "");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(AssetDataResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<AssetDataResponse.ArrayBean> data = response.getArray();
                    if (data != null) {
                        if (data.size() > 0) {
                            AssetDataResponse.ArrayBean assetBean = data.get(0);
                            if (assetBean != null) {
                                getUI().updateAssettByToken(tokenId, assetBean.getFree());
                                return;
                            }
                        }
                    }
                    getUI().updateAssettByToken(tokenId, "");
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public void getSubscribeResult(String productId, String transferId) {
        if (!UserInfo.isLogin()) {
            return;
        }
        FinanceApi.getSubscribeResult(productId, transferId, new SimpleResponseListener<StakingPurchaseResultResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("", "");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(StakingPurchaseResultResponse response) {
                super.onSuccess(response);
                if (response != null && !TextUtils.isEmpty(response.getCode()) && response.getCode().equals("53800")) {  //处理中
                    ToastUtils.showLong(getActivity(), getString(R.string.string_waiting_for_result_failed));
                    getFinanceDetail(productId);
                } else if (CodeUtils.isSuccess(response, true)) {
                    if (response.getCode().equals("200")) {
                        IntentUtils.goStakingPurchaseResult(getActivity(), productId, transferId);
                    }
                } else {
                    getFinanceDetail(productId);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });

    }

    /**
     * 申购成功
     *
     * @param financeBean
     * @param amount
     */
    public void purchase(final ProductsBean financeBean, String amount) {
        FinanceApi.stakingPurchase(financeBean.getType(),financeBean.getProductId(), amount, new SimpleResponseListener<StakingPurchaseResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("", "");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(StakingPurchaseResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    WaitingForPurchaseResultDialog dialog = new WaitingForPurchaseResultDialog(getActivity(), financeBean.getProductId(), response.getTransferId(), new WaitingForPurchaseResultDialog.OnDialogObserver() {
                        @Override
                        public void onWaitingFinished() {
                            getSubscribeResult(financeBean.getProductId(), response.getTransferId());
                        }

                        @Override
                        public void onReceiveResult(StakingPurchaseResultResponse resultResponse) {
                            if (response != null && !TextUtils.isEmpty(response.getCode()) && response.getCode().equals("53800")) {  //处理中
                                ToastUtils.showLong(getActivity(), getString(R.string.string_waiting_for_result_failed));
                                getFinanceDetail(financeBean.getProductId());
                            } else if (CodeUtils.isSuccess(resultResponse, true)) {
                                if (response.getCode().equals("200")) {
                                    IntentUtils.goStakingPurchaseResult(getActivity(), financeBean.getProductId(), response.getTransferId());
                                }
                            } else {
                                getFinanceDetail(financeBean.getProductId());
                            }
                        }
                    });
                    dialog.ShowDialog();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

}
