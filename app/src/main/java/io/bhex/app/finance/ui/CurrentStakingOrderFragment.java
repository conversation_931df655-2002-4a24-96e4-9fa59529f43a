/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CurrentEntrustOrderFragment.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.ui;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.List;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import io.bhex.app.base.BaseListFreshFragment;
import io.bhex.app.finance.adapter.StakingOrderListAdapter;
import io.bhex.app.finance.presenter.CurrentStakingFragmentPresenter;
import io.bhex.sdk.finance.bean.StakingOrderListResponse;

/**
 * ================================================
 * 描   述：持有中
 * ================================================
 */

public class CurrentStakingOrderFragment extends BaseListFreshFragment<CurrentStakingFragmentPresenter, CurrentStakingFragmentPresenter.CurrentStakingOrderUI> implements CurrentStakingFragmentPresenter.CurrentStakingOrderUI, BaseQuickAdapter.RequestLoadMoreListener, OnRefreshListener {

    @Override
    protected CurrentStakingFragmentPresenter.CurrentStakingOrderUI getUI() {
        return this;
    }

    @Override
    protected CurrentStakingFragmentPresenter createPresenter() {
        return new CurrentStakingFragmentPresenter();
    }

    @Override
    public void showOrders(List<StakingOrderListResponse.ArrayBean> currentOrders) {
        if (adapter == null) {
            adapter = new StakingOrderListAdapter(getActivity(),currentOrders);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this,recyclerView);
            adapter.setEnableLoadMore(true);
            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            recyclerView.setItemAnimator(new DefaultItemAnimator());

            adapter.setEmptyView(emptyView);
            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(currentOrders);
        }
    }

}
