/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CoinPlusOrdersFragment.java
 *   @Date: 3/14/19 6:09 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.finance.ui;

import android.os.Bundle;
import android.view.View;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseListFreshFragment;
import io.bhex.app.finance.presenter.CoinPlusOrdersPresenter;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.sdk.finance.bean.FinanceOrderListResponse;
import io.bhex.sdk.trade.bean.OptionAssetListResponse;

import static io.bhex.baselib.constant.AppData.INTENT.KEY_ASSET;

public class CoinPlusOrdersFragment extends BaseListFreshFragment<CoinPlusOrdersPresenter, CoinPlusOrdersPresenter.CoinPlusOrdersUI> implements CoinPlusOrdersPresenter.CoinPlusOrdersUI {
    @Override
    protected CoinPlusOrdersPresenter.CoinPlusOrdersUI getUI() {
        return this;
    }

    private OptionAssetListResponse.OptionAssetBean assetItemBean;
    @Override
    protected CoinPlusOrdersPresenter createPresenter() {
        return new CoinPlusOrdersPresenter();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if(getArguments() != null){
            try {

                assetItemBean = (OptionAssetListResponse.OptionAssetBean)getArguments().getSerializable(KEY_ASSET);
            }
            catch (Exception e){

            }
        }
    }

    @Override
    public void showOrders(List<FinanceOrderListResponse.FinanceOrderBean> currentOrders) {
        if (adapter == null) {
            adapter = new CoinPlusRecordAdapter(currentOrders);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this, recyclerView);
            adapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                    //to do
                    List<FinanceOrderListResponse.FinanceOrderBean> data = adapter.getData();
                    if (data != null ) {
                        FinanceOrderListResponse.FinanceOrderBean financeOrderBean = data.get(position);
                        if (financeOrderBean != null && financeOrderBean.type != 2) {
                            IntentUtils.goFinanceResult(getActivity(),true,"",financeOrderBean.id);
                        }
                    }
                }
            });
            adapter.setEnableLoadMore(true);

            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            recyclerView.setItemAnimator(new DefaultItemAnimator());

            adapter.setEmptyView(emptyView);
            recyclerView.setAdapter(adapter);

        } else {
            adapter.setNewData(currentOrders);
        }
    }

    public class CoinPlusRecordAdapter extends BaseQuickAdapter<FinanceOrderListResponse.FinanceOrderBean, BaseViewHolder> {


        public CoinPlusRecordAdapter(List<FinanceOrderListResponse.FinanceOrderBean> data) {
            super(R.layout.item_coinplus_order_layout, data);
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final FinanceOrderListResponse.FinanceOrderBean itemModel) {

            if(itemModel == null)
                return;
            //baseViewHolder.setVisible(R.id.item_divider, baseViewHolder.getAdapterPosition() != mData.size());

            baseViewHolder.setText(R.id.item_record_name, itemModel.token);
            baseViewHolder.setText(R.id.item_record_amount, itemModel.amount);
            String type = "";
            if(itemModel.type == 0)
                type = getString(R.string.string_asset_coinplus_buy);
            else if(itemModel.type == 1)
                type = getString(R.string.string_asset_coinplus_sell);
            else if(itemModel.type == 2) {
                type = getString(R.string.string_asset_coinplus_get);
                baseViewHolder.setVisible(R.id.arrow_image, true);
            }
            baseViewHolder.setText(R.id.item_record_status, type);
            baseViewHolder.setText(R.id.item_record_time, DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.updatedAt), "HH:mm:ss yyyy/MM/dd"));
            baseViewHolder.addOnClickListener(R.id.itemView);
        }

    }

}
