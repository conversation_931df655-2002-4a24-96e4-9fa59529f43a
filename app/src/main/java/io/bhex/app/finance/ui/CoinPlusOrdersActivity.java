/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CoinPlusOrdersActivity.java
 *   @Date: 3/14/19 4:30 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.finance.ui;

import android.view.View;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.view.TopBar;
import io.bhex.app.web.presenter.NullPresenter;

public class CoinPlusOrdersActivity extends BaseActivity<NullPresenter,NullPresenter.NullUI> implements NullPresenter.NullUI {
    private CoinPlusOrdersFragment mCoinPlusOrdersFragment;
    private TopBar topBar;

    @Override
    protected int getContentView() {
        return R.layout.activity_fragment_container_layout;
    }

    @Override
    protected NullPresenter createPresenter() {
        return new NullPresenter();
    }

    @Override
    protected NullPresenter.NullUI getUI() {
        return this;
    }

    @Override
    protected boolean isNeedLogin() {
        return true;
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = this.findViewById(R.id.topBar);
        this.setTopbarNight(topBar);
        topBar.setLeftOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        mCoinPlusOrdersFragment = new CoinPlusOrdersFragment();
        topBar.setTitle(getString(R.string.string_asset_coinplus_order));

        getSupportFragmentManager()    //
                .beginTransaction()
                .add(R.id.fragment_container, mCoinPlusOrdersFragment)   // 此处的R.id.fragment_container是要盛放fragment的父容器
                .commit();

    }

}
