/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AllOrdersActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.ui;

import android.util.Pair;
import android.view.View;

import com.google.android.material.tabs.TabLayout;

import java.util.ArrayList;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;
import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.finance.presenter.AllStakingOrderPresenter;
import io.bhex.app.skin.view.SkinTabLayout;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.view.TopBar;

/**
 * ================================================
 * 描   述：理财订单
 * ================================================
 */

public class AllStakingOrdersActivity extends BaseActivity<AllStakingOrderPresenter, AllStakingOrderPresenter.AllStakingOrderUI> implements AllStakingOrderPresenter.AllStakingOrderUI, View.OnClickListener, ViewPager.OnPageChangeListener {
    private SkinTabLayout tabLayout;
    private ViewPager viewPager;
    private ArrayList<Pair<String, Fragment>> items;
    private EntrustAdapter entrustAdapter;
    private TopBar topBar;

    @Override
    protected int getContentView() {
        return R.layout.activity_all_staking_orders_layout;
    }

    @Override
    protected AllStakingOrderPresenter createPresenter() {
        return new AllStakingOrderPresenter();
    }

    @Override
    protected AllStakingOrderPresenter.AllStakingOrderUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        tabLayout = viewFinder.find(R.id.tabLayout);
        viewPager = viewFinder.find(R.id.viewPager);
        initTabs();
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewPager.addOnPageChangeListener(this);
    }

    private void initTabs() {
        items = new ArrayList<>();

        items.add(new Pair<String, Fragment>(getString(R.string.string_unfinished_staking_order), new CurrentStakingOrderFragment()));
        items.add(new Pair<String, Fragment>(getString(R.string.string_finished_staking_order), new StakingOrderHistoryFragment()));
        entrustAdapter = new EntrustAdapter(getSupportFragmentManager());
        viewPager.setAdapter(entrustAdapter);
        tabLayout.setupWithViewPager(viewPager);
//        tab.setTabTextColors(getResources().getColor(R.color.color_white),getResources().getColor(R.color.color_black));
        tabLayout.setTabMode(TabLayout.MODE_SCROLLABLE);
        tabLayout.setTabGravity(TabLayout.GRAVITY_CENTER);

//        viewPager.addOnPageChangeListener(this);
        CommonUtil.setUpIndicatorWidthByReflex(tabLayout,15,15);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

        }
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    private class EntrustAdapter extends FragmentPagerAdapter {

        public EntrustAdapter(FragmentManager fm) {
            super(fm);
        }

        @Override
        public Fragment getItem(int position) {
            return items.get(position).second;
        }

        @Override
        public int getCount() {
            return items.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return items.get(position).first;
        }

    }
}
