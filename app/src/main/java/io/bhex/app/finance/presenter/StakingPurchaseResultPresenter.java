/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinancePurchasePresenter.java
 *   @Date: 19-3-11 下午4:44
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.presenter;

import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.BaseResponse;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.finance.FinanceApi;
import io.bhex.sdk.finance.bean.ProductsBean;
import io.bhex.sdk.finance.bean.RepaymentScheduleResponse;
import io.bhex.sdk.finance.bean.StakingPurchaseResultResponse;

public class StakingPurchaseResultPresenter extends BasePresenter<StakingPurchaseResultPresenter.StakingPurchaseResultUI> {

    public interface StakingPurchaseResultUI extends AppUI{

        void updateRepaymentSchedule(List<RepaymentScheduleResponse.SchedulesBean> schedules);

        void showPurchaseResult(StakingPurchaseResultResponse response);
    }


    public void getSubscribeResult(String productId,String transferId) {
        if (!UserInfo.isLogin()) {
            return;
        }

        FinanceApi.getSubscribeResult(productId,transferId,new SimpleResponseListener<StakingPurchaseResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("", "");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(StakingPurchaseResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    if (response != null) {
                        if (response.getCode().equals("200")) {
                            getUI().showPurchaseResult(response);
                            getRepaymentSchedule(response.getOrderId(),productId);
                        }
                    }

                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });

    }

    /**
     *
     * @param orderId
     * @param productId
     */
    public void getRepaymentSchedule(String orderId,String productId) {
        FinanceApi.getRepaymentSchedule(productId,orderId,new SimpleResponseListener<RepaymentScheduleResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(RepaymentScheduleResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().updateRepaymentSchedule(response.getSchedules());
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

}
