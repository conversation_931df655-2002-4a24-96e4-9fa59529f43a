/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinancePurchaseActivity.java
 *   @Date: 19-3-11 下午4:44
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.ui;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.finance.adapter.RepaymentScheduleAdapter;
import io.bhex.app.finance.presenter.StakingPurchaseResultPresenter;
import io.bhex.app.utils.ActivityCache;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.finance.bean.ProductsBean;
import io.bhex.sdk.finance.bean.RepaymentScheduleResponse;
import io.bhex.sdk.finance.bean.StakingPurchaseResultResponse;

public class StakingPurchaseResultActivity extends BaseActivity<StakingPurchaseResultPresenter, StakingPurchaseResultPresenter.StakingPurchaseResultUI> implements StakingPurchaseResultPresenter.StakingPurchaseResultUI, View.OnClickListener, OnRefreshListener {
    private TopBar topBar;
    private String productId;
    private String transferId;
    private RecyclerView recyclerView;
    private RepaymentScheduleAdapter adapter;
    private SmartRefreshLayout swipeRefresh;
    private View emptyView;

    @Override
    protected int getContentView() {
        return R.layout.activity_staking_purchase_result_layout;
    }

    @Override
    protected StakingPurchaseResultPresenter createPresenter() {
        return new StakingPurchaseResultPresenter();
    }

    @Override
    protected StakingPurchaseResultPresenter.StakingPurchaseResultUI getUI() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActivityCache.getInstance().addFinanceActivity(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        ActivityCache.getInstance().removeFinanceActivity(this);
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        this.setTopbarNight(topBar);
        topBar.setTitleGravity();
        Intent intent = getIntent();
        if (intent != null) {
            productId = intent.getStringExtra("productId");
            transferId = intent.getStringExtra("transferId");
        }
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        recyclerView = viewFinder.find(R.id.recyclerView);

        topBar.setRightOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // FinanceList是singletask，不需要清理中间的activity
                IntentUtils.goFinanceList(StakingPurchaseResultActivity.this);
            }
        });
        LayoutInflater layoutInflater = LayoutInflater.from(StakingPurchaseResultActivity.this);
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
        emptyView.setLayoutParams(layoutParams);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            // FinanceList是singletask，不需要清理中间的activity
            IntentUtils.goFinanceList(StakingPurchaseResultActivity.this);
            return true;
        }

        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        swipeRefresh.setOnRefreshListener(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
        viewFinder.find(R.id.nestedScrollView).setBackgroundResource(CommonUtil.isBlackMode()?R.color.purchase_result_bg_night:R.color.purchase_result_bg);
        if (!TextUtils.isEmpty(productId)) {
            getPresenter().getSubscribeResult(productId,transferId);
        }
    }
    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        getPresenter().getSubscribeResult(productId,transferId);
        refreshLayout.finishRefresh(1000);

    }
    @Override
    protected boolean isNeedLogin() {
        return true;
    }

    @Override
    public void updateRepaymentSchedule(List<RepaymentScheduleResponse.SchedulesBean> schedules) {
        if (adapter == null) {
            adapter = new RepaymentScheduleAdapter(StakingPurchaseResultActivity.this, schedules);
            adapter.isFirstOnly(false);
            adapter.setEmptyView(emptyView);
            adapter.setEnableLoadMore(false);
            recyclerView.setLayoutManager(new LinearLayoutManager(StakingPurchaseResultActivity.this));

            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(schedules);
        }
    }

    @Override
    public void showPurchaseResult(StakingPurchaseResultResponse response) {
        if (response != null) {
            viewFinder.textView(R.id.tv_purchase_amount).setText(getString(R.string.tv_purchase_succeed_amount,response.getAmount(),response.getTokenName()));
            viewFinder.textView(R.id.tv_reference_apr).setText(getString(R.string.string_reference_apr_formate,response.getReferenceApr()+"%"));
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

        }
    }

}
