/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinanceRecodeDetailActivity.java
 *   @Date: 19-3-12 下午4:41
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.ui;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.finance.presenter.FinanceRecodeDetailPresenter;
import io.bhex.app.utils.ActivityCache;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.finance.bean.RecordBeanResponse;

public class FinanceRecodeDetailActivity extends BaseActivity<FinanceRecodeDetailPresenter, FinanceRecodeDetailPresenter.FinanceRecodeDetailUI> implements FinanceRecodeDetailPresenter.FinanceRecodeDetailUI, View.OnClickListener {
    private View headerResultView;
    private TextView resultStatusTx;
    private View headerRecordView;
    private TextView resultAmountTx;
    private TextView resultTipsTx;
    private TextView recordNameTx;
    private TextView recordAmountTx;
    private TextView recordTimeTx;
    private TextView recordStatusTx;
    private View redeemSuccessView;
    private View progressA;
    private ImageView progressAImg;
    private TextView progressATime;
    private TextView progressADesc;
    private View progressB;
    private ImageView progressBImg;
    private TextView progressBTime;
    private TextView progressBDesc;
    private View progressC;
    private ImageView progressCImg;
    private TextView progressCTime;
    private TextView progressCDesc;
    private boolean mFromOrderPage;
    private String currentSevenYearRate="";
    private RecordBeanResponse currentRecordBean;
    private TopBar topBar;

    @Override
    protected int getContentView() {
        return R.layout.activity_finance_record_detail_layout;
    }

    @Override
    protected FinanceRecodeDetailPresenter createPresenter() {
        return new FinanceRecodeDetailPresenter();
    }

    @Override
    protected FinanceRecodeDetailPresenter.FinanceRecodeDetailUI getUI() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActivityCache.getInstance().addFinanceActivity(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        ActivityCache.getInstance().removeFinanceActivity(this);
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        this.setTopbarNight(topBar);
        headerResultView = viewFinder.find(R.id.result_header);
        headerRecordView = viewFinder.find(R.id.record_header);
        //成功赎回状态View
        redeemSuccessView = viewFinder.find(R.id.redeem_success);
        //申购赎回结果状态
        resultStatusTx = viewFinder.textView(R.id.status);
        resultAmountTx = viewFinder.textView(R.id.aboutClosePrice);
        resultTipsTx = viewFinder.textView(R.id.result_tips);
        //订单详情状态
        recordNameTx = viewFinder.textView(R.id.record_name);
        recordAmountTx = viewFinder.textView(R.id.record_amount);
        recordTimeTx = viewFinder.textView(R.id.record_time);
        recordStatusTx = viewFinder.textView(R.id.record_status);
        /**状态进度*************/
        //第一步
        progressA = viewFinder.find(R.id.progress_a);
        progressAImg = viewFinder.imageView(R.id.progress_a_img);
        progressATime = viewFinder.textView(R.id.progress_a_time);
        progressADesc = viewFinder.textView(R.id.progress_a_desc);
        //第二步
        progressB = viewFinder.find(R.id.progress_b);
        progressBImg = viewFinder.imageView(R.id.progress_b_img);
        progressBTime = viewFinder.textView(R.id.progress_b_time);
        progressBDesc = viewFinder.textView(R.id.progress_b_desc);
        //第三步
        progressC = viewFinder.find(R.id.progress_c);
        progressCImg = viewFinder.imageView(R.id.progress_c_img);
        progressCTime = viewFinder.textView(R.id.progress_c_time);
        progressCDesc = viewFinder.textView(R.id.progress_c_desc);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.btn_view_assets).setOnClickListener(this);
    }

    @Override
    protected boolean isNeedLogin() {
        return true;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_view_assets:
                IntentUtils.goCoinPlusAsset(this);
                if (!mFromOrderPage) {
                    ActivityCache.getInstance().finishFinanceActivity();
                }
                break;
        }
    }

    @Override
    public void showUIView(boolean isFromOrderPage) {
        mFromOrderPage = isFromOrderPage;
        if (isFromOrderPage) {
            //来自订单页
            headerResultView.setVisibility(View.GONE);
            headerRecordView.setVisibility(View.VISIBLE);
            viewFinder.find(R.id.btn_view_assets).setVisibility(View.GONE);
        }else{
            //来自申购赎回页
            headerResultView.setVisibility(View.VISIBLE);
            headerRecordView.setVisibility(View.GONE);
            viewFinder.find(R.id.btn_view_assets).setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void showRecordDetail(RecordBeanResponse response) {
        if (response != null) {
            currentRecordBean = response;
            int type = response.getType();
            int status = response.getStatus();
            String serverTime = response.getServerTime();
            String updatedAt = response.getUpdatedAt();
            String createdAt = response.getCreatedAt();
            String statisticsTime = response.getStatisticsTime();
            redeemSuccessView.setVisibility(View.GONE);
            if (type==0) {//申购
                topBar.setTitle(getString(R.string.string_finance_purchase_result));
                if (status==1) {//申购成功
                    recordStatusTx.setText(getString(R.string.string_finance_purchase_success));
                    resultStatusTx.setText(getString(R.string.string_finance_purchase_success));

                    progressATime.setText(DateUtils.getSimpleTimeFormat(updatedAt,AppData.Config.TIME_FORMAT_MD));
                    progressADesc.setText(getString(R.string.string_finance_purchase_success));

                    progressBTime.setText(DateUtils.addDays(updatedAt,1,AppData.Config.TIME_FORMAT_MD));
                    progressBDesc.setText(getString(R.string.string_finance_cal_profit));

                    progressCTime.setText(DateUtils.addDays(updatedAt,2,AppData.Config.TIME_FORMAT_MD));
                    progressCDesc.setText(getString(R.string.string_finance_profit_to_account));
                    setStatusViewVisible(true,true,true);
                    int diffDay = DateUtils.calDaysByCalendar(response.getServerTime(),response.getUpdatedAt());
                    if (diffDay==0) {
                        //T+0
                        progressAImg.setImageResource(R.mipmap.progressing);
                        progressBImg.setImageResource(R.mipmap.progress_grey);
                        progressCImg.setImageResource(R.mipmap.icon_progress_circle_grey);

                    }else if(diffDay==1){
                        //T+1
                        progressAImg.setImageResource(R.mipmap.progress_complete);
                        progressBImg.setImageResource(R.mipmap.progressing);
                        progressCImg.setImageResource(R.mipmap.icon_progress_circle_grey);
                    }else{
                        //T+2及以上
                        progressAImg.setImageResource(R.mipmap.progress_complete);
                        progressBImg.setImageResource(R.mipmap.progress_complete);
                        progressCImg.setImageResource(R.mipmap.icon_progress_circle_blue);
                    }
                }else if(status==3){//申购失败
                    setStatusViewVisible(true,false,false);
                    progressAImg.setImageResource(R.mipmap.icon_progress_circle_blue);
                    progressATime.setText(DateUtils.getSimpleTimeFormat(updatedAt,AppData.Config.TIME_FORMAT_MD));
                    recordStatusTx.setText(getString(R.string.string_finance_purchase_failed));
                }else{//申购中
                    recordStatusTx.setText(getString(R.string.string_finance_purchase_success));
                    resultStatusTx.setText(getString(R.string.string_finance_purchase_success));
                    //T+0
                    progressAImg.setImageResource(R.mipmap.progressing);
                    progressBImg.setImageResource(R.mipmap.progress_grey);
                    progressCImg.setImageResource(R.mipmap.icon_progress_circle_grey);

                    progressATime.setText(DateUtils.getSimpleTimeFormat(updatedAt,AppData.Config.TIME_FORMAT_MD));
                    progressADesc.setText(getString(R.string.string_finance_purchase_success));

                    progressBTime.setText(DateUtils.addDays(updatedAt,1,AppData.Config.TIME_FORMAT_MD));
                    progressBDesc.setText(getString(R.string.string_finance_cal_profit));

                    progressCTime.setText(DateUtils.addDays(updatedAt,2,AppData.Config.TIME_FORMAT_MD));
                    progressCDesc.setText(getString(R.string.string_finance_profit_to_account));
                    setStatusViewVisible(true,true,true);

                }
            }else if(type ==1){//赎回
                topBar.setTitle(getString(R.string.string_finance_redemption_result));
                setStatusViewVisible(true,true,false);
                if (status==1) {
                    //赎回成功
                    redeemSuccessView.setVisibility(View.VISIBLE);

                    recordStatusTx.setText(getString(R.string.string_finance_redeem_success));

                    progressAImg.setImageResource(R.mipmap.progress_complete);
                    progressBImg.setImageResource(R.mipmap.icon_progress_circle_blue);

                    progressATime.setText(DateUtils.getSimpleTimeFormat(createdAt,AppData.Config.TIME_FORMAT_MD));
                    progressADesc.setText(getString(R.string.string_finance_redeem_applying));

                    progressBTime.setText(getString(R.string.string_finance_expected_24h));
                    progressBDesc.setText(getString(R.string.string_finance_redeem_success));
                }else{
                    //赎回中
                    recordStatusTx.setText(getString(R.string.string_finance_redeeming));

                    progressAImg.setImageResource(R.mipmap.progressing);
                    progressBImg.setImageResource(R.mipmap.icon_progress_circle_grey);

                    progressATime.setText(DateUtils.getSimpleTimeFormat(createdAt,AppData.Config.TIME_FORMAT_MD));
                    progressADesc.setText(getString(R.string.string_finance_redeem_applying));

                    progressBTime.setText(getString(R.string.string_finance_expected_24h));
                    progressBDesc.setText(getString(R.string.string_finance_redeem_success));
                }
            }else if(type ==2){//收益
                topBar.setTitle(getString(R.string.string_asset_coinplus_get));
                setStatusViewVisible(true,false,false);
                if (status==1) {
                    recordStatusTx.setText(getString(R.string.string_finance_profit_success));

                    progressAImg.setImageResource(R.mipmap.icon_progress_circle_blue);

                    progressATime.setText(DateUtils.getSimpleTimeFormat(updatedAt,AppData.Config.TIME_FORMAT_MD));
                    progressADesc.setText(getString(R.string.string_finance_profit_success));
                }else{
                    //收益发放中
                    recordStatusTx.setText(getString(R.string.string_finance_profit_waiting));

                    progressAImg.setImageResource(R.mipmap.icon_progress_circle_grey);

                    progressATime.setText(DateUtils.getSimpleTimeFormat(updatedAt,AppData.Config.TIME_FORMAT_MD));
                    progressADesc.setText(getString(R.string.string_finance_profit_waiting));
                }
            }
            if (mFromOrderPage) {
                //来自订单页
                recordNameTx.setText(response.getToken());
                recordAmountTx.setText(response.getAmount());
                recordTimeTx.setText(DateUtils.getSimpleTimeFormat(type==2?statisticsTime:createdAt,AppData.Config.TIME_FORMAT_MD));

            }else{
                //来自申购赎回页
                if (type==0) {
                   //申购
                    resultAmountTx.setVisibility(View.VISIBLE);
                    resultAmountTx.setText(response.getAmount()+" "+response.getToken());
                    resultTipsTx.setText(getString(R.string.string_rate_of_return)+ NumberUtils.roundFormatDown(NumberUtils.mul(currentSevenYearRate,"100"),2)+"%");
                }else{
                    //赎回
                    resultStatusTx.setText(getString(R.string.string_finance_redeem_submit_tips));
                    resultAmountTx.setVisibility(View.GONE);
                    resultTipsTx.setText(getString(R.string.string_finance_redeem_amount_format,response.getAmount(),response.getToken()));
                }

            }
        }
    }

    /**
     * 显示进度状态
     * @param isVisibleA
     * @param isVisibleB
     * @param isVisibleC
     */
    private void setStatusViewVisible(boolean isVisibleA, boolean isVisibleB, boolean isVisibleC) {
        progressA.setVisibility(isVisibleA?View.VISIBLE:View.GONE);
        progressB.setVisibility(isVisibleB?View.VISIBLE:View.GONE);
        progressC.setVisibility(isVisibleC?View.VISIBLE:View.GONE);
    }

    @Override
    public void showSevenYearRate(String sevenYearRate) {
        currentSevenYearRate = sevenYearRate;
    }
}
