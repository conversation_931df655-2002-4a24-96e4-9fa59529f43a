/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CurrentEntrustOrderFragment.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.ui;


import com.chad.library.adapter.base.BaseQuickAdapter;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.List;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import io.bhex.app.base.BaseListFreshFragment;
import io.bhex.app.finance.adapter.StakingOrderListAdapter;
import io.bhex.app.finance.presenter.StakingOrderHistoryFragmentPresenter;
import io.bhex.sdk.finance.bean.StakingOrderListResponse;

/**
 * ================================================
 * 描   述：已结束
 * ================================================
 */

public class StakingOrderHistoryFragment extends BaseListFreshFragment<StakingOrderHistoryFragmentPresenter, StakingOrderHistoryFragmentPresenter.StakingOrderHistoryOrderUI> implements StakingOrderHistoryFragmentPresenter.StakingOrderHistoryOrderUI, BaseQuickAdapter.RequestLoadMoreListener, OnRefreshListener {

    @Override
    protected StakingOrderHistoryFragmentPresenter.StakingOrderHistoryOrderUI getUI() {
        return this;
    }

    @Override
    protected StakingOrderHistoryFragmentPresenter createPresenter() {
        return new StakingOrderHistoryFragmentPresenter();
    }
    @Override
    public void showOrders(List<StakingOrderListResponse.ArrayBean> currentOrders) {
        if (adapter == null) {
            adapter = new StakingOrderListAdapter(getActivity(),currentOrders);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this,recyclerView);
            adapter.setEnableLoadMore(true);
            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            recyclerView.setItemAnimator(new DefaultItemAnimator());

            adapter.setEmptyView(emptyView);
            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(currentOrders);
        }
    }

}
