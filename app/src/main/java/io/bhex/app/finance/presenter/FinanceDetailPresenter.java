/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinanceDetailPresenter.java
 *   @Date: 19-3-8 下午5:15
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.finance.FinanceApi;
import io.bhex.sdk.finance.bean.FinanceBean;

public class FinanceDetailPresenter extends BasePresenter<FinanceDetailPresenter.FinanceDetailUI> {
    public interface FinanceDetailUI extends AppUI{
        void showFinanceDetail(FinanceBean response);
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    public void getFinanceDetail(String id) {
        FinanceApi.getFinanceDetail(id,new SimpleResponseListener<FinanceBean>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(FinanceBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showFinanceDetail(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
