/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinanceListAdapter.java
 *   @Date: 19-3-8 下午4:26
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.adapter;

import android.content.Context;
import androidx.annotation.Nullable;
import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.NumberUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.finance.bean.ProductsBean;

public class StakingProductListAdapter  extends BaseQuickAdapter<ProductsBean, BaseViewHolder> {

    public StakingProductListAdapter(Context context, @Nullable List<ProductsBean> data) {
        super(R.layout.item_finance_list_item_layout, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, ProductsBean item) {
        helper.setText(R.id.finance_title,item.getProductName());
        int status = item.getStatus();
        if (status==0) {
            helper.setVisible(R.id.finance_label,true); // TODO 待发售的倒计时。
            helper.setBackgroundRes(R.id.finance_label,R.drawable.bg_staking_product_status_comming);
            if (TextUtils.isEmpty(item.getRemainTimeContent())) {
                helper.setText(R.id.finance_label, mContext.getResources().getString(R.string.string_finance_status_go_on_sale));
            } else {
                helper.setText(R.id.finance_label, item.getRemainTimeContent());
            }
            helper.setTextColor(R.id.finance_label, mContext.getResources().getColor(R.color.blue));
        }else if (status==1) {
            helper.setBackgroundRes(R.id.finance_label,R.drawable.bg_staking_product_status_normal);
            helper.setText(R.id.finance_label,mContext.getResources().getString(R.string.string_finance_status_selling));
            helper.setVisible(R.id.finance_label,true);
            helper.setTextColor(R.id.finance_label, mContext.getResources().getColor(R.color.color_white));
         }else if (status==3) {
            helper.setBackgroundRes(R.id.finance_label,R.drawable.bg_staking_product_finished);
            helper.setText(R.id.finance_label,mContext.getResources().getString(R.string.string_finance_status_sold_out));
            helper.setVisible(R.id.finance_label,true);
            helper.setTextColor(R.id.finance_label, mContext.getResources().getColor(CommonUtil.isBlackMode()?R.color.gray_300_night:R.color.gray));
        }else if (status==2||status==4||status==5) {
            helper.setBackgroundRes(R.id.finance_label,R.drawable.bg_staking_product_finished);
            helper.setText(R.id.finance_label,mContext.getResources().getString(R.string.string_finance_status_finished));
            helper.setTextColor(R.id.finance_label, mContext.getResources().getColor(CommonUtil.isBlackMode()?R.color.gray_300_night:R.color.gray));
            helper.setVisible(R.id.finance_label,true);
        }

        helper.setText(R.id.finance_rate_of_return_title,mContext.getResources().getString(R.string.string_reference_apr));
        helper.setText(R.id.finance_rate_of_return,item.getReferenceApr()+"%");
        double remainCont = NumberUtils.sub(item.getUpLimitLots(),item.getSoldLots());
        double remainAmount = NumberUtils.mul(item.getPerLotAmount(),remainCont+"");
        helper.setText(R.id.finance_amount,NumberUtils.subZeroAndDot(NumberUtils.roundFormatDown(remainAmount, AppData.Config.DIGIT_DEFAULT_VALUE)));

        helper.setText(R.id.finance_amount_title,mContext.getResources().getString(R.string.string_finance_surplus_amount)+"("+item.getTokenName()+")");
        helper.addOnClickListener(R.id.itemView);
    }

}
