/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinanceDetailActivity.java
 *   @Date: 19-3-8 下午5:16
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.ui;

import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.Html;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.google.android.material.tabs.TabLayout;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.finance.presenter.StakingProductDetailPresenter;
import io.bhex.app.utils.ActivityCache;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.account.LoginResultCallback;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.finance.bean.ProductsBean;

//import com.google.android.material.tabs.TabLayout;

public class StakingProductDetailActivity extends BaseActivity<StakingProductDetailPresenter,StakingProductDetailPresenter.StakingProductDetailUI> implements StakingProductDetailPresenter.StakingProductDetailUI, View.OnClickListener {

    private ProductsBean financeBean;
    private TopBar topBar;
    private TabLayout tabLayout;
    private TextView tipsContentTx;
    private TextView returnRateTx;
    private ProgressBar progressbar;
    Runnable mRunnable;
    Handler mHandler;
    private Button btnPurchase;
    private long mRemainSec;
    private Button btnTimePurchase;

    @Override
    protected int getContentView() {
        return R.layout.activity_staking_product_detail_layout;
    }

    @Override
    protected StakingProductDetailPresenter createPresenter() {
        return new StakingProductDetailPresenter();
    }

    @Override
    protected StakingProductDetailPresenter.StakingProductDetailUI getUI() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActivityCache.getInstance().addFinanceActivity(this);
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        Intent intent = getIntent();
        if (intent != null) {
           financeBean = (ProductsBean) intent.getSerializableExtra("detail");
            if (financeBean != null) {
                topBar.setTitle(financeBean.getProductName());
            }
        }
        topBar.setTitleGravity();
        tabLayout = viewFinder.find(R.id.tabLayout);
        TabLayout.Tab tab = tabLayout.newTab();
        tab.setText(getResources().getString(R.string.string_finance_product_rules));
        tabLayout.addTab(tab);
        CommonUtil.setUpIndicatorWidthByReflex(tabLayout,15,15);
        tab.select();
        returnRateTx = viewFinder.textView(R.id.returnRate);
        tipsContentTx = viewFinder.textView(R.id.finance_trade_tips_content);
        progressbar=viewFinder.find(R.id.progressbar2);
        btnPurchase=viewFinder.find(R.id.btn_purchase);
        btnTimePurchase=viewFinder.find(R.id.btn_time_purchase);

        viewFinder.find(R.id.iv_lock_position).setAlpha(0.5f);
        viewFinder.find(R.id.start_interest_date_line).setAlpha(0.4f);
        viewFinder.find(R.id.iv_start_interest_date).setAlpha(0.5f);
        viewFinder.find(R.id.end_date_line).setAlpha(0.4f);
        viewFinder.find(R.id.iv_end_date).setAlpha(0.5f);
        initTimer();
        if (financeBean != null) {
            updateFinance(financeBean);
        } else {
            viewFinder.textView(R.id.tv_amount_title).setText(getString(R.string.string_product_amount,""));
            viewFinder.textView(R.id.tv_remain_amount_title).setText(getString(R.string.string_product_remain_amount,""));
        }

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                if (tab.getPosition()==0) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        tipsContentTx.setText(Html.fromHtml(financeBean.getProductDetails(),Html.FROM_HTML_MODE_COMPACT));
                    }else {
                        tipsContentTx.setText(Html.fromHtml(financeBean.getProductDetails()));
                    }
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {

            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });
        viewFinder.find(R.id.btn_purchase).setOnClickListener(this);
    }

    @Override
    protected int setStatusBarColor() {
        return getResources().getColor(R.color.blue);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (financeBean != null) {
            getPresenter().getFinanceDetail(financeBean.getProductId());
        }
    }

    @Override
    public void showFinanceDetail(ProductsBean response) {
        if (response != null) {
            financeBean=response;
            updateFinance(response);
        }
    }

    public void updateFinance(ProductsBean response) {
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//            tipsContentTx.setText(Html.fromHtml(financeBean.getProductDetails(),Html.FROM_HTML_MODE_COMPACT));
//        }else {
//            tipsContentTx.setText(Html.fromHtml(financeBean.getProductDetails()));
//        }
        tipsContentTx.setText(financeBean.getProductDetails());
        viewFinder.textView(R.id.tv_days_value).setText(response.getTimeLimit()+"");
        double remainCont = NumberUtils.sub(response.getUpLimitLots(),response.getSoldLots());
        double remainAmount = NumberUtils.mul(response.getPerLotAmount(),remainCont+"");
        double totalAmount = NumberUtils.mul(response.getPerLotAmount(),response.getUpLimitLots());
        viewFinder.textView(R.id.tv_amount_value).setText(NumberUtils.subZeroAndDot(NumberUtils.roundFormatDown(totalAmount, AppData.Config.DIGIT_DEFAULT_VALUE)));

        viewFinder.textView(R.id.tv_remain_amount_value).setText(NumberUtils.subZeroAndDot(NumberUtils.roundFormatDown(remainAmount, AppData.Config.DIGIT_DEFAULT_VALUE)));
        viewFinder.textView(R.id.tv_amount_title).setText(getString(R.string.string_product_amount,financeBean.getTokenName()));
        viewFinder.textView(R.id.tv_remain_amount_title).setText(getString(R.string.string_product_remain_amount,financeBean.getTokenName()));
        returnRateTx.setText(financeBean.getReferenceApr()+"%");
        String soldRate= NumberUtils.divStr(response.getUpLimitLots(),response.getSoldLots());
        progressbar.setProgress((int)NumberUtils.mul(soldRate,"100"));
        progressbar.setProgress((int)NumberUtils.mul(soldRate,"100"));
        viewFinder.textView(R.id.tv_sold_rate).setText(NumberUtils.roundFormatDown(NumberUtils.mul(soldRate,"100"),2)+"%");

        viewFinder.textView(R.id.tv_lock_date).setText(DateUtils.getSimpleTimeFormat(financeBean.getSubscribeEndDate(),AppData.Config.TIME_FORMAT4));
        viewFinder.textView(R.id.tv_start_interest_date).setText(DateUtils.getSimpleTimeFormat(financeBean.getInterestStartDate(),AppData.Config.TIME_FORMAT4));
        viewFinder.textView(R.id.tv_end_date).setText(DateUtils.getSimpleTimeFormat(financeBean.getProductEndDate(),AppData.Config.TIME_FORMAT4));

        if(financeBean.getStatus()==0) {
            btnTimePurchase.setVisibility(View.VISIBLE);
            btnPurchase.setVisibility(View.GONE);
            mRemainSec = (long)NumberUtils.div(1000,NumberUtils.sub(financeBean.getSubscribeStartDate(), System.currentTimeMillis()+""));
            btnTimePurchase.setText(getString(R.string.string_product_remain_time_before_start,getString(R.string.string_placeholder)));
            if (mRunnable != null) {
                mHandler.removeCallbacks(mRunnable);
                //子线程中调用：
                new Thread(mRunnable).start();
            }
        } else if(financeBean.getStatus()==1) {
            btnPurchase.setVisibility(View.VISIBLE);
            btnTimePurchase.setVisibility(View.GONE);
            btnPurchase.setEnabled(true);
            btnPurchase.setText(getString(R.string.string_asset_coinplus_gobuy));
        } else if(financeBean.getStatus()==3) {
            btnPurchase.setVisibility(View.VISIBLE);
            btnTimePurchase.setVisibility(View.GONE);
            btnPurchase.setEnabled(false);
            btnPurchase.setText(getString(R.string.string_finance_status_sold_out));
            // 售罄而且已经结束申购
            if(System.currentTimeMillis()>Long.parseLong(financeBean.getSubscribeEndDate())) {
                viewFinder.find(R.id.iv_lock_position).setAlpha(1);
                viewFinder.find(R.id.start_interest_date_line).setAlpha(0.4f);
                viewFinder.find(R.id.iv_start_interest_date).setAlpha(0.5f);
                viewFinder.find(R.id.end_date_line).setAlpha(0.4f);
                viewFinder.find(R.id.iv_end_date).setAlpha(0.5f);
            }
        } else {
            if(financeBean.getStatus()==2) {

                viewFinder.find(R.id.iv_lock_position).setAlpha(1);
                viewFinder.find(R.id.start_interest_date_line).setAlpha(0.4f);
                viewFinder.find(R.id.iv_start_interest_date).setAlpha(0.5f);
                viewFinder.find(R.id.end_date_line).setAlpha(0.4f);
                viewFinder.find(R.id.iv_end_date).setAlpha(0.5f);
            } else if(financeBean.getStatus()==4) {
                viewFinder.find(R.id.iv_lock_position).setAlpha(1);
                viewFinder.find(R.id.start_interest_date_line).setAlpha(1);
                viewFinder.find(R.id.iv_start_interest_date).setAlpha(1);
                viewFinder.find(R.id.end_date_line).setAlpha(0.4f);
                viewFinder.find(R.id.iv_end_date).setAlpha(0.5f);
            } else if(financeBean.getStatus()==5) {
                viewFinder.find(R.id.iv_lock_position).setAlpha(1);
                viewFinder.find(R.id.start_interest_date_line).setAlpha(1);
                viewFinder.find(R.id.iv_start_interest_date).setAlpha(1);
                viewFinder.find(R.id.end_date_line).setAlpha(1);
                viewFinder.find(R.id.iv_end_date).setAlpha(1);
            }
            btnPurchase.setVisibility(View.VISIBLE);
            btnTimePurchase.setVisibility(View.GONE);
            btnPurchase.setEnabled(false);
            btnPurchase.setText(getString(R.string.string_finance_status_finished));
        }
    }

    private void initTimer() {
        mHandler = new Handler() {
            @Override
            public void handleMessage(Message msg) {
                switch (msg.what) {
                    case 1:
                        btnTimePurchase.setText(String.valueOf(msg.obj));
                        break;
                    default:
                }

            }
        };
        mRunnable = new Runnable() {
            @Override
            public void run() {
                if (mRemainSec > 0) {
                    String content = "";
                    mRemainSec = mRemainSec-1;
                    if (mRemainSec>84600) {
                        long remainSec= mRemainSec % 86400;
                        content = getString(R.string.string_remain_time_format,(int) mRemainSec / 86400, (int) remainSec / (60 * 60), (int) (remainSec % (60 * 60)) / 60, (int) remainSec % 60);
                    } else {
                        content = String.format("%02d:%02d:%02d", (int) mRemainSec / (60 * 60), (int) (mRemainSec % (60 * 60)) / 60, (int) mRemainSec % 60);
                    }
                    content = getString(R.string.string_product_remain_time_before_start,content);
                    Message message = mHandler.obtainMessage(1, 0, 0, content);
                    mHandler.sendMessage(message);
                    //每隔1s循环执行run方法
                    mHandler.postDelayed(this, 1000);
                } else {
                    // 更新下状态
                    getPresenter().getFinanceDetail(financeBean.getProductId());
                }
            }
        };
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.btn_purchase:

                UserInfo.LoginAndGoin(this, new LoginResultCallback() {
                    @Override
                    public void onLoginSucceed() {
                        super.onLoginSucceed();

                        if (financeBean != null) {
                            IntentUtils.goStakingPurchase(StakingProductDetailActivity.this,financeBean.getProductId());
                        }
                    }
                });
                break;
        }
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mRunnable != null) {
            mHandler.removeCallbacks(mRunnable);
        }
        ActivityCache.getInstance().removeFinanceActivity(this);
    }
}
