/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinancePurchasePresenter.java
 *   @Date: 19-3-11 下午4:44
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.presenter;

import android.text.TextUtils;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.IntentUtils;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.finance.FinanceApi;
import io.bhex.sdk.finance.bean.FinanceAssetDetailResponse;
import io.bhex.sdk.finance.bean.FinanceBean;
import io.bhex.sdk.finance.bean.FinanceRecordBeanResponse;
import io.bhex.sdk.trade.AssetApi;
import io.bhex.sdk.trade.bean.AssetDataResponse;

public class FinancePurchasePresenter extends BasePresenter<FinancePurchasePresenter.FinancePurchaseUI> {

    public interface FinancePurchaseUI extends AppUI{

        void showFinanceDetail(FinanceBean response, String userLastLimit);

        void updateAssettByToken(String tokenId, String free);
    }


    public void getFinanceDetail(String productId) {
        if (!UserInfo.isLogin()) {
            return;
        }

        FinanceApi.financeAssetDetail(productId,new SimpleResponseListener<FinanceAssetDetailResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(FinanceAssetDetailResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    FinanceBean product = response.getProduct();
                    if (product != null) {
                        getUI().showFinanceDetail(product,response.getUserLastLimit());
                        getAsset(product.getToken());
                    }

                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });

    }

    /**
     * 获取资产
     * @param tokenId
     */
    protected void getAsset(final String tokenId) {
        if (!UserInfo.isLogin()) {
            return;
        }
        AssetApi.RequestTokenIdAsset(tokenId,new SimpleResponseListener<AssetDataResponse>(){
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(AssetDataResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    List<AssetDataResponse.ArrayBean> data = response.getArray();
                    if (data != null) {
                        if (data.size()>0) {
                            AssetDataResponse.ArrayBean assetBean = data.get(0);
                            if (assetBean != null) {
                                getUI().updateAssettByToken(tokenId,assetBean.getFree());
                                return;
                            }
                        }
                    }
                    getUI().updateAssettByToken(tokenId,"");
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        } );
    }

    /**
     * 申购成功
     * @param financeBean
     * @param amount
     */
    public void purchase(final FinanceBean financeBean, String amount) {
        FinanceApi.financePurchase(financeBean.getId(),amount,new SimpleResponseListener<FinanceRecordBeanResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(FinanceRecordBeanResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    String recordId = response.getRecordId();
                    if (!TextUtils.isEmpty(recordId)) {
                        ToastUtils.showShort(getActivity(),getString(R.string.string_finance_purchase_success));
                        IntentUtils.goFinanceResult(getActivity(),false,financeBean.getSevenYearRate(),response.getRecordId());
                    }else{
                        ToastUtils.showShort(getActivity(),getString(R.string.string_data_exception));
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

}
