/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: FinanceListActivity.java
 *   @Date: 19-3-8 下午2:18
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.finance.ui;

import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.google.android.material.tabs.TabLayout;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;
import androidx.viewpager.widget.ViewPager;
import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.finance.adapter.FinanceListAdapter;
import io.bhex.app.finance.adapter.StakingProductListAdapter;
import io.bhex.app.finance.presenter.FinanceListPresenter;
import io.bhex.app.skin.view.SkinTabLayout;
import io.bhex.app.utils.BasicFunctionsUtil;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.app.utils.StatusBarExtUtil;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.images.CImageLoader;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.account.LoginResultCallback;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.config.bean.BasicFunctionsConfig;
import io.bhex.sdk.finance.bean.FinanceBean;
import io.bhex.sdk.finance.bean.ProductsBean;
import io.bhex.sdk.finance.bean.StakingConfigResponse;
import io.bhex.sdk.finance.bean.StakingProductListResponse;

public class FinanceListActivity extends BaseActivity<FinanceListPresenter, FinanceListPresenter.FinanceListUI> implements FinanceListPresenter.FinanceListUI, OnRefreshListener {
    private SmartRefreshLayout swipeRefresh;
    private RecyclerView recyclerView;
    private FinanceListAdapter adapter;
    private StakingProductListAdapter regularAdapter;
    private StakingProductListAdapter holdAdapter;
    private TopBar topBar;
    private ArrayList<String> items = new ArrayList<>();
    protected SkinTabLayout tabLayout;
    private RecyclerView regularRecyclerView;
    private RecyclerView holdRecyclerView;
    ProductAdapter pageAdapter;
    private List<FinanceBean> mCurrentProducts;
    private List<ProductsBean> mRegularProducts;
    private List<ProductsBean> mHoldProducts;
    private Map<Integer,ProductsBean> mRegularProductMap=new HashMap<>();
    private Map<Integer,ProductsBean> mHoldProductMap=new HashMap<>();
    Runnable mRunnable;
    Handler mHandler;
    private int state;
    private ViewPager viewPager;

    @Override
    protected int getContentView() {
        return R.layout.activity_finance_list_layout;
    }

    @Override
    protected FinanceListPresenter createPresenter() {
        return new FinanceListPresenter();
    }

    @Override
    protected FinanceListPresenter.FinanceListUI getUI() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void initView() {
        super.initView();
//        topBar = viewFinder.find(R.id.topBar);
//        topBar.setBgColor(getResources().getColor(R.color.finance_product_top_bar_bg));
//        topBar.setLeftImg(R.mipmap.white_back);
//        topBar.setTitleGravity();
//        topBar.setRightOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                if (UserInfo.isLogin(FinanceListActivity.this, AppData.INTENT.LOGIN_CALLER_TRADE)) {
//                    IntentUtils.goCoinPlusOrders(FinanceListActivity.this);
//                }
//            }
//        });
        ((Toolbar)viewFinder.find(R.id.toolbar)).setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        swipeRefresh.setOnRefreshListener(this);
        tabLayout = viewFinder.find(R.id.tabLayout);
        recyclerView = viewFinder.find(R.id.recyclerView);

        regularRecyclerView = viewFinder.find(R.id.regular_recyclerView);
        holdRecyclerView = viewFinder.find(R.id.hold_recyclerView);
        initTabs();
        initTimer();
    }

    private void initTabs() {
        BasicFunctionsConfig basicFunctionsConfig = BasicFunctionsUtil.getBasicFunctionsConfig();
        if (!basicFunctionsConfig.isBonus()) {
            items.add(getString(R.string.string_staking_product_current)); // 活期暂时固定
            recyclerView.setVisibility(View.VISIBLE);
        }
        pageAdapter = new ProductAdapter(getSupportFragmentManager());
        viewPager = viewFinder.find(R.id.viewPager);
        viewPager.setAdapter(pageAdapter);
        tabLayout.setupWithViewPager(viewPager);
        tabLayout.setTabMode(TabLayout.MODE_FIXED );
        tabLayout.setTabGravity(TabLayout.GRAVITY_FILL);

        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                if (position > -1 && items.size() > position) {
                    if (items.get(position).equals(getString(R.string.string_staking_product_current))) {
//                        setCurrentProducts();
                        recyclerView.setVisibility(View.VISIBLE);
                        regularRecyclerView.setVisibility(View.GONE);
                        holdRecyclerView.setVisibility(View.GONE);
                        viewFinder.textView(R.id.tv_bill).setText(getString(R.string.string_finance_order));
                    } else if (items.get(position).equals(getString(R.string.string_staking_product_regular))) {
                        recyclerView.setVisibility(View.GONE);
                        regularRecyclerView.setVisibility(View.VISIBLE);
                        holdRecyclerView.setVisibility(View.GONE);
                        viewFinder.textView(R.id.tv_bill).setText(getString(R.string.title_order));
//                        setRegularProducts();
                    } else if (items.get(position).equals(getString(R.string.string_staking_product_hold))) {
                        recyclerView.setVisibility(View.GONE);
                        regularRecyclerView.setVisibility(View.GONE);
                        holdRecyclerView.setVisibility(View.VISIBLE);
                        viewFinder.textView(R.id.tv_bill).setText(getString(R.string.title_order));
//                        setHoldProducts();
                    }
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        if (items.size()>0) {
            viewPager.setCurrentItem(0);
        }
        CommonUtil.setUpIndicatorWidthByReflex(tabLayout, 15, 15);
    }
    @Override
    protected void addEvent(){
        super.addEvent();
        AppBarLayout app_bar= viewFinder.find(R.id.app_bar);
        Toolbar toolbar =viewFinder.find(R.id.toolbar);
        CollapsingToolbarLayout collapsingToolbarLayout = viewFinder.find(R.id.toolbar_layout);
        app_bar.addOnOffsetChangedListener(new AppBarLayout.OnOffsetChangedListener() {
            @Override
            public void onOffsetChanged(AppBarLayout appBarLayout, int verticalOffset) {

                if (verticalOffset == 0) {
                    if (state !=1) {
                        state = 1;//修改状态标记为展开
                        viewFinder.find(R.id.tv_bar_title).setVisibility(View.GONE);
                        toolbar.setBackgroundColor(getResources().getColor(R.color.transparent));
                    }
                } else if (Math.abs(verticalOffset) >= appBarLayout.getTotalScrollRange()) {
                    if (state != 2) {
                        viewFinder.find(R.id.tv_bar_title).setVisibility(View.VISIBLE);
                        toolbar.setBackgroundColor(getResources().getColor(R.color.finance_product_top_bar_bg));
                        state = 2;//修改状态标记为折叠
                    }
                } else {
                    if (state !=3) {
                        viewFinder.find(R.id.tv_bar_title).setVisibility(View.GONE);
                        toolbar.setBackgroundColor(getResources().getColor(R.color.transparent));
                        state = 3;//修改状态标记为中间
                    }
                }
            }
        });
        viewFinder.find(R.id.rl_bill).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                UserInfo.LoginAndGoin(FinanceListActivity.this, new LoginResultCallback() {
                    @Override
                    public void onLoginSucceed() {
                        int position = viewPager.getCurrentItem();
                        if (position > -1 && items.size() > position) {
                            if (items.get(position).equals(getString(R.string.string_staking_product_current))) {
                                IntentUtils.goCoinPlusOrders(FinanceListActivity.this);
                            } else if (items.get(position).equals(getString(R.string.string_staking_product_regular))) {
                                IntentUtils.goStakingOrders(FinanceListActivity.this);
                            } else if (items.get(position).equals(getString(R.string.string_staking_product_hold))) {
                                IntentUtils.goStakingOrders(FinanceListActivity.this);
                            }
                        }
                    }
                });

            }
        });
    }
    @Override
    protected void onResume() {
        super.onResume();
        swipeRefresh.setBackgroundResource(CommonUtil.isBlackMode()?R.color.white_night:R.color.white);
        StatusBarExtUtil.setStatusColor(this,true,false, SkinColorUtil.getWhite(this));
    }

    @Override
    public void showFinanceList(List<FinanceBean> data) {
        if (data != null && data.size() > 0) {
            mCurrentProducts = data;
            setCurrentProducts();
        }
    }

    private void initTimer() {
        mHandler = new Handler() {
            @Override
            public void handleMessage(Message msg) {
                switch (msg.what) {
                    case 1:
                        int position = (Integer)msg.obj;
                        if (msg.arg1==0) {
                            regularAdapter.notifyItemChanged(position);
                        } else {
                            holdAdapter.notifyItemChanged(position);
                        }
                        break;
                    default:
                }

            }
        };
        mRunnable = new Runnable() {
            @Override
            public void run() {
                if (mRegularProductMap.size()>0) {
                    for (Integer key: mRegularProductMap.keySet()) {
                        ProductsBean productsBean = mRegularProductMap.get(key);
                        long remainSec = (long) NumberUtils.div(1000,NumberUtils.sub(productsBean.getSubscribeStartDate(), System.currentTimeMillis()+""));

                        if (remainSec > 0) {
                            String content = "";
                            if (remainSec >84600) {
                                long dayRemainSec= remainSec % 86400;
                                content = getString(R.string.string_remain_time_format,(int) remainSec / 86400, (int) dayRemainSec / (60 * 60), (int) (dayRemainSec % (60 * 60)) / 60, (int) dayRemainSec % 60);
                            } else {
                                content = String.format("%02d:%02d:%02d", (int) remainSec / (60 * 60), (int) (remainSec % (60 * 60)) / 60, (int) remainSec % 60);
                            }
                            content = getString(R.string.string_product_remain_time_before_start,content);
                            Message message = mHandler.obtainMessage(1, 0, 0, key);
                            productsBean.setRemainTimeContent(content);
                            mHandler.sendMessage(message);
                        } else {
                            mRegularProductMap.remove(key);
                            productsBean.setStatus(1);
                            Message message = mHandler.obtainMessage(1, 0, 0, key);
                            mHandler.sendMessage(message);
                        }
                    }
                }
                if (mHoldProductMap.size()>0) {
                    for (Integer key: mHoldProductMap.keySet()) {
                        ProductsBean productsBean = mHoldProductMap.get(key);
                        long remainSec = (long) NumberUtils.div(1000,NumberUtils.sub(productsBean.getSubscribeStartDate(), System.currentTimeMillis()+""));

                        if (remainSec > 0) {
                            String content = "";
                            if (remainSec >84600) {
                                long dayRemainSec= remainSec % 86400;
                                content = getString(R.string.string_remain_time_format, (int) remainSec / 86400, (int) dayRemainSec / (60 * 60), (int) (dayRemainSec % (60 * 60)) / 60, (int) dayRemainSec % 60);
                            } else {
                                content = String.format("%02d:%02d:%02d", (int) remainSec / (60 * 60), (int) (remainSec % (60 * 60)) / 60, (int) remainSec % 60);
                            }
                            content = getString(R.string.string_product_remain_time_before_start,content);
                            Message message = mHandler.obtainMessage(1, 1, 0, key);
                            productsBean.setRemainTimeContent(content);
                            mHandler.sendMessage(message);
                        } else {
                            mHoldProductMap.remove(key);
                            productsBean.setStatus(1);
                            Message message = mHandler.obtainMessage(1, 1, 0, key);
                            mHandler.sendMessage(message);
                        }
                    }

                }
                if (mHoldProductMap.size()>0||mRegularProductMap.size()>0) {
                    //每隔1s循环执行run方法
                    mHandler.postDelayed(this, 1000);
                }
            }
        };
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mRunnable != null) {
            mHandler.removeCallbacks(mRunnable);
        }
    }
    private void setCurrentProducts() {

        if (adapter == null) {
            adapter = new FinanceListAdapter(this, mCurrentProducts);
            adapter.isFirstOnly(false);

            LayoutInflater layoutInflater = LayoutInflater.from(this);
            View emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
            ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
            layoutParams.height = PixelUtils.dp2px(200);
            emptyView.setLayoutParams(layoutParams);

            recyclerView.setLayoutManager(new LinearLayoutManager(this));
            recyclerView.setItemAnimator(new DefaultItemAnimator());

            adapter.setEmptyView(emptyView);
            recyclerView.setAdapter(adapter);
            adapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                    FinanceBean financeBean = (FinanceBean) adapter.getData().get(position);
                    IntentUtils.goFinanceDetail(FinanceListActivity.this, financeBean);
                }
            });
        } else {
            adapter.setNewData(mCurrentProducts);
        }
    }

    @Override
    public void showStakingProduct(List<StakingProductListResponse.ArrayBean> data) {
        mHoldProductMap.clear();
        mRegularProductMap.clear();
        if (mRegularProducts!=null) {
            mRegularProducts.clear();
        }
        if (mHoldProducts!=null) {
            mHoldProducts.clear();
        }
        if (data != null && data.size() > 0) {
            for (StakingProductListResponse.ArrayBean productsBean : data) {
                if (productsBean.getType()==AppData.Config.STAKING_PRODUCE_REGULAR && productsBean.getProducts() != null && productsBean.getProducts().size() > 0) {
                    if (!items.contains(getString(R.string.string_staking_product_regular))) {
                        items.add(getString(R.string.string_staking_product_regular));
                    }
                    mRegularProducts = productsBean.getProducts();
                } else if (productsBean.getType()==AppData.Config.STAKING_PRODUCE_HOLD && productsBean.getProducts() != null && productsBean.getProducts().size() > 0) {
                    if (!items.contains(getString(R.string.string_staking_product_hold))) {
                        items.add(getString(R.string.string_staking_product_hold));
                    }
                    mHoldProducts = productsBean.getProducts();
                }
            }
            setHoldProducts();
            setRegularProducts();
            if (mRegularProducts!=null) {
                for (int i=0;i<mRegularProducts.size();i++) {
                    if (mRegularProducts.get(i).getStatus()==0) {
                        mRegularProductMap.put(i,mRegularProducts.get(i));
                    }
                }
            }
            if (mHoldProducts!=null) {
                for (int i=0;i<mHoldProducts.size();i++) {
                    if (mHoldProducts.get(i).getStatus()==0) {
                        mHoldProductMap.put(i,mHoldProducts.get(i));
                    }
                }
            }
            if (mRunnable != null) {
                mHandler.removeCallbacks(mRunnable);
                //子线程中调用：
                new Thread(mRunnable).start();
            }
            pageAdapter.notifyDataSetChanged();
        }
        if (viewPager.getCurrentItem()==-1&&items.size()>0) {
            viewPager.setCurrentItem(0);
        }
        int position = viewPager.getCurrentItem();
        if (position > -1 && items.size() > position) {
            if (items.get(position).equals(getString(R.string.string_staking_product_current))) {
//                        setCurrentProducts();
                recyclerView.setVisibility(View.VISIBLE);
                regularRecyclerView.setVisibility(View.GONE);
                holdRecyclerView.setVisibility(View.GONE);
                viewFinder.textView(R.id.tv_bill).setText(getString(R.string.string_finance_order));
            } else if (items.get(position).equals(getString(R.string.string_staking_product_regular))) {
                recyclerView.setVisibility(View.GONE);
                regularRecyclerView.setVisibility(View.VISIBLE);
                holdRecyclerView.setVisibility(View.GONE);
                viewFinder.textView(R.id.tv_bill).setText(getString(R.string.title_order));
//                        setRegularProducts();
            } else if (items.get(position).equals(getString(R.string.string_staking_product_hold))) {
                recyclerView.setVisibility(View.GONE);
                regularRecyclerView.setVisibility(View.GONE);
                holdRecyclerView.setVisibility(View.VISIBLE);
                viewFinder.textView(R.id.tv_bill).setText(getString(R.string.title_order));
//                        setHoldProducts();
            }
        }
    }

    @Override
    public void updateStakingConfig(StakingConfigResponse response) {

        if (response!=null&&response.getStakingSettings()!=null) {
            ImageView topPoster = viewFinder.imageView(R.id.topBanner);
            if (!TextUtils.isEmpty(response.getStakingSettings().getAppTitle())) {
                viewFinder.textView(R.id.tv_bar_title).setText(response.getStakingSettings().getAppTitle());
            }
            ViewGroup.LayoutParams topPosterLayoutParams = topPoster.getLayoutParams();
            int screenWidth = PixelUtils.getScreenWidth();
            int topPosterHeight = screenWidth*220/375;
            topPosterLayoutParams.width = screenWidth;
            topPosterLayoutParams.height = topPosterHeight;
            topPoster.setLayoutParams(topPosterLayoutParams);
            if (!TextUtils.isEmpty(response.getStakingSettings().getAppBackground())) {

//                Glide.with(this).load(response.getStakingSettings().getAppBackground()).asBitmap().into(new SimpleTarget<Bitmap>() {
//                    @Override
//                    public void onResourceReady(Bitmap resource, GlideAnimation<? super Bitmap> glideAnimation) {
//                        Drawable drawable = new BitmapDrawable(resource);
//                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
//                            mShareBackgroundSign.setBackground(drawable);    //设置背景
//                        }
////                        //= resource;
////                        if(mWaterScale>0){
////                            matrix.reset();
////                            matrix.postScale(mWaterScale,mWaterScale);
////                            mWaterBmp = Bitmap.createBitmap(resource,0,0,resource.getWidth(),resource.getHeight(),matrix,false);
////                        }else{
////                            mWaterBmp = resource;
////                        }
////
//
//                    }
//                });
                CImageLoader.getInstance().load(topPoster,response.getStakingSettings().getAppBackground(),R.mipmap.bg_staking_product_header_default);
            }
//            viewFinder.textView(R.id.tv_title).setText(response.getStakingSettings().getAppTitle());

        }
    }

    private void setHoldProducts() {

        if (holdAdapter == null) {
            holdAdapter = new StakingProductListAdapter(this, mHoldProducts);
            holdAdapter.isFirstOnly(false);

            LayoutInflater layoutInflater = LayoutInflater.from(this);
            View emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
            ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
            layoutParams.height = PixelUtils.dp2px(200);
            emptyView.setLayoutParams(layoutParams);

            holdRecyclerView.setLayoutManager(new LinearLayoutManager(this));
//            holdRecyclerView.setItemAnimator(new DefaultItemAnimator());

            ((SimpleItemAnimator)holdRecyclerView.getItemAnimator()).setSupportsChangeAnimations(false);
            holdAdapter.setEmptyView(emptyView);
            holdRecyclerView.setAdapter(holdAdapter);
            holdAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                    ProductsBean financeBean = (ProductsBean) adapter.getData().get(position);
                    IntentUtils.goStakingProductDetail(FinanceListActivity.this, financeBean);
                }
            });
        } else {
            holdAdapter.setNewData(mHoldProducts);
        }
    }

    private void setRegularProducts() {

        if (regularAdapter == null) {
            regularAdapter = new StakingProductListAdapter(this, mRegularProducts);
            regularAdapter.isFirstOnly(false);
            LayoutInflater layoutInflater = LayoutInflater.from(this);
            View emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
            ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
            layoutParams.height = PixelUtils.dp2px(200);
            emptyView.setLayoutParams(layoutParams);

            regularRecyclerView.setLayoutManager(new LinearLayoutManager(this));
//            regularRecyclerView.setItemAnimator(new DefaultItemAnimator());

            ((SimpleItemAnimator)regularRecyclerView.getItemAnimator()).setSupportsChangeAnimations(false);
            regularAdapter.setEmptyView(emptyView);
            regularRecyclerView.setAdapter(regularAdapter);
            regularAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                    ProductsBean financeBean = (ProductsBean) adapter.getData().get(position);
                    IntentUtils.goStakingProductDetail(FinanceListActivity.this, financeBean);
                }
            });
        } else {
            regularAdapter.setNewData(mRegularProducts);
        }
    }

    @Override
    protected boolean translucentStatusBar() {
        return true;
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        BasicFunctionsConfig basicFunctionsConfig = BasicFunctionsUtil.getBasicFunctionsConfig();
        if (!basicFunctionsConfig.isBonus()) {
            getPresenter().getFinanceList();
        }
        if (!basicFunctionsConfig.isStaking()) {
            getPresenter().getStakingProductList();
        }
        getPresenter().getStakingConfigInfo();
        refreshLayout.finishRefresh(1000);
    }

    private class ProductAdapter extends FragmentPagerAdapter {

        public ProductAdapter(FragmentManager fm) {
            super(fm);
        }

        @Override
        public Fragment getItem(int position) {
            return null;
        }

        @Override
        public int getCount() {
            return items.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return items.get(position);
        }


    }

}
