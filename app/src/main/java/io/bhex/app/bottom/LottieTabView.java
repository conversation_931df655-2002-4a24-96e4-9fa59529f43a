package io.bhex.app.bottom;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Checkable;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.airbnb.lottie.LottieAnimationView;

import java.io.File;

import io.bhex.app.R;
import io.bhex.app.app.BHexApplication;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.FileTools;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.sdk.account.UserInfo;

public class LottieTabView extends FrameLayout implements Checkable {

    private View mRootView;
    LottieTabViewCallback mLottieTabViewCallback;

    private LottieAnimationView tab_animation_view;

    private CheckableTextView tab_name_view;

    private int mSelectTextColor;
    private int mUnSelectTextColor;
    private Drawable mIconNormal;
    private String tabNameLabel;

    private String lottiePath;
    private boolean isLogin;
    private boolean checked;

    public LottieTabView(@NonNull Context context) {
        this(context,null);
    }

    public LottieTabView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs,0);
    }

    public LottieTabView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context,attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.LottieTabView);
        mIconNormal =  ta.getDrawable(R.styleable.LottieTabView_icon_normal);
        tabNameLabel =  ta.getString(R.styleable.LottieTabView_tab_name);
        lottiePath =  ta.getString(R.styleable.LottieTabView_lottie_path);
        isLogin =  ta.getBoolean(R.styleable.LottieTabView_tab_login,false);
        ta.recycle();
        initView(context);
    }

    private void initView(Context context) {
        mRootView = LayoutInflater.from(context).inflate(R.layout.layout_tab_view, null, false);
        tab_animation_view = mRootView.findViewById(R.id.tab_animation_view);
        tab_name_view = mRootView.findViewById(R.id.tab_name);
        if(!TextUtils.isEmpty(lottiePath)){
            tab_animation_view.setAnimation(lottiePath);
        }
        ///titleImg.setAnimation(ANIMATIONS[index]);
        //tab_animation_view.useHardwareAcceleration(true);
        tab_animation_view.enableMergePathsForKitKatAndAbove(true);
        tab_animation_view.setImageDrawable(mIconNormal);
        tab_name_view.setText(tabNameLabel);

        addView(mRootView);
    }

    public void setTextUnSelectColor(int textUnSelectColor){
        mUnSelectTextColor = textUnSelectColor;
        //tab_name_view.setTextColor(mSelectTextColor);
    }

    public void setTextSelectColor(int textSelectColor){
        mSelectTextColor = textSelectColor;
        //tab_name_view.setTextColor(mSelectTextColor);
    }

    public void setTabTextSize(float textSize){
        tab_name_view.setTextSize(TypedValue.COMPLEX_UNIT_SP,textSize);
    }

    @Override
    public void setChecked(boolean checked) {
        if(this.checked!=checked){
            this.checked = checked;
        }
        boolean isExists = FileTools.fileIsExistsFromAssets(getContext(),lottiePath);
        if(isExists) {
            loadAnimationJson(this.checked);
        }else{
            loadImageView(this.checked);
        }
    }

    private void loadAnimationJson(boolean isChecked){
        if(checked){
            if(CommonUtil.isBlackMode()){
                lottiePath = lottiePath.replace("_light.json","_dark.json");
            }else{
                lottiePath  = lottiePath.replace("_dark.json","_light.json");
            }
            BHexApplication.getMainHandler().postDelayed(()->{
                if(!isLogin||UserInfo.isLogin()){
                    tab_animation_view.setAnimation(lottiePath);
                    tab_animation_view.playAnimation();
                    tab_name_view.setChecked(checked);
                }
            },100);


        }else{
            tab_animation_view.clearAnimation();
            tab_animation_view.setImageDrawable(mIconNormal);
            tab_name_view.setChecked(checked);
        }
    }

    private void loadImageView(boolean isChecked){
        if(CommonUtil.isBlackMode()){
            if(checked){
                if(getId()==R.id.main_home_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_home_checked_night);
                }else if(getId()==R.id.main_market_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_hangqing_checked_night);
                }else if(getId()==R.id.main_trade_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_trade_checked_night);
                }else if(getId()==R.id.main_contract_trade_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_contract_checked_night);
                }else if(getId()==R.id.main_asset){
                    tab_animation_view.setImageResource(R.mipmap.tab_asset_checked_night);
                }
            }else{
                if(getId()==R.id.main_home_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_home_night);
                }else if(getId()==R.id.main_market_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_hangqing_night);
                }else if(getId()==R.id.main_trade_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_trade_night);
                }else if(getId()==R.id.main_contract_trade_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_contract_night);
                }else if(getId()==R.id.main_asset){
                    tab_animation_view.setImageResource(R.mipmap.tab_asset_night);
                }
            }
        }else{
            if(checked){
                if(getId()==R.id.main_home_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_home_checked);
                }else if(getId()==R.id.main_market_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_hangqing_checked);
                }else if(getId()==R.id.main_trade_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_trade_checked);
                }else if(getId()==R.id.main_contract_trade_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_contract_checked);
                }else if(getId()==R.id.main_asset){
                    tab_animation_view.setImageResource(R.mipmap.tab_asset_checked);
                }
            }else{
                if(getId()==R.id.main_home_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_home);
                }else if(getId()==R.id.main_market_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_hangqing);
                }else if(getId()==R.id.main_trade_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_trade);
                }else if(getId()==R.id.main_contract_trade_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_contract);
                }else if(getId()==R.id.main_asset){
                    tab_animation_view.setImageResource(R.mipmap.tab_asset);
                }
            }
        }

        tab_name_view.setChecked(checked);

    }

    @Override
    public boolean isChecked() {
        return this.checked;
    }

    @Override
    public void toggle() {
        setChecked(!this.checked);
    }

    public void setLottieAnimation(String path){
        DebugLog.d("LottieTabView===>:","path==="+path);
        tab_animation_view.setAnimation(path);
        tab_animation_view.cancelAnimation();
    }

    public void setLottieTabViewCallback(LottieTabViewCallback lottieTabViewCallback) {
        this.mLottieTabViewCallback = mLottieTabViewCallback;
    }

    /**
     * 刷新icon
     */
    public void refreshIcons() {
        if(CommonUtil.isBlackMode()){
            if(checked){
                if(getId()==R.id.main_home_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_home_checked_night);
                }else if(getId()==R.id.main_market_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_hangqing_checked_night);
                }else if(getId()==R.id.main_trade_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_trade_checked_night);
                }else if(getId()==R.id.main_contract_trade_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_contract_checked_night);
                }else if(getId()==R.id.main_asset){
                    tab_animation_view.setImageResource(R.mipmap.tab_asset_checked_night);
                }
            }else{
                if(getId()==R.id.main_home_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_home_night);
                }else if(getId()==R.id.main_market_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_hangqing_night);
                }else if(getId()==R.id.main_trade_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_trade_night);
                }else if(getId()==R.id.main_contract_trade_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_contract_night);
                }else if(getId()==R.id.main_asset){
                    tab_animation_view.setImageResource(R.mipmap.tab_asset_night);
                }
            }
        }else{
            if(checked){
                if(getId()==R.id.main_home_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_home_checked);
                }else if(getId()==R.id.main_market_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_hangqing_checked);
                }else if(getId()==R.id.main_trade_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_trade_checked);
                }else if(getId()==R.id.main_contract_trade_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_contract_checked);
                }else if(getId()==R.id.main_asset){
                    tab_animation_view.setImageResource(R.mipmap.tab_asset_checked);
                }
            }else{
                if(getId()==R.id.main_home_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_home);
                }else if(getId()==R.id.main_market_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_hangqing);
                }else if(getId()==R.id.main_trade_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_trade);
                }else if(getId()==R.id.main_contract_trade_tab){
                    tab_animation_view.setImageResource(R.mipmap.tab_contract);
                }else if(getId()==R.id.main_asset){
                    tab_animation_view.setImageResource(R.mipmap.tab_asset);
                }
            }
        }
    }

    public interface  LottieTabViewCallback{
        public void callback(int resId);
    }
}
