package io.bhex.app.bottom;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.util.AttributeSet;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.baselib.utils.DebugLog;

/**
 * <AUTHOR>
 * 2020年7月7日09:37:56
 *
 */
public class LottieTabLayout extends LinearLayout {

    private int mSelectTextColor;
    private int mUnSelectTextColor;
    private float mTextSize;
    private TabLayoutSelectListener mTabLayoutSelectListener;
    private List<LottieTabView> menuItems = new ArrayList<>();
    private LottieTabView mSelectTabView;

    public LottieTabLayout(Context context) {
        super(context);
        init(context,null,0);
    }

    public LottieTabLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context,attrs,0);
    }

    public LottieTabLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context,attrs,defStyleAttr);
    }

    private void init(Context context, AttributeSet attrs, int defStyleAttr) {
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.LottieTabLayout);
        mSelectTextColor = ta.getColor(R.styleable.LottieTabLayout_tab_selectColor, Color.BLACK);
        mUnSelectTextColor = ta.getColor(R.styleable.LottieTabLayout_tab_unselectColor, Color.BLACK);
        mTextSize = ta.getDimension(R.styleable.LottieTabLayout_tab_textSize,10);
        ta.recycle();

        DebugLog.d("LottieTabLayout===>:","count=1="+getChildCount());

    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        DebugLog.d("LottieTabLayout===>:","count=2="+getChildCount());
        menuItems.clear();
        for (int i = 0; i < getChildCount(); i++) {
            LottieTabView lottieTabView = (LottieTabView)getChildAt(i);
            lottieTabView.setTabTextSize(mTextSize);
            lottieTabView.setTextUnSelectColor(mUnSelectTextColor);
            lottieTabView.setTextSelectColor(mSelectTextColor);

            lottieTabView.setOnClickListener(v->{
                if(mSelectTabView!=null && mSelectTabView.getId()==v.getId()){
                    return;
                }
                for (int j = 0; j < menuItems.size(); j++) {
                    menuItems.get(j).setChecked(false);
                }
                for (int j = 0; j < menuItems.size(); j++) {
                    if(menuItems.get(j).getId()==v.getId()){
                        menuItems.get(j).setChecked(true);
                        mSelectTabView = menuItems.get(j);
                        break;
                    }
                }
                if(mTabLayoutSelectListener!=null){
                    mTabLayoutSelectListener.checkTabLayout(lottieTabView,lottieTabView.getId());
                }
            });

            menuItems.add(lottieTabView);
        }
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);

    }

    public void setSelectedItemId(int resId){
        for (int j = 0; j < menuItems.size(); j++) {
            menuItems.get(j).setChecked(false);
        }

        for (int j = 0; j < menuItems.size(); j++) {
            if(menuItems.get(j).getId()==resId){
                menuItems.get(j).setChecked(true);
                mSelectTabView = menuItems.get(j);
                break;
            }
        }

        if(mTabLayoutSelectListener!=null){
            mTabLayoutSelectListener.checkTabLayout(mSelectTabView,mSelectTabView.getId());
        }
    }


    public List<LottieTabView> getMenu(){
        return menuItems;
    }

    /**
     * 重新加载icons
     */
    public void reloadMenuIcons() {
        for (int j = 0; j < menuItems.size(); j++) {
            menuItems.get(j).refreshIcons();
//            if(menuItems.get(j).getId() == mSelectTabView.getId()){
//                menuItems.get(j).refreshIcons(true);
//            }else{
//                menuItems.get(j).refreshIcons(false);
//            }
        }
    }

    public interface TabLayoutSelectListener{
        public void checkTabLayout(LottieTabView item,int checkId);
    }

    public void setTabLayoutSelectListener(TabLayoutSelectListener tabLayoutSelectListener) {
        this.mTabLayoutSelectListener = tabLayoutSelectListener;
    }
}
