/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CoinListMapEvent.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.event;

import java.util.LinkedHashMap;

import io.bhex.sdk.quote.bean.QuoteTokensBean;


public class CoinListMapEvent {
    private LinkedHashMap<String, QuoteTokensBean.TokenItem> coinMap;

    public LinkedHashMap<String, QuoteTokensBean.TokenItem> getCoinMap() {
        return coinMap;
    }

    public void setCoinMap(LinkedHashMap<String, QuoteTokensBean.TokenItem> coinMap) {
        this.coinMap = coinMap;
    }
}
