/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OrderFilterEvent.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.event;

/**
 * ================================================
 * 描   述：订单过滤事件
 * ================================================
 */

public class OrderFilterEvent {
    public String baseToken="";
    public String quoteToken="";
    //全部：""  买入："BUY"  卖出："SELL"
    public String orderStatus="";
    //全部：""  限价："LIMIT"  市价："MARKET"
    public String priceMode="";
}
