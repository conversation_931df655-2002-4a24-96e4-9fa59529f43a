/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: StatusBarEvent.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.event;

/**
 * ================================================
 * 描   述：状态栏eventbus通知
 * ================================================
 */

public class StatusBarEvent {
    private boolean isSetTransparent;
    private float aphla;//透明度

    public float getAphla() {
        return aphla;
    }

    public void setAphla(float aphla) {
        this.aphla = aphla;
    }

//    public StatusBarEvent(boolean isSetTransparent) {
//        this.isSetTransparent = isSetTransparent;
//    }
//
//    public boolean isSetTransparent() {
//        return isSetTransparent;
//    }
//
//    public void setSetTransparent(boolean setTransparent) {
//        isSetTransparent = setTransparent;
//    }
}
