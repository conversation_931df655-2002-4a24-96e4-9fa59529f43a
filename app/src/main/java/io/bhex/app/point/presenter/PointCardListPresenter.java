/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PointCardListPresenter.java
 *   @Date: 18-12-17 下午4:03
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.point.presenter;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.point.PointCardApi;
import io.bhex.sdk.point.bean.AppPointCardListResponse;

public class PointCardListPresenter extends BasePresenter<PointCardListPresenter.PointCardListUI> {
    public interface PointCardListUI extends AppUI{

        void showPointCardTypeList(List<AppPointCardListResponse.PointCardTypeBean> data);

        void loadMoreComplete();

        void loadMoreFailed();
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, PointCardListUI ui) {
        super.onUIReady(activity, ui);
        getPointCardList();
    }

    /**
     * 获取点卡列表
     */
    public void getPointCardList() {
        if (!UserInfo.isLogin()) {
            return;
        }
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(),getResources().getString(R.string.hint_network_not_connect));
            return;
        }
        PointCardApi.requestPointCardList(new SimpleResponseListener<AppPointCardListResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(AppPointCardListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    List<AppPointCardListResponse.PointCardTypeBean> data = response.getArray();
                    if (data != null) {
                        getUI().showPointCardTypeList(data);
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
