/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PointCardActivity.java
 *   @Date: 18-12-2 下午2:04
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.point.ui;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.point.adapter.MyPointBalanceListAdapter;
import io.bhex.app.point.presenter.PointCardPresenter;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.point.bean.PointCardBalanceListResponse;

public class PointCardActivity extends BaseActivity<PointCardPresenter,PointCardPresenter.PointCardUI> implements PointCardPresenter.PointCardUI, View.OnClickListener, OnRefreshListener {
    private TopBar topBar;
    private RecyclerView recyclerView;
    private SmartRefreshLayout swipeRefresh;
    private View emptyView;
    private MyPointBalanceListAdapter adapter;

    @Override
    protected int getContentView() {
        return R.layout.activity_pointcard_layout;
    }

    @Override
    protected PointCardPresenter createPresenter() {
        return new PointCardPresenter();
    }

    @Override
    protected PointCardPresenter.PointCardUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        this.setTopbarNight(topBar);
        topBar.setRightOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IntentUtils.goPointCardBuyFlow(PointCardActivity.this);
            }
        });

        viewFinder.find(R.id.pay_btn).setOnClickListener(this);
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        swipeRefresh.setOnRefreshListener(this);
        recyclerView = viewFinder.find(R.id.recyclerView);

        LayoutInflater layoutInflater = LayoutInflater.from(this);
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
//        layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
        emptyView.setLayoutParams(layoutParams);

    }

    @Override
    protected void addEvent() {
        super.addEvent();


    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.pay_btn:
                IntentUtils.goPointCardBuyList(this);
                break;
        }
    }

    @Override
    public void showPointBalanceList(List<PointCardBalanceListResponse.BalanceBean> currentList) {
        if (currentList != null) {
            if (adapter == null) {
                adapter = new MyPointBalanceListAdapter(currentList);
                adapter.isFirstOnly(false);
//                adapter.setOnLoadMoreListener(this,recyclerView);
//                adapter.setEnableLoadMore(true);

                recyclerView.setLayoutManager(new LinearLayoutManager(PointCardActivity.this));
                recyclerView.setItemAnimator(new DefaultItemAnimator());

                adapter.setEmptyView(emptyView);
                recyclerView.setAdapter(adapter);
            } else {
                adapter.setNewData(currentList);
            }
        }
    }

    @Override
    public void loadMoreComplete() {
        if (adapter != null) {
            adapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {
        if (adapter != null) {
            adapter.loadMoreFail();
        }
    }

    @Override
    public void showBalance(PointCardBalanceListResponse response) {
        String pointLimit = response.getPointLimit();
        if (!TextUtils.isEmpty(pointLimit)) {
            if (Double.valueOf(pointLimit)==0d) {
                //额度为零，删除购买入口
                viewFinder.find(R.id.pay_btn).setVisibility(View.GONE);
            }else{
                viewFinder.find(R.id.pay_btn).setVisibility(View.VISIBLE);
            }
        }
    }

    @Override
    public void onRefresh(RefreshLayout refreshLayout) {
        getPresenter().getPointBalanceList();
        refreshLayout.finishRefresh(1000);
    }

}
