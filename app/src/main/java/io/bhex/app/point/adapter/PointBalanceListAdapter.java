/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PointBalanceListAdapter.java
 *   @Date: 18-12-2 下午4:29
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.point.adapter;

import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.DateUtils;
import io.bhex.sdk.data_manager.RateAndLocalManager;
import io.bhex.sdk.point.bean.PointBalanceFlowResponse;

public class PointBalanceListAdapter  extends BaseQuickAdapter<PointBalanceFlowResponse.BalanceFlowBean, BaseViewHolder> {

    public PointBalanceListAdapter(List<PointBalanceFlowResponse.BalanceFlowBean> data) {
        super(R.layout.item_point_balance_list_layout, data);
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final PointBalanceFlowResponse.BalanceFlowBean itemModel) {
        if (RateAndLocalManager.GetInstance(mContext).getCurLocalLanguage().contains("zh")) {
            baseViewHolder.setText(R.id.item_name,itemModel.getDescribe());
        }else{
            baseViewHolder.setText(R.id.item_name,itemModel.getDescribe_en());
        }
        String quantity = itemModel.getQuantity();
        if (!TextUtils.isEmpty(quantity)){
            if (!quantity.contains("-")) {
                baseViewHolder.setText(R.id.item_amount,"+"+itemModel.getQuantity()+" "+itemModel.getTokenName());
            }else{
                baseViewHolder.setText(R.id.item_amount,itemModel.getQuantity()+" "+itemModel.getTokenName());
            }
        }

        baseViewHolder.setText(R.id.item_time,DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getCreatedAt()), "HH:mm:ss yyyy/MM/dd"));

    }
}