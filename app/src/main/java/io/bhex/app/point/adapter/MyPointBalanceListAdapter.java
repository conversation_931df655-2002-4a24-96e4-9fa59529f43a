/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MyPointBalanceListAdapter.java
 *   @Date: 18-12-17 下午3:41
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.point.adapter;

import android.text.TextUtils;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.HashMap;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.data_manager.RateAndLocalManager;
import io.bhex.sdk.point.bean.PointBalanceFlowResponse;
import io.bhex.sdk.point.bean.PointBalanceResponse;
import io.bhex.sdk.point.bean.PointCardBalanceListResponse;

public class MyPointBalanceListAdapter extends BaseQuickAdapter<PointCardBalanceListResponse.BalanceBean, BaseViewHolder> {

    public MyPointBalanceListAdapter(List<PointCardBalanceListResponse.BalanceBean> data) {
        super(R.layout.item_my_point_card_layout, data);
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final PointCardBalanceListResponse.BalanceBean itemModel) {
        if (itemModel != null) {
            baseViewHolder.setText(R.id.point_name,itemModel.getPointCardName());
            String total = itemModel.getTotal();
            total = NumberUtils.roundFormatDown(total, AppData.Config.DIGIT_DEFAULT_VALUE);
           baseViewHolder.setText(R.id.hp_surplus,total);

            String available = itemModel.getFree();
            available = NumberUtils.roundFormatDown(available, AppData.Config.DIGIT_DEFAULT_VALUE);
            baseViewHolder.setText(R.id.hp_available,available);
//            viewFinder.textView(R.id.hp_available).setText(available);

            String lock = itemModel.getLocked();
            lock = NumberUtils.roundFormatDown(lock, AppData.Config.DIGIT_DEFAULT_VALUE);
            baseViewHolder.setText(R.id.hp_frozen,lock);
            PointCardBalanceListResponse.BalanceBean.PointCardMetadataBean pointCardMetadata = itemModel.getPointCardMetadata();
            if (pointCardMetadata != null) {
                if (!TextUtils.isEmpty(pointCardMetadata.getBaseExchangeRateDesc())&&!TextUtils.isEmpty(pointCardMetadata.getExchangeRateDescSuffix())) {
                    baseViewHolder.setText(R.id.hp_fee_tips,pointCardMetadata.getBaseExchangeRateDesc()+pointCardMetadata.getExchangeRateDescSuffix());
                }
            }

//            PointBalanceResponse.BaseQuoteBean baseQuote = response.getBaseQuota();
//            if (baseQuote != null) {
//                String base = baseQuote.getBase();
//                HashMap<String, String> quoteMap = baseQuote.getQuota();
//                String btcValue = quoteMap.get("BTC");
//                double div = NumberUtils.div(base,"10000");
//                double btcAbout = NumberUtils.mul(String.valueOf(div), btcValue);
//
//                String btc = NumberUtils.roundFormatDown(btcAbout, 2);
//                if (btc.endsWith(".00")){
//                    btc = btc.replace(".00","");
//                }
//                ((TextView)headerView.findViewById(R.id.hp_fee_tips)).setText(getString(R.string.string_format_point_value_fee,"10000",btc,"BTC"));
////                viewFinder.textView(R.id.hp_fee_tips).setText(getString(R.string.string_format_point_value_fee,"10000",btc,"BTC"));
//            }

        }
    }
}