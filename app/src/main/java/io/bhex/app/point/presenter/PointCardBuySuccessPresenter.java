/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PointCardBuySuccessPresenter.java
 *   @Date: 18-12-2 下午2:13
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.point.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BasePresenter;

public class PointCardBuySuccessPresenter extends BasePresenter<PointCardBuySuccessPresenter.PointCardBuySuccessUI> {
    public interface PointCardBuySuccessUI extends AppUI{

    }
}
