/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BuyPointCardPresenter.java
 *   @Date: 18-11-30 下午7:42
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.point.presenter;

import android.content.Intent;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.point.PointCardApi;
import io.bhex.sdk.point.bean.AppPointCardListResponse;
import io.bhex.sdk.point.bean.BuyPointResponse;
import io.bhex.sdk.point.bean.PointCardBalanceListResponse;
import io.bhex.sdk.point.bean.PointCardPackageDetail;
import io.bhex.sdk.trade.AssetApi;
import io.bhex.sdk.trade.bean.AssetDataResponse;

public class BuyPointCardPresenter extends BasePresenter<BuyPointCardPresenter.BuyPointCardUI> {
    private String pointPackId="";
    private AppPointCardListResponse.PointCardTypeBean pointPack;

    public void buyPointCard(String numStr, String payToken) {
        if (!UserInfo.isLogin()) {
            ToastUtils.showLong(getActivity(),getString(R.string.string_login_exception_and_retry));
            return;
        }
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }
        PointCardApi.requestBuyPointCard(UserManager.getInstance().getDefaultAccountId(),"",pointPackId,numStr,payToken,"",new SimpleResponseListener<BuyPointResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(BuyPointResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    IntentUtils.goPointCardBuySuccess(getActivity(),response);
                    getActivity().finish();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public interface BuyPointCardUI extends AppUI{

        void showPointDetail(PointCardPackageDetail response);

        void showPointBalance(PointCardBalanceListResponse response);

        void updateAssettByToken(String tokenId, String free);

    }

    @Override
    public void onUIReady(BaseCoreActivity activity, BuyPointCardUI ui) {
        super.onUIReady(activity, ui);
        Intent intent = getActivity().getIntent();
        if (intent != null) {
            pointPack = (AppPointCardListResponse.PointCardTypeBean)intent.getSerializableExtra("pointPack");
            if (pointPack != null) {
                pointPackId = pointPack.getPointPackId();
                getPointCardDetail(pointPackId);
                getPointBalance(pointPackId);
            }
        }
    }

    /**
     * 获取资产
     * @param tokenId
     */
    public void getAsset(final String tokenId) {
        AssetApi.RequestTokenIdAsset(tokenId,new SimpleResponseListener<AssetDataResponse>(){
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(AssetDataResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    List<AssetDataResponse.ArrayBean> data = response.getArray();
                    if (data != null) {
                        if (data.size()>0) {
                            AssetDataResponse.ArrayBean assetBean = data.get(0);
                            if (assetBean != null) {
                                if (tokenId.equals(assetBean.getTokenId())) {
                                    getUI().updateAssettByToken(assetBean.getTokenName(),assetBean.getFree());
                                }
                            }
                        }
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        } );
    }

    /**
     * 请求点卡余额
     * @param pointPackId
     */
    private void getPointBalance(String pointPackId) {
        PointCardApi.requestPointCardBalanceList(new SimpleResponseListener<PointCardBalanceListResponse>(){
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(PointCardBalanceListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showPointBalance(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

//    /**
//     * 获取默认点卡
//     */
//    private void getPointCard() {
//        PointCardApi.requestPointCardList("",new SimpleResponseListener<PointCardListResponse>(){
//            @Override
//            public void onFinish() {
//                super.onFinish();
//            }
//
//            @Override
//            public void onSuccess(PointCardListResponse response) {
//                super.onSuccess(response);
//                if (CodeUtils.isSuccess(response,true)) {
//                    List<PointCardBean> data = response.getArray();
//                    if (data != null) {
//                        if (data.size()>0) {
//                            PointCardBean pointCardBean = data.get(0);
//                            pointPackId = pointCardBean.getPointPackId();
//                            getPointBalance(pointPackId);
//                            getPointCardDetail(pointCardBean.getPointPackId());
//                        }
//                    }
//                }
//            }
//
//            @Override
//            public void onError(Throwable error) {
//                super.onError(error);
//            }
//        });
//    }

    public void getPointCardDetail(String pointPackId) {
        PointCardApi.requestPointCardDetail(pointPackId,new SimpleResponseListener<PointCardPackageDetail>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(PointCardPackageDetail response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showPointDetail(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
