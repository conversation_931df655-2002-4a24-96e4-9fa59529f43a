/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PointBuyRecordAdapter.java
 *   @Date: 18-12-2 下午10:02
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.point.adapter;

import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.sdk.point.bean.PointBalanceFlowResponse;
import io.bhex.sdk.point.bean.PointBuyFlowResponse;

public class PointBuyRecordAdapter extends BaseQuickAdapter<PointBuyFlowResponse.BuyFlowBean, BaseViewHolder> {

    public PointBuyRecordAdapter(List<PointBuyFlowResponse.BuyFlowBean> data) {
        super(R.layout.item_point_records_list_layout, data);
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final PointBuyFlowResponse.BuyFlowBean itemModel) {
//        String payAmount = NumberUtils.subZeroAndDot(itemModel.getPayQuantity()+"");
//        String buyAmount = NumberUtils.subZeroAndDot(itemModel.getQuantity());
        String payAmount = itemModel.getPayQuantity();
        String buyAmount = itemModel.getQuantity();
        String remark = itemModel.getRemark();
        baseViewHolder.setText(R.id.item_name,mContext.getResources().getString(R.string.string_format_point_pay_amount,payAmount,itemModel.getPayTokenId()));
        baseViewHolder.setText(R.id.item_amount,mContext.getResources().getString(R.string.string_format_point_buy_amount,buyAmount)+" "+itemModel.getPointCardName());
        if (!TextUtils.isEmpty(itemModel.getRemark())) {
            if (!remark.startsWith("0")) {
                baseViewHolder.setText(R.id.item_gift_amount,mContext.getResources().getString(R.string.string_gave_away)+" "+remark);
            }
        }
        baseViewHolder.setText(R.id.item_time,DateUtils.getSimpleTimeFormat(Long.valueOf(itemModel.getCreatedAt()), "HH:mm:ss yyyy/MM/dd"));

    }
}