/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PointCardTypeListAdapter.java
 *   @Date: 18-12-17 下午5:29
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.point.adapter;

import android.graphics.Color;
import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.NumberUtils;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.point.bean.AppPointCardListResponse;
import io.bhex.sdk.point.bean.PointCardBalanceListResponse;

/**
 * 点卡类型列表
 */
public class PointCardTypeListAdapter extends BaseQuickAdapter<AppPointCardListResponse.PointCardTypeBean, BaseViewHolder> {

    public PointCardTypeListAdapter(List<AppPointCardListResponse.PointCardTypeBean> data) {
        super(R.layout.item_my_point_card_list_layout, data);
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final AppPointCardListResponse.PointCardTypeBean itemModel) {
        if (itemModel != null) {
            String pointCardId = itemModel.getPointCardId();
            if (pointCardId.equals("BHEX_UCARD")) {
                baseViewHolder.setBackgroundRes(R.id.content,R.mipmap.point_card_package_bg_black);
                baseViewHolder.setBackgroundRes(R.id.btn_buy,R.mipmap.pointcard_btn_buy_black);
                baseViewHolder.setTextColor(R.id.point_name,Color.parseColor("#E6FFFFFF"));
                baseViewHolder.setTextColor(R.id.point_unit_desc,Color.parseColor("#99FFFFFF"));
                baseViewHolder.setTextColor(R.id.point_mouding,Color.parseColor("#B32F2626"));
                baseViewHolder.setTextColor(R.id.point_tips,Color.parseColor("#992F2626"));
                baseViewHolder.setTextColor(R.id.btn_buy,Color.parseColor("#5A5C60"));
            }else{
                baseViewHolder.setBackgroundRes(R.id.content,R.mipmap.point_card_package_bg_yellow);
                baseViewHolder.setBackgroundRes(R.id.btn_buy,R.mipmap.pointcard_btn_buy_yellow);

                baseViewHolder.setTextColor(R.id.point_name,Color.parseColor("#E6735D4E"));
                baseViewHolder.setTextColor(R.id.point_unit_desc,Color.parseColor("#99735D4E"));
                baseViewHolder.setTextColor(R.id.point_mouding,Color.parseColor("#CC735D4E"));
                baseViewHolder.setTextColor(R.id.point_tips,Color.parseColor("#B3735D4E"));
                baseViewHolder.setTextColor(R.id.btn_buy,Color.parseColor("#CC735D4E"));
            }
            baseViewHolder.setText(R.id.point_name,itemModel.getName());
            AppPointCardListResponse.PointCardTypeBean.PointCardBean pointCard = itemModel.getPointCard();
            if (pointCard != null) {
                baseViewHolder.setText(R.id.point_unit_desc,pointCard.getBaseExchangeRateDesc());
                baseViewHolder.setText(R.id.point_mouding,pointCard.getBaseTokenDesc());
                baseViewHolder.setText(R.id.point_tips,pointCard.getDesc());
            }
            baseViewHolder.addOnClickListener(R.id.content);
            baseViewHolder.addOnClickListener(R.id.btn_buy);
        }
    }
}