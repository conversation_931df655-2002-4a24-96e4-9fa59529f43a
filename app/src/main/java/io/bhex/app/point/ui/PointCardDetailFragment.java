/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PointCardDetailFragment.java
 *   @Date: 18-12-14 下午5:26
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.point.ui;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;

import io.bhex.app.R;
import io.bhex.app.base.BaseFragment;
import io.bhex.app.point.adapter.PointBalanceListAdapter;
import io.bhex.app.point.presenter.PointCardDetailPresenter;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.point.bean.PointBalanceFlowResponse;

/**
 * 点卡明细
 */
public class PointCardDetailFragment extends BaseFragment<PointCardDetailPresenter,PointCardDetailPresenter.PointCardDetailUI> implements PointCardDetailPresenter.PointCardDetailUI, OnRefreshListener, BaseQuickAdapter.RequestLoadMoreListener {
    private SmartRefreshLayout swipeRefresh;
    private RecyclerView recyclerView;
    private View emptyView;
    private PointBalanceListAdapter adapter;

    @Override
    protected PointCardDetailPresenter.PointCardDetailUI getUI() {
        return this;
    }

    @Override
    protected PointCardDetailPresenter createPresenter() {
        return new PointCardDetailPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_pointcard_detail_layout,null,false);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        swipeRefresh.setOnRefreshListener(this);
        recyclerView = viewFinder.find(R.id.recyclerView);
        LayoutInflater layoutInflater = LayoutInflater.from(getActivity());
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
        emptyView.setLayoutParams(layoutParams);

    }

    @Override
    public void showPointBalanceList(ArrayList<PointBalanceFlowResponse.BalanceFlowBean> currentList) {
        if (currentList != null) {
            if (adapter == null) {
//            if (currentOrders != null) {
//                if (currentOrders.isEmpty()) {
//                    currentOrders = null;
//                }
//            }
                adapter = new PointBalanceListAdapter(currentList);
//            adapter.openLoadAnimation(BaseQuickAdapter.SCALEIN);
                adapter.isFirstOnly(false);
                adapter.setOnLoadMoreListener(this,recyclerView);
                adapter.setEnableLoadMore(true);
//            swipeRefresh.setColorSchemeColors(Color.RED, Color.BLUE, Color.GREEN);
//            swipeRefresh.setOnRefreshListener(this);

                recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
                recyclerView.setItemAnimator(new DefaultItemAnimator());
//        recyclerView.addItemDecoration(new DividerItemDecoration(getContext(), LinearLayoutManager.VERTICAL));

                adapter.setEmptyView(emptyView);
                recyclerView.setAdapter(adapter);
            } else {
                adapter.setNewData(currentList);
            }
        }
    }

    @Override
    public void loadMoreComplete() {
        if (adapter != null) {
            adapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {
        if (adapter != null) {
            adapter.loadMoreFail();
        }
    }

    @Override
    public void loadEnd() {
        if (adapter != null) {
            adapter.loadMoreEnd();
        }
    }

    @Override
    public void onLoadMoreRequested() {
        getPresenter().getPointCardDetailList(true);
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        getPresenter().getPointCardDetailList(false);
        refreshLayout.finishRefresh(1000);
    }
}
