/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PointCardRecordsPresenter.java
 *   @Date: 18-12-14 下午6:10
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.point.presenter;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BaseFragmentPresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.point.PointCardApi;
import io.bhex.sdk.point.bean.PointBuyFlowResponse;

public class PointCardRecordsPresenter extends BaseFragmentPresenter<PointCardRecordsPresenter.PointCardRecordsUI> {

    private String flowId="";
    private List<PointBuyFlowResponse.BuyFlowBean> currentList = new ArrayList<>();

    public interface PointCardRecordsUI extends AppUI{
        void loadMoreComplete();

        void showPointRecords(List<PointBuyFlowResponse.BuyFlowBean> currentList);

        void loadEnd();

        void loadMoreFailed();
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, PointCardRecordsUI ui) {
        super.onUIReady(activity, ui);
        getPointBuyRecords(false);
    }

    public void getPointBuyRecords(final boolean isLoadMore) {
        if (!UserInfo.isLogin()) {
            if (isLoadMore) {
                getUI().loadMoreComplete();
            }
            return;
        }
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(),getResources().getString(R.string.hint_network_not_connect));
            return;
        }

        if (isLoadMore) {
            if (currentList != null) {
                if (!currentList.isEmpty()) {
                    flowId = currentList.get(currentList.size() - 1).getFlowId();
                }
            }
        }else{
            flowId ="";
        }
        PointCardApi.requestBuyPointCardFlow(UserManager.getInstance().getDefaultAccountId(),flowId,"",AppData.Config.PAGE_LIMIT+"",new SimpleResponseListener<PointBuyFlowResponse>(){
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(PointBuyFlowResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    List<PointBuyFlowResponse.BuyFlowBean> data = response.getArray();
                    if (data != null) {
                        if(!isLoadMore){
                            currentList.clear();
                        }
                        currentList.addAll(data);
                        getUI().showPointRecords(currentList);
                        if (data.size()< AppData.Config.PAGE_LIMIT) {
                            getUI().loadEnd();
                        }else{
                            getUI().loadMoreComplete();
                        }
                    }else{
                        getUI().loadMoreComplete();
                    }
                }else{
                    getUI().loadMoreFailed();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
                if (isLoadMore) {
                    getUI().loadMoreFailed();
                }
            }
        });
    }
}
