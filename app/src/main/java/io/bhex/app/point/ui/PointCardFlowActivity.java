/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PointCardFlowActivity.java
 *   @Date: 18-12-2 下午2:08
 *   @Author: ppzhao
 *   @Description:点卡购买记录
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.point.ui;


import android.view.View;
import android.widget.TextView;

import androidx.core.util.Pair;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.point.presenter.PointCardFlowPresenter;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.app.view.ShadowDrawable;
import io.bhex.baselib.utils.PixelUtils;

public class PointCardFlowActivity extends BaseActivity<PointCardFlowPresenter, PointCardFlowPresenter.PointCardBuyFlowUI> implements PointCardFlowPresenter.PointCardBuyFlowUI, View.OnClickListener, ViewPager.OnPageChangeListener {
    private ViewPager viewPager;
    private PointCardFragmentAdapter fragmentAdapter;
    private boolean isPointDetail=true;
    private TextView tabA;
    private TextView tabB;
    private View tabLinear;

    @Override
    protected int getContentView() {
        return R.layout.activity_pointcard_buy_flow_layout;
    }

    @Override
    protected PointCardFlowPresenter createPresenter() {
        return new PointCardFlowPresenter();
    }

    @Override
    protected PointCardFlowPresenter.PointCardBuyFlowUI getUI() {
        return this;
    }


    @Override
    protected void initView() {
        super.initView();
        viewPager = viewFinder.find(R.id.viewPager);
        initTabView();
        initFragment();
    }

    private void initTabView() {
        tabA = viewFinder.textView(R.id.tab_a);
        tabB = viewFinder.textView(R.id.tab_b);
        tabLinear = viewFinder.find(R.id.tabLinear);
        setShadow(tabLinear);
        viewFinder.find(R.id.tab_a).setOnClickListener(this);
        viewFinder.find(R.id.tab_b).setOnClickListener(this);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.back_btn).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.back_btn:
                finish();
                break;
            case R.id.tab_a:
                switchTab(true);
                viewPager.setCurrentItem(0);
                break;
            case R.id.tab_b:
                switchTab(false);
                viewPager.setCurrentItem(1);
                break;
        }
    }

    private void switchTab(boolean isDetail) {
        isPointDetail = isDetail;
        if (isPointDetail) {
            tabA.setTextColor(getResources().getColor(R.color.blue));
            tabA.setTextAppearance(this,R.style.Body_Blue_Bold);
            tabB.setTextColor(SkinColorUtil.getDark(this));
            tabB.setTextAppearance(this,R.style.Body_Grey);
            tabA.setBackgroundResource(R.drawable.bg_corner_rect_blue);
            tabB.setBackgroundColor(SkinColorUtil.getWhite(this));
        } else {
            tabB.setTextColor(getResources().getColor(R.color.blue));
            tabB.setTextAppearance(this,R.style.Body_Blue_Bold);
            tabA.setTextColor(getResources().getColor(CommonUtil.isBlackMode() ? R.color.dark_night : R.color.dark));
            tabA.setTextAppearance(this,R.style.Body_Grey);
            tabB.setBackgroundResource(R.drawable.bg_corner_rect_blue);
            tabA.setBackgroundResource(R.color.white);
        }

    }

    /**
     * 设置背景和阴影
     *
     * @param view
     */
    private void setShadow(View view) {
        ShadowDrawable.setShadowDrawable(view,
//                getResources().getColor(R.color.white),
                PixelUtils.dp2px(2),
                getResources().getColor(CommonUtil.isBlackMode() ? R.color.dark10_night : R.color.dark10),
                PixelUtils.dp2px(2),
                0,
                PixelUtils.dp2px(1));
    }

    private List<Pair<String, Fragment>> items;

    private void initFragment() {

        items = new ArrayList<>();

        PointCardDetailFragment detailFragment = new PointCardDetailFragment();
        PointCardRecordsFragment recordsFragment = new PointCardRecordsFragment();
        items.add(new Pair<String, Fragment>("detail", detailFragment));
        items.add(new Pair<String, Fragment>("records", recordsFragment));
        fragmentAdapter = new PointCardFragmentAdapter(getSupportFragmentManager());
        viewPager.setAdapter(fragmentAdapter);
        viewPager.setCurrentItem(0);

        viewPager.addOnPageChangeListener(this);
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {
        if (position==0){
            switchTab(true);
        }else{
            switchTab(false);
        }
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    private class PointCardFragmentAdapter extends FragmentPagerAdapter {

        public PointCardFragmentAdapter(FragmentManager fm) {
            super(fm);
        }

        @Override
        public Fragment getItem(int position) {
            return items.get(position).second;
        }

        @Override
        public int getCount() {
            return items.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return items.get(position).first;
        }


    }
}
