/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PointCardRuleActivity.java
 *   @Date: 18-12-2 下午2:20
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.point.ui;

import android.text.Html;
import android.text.TextUtils;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.point.presenter.PointCardRulePresenter;

public class PointCardRuleActivity extends BaseActivity<PointCardRulePresenter,PointCardRulePresenter.PointCardRuleUI> implements PointCardRulePresenter.PointCardRuleUI {
    @Override
    protected int getContentView() {
        return R.layout.activity_pointcard_rule_layoout;
    }

    @Override
    protected PointCardRulePresenter createPresenter() {
        return new PointCardRulePresenter();
    }

    @Override
    protected PointCardRulePresenter.PointCardRuleUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();


    }

    @Override
    public void showContent(String content) {
        if (!TextUtils.isEmpty(content)) {
            viewFinder.textView(R.id.content).setText(Html.fromHtml(content));
        }
    }
}
