/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PointCardFlowPresenter.java
 *   @Date: 18-12-2 下午2:07
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.point.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;

public class PointCardFlowPresenter extends BasePresenter<PointCardFlowPresenter.PointCardBuyFlowUI> {

    public interface PointCardBuyFlowUI extends AppUI{
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, PointCardBuyFlowUI ui) {
        super.onUIReady(activity, ui);
    }


}
