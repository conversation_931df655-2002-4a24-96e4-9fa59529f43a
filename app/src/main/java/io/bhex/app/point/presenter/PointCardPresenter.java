/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PointCardPresenter.java
 *   @Date: 18-12-2 下午2:03
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.point.presenter;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.point.PointCardApi;
import io.bhex.sdk.point.bean.PointCardBalanceListResponse;

public class PointCardPresenter extends BasePresenter<PointCardPresenter.PointCardUI> {

    public interface PointCardUI extends AppUI{

        void showPointBalanceList(List<PointCardBalanceListResponse.BalanceBean> currentList);

        void loadMoreComplete();

        void loadMoreFailed();

        void showBalance(PointCardBalanceListResponse response);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, PointCardUI ui) {
        super.onUIReady(activity, ui);

    }

    @Override
    public void onResume() {
        super.onResume();
        getPointBalanceList();
    }

    /**
     * 请求点卡余额列表
     */
    public void getPointBalanceList() {
        if (!UserInfo.isLogin()) {
            return;
        }
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(),getResources().getString(R.string.hint_network_not_connect));
            return;
        }
        PointCardApi.requestPointCardBalanceList(new SimpleResponseListener<PointCardBalanceListResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(PointCardBalanceListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showBalance(response);
                    List<PointCardBalanceListResponse.BalanceBean> balanceList = response.getBalanceList();
                    if (balanceList != null) {
                        getUI().showPointBalanceList(balanceList);
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

}
