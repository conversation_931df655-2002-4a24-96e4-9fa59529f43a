/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PointCardBuySuccessActivity.java
 *   @Date: 18-12-2 下午2:16
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.point.ui;

import android.content.Intent;
import android.view.View;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.point.presenter.PointCardBuySuccessPresenter;
import io.bhex.app.utils.IntentUtils;
import io.bhex.sdk.point.bean.BuyPointResponse;

public class PointCardBuySuccessActivity extends BaseActivity<PointCardBuySuccessPresenter,PointCardBuySuccessPresenter.PointCardBuySuccessUI> implements PointCardBuySuccessPresenter.PointCardBuySuccessUI, View.OnClickListener {
    @Override
    protected int getContentView() {
        return R.layout.activity_pointcard_buy_success_layout;
    }

    @Override
    protected PointCardBuySuccessPresenter createPresenter() {
        return new PointCardBuySuccessPresenter();
    }

    @Override
    protected PointCardBuySuccessPresenter.PointCardBuySuccessUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        Intent intent = getIntent();
        if (intent != null) {
            BuyPointResponse buyPointResponse = (BuyPointResponse) intent.getSerializableExtra("pointResult");
            if (buyPointResponse != null) {

                viewFinder.textView(R.id.tips_a).setText(getString(R.string.string_format_tips_point_buy_success,buyPointResponse.getPointQuantity()+""));
                List<BuyPointResponse.BonusTokenListBean> bonusTokenList = buyPointResponse.getBonusTokenList();
                if (bonusTokenList != null&&bonusTokenList.size()>0) {
                    BuyPointResponse.BonusTokenListBean bonusTokenListBean = bonusTokenList.get(0);
                    if (bonusTokenListBean != null) {
                        int quantity = bonusTokenListBean.getQuantity();
                        if (quantity>0) {
                            viewFinder.textView(R.id.tips_b).setText(getString(R.string.string_format_tips_gift_buy_success,bonusTokenListBean.getQuantity()+"",bonusTokenListBean.getTokenId()));
                        }
                    }
                }
            }
        }

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.btn_buy).setOnClickListener(this);
        viewFinder.find(R.id.btn_lookup).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.btn_buy:
//                IntentUtils.goPointCardBuy(this, pointPackId);
                finish();
                break;
            case R.id.btn_lookup:
                IntentUtils.goPointCard(this);
                finish();
                break;
        }
    }
}
