/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PointCardListActivity.java
 *   @Date: 18-12-17 下午4:04
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.point.ui;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.point.adapter.PointCardTypeListAdapter;
import io.bhex.app.point.presenter.PointCardListPresenter;
import io.bhex.app.utils.IntentUtils;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.point.bean.AppPointCardListResponse;

public class PointCardListActivity extends BaseActivity<PointCardListPresenter,PointCardListPresenter.PointCardListUI> implements PointCardListPresenter.PointCardListUI, OnRefreshListener {
    private SmartRefreshLayout swipeRefresh;
    private RecyclerView recyclerView;
    private View emptyView;
    private PointCardTypeListAdapter adapter;

    @Override
    protected int getContentView() {
        return R.layout.activity_pointcard_list_layout;
    }

    @Override
    protected PointCardListPresenter createPresenter() {
        return new PointCardListPresenter();
    }

    @Override
    protected PointCardListPresenter.PointCardListUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        swipeRefresh.setOnRefreshListener(this);
        recyclerView = viewFinder.find(R.id.recyclerView);

        LayoutInflater layoutInflater = LayoutInflater.from(this);
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
//        layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
        emptyView.setLayoutParams(layoutParams);

    }

    @Override
    protected void addEvent() {
        super.addEvent();


    }

    @Override
    public void showPointCardTypeList(final List<AppPointCardListResponse.PointCardTypeBean> data) {
        if (data != null) {
            if (adapter == null) {
                adapter = new PointCardTypeListAdapter(data);
                adapter.isFirstOnly(false);
//                adapter.setOnLoadMoreListener(this,recyclerView);
//                adapter.setEnableLoadMore(true);

                recyclerView.setLayoutManager(new LinearLayoutManager(this));
                recyclerView.setItemAnimator(new DefaultItemAnimator());

                adapter.setEmptyView(emptyView);
                adapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                    @Override
                    public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                        if (view.getId()==R.id.btn_buy||view.getId()==R.id.content) {
                            if (data != null) {
                                AppPointCardListResponse.PointCardTypeBean pointCardTypeBean = data.get(position);
//                                String pointPackId = pointCardTypeBean.getPointPackId();
                                IntentUtils.goPointCardBuy(PointCardListActivity.this,pointCardTypeBean);
                            }
                        }
                    }
                });
                recyclerView.setAdapter(adapter);
            } else {
                adapter.setNewData(data);
            }
        }
    }

    @Override
    public void loadMoreComplete() {
        if (adapter != null) {
            adapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {
        if (adapter != null) {
            adapter.loadMoreFail();
        }
    }

    @Override
    public void onRefresh(RefreshLayout refreshLayout) {
        getPresenter().getPointCardList();
        refreshLayout.finishRefresh(1000);
    }

}
