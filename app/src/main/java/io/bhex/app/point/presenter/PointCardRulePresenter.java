/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PointCardRulePresenter.java
 *   @Date: 18-12-2 下午2:19
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.point.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.data_manager.RateAndLocalManager;
import io.bhex.sdk.point.PointCardApi;
import io.bhex.sdk.point.bean.PointRuleResponse;

public class PointCardRulePresenter extends BasePresenter<PointCardRulePresenter.PointCardRuleUI> {
    public interface PointCardRuleUI extends AppUI{

        void showContent(String content);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, PointCardRuleUI ui) {
        super.onUIReady(activity, ui);
        getPointRule();
    }

    private void getPointRule() {
        PointCardApi.requestPointCardRule(RateAndLocalManager.GetInstance(getActivity()).getCurLocalLanguage(),new SimpleResponseListener<PointRuleResponse>(){
            @Override
            public void onSuccess(PointRuleResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    PointRuleResponse.DataBean data = response.getData();
                    if (data != null) {
                        String content = data.getLanguage();
                        getUI().showContent(content);
                    }
                }
            }
        });
    }
}
