/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BaseFragment.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.base;

import android.app.Activity;
import android.view.View;
import android.widget.TextView;

import com.umeng.analytics.MobclickAgent;

import io.bhex.app.R;
import io.bhex.app.utils.KeyBoardUtil;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.mvp.BaseFragmentPresenter;
import io.bhex.baselib.mvp.BaseMVPFragment;


public abstract class BaseFragment<P extends BaseFragmentPresenter<V>, V extends AppUI> extends BaseMVPFragment<P,V> {
    @Override
    public void onResume() {
        super.onResume();
        MobclickAgent.onPageStart(this.getClass().getName());
    }

    @Override
    public void onPause() {
        super.onPause();
        MobclickAgent.onPageEnd(this.getClass().getName());
    }

    @Override
    protected void onVisibleChanged(boolean visible) {
        super.onVisibleChanged(visible);
    }

    @Override
    protected void setRootView(View rootView) {
        super.setRootView(rootView);
        //RateAndLocalManager.GetInstance(getActivity()).SetCurLocalKind(RateAndLocalManager.GetInstance(getActivity()).getCurLocalKind());
        initViews();
        addEvent();
    }

    public interface NotifyActivity{
        void onNotifyActivity();
    }

    public  NotifyActivity mOnNotifyActivity;

    public void setOnNotifyActivity(NotifyActivity onNotifyActivity){
        mOnNotifyActivity = onNotifyActivity;
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        if(activity instanceof NotifyActivity)
            setOnNotifyActivity((NotifyActivity)activity);
    }

    public void ActivityNotifyFragment(){
        if(mOnNotifyActivity != null)
            mOnNotifyActivity.onNotifyActivity();
    }

    protected void initViews() {

    }

    protected void addEvent() {

    }

    public void showProgressDialog(){
        showProgressDialog("","");
    }

    public void showProgressDialog(String title, String hint) {
        if (getActivity() instanceof BaseActivity)
            ((BaseActivity) getActivity()).showProgressDialog(title, hint);
    }

    public void dismissProgressDialog() {
        if (getActivity() instanceof BaseActivity)
            ((BaseActivity) getActivity()).dismissProgressDialog();
    }

    protected void setTopbarNight(TopBar topBar){
        topBar.setLeftImg(R.mipmap.btn_head_back);
    }

    /**
     * 打开软键盘
     * @param textView
     */
    public void openKeyBoard(TextView textView){
        KeyBoardUtil.openKeybord(textView,getActivity());
    }

    /**
     * 关闭软键盘
     * @param textView
     */
    public void closeKeyBoard(TextView textView){
        KeyBoardUtil.closeKeybord(textView,getActivity());
    }

    /**
     * 隐藏软键盘
     */
    public void hideKeyBoard(){
        KeyBoardUtil.hideKeyboard(getActivity());
    }
}
