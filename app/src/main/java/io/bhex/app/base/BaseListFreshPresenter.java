/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BaseListFreshPresenter.java
 *   @Date: 1/24/19 7:48 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.base;

import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BaseFragmentPresenter;

public abstract class BaseListFreshPresenter<V extends BaseListFreshPresenter.BaseListFreshUI> extends BaseFragmentPresenter<V> {
    public interface BaseListFreshUI extends AppUI {
        void loadMoreComplete();

        void loadMoreFailed();

        void loadEnd();
    }
    protected String mPageId = "";

    public void loadMore() {
        getData(true);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, V ui) {
        super.onUIReady(activity, ui);
        getData(false);
    }

    @Override
    public void onResume(){
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onVisibleChanged(boolean visible) {
        super.onVisibleChanged(visible);
    }

    public abstract void getData(final boolean isLoadMore);


}
