/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: WebJsBean.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.web.presenter;

import java.util.List;

public class WebJsBean {
    public String code;//OK CANCEL ERROR
    public String page;
    //public JsData data;
    public boolean multiple;
    public boolean isFirstPage;
    public List<String> data;
    public static class JsData{
        public String language;
        public String url;
    }
    public String title;
    public String desc;
    public String link;
    public String imgUrl;
    public int currentImageIndex;
    public List<String> imageArray;
    public boolean disableRefresh;//禁止下拉刷新
    public boolean disableLoadMore;//禁止加载更多
}
