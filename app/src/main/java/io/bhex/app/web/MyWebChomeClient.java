/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MyWebChomeClient.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.web;

import android.net.Uri;
import android.webkit.JsResult;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebView;

import io.bhex.baselib.utils.ToastUtils;


public class MyWebChomeClient extends WebChromeClient {

    private OpenFileChooserCallBack mOpenFileChooserCallBack;
    
    @Override
	public void onReceivedTitle(WebView view, String title) {
		super.onReceivedTitle(view, title);
		if(mOpenFileChooserCallBack != null)
			mOpenFileChooserCallBack.setWevTitle(title);
	}

	@Override
	public void onProgressChanged(WebView view, int newProgress) {
		if(mOpenFileChooserCallBack != null){
            mOpenFileChooserCallBack.setProgress(newProgress);
        }
		super.onProgressChanged(view, newProgress);
	}

    public MyWebChomeClient(OpenFileChooserCallBack openFileChooserCallBack) {
        mOpenFileChooserCallBack = openFileChooserCallBack;
    }

    //For Android 3.0+
    public void openFileChooser(ValueCallback<Uri> uploadMsg, String acceptType) {
    	if(mOpenFileChooserCallBack != null)
    		mOpenFileChooserCallBack.openFileChooserCallBack(uploadMsg, acceptType);
    }

    // For Android < 3.0
    public void openFileChooser(ValueCallback<Uri> uploadMsg) {
        openFileChooser(uploadMsg, "");
    }

    @Override
    public boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> filePathCallback, FileChooserParams fileChooserParams) {
        if(mOpenFileChooserCallBack != null)
            mOpenFileChooserCallBack.openFilesChooserCallBack(filePathCallback, "");
        return true;
    }

    // For Android  > 4.1.1
    public void openFileChooser(ValueCallback<Uri> uploadMsg, String acceptType, String capture) {
        openFileChooser(uploadMsg, acceptType);
    }


    @Override
    public boolean onJsAlert(WebView view, String url, String message, final JsResult result)
    {
        ToastUtils.showShort(message);
        return true;
    }

    public interface OpenFileChooserCallBack {
        void openFileChooserCallBack(ValueCallback<Uri> uploadMsg, String acceptType);
        void openFilesChooserCallBack(ValueCallback<Uri[]> uploadMsg, String acceptType);
        void setWevTitle(String title);
        void setProgress(int newProgress);
    }

}