/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: WebActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.web.ui;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.web.WebViewFragment;
import io.bhex.app.web.presenter.NullPresenter;

import static io.bhex.app.web.WebViewFragment.TITLE;
import static io.bhex.app.web.WebViewFragment.KEY_URL;


public class WebActivity extends BaseActivity<NullPresenter,NullPresenter.NullUI> implements NullPresenter.NullUI {
    private WebViewFragment mWebviewFragment;

    @Override
    protected int getContentView() {
        return R.layout.activity_fragment_container_layout;
    }

    @Override
    protected NullPresenter createPresenter() {
        return new NullPresenter();
    }

    @Override
    protected NullPresenter.NullUI getUI() {
        return this;
    }

    public static void runActivity(Context context, String title, String url) {
        Intent intent = new Intent(context, WebActivity.class);
        intent.putExtra(KEY_URL, url);
        intent.putExtra(TITLE, title);
        context.startActivity(intent);
    }

    public static void runActivityForResult(Activity context, String title, String url,int requestCode) {
        Intent intent = new Intent(context, WebActivity.class);
        intent.putExtra(KEY_URL, url);
        intent.putExtra(TITLE, title);
        context.startActivityForResult(intent,requestCode);
    }

    @Override
    protected void initView() {
        super.initView();
        mWebviewFragment = new WebViewFragment();
        Bundle bundle = new Bundle();

        String url = getIntent().getStringExtra(KEY_URL);
        String title = getIntent().getStringExtra(TITLE);

        findViewById(R.id.topBar).setVisibility(View.GONE);

        bundle.putString(KEY_URL, url);
        bundle.putString(TITLE, title);
        mWebviewFragment.setArguments(bundle);
        getSupportFragmentManager()    //
                .beginTransaction()
                .add(R.id.fragment_container, mWebviewFragment)   // 此处的R.id.fragment_container是要盛放fragment的父容器
                .commit();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {

        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if(mWebviewFragment != null && mWebviewFragment.isVisible()) {
                if(mWebviewFragment.GoBack() == true)// 返回前一个页面
                    return true;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if(mWebviewFragment != null)
            mWebviewFragment.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if(mWebviewFragment != null)
            mWebviewFragment.onRequestPermissionsResult(requestCode, permissions, grantResults);

    }
}
