/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: JsCallNative.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.web.js;

import android.webkit.JavascriptInterface;

import io.bhex.baselib.utils.ToastUtils;


public class JsCallNative extends Object {
    private final JsCallback mCallback;

    public JsCallNative(JsCallback jsCallback) {
        mCallback = jsCallback;
    }

    // 定义JS需要调用的方法
    // 被JS调用的方法必须加入@JavascriptInterface注解
    @JavascriptInterface
    public void recaptchaSuccess(String token) {

        mCallback.Success(token);
    }

    public void recaptchaFail(String msg){
        mCallback.Failed(msg);
    }


    public interface JsCallback{
        void Success(String token);
        void Failed(String failMsg);
    }
}
