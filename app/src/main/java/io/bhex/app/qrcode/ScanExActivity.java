/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: ScanActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.qrcode;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.os.Vibrator;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;

import com.bhex.qrlib.XQRCode;
import com.bhex.qrlib.ui.CaptureFragment;
import com.bhex.qrlib.util.QRCodeAnalyzeUtils;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.qrcode.core.QRCodeView;
import io.bhex.app.qrcode.zxing.ZXingView;
import io.bhex.app.utils.PathUtils;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.ToastUtils;
import pub.devrel.easypermissions.AfterPermissionGranted;
import pub.devrel.easypermissions.EasyPermissions;

/**
 * ================================================
 * 描   述：扫码
 * ================================================
 */

public class ScanExActivity extends BaseActivity<ScanPresenter,ScanPresenter.ScanUI> implements ScanPresenter.ScanUI,EasyPermissions.PermissionCallbacks {

    private static final String TAG = "ScanExActivity";
    private static final int REQUEST_CODE_CHOOSE_QRCODE_FROM_GALLERY = 0x111;
    private static final int REQUEST_CODE_QRCODE_PERMISSIONS = 1;
    private static final int REQUEST_CODE_SELECT_IMAGE = 0x012;
    private TopBar topBar;

    @Override
    protected int getContentView() {
        return R.layout.activity_scan_ex_layout;
    }

    @Override
    protected ScanPresenter createPresenter() {
        return new ScanPresenter();
    }

    @Override
    protected ScanPresenter.ScanUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        topBar.setRightOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //调用相册
                Intent intent = new Intent(Intent.ACTION_PICK,
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
                startActivityForResult(intent, REQUEST_CODE_SELECT_IMAGE);
            }
        });
    }
    /**
     * 初始化采集
     */
    protected void initCaptureFragment() {
        CaptureFragment captureFragment = new CaptureFragment();
        captureFragment.setAnalyzeCallback(analyzeCallback);
        captureFragment.setCameraInitCallBack(cameraInitCallBack);
        getSupportFragmentManager().beginTransaction().replace(R.id.fl_zxing_container, captureFragment).commit();
    }
    /**
     * 二维码解析回调函数
     */
    QRCodeAnalyzeUtils.AnalyzeCallback analyzeCallback = new QRCodeAnalyzeUtils.AnalyzeCallback() {
        @Override
        public void onAnalyzeSuccess(Bitmap bitmap, String result) {
            handleAnalyzeSuccess(bitmap, result);
        }

        @Override
        public void onAnalyzeFailed() {
            handleAnalyzeFailed();
        }
    };
    /**
     * 处理扫描成功
     *
     * @param bitmap
     * @param result
     */
    protected void handleAnalyzeSuccess(Bitmap bitmap, String result) {
        Intent resultIntent = new Intent();
        resultIntent.putExtra("result", result);
        setResult(RESULT_OK, resultIntent);
        finish();
    }

    /**
     * 处理解析失败
     */
    protected void handleAnalyzeFailed() {
        ToastUtils.showShort(this, getString(R.string.string_not_find_qrcode));
    }
    /**
     * 照相机初始化监听
     */
    CaptureFragment.CameraInitCallBack cameraInitCallBack = new CaptureFragment.CameraInitCallBack() {
        @Override
        public void callBack(@Nullable Exception e) {
            if (e != null) {
                ScanExActivity.showNoPermissionTip(ScanExActivity.this);
                onCameraInitFailed();
            } else {
                //照相机初始化成功
                onCameraInitSuccess();
            }
        }
    };
    /**
     * 显示无照相机权限提示
     *
     * @param activity
     * @param listener 确定点击事件
     * @return
     */
    public static AlertDialog showNoPermissionTip(final Activity activity, DialogInterface.OnClickListener listener) {
        return new AlertDialog.Builder(activity)
                .setTitle(R.string.xqrcode_pay_attention)
                .setMessage(R.string.xqrcode_not_get_permission)
                .setPositiveButton(R.string.string_submit, listener)
                .show();
    }

    /**
     * 显示无照相机权限提示
     *
     * @param activity
     * @return
     */
    public static AlertDialog showNoPermissionTip(final Activity activity) {
        return showNoPermissionTip(activity, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                activity.finish();
            }
        });
    }

    /**
     * 相机初始化成功
     */
    protected void onCameraInitSuccess() {

    }

    /**
     * 相机初始化失败
     */
    protected void onCameraInitFailed() {

    }
    @Override
    protected void onStart() {
        super.onStart();
        requestCodeQRCodePermissions();
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        //获取图片路径
        if (requestCode == REQUEST_CODE_SELECT_IMAGE && resultCode == Activity.RESULT_OK && data != null) {
            Uri selectedImage = data.getData();
            getAnalyzeQRCodeResult(selectedImage);
        }
    }
    private void getAnalyzeQRCodeResult(Uri uri) {
        XQRCode.analyzeQRCode(PathUtils.getFilePathByUri(this, uri), analyzeCallback);
    }
    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this);
    }

    @Override
    public void onPermissionsGranted(int requestCode, List<String> perms) {
        initCaptureFragment();
    }

    @Override
    public void onPermissionsDenied(int requestCode, List<String> perms) {
        ScanExActivity.showNoPermissionTip(ScanExActivity.this);
//        requestCodeQRCodePermissions();
    }

    @AfterPermissionGranted(REQUEST_CODE_QRCODE_PERMISSIONS)
    private void requestCodeQRCodePermissions() {
        String[] perms = {Manifest.permission.CAMERA, Manifest.permission.READ_EXTERNAL_STORAGE};
        if (!EasyPermissions.hasPermissions(this, perms)) {
            EasyPermissions.requestPermissions(this, getString(R.string.scan_permission_hint), REQUEST_CODE_QRCODE_PERMISSIONS, perms);
        }else{
            initCaptureFragment();
        }
    }
}
