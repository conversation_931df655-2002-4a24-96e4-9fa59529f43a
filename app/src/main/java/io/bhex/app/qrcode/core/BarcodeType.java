/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BarcodeType.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.qrcode.core;

public enum BarcodeType {
    /**
     * 所有格式
     */
    ALL,
    /**
     * 所有一维条码格式
     */
    ONE_DIMENSION,
    /**
     * 所有二维条码格式
     */
    TWO_DIMENSION,
    /**
     * 仅 QR_CODE
     */
    ONLY_QR_CODE,
    /**
     * 仅 CODE_128
     */
    ONLY_CODE_128,
    /**
     * 仅 EAN_13
     */
    ONLY_EAN_13,
    /**
     * 高频率格式，包括 QR_CODE、EAN_13、CODE_128
     */
    HIGH_FREQUENCY,
    /**
     * 自定义格式
     */
    CUSTOM
}
