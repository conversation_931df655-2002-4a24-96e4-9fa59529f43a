package io.bhex.app.push;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;

import java.util.HashMap;

import io.bhex.app.R;
import io.bhex.app.app.BHexApplication;
import io.bhex.app.main.bean.ShortcutEntry;
import io.bhex.app.main.ui.MainActivity;
import io.bhex.app.utils.ActivityCache;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.web.ui.WebActivity;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.JsonConvertor;
import io.bhex.baselib.utils.SP;
import io.bhex.sdk.utils.UtilsApi;

public class PushTranslateActivity extends BaseCoreActivity {

    private String mUrl = "";
    private String mType = "";
    private HashMap<String, String> mParamMap = new HashMap<>();
    private String mPage = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_push_translate);
        DebugLog.e("PushTranslateActivity","onCreate begin init param");
        Intent intent = getIntent();
        mUrl = intent.getStringExtra("url");
        mPage = intent.getStringExtra("page");
        mType = intent.getStringExtra("type");
        String params= intent.getStringExtra("param");
        String pushOrderId = intent.getStringExtra("reqOrderId");
        DebugLog.e("PushTranslateActivity","mUrl："+mUrl+"，mPage"+mPage+"，mType："+mType);
        if (TextUtils.isEmpty(mType)) {
            Uri uri = intent.getData();
            if (uri != null) {
                mUrl = uri.getQueryParameter("url");
                mPage = uri.getQueryParameter("page");
                mType = uri.getQueryParameter("type");
                params = uri.getQueryParameter("param");
                pushOrderId = uri.getQueryParameter("reqOrderId");
            }
        }
        if(!TextUtils.isEmpty(params)) {
            try {
                params =params.toUpperCase();
                mParamMap = JsonConvertor.getInstance().fromJson(params,HashMap.class);
            } catch (Exception e){
                mParamMap =new HashMap<>();
            }
        }
        if (!TextUtils.isEmpty(pushOrderId)) {
            UtilsApi.RequestSendPushClickReport(this, SP.get(AppData.SPKEY.PUSH_TOKEN, ""), pushOrderId);
        }

    }

    @Override
    protected void onResume() {
        super.onResume();

        DebugLog.e("PushTranslateActivity","mUrl："+mUrl+"，mPage"+mPage+"，mType："+mType);
        if (ActivityCache.getInstance().getActivityMap().size() == 0) {
            IntentUtils.goMain(PushTranslateActivity.this);
            // 如果首页未启动
            BHexApplication.getMainHandler().postDelayed(()->{
                if (TextUtils.isEmpty(mType) || mType.equalsIgnoreCase("1")) {
                    if (!TextUtils.isEmpty(mUrl)) {
                        WebActivity.runActivity(MainActivity.getInstance()!=null?MainActivity.getInstance():this, "", mUrl);
                    }
                } else {
                    ShortcutEntry.handleNavigatePage(MainActivity.getInstance()!=null?MainActivity.getInstance():this, mParamMap, mPage);
                }
            },500);
        } else {
            if (TextUtils.isEmpty(mType) || mType.equalsIgnoreCase("1")) {
                if (!TextUtils.isEmpty(mUrl)) {
                    WebActivity.runActivity(this, "", mUrl);
                }
            } else {
                if (!TextUtils.isEmpty(mPage)) {
                    ShortcutEntry.handleNavigatePage(this, mParamMap, mPage);
                }
            }
        }
        finish();
    }

    @Override
    protected  void onNewIntent(Intent intent) {
        super.onNewIntent(intent);

        DebugLog.e("PushTranslateActivity","onNewIntent");
    }
}
