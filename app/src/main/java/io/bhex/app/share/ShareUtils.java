/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: ShareUtils.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.share;

import android.app.Activity;
import android.graphics.Bitmap;

import io.bhex.app.R;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.DialogUtils;

/**
 * ================================================
 * 描   述：分享
 * ================================================
 */

public class ShareUtils {
    /**
     * 分享网址
     *
     * @param activity
     * @param url
     * @param title
     * @param desc
     */
    public static void shareWeb(final Activity activity, final String url, final String title, final String desc) {
        DialogUtils.showShareDialog(activity, "", R.drawable.custom_socialize_copyurl, activity.getResources().getString(R.string.string_copy_link), true, new DialogUtils.OnShareListener() {
            @Override
            public void onShareWx() {
                ShareManager.getInstance().shareWebToFriend(title,desc,url);
            }

            @Override
            public void onShareWxCircle() {
                ShareManager.getInstance().shareWebToCircle(title,desc,url);
            }

            @Override
            public void onMore() {
                SystemShareUtils.shareUrl(activity,desc + "  " + url);
            }

            @Override
            public void onSavePic() {
                CommonUtil.copyText(activity, desc + "  " + url);
            }

            @Override
            public void onCancel() {

            }
        });
    }


    public static void share(SHARE_MEDIA shareMedia, final Bitmap bitmap) {
        if (shareMedia.getName().equals(SHARE_MEDIA.WEIXIN.getName())) {
            ShareManager.getInstance().shareImageToFriend(bitmap);
        }else{
            ShareManager.getInstance().shareImageToCircle(bitmap);
        }
    }

}
