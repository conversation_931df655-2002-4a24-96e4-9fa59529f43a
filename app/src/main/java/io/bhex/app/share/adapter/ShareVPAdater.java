package io.bhex.app.share.adapter;

import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2019-12-12
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class ShareVPAdater extends PagerAdapter {

    private List<View> mViewList = new ArrayList<>();

    public ShareVPAdater(List<View> views) {
        mViewList = views;
    }

    @Override
    public int getCount() {
        return mViewList.size();
    }

    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        return view == object;
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        View view = (View) object;
        container.removeView(view);
    }

    @Override
    public Object instantiateItem(ViewGroup container, int position) {
        View symbolItem = null;
        if (position < mViewList.size())
            symbolItem = mViewList.get(position);

        container.addView(symbolItem);
        return symbolItem;// 返回填充的View对象
    }

    public void setData(List<View> views) {
        mViewList = views;
        notifyDataSetChanged();
    }

    public List<View> getDataList() {
        return mViewList;
    }
}
