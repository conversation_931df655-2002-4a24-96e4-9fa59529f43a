/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: ShareListener.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.share;

import android.content.Context;
import android.widget.Toast;

public class ShareListener {
    private final Context mContext;

    public ShareListener(Context context) {
        mContext =context;
    }

    /**
     * @descrption 分享开始的回调
     * @param platform 平台类型
     */
    public void onStart(SHARE_MEDIA platform) {

    }

    /**
     * @descrption 分享成功的回调
     * @param platform 平台类型
     */
    public void onResult(SHARE_MEDIA platform) {
        Toast.makeText(mContext,"成功了",Toast.LENGTH_LONG).show();
    }

    /**
     * @descrption 分享失败的回调
     * @param platform 平台类型
     * @param t 错误原因
     */
    public void onError(SHARE_MEDIA platform, Throwable t) {
        Toast.makeText(mContext,"失败"+t.getMessage(),Toast.LENGTH_LONG).show();
    }

    /**
     * @descrption 分享取消的回调
     * @param platform 平台类型
     */
    public void onCancel(SHARE_MEDIA platform) {
        Toast.makeText(mContext,"取消了",Toast.LENGTH_LONG).show();

    }
}
