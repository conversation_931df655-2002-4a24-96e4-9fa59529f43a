package io.bhex.app.share;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;

import com.tencent.mm.opensdk.modelmsg.SendMessageToWX;
import com.tencent.mm.opensdk.modelmsg.WXImageObject;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.modelmsg.WXTextObject;
import com.tencent.mm.opensdk.modelmsg.WXWebpageObject;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

import java.io.File;

import io.bhex.app.BuildConfig;
import io.bhex.app.R;
import io.bhex.app.app.BHexApplication;
import io.bhex.app.share.utils.WXUtil;
import io.bhex.baselib.utils.ToastUtils;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-06-25
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class ShareManager {
    private static final ShareManager ourInstance = new ShareManager();
    private static final int THUMB_SIZE = 150;
//    private int mTargetScene = SendMessageToWX.Req.WXSceneSession;
    private int mTargetScene = SendMessageToWX.Req.WXSceneTimeline;

    private final IWXAPI api;

    public static ShareManager getInstance() {
        return ourInstance;
    }

    private ShareManager() {
        api = WXAPIFactory.createWXAPI(BHexApplication.getInstance(), BuildConfig.WEIXIN_ID, false);
    }

    public void shareText(String text){
        if (text == null || text.length() == 0) {
            return;
        }

        WXTextObject textObj = new WXTextObject();
        textObj.text = text;

        WXMediaMessage msg = new WXMediaMessage();
        msg.mediaObject = textObj;
        // msg.title = "Will be ignored";
        msg.description = text;
        msg.mediaTagName = "我是mediaTagName啊";

        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("text");
        req.message = msg;
        req.scene = mTargetScene;

        api.sendReq(req);
    }

    public void shareWebToFriend(String title,String desc,String webUrl){
        shareWeb(title,desc,webUrl, SendMessageToWX.Req.WXSceneSession);
    }

    public void shareWebToCircle(String title,String desc,String webUrl){
        shareWeb(title,desc,webUrl, SendMessageToWX.Req.WXSceneTimeline);
    }

    private void shareWeb(String title,String desc,String webUrl,int mTargetScene){
        WXWebpageObject webpage = new WXWebpageObject();
        webpage.webpageUrl = webUrl;
        WXMediaMessage msg = new WXMediaMessage(webpage);
        msg.title = title;
        msg.description = desc;
        Bitmap bmp = BitmapFactory.decodeResource(BHexApplication.getInstance().getResources(), R.mipmap.ic_launcher);
        Bitmap thumbBmp = Bitmap.createScaledBitmap(bmp, THUMB_SIZE, THUMB_SIZE, true);
        bmp.recycle();
        msg.thumbData = WXUtil.bmpToByteArray(thumbBmp, true);

        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("webpage");
        req.message = msg;
        req.scene = mTargetScene;
        api.sendReq(req);
    }

    public void shareImageToFriend(Bitmap bitmap){
        shareImage(bitmap, SendMessageToWX.Req.WXSceneSession);
    }

    public void shareImageToCircle(Bitmap bitmap){
        shareImage(bitmap, SendMessageToWX.Req.WXSceneTimeline);
    }

    private void shareImage(int imgResId){
        Bitmap bmp = BitmapFactory.decodeResource(BHexApplication.getInstance().getResources(), imgResId);
        WXImageObject imgObj = new WXImageObject(bmp);

        WXMediaMessage msg = new WXMediaMessage();
        msg.mediaObject = imgObj;

        Bitmap thumbBmp = Bitmap.createScaledBitmap(bmp, THUMB_SIZE, THUMB_SIZE, true);
        bmp.recycle();
        msg.thumbData = WXUtil.bmpToByteArray(thumbBmp, true);

        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("img");
        req.message = msg;
        req.scene = mTargetScene;
        api.sendReq(req);
    }

    private void shareImage(Bitmap bmp,int mTargetScene){
        WXImageObject imgObj = new WXImageObject(bmp);

        WXMediaMessage msg = new WXMediaMessage();
        msg.mediaObject = imgObj;

//        Bitmap thumbBmp = Bitmap.createScaledBitmap(bmp, bmp.getWidth(), bmp.getHeight(), true);
//        bmp.recycle();
//        msg.thumbData = WXUtil.bmpToByteArray(thumbBmp, true);

        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("img");
        req.message = msg;
        req.scene = mTargetScene;
        api.sendReq(req);
    }

    private void shareImage(String filePath){
        File file = new File(filePath);
        if (!file.exists()) {
            String tip =BHexApplication.getInstance().getString(R.string.send_img_file_not_exist);
            ToastUtils.showLong(tip);
            return;
        }

        WXImageObject imgObj = new WXImageObject();
        imgObj.setImagePath(filePath);

        WXMediaMessage msg = new WXMediaMessage();
        msg.mediaObject = imgObj;

//        Bitmap bmp = BitmapFactory.decodeFile(filePath);
//        Bitmap thumbBmp = Bitmap.createScaledBitmap(bmp, THUMB_SIZE, THUMB_SIZE, true);
//        bmp.recycle();
//        msg.thumbData = WXUtil.bmpToByteArray(thumbBmp, true);

        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("img");
        req.message = msg;
        req.scene = mTargetScene;
        api.sendReq(req);
    }


    private String buildTransaction(final String type) {
        return (type == null) ? String.valueOf(System.currentTimeMillis()) : type + System.currentTimeMillis();
    }

}
