package io.bhex.app.share;

import android.text.TextUtils;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-06-25
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public enum SHARE_MEDIA {
    WEIXIN,
    WEIXIN_CIRCLE,
    WEIXIN_FAVORITE;

    public static SHARE_MEDIA convertToEmun(String var0) {
        if (TextUtils.isEmpty(var0)) {
            return null;
        } else if (var0.equals("wxtimeline")) {
            return WEIXIN_CIRCLE;
        } else if (var0.equals("wxsession")) {
            return WEIXIN;
        } else {
            SHARE_MEDIA[] var1 = values();
            SHARE_MEDIA[] var2 = var1;
            int var3 = var1.length;

            for(int var4 = 0; var4 < var3; ++var4) {
                SHARE_MEDIA var5 = var2[var4];
                if (var5.toString().trim().equals(var0)) {
                    return var5;
                }
            }

            return null;
        }
    }

    private SHARE_MEDIA() {
    }

    public String toString() {
        return super.toString();
    }

    public String getName() {
        if (this.toString().equals("WEIXIN")) {
            return "wxsession";
        } else if (this.toString().equals("WEIXIN_CIRCLE")) {
            return "wxtimeline";
        } else {
            return this.toString().equals("WEIXIN_FAVORITE") ? "wxfavorite" : this.toString().toLowerCase();
        }
    }
}
