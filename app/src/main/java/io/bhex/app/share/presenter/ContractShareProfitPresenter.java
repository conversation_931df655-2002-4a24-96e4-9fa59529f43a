/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: ShareProfitPresenter.java
 *   @Date: 19-5-29 下午5:04
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.share.presenter;

import android.os.Handler;
import android.text.TextUtils;

import io.bhex.app.base.AppUI;
import io.bhex.app.utils.ShareConfigUtils;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.config.ConfigApi;
import io.bhex.sdk.config.bean.ShareConfigBean;
import io.bhex.sdk.invite.InviteApi;
import io.bhex.sdk.invite.bean.InviteResponse;

public class ContractShareProfitPresenter extends BasePresenter<ContractShareProfitPresenter.ContractShareProfitUI> {
    public interface ContractShareProfitUI extends AppUI{

        void setShareConfig(ShareConfigBean response);

        void showShareInfo(InviteResponse response);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, ContractShareProfitUI ui) {
        super.onUIReady(activity, ui);
        getShareInfo();
        getShareConfig();
    }

    public void getShareInfo(){
        if (!UserInfo.isLogin()) {
            return;
        }
        InviteApi.inviteShareInfo(new SimpleResponseListener<InviteResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(InviteResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().showShareInfo(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    /**
     * 获取分享配置
     */
    private void getShareConfig() {
        ConfigApi.getShareConfig(new SimpleResponseListener<ShareConfigBean>(){

            @Override
            public void onSuccess(ShareConfigBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    getUI().setShareConfig(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }

            @Override
            public ShareConfigBean parserResponse(Handler uiHandler, String response, Class<ShareConfigBean> clazz) {
                if (!TextUtils.isEmpty(response)) {
                    ShareConfigUtils.saveShareConfigData(response);
                }
                return super.parserResponse(uiHandler, response, clazz);
            }
        });
    }
}
