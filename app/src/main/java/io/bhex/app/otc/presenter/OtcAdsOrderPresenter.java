/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcAdsOrderPresenter.java
 *   @Date: 19-2-28 下午4:56
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.presenter;

import android.os.Bundle;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.IntentUtils;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BaseFragmentPresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.otc.OtcAdsApi;
import io.bhex.sdk.otc.OtcConstant;
import io.bhex.sdk.otc.bean.OtcAdsListResponse;

public class OtcAdsOrderPresenter extends BaseFragmentPresenter<OtcAdsOrderPresenter.OtcAdsOrderUI> {
    private int orderStatus = 0;
    private int page = 1;
    private int pageSize = OtcConstant.PAGE_LIMIT;
    private List<OtcAdsListResponse.AdBean> currentHistoryData = new ArrayList<>();

    public void loadMore() {
        getOrders(true, orderStatus);
    }

    public void refreshOrders() {
        getOrders(false, orderStatus);
    }

    public void offlineAds(final OtcAdsListResponse.AdBean adBean, final boolean isModifyAd) {
        OtcAdsApi.cancelAd(adBean.getId(),new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    if (response.isSuccess()) {
                        if (isModifyAd) {
                            IntentUtils.goOtcPublishAds(getActivity(),adBean);
                        }else{
                            ToastUtils.showLong(getActivity(),getString(R.string.string_otc_offline_success));
                            getOrders(false, orderStatus);

                        }
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public interface OtcAdsOrderUI extends AppUI {
        void loadMoreComplete();

        void loadMoreFailed();

        void loadEnd();

        void showOrderList(List<OtcAdsListResponse.AdBean> datas);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, OtcAdsOrderUI ui) {
        super.onUIReady(activity, ui);
        Bundle arguments = getFragment().getArguments();
        if (arguments != null) {
            orderStatus = arguments.getInt("orderStatus");
            getOrders(false, orderStatus);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        getOrders(false, orderStatus);
    }

    /**
     * 获取法币订单列表
     *
     * @param isAutoLoad
     * @param orderStatus
     */
    public void getOrders(final boolean isAutoLoad, int orderStatus) {
//        OtcAdsApi.getAdsList(0,"",new SimpleResponseListener<OtcAdsListResponse>(){});

        if (!isAutoLoad) {
            page = 1;
            pageSize = OtcConstant.PAGE_LIMIT;
        }
        OtcAdsApi.getAdsList(orderStatus, "", page, pageSize, new SimpleResponseListener<OtcAdsListResponse>() {
            @Override
            public void onBefore() {
                super.onBefore();
                if (!isAutoLoad) {
                    getUI().showProgressDialog("", "");
                }
            }

            @Override
            public void onFinish() {
                super.onFinish();
                if (!isAutoLoad) {
                    getUI().dismissProgressDialog();
                }
                getUI().loadMoreComplete();
            }

            @Override
            public void onSuccess(OtcAdsListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    List<OtcAdsListResponse.AdBean> datas = response.getItems();
                    if (datas != null) {
                        page++;
                        if (isAutoLoad) {
                            currentHistoryData.addAll(datas);
                        } else {
                            currentHistoryData = datas;
                        }
                        getUI().showOrderList(currentHistoryData);
                        if (datas.size() < pageSize) {
                            getUI().loadEnd();
                        } else {
                            getUI().loadMoreComplete();
                        }
                    } else {
                        getUI().loadMoreComplete();
                    }
                } else {
                    getUI().loadMoreFailed();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });

    }
}
