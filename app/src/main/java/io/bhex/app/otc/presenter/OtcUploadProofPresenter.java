/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcUploadProofPresenter.java
 *   @Date: 19-1-31 上午11:38
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.presenter;

import android.text.TextUtils;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.otc.OtcConstant;
import io.bhex.sdk.otc.OtcMessageApi;
import io.bhex.sdk.otc.bean.OtcOrderInfoResponse;
import io.bhex.sdk.otc.bean.OtcUploadImgResponse;

public class OtcUploadProofPresenter extends BasePresenter<OtcUploadProofPresenter.OtcUploadProofUI> {
    public void submitProof(OtcOrderInfoResponse orderInfo, String reason, List<String> proofList) {
        //提交文本证明
        sendMessage(orderInfo.getId(),reason,OtcConstant.OTC_PROOF_MESSAGE_TYPE_TXT);

        if (proofList != null) {
            //循环提交图片证明
            for (String url : proofList) {
                if (!url.equals("+")) {
                    sendMessage(orderInfo.getId(),url,OtcConstant.OTC_PROOF_MESSAGE_TYPE_IMAGE);
                }
            }
            ToastUtils.showShort(getString(R.string.string_submit_success));
        }

    }

    public interface OtcUploadProofUI extends AppUI{

    }

    @Override
    public void onUIReady(BaseCoreActivity activity, OtcUploadProofUI ui) {
        super.onUIReady(activity, ui);
    }

    /**
     * 上传图片
     * @param path
     * @param simpleResponseListener
     */
    public void uploadImage(String orderId,String path, SimpleResponseListener<OtcUploadImgResponse> simpleResponseListener) {
        if (TextUtils.isEmpty(orderId)) {
            return;
        }
        OtcMessageApi.uploadImage(path,simpleResponseListener);
    }

    /**
     * 发送消息
     * @param msg
     * @param msgType
     */
    public void sendMessage(String orderId,String msg, int msgType) {
        if (TextUtils.isEmpty(orderId)) {
            return;
        }
        OtcMessageApi.sendMessage(orderId,msg,msgType,new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    if (response.isSuccess()) {
                        return;
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
