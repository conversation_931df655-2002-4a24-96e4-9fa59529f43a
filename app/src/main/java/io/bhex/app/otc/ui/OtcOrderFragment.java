/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcOrderFragment.java
 *   @Date: 19-1-25 下午2:40
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.ui;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseFragment;
import io.bhex.app.otc.adapter.OtcOrdersListAdapter;
import io.bhex.app.otc.presenter.OtcOrderPresenter;
import io.bhex.app.utils.IntentUtils;
import io.bhex.sdk.otc.bean.OtcOrderInfoResponse;

public class OtcOrderFragment extends BaseFragment<OtcOrderPresenter,OtcOrderPresenter.OtcOrderUI> implements OtcOrderPresenter.OtcOrderUI, OnRefreshListener, BaseQuickAdapter.RequestLoadMoreListener {
    private SmartRefreshLayout swipeRefresh;
    private RecyclerView recyclerView;
    private View emptyView;
    private OtcOrdersListAdapter adapter;
    private List<OtcOrderInfoResponse> showOrders;

    @Override
    protected OtcOrderPresenter.OtcOrderUI getUI() {
        return this;
    }

    @Override
    protected OtcOrderPresenter createPresenter() {
        return new OtcOrderPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.otc_orders_fragment_layout,null,false);
    }


    @Override
    protected void initViews() {
        super.initViews();
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        swipeRefresh.setOnRefreshListener(this);
        recyclerView = viewFinder.find(R.id.recyclerView);

        LayoutInflater layoutInflater = LayoutInflater.from(getActivity());
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
//        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
//        layoutParams.height = PixelUtil.dp2px(200, this);
//        emptyView.setLayoutParams(layoutParams);

    }

    @Override
    protected void addEvent() {
        super.addEvent();

    }

    @Override
    public void onLoadMoreRequested() {
//        swipeRefresh.postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                adapter.loadComplete();
//            }
//        }, 500);
        getPresenter().loadMore();
    }

    @Override
    public void loadMoreComplete() {
        if (adapter != null) {
            adapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {
        if (adapter != null) {
            adapter.loadMoreFail();
        }
    }

    @Override
    public void loadEnd() {
        if (adapter != null) {
            adapter.loadMoreEnd();
        }
    }

//    @Override
//    public List<OtcOrderInfoResponse> getShowOrders() {
//        return showOrders;
//    }


    @Override
    public void onRefresh(RefreshLayout refreshLayout) {
        getPresenter().refreshOrders();
        refreshLayout.finishRefresh(1000);
    }

    List<OtcOrderInfoResponse> appealOrders = new ArrayList<>();
    @Override
    public void showOrderList(List<OtcOrderInfoResponse> currentOrders) {
        showOrders = currentOrders;
//        //排序把申诉的列表排到最上面
//        appealOrders.clear();
//        for (OtcOrderInfoResponse currentOrder : currentOrders) {
//            if (currentOrder.getStatus()==30) {
//                appealOrders.add(currentOrder);
//            }
//        }
//        for (OtcOrderInfoResponse appealOrder : appealOrders) {
//            currentOrders.remove(appealOrder);
//        }
//        currentOrders.addAll(0,appealOrders);

        if (adapter == null) {
//            if (currentOrders != null) {
//                if (currentOrders.isEmpty()) {
//                    currentOrders = null;
//                }
//            }
            adapter = new OtcOrdersListAdapter(currentOrders);
//            adapter.openLoadAnimation(BaseQuickAdapter.SCALEIN);
            adapter.isFirstOnly(false);
            if (getPresenter().getIsNeedLoadMore()) {
                adapter.setOnLoadMoreListener(this,recyclerView);
            }
            adapter.setEnableLoadMore(getPresenter().getIsNeedLoadMore());
//            swipeRefresh.setColorSchemeColors(Color.RED, Color.BLUE, Color.GREEN);
//            swipeRefresh.setOnRefreshListener(this);

            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            recyclerView.setItemAnimator(new DefaultItemAnimator());
//        recyclerView.addItemDecoration(new DividerItemDecoration(getContext(), LinearLayoutManager.VERTICAL));

            adapter.setEmptyView(emptyView);
            recyclerView.setAdapter(adapter);
            adapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                        OtcOrderInfoResponse itemModel = (OtcOrderInfoResponse) adapter.getData().get(position);
                        boolean isBuy = itemModel.getSide() == 0;
                        IntentUtils.goOtcBuySellPay(getActivity(),isBuy,itemModel.getId());
                }
            });
        } else {
            adapter.setNewData(currentOrders);
        }
    }
}
