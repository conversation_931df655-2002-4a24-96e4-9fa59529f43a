/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcPublishAdsActivity.java
 *   @Date: 19-2-28 上午11:56
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.ui;

import android.content.Intent;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.TextView;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.otc.adapter.OtcCurrencyAdapter;
import io.bhex.app.otc.adapter.OtcSymbolAdapter;
import io.bhex.app.otc.presenter.OtcPublishAdsPresenter;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.app.view.InputView;
import io.bhex.app.view.PointLengthFilter;
import io.bhex.app.view.PopWindow;
import io.bhex.app.view.ShadowDrawable;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.utils.NumberUtils;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.otc.bean.OtcAdsListResponse;
import io.bhex.sdk.otc.bean.OtcConfigResponse;
import io.bhex.sdk.otc.bean.OtcLastPriceResponse;
import io.bhex.sdk.otc.bean.OtcUserInfo;
import io.bhex.sdk.otc.bean.TradeFeeRateBean;

public class OtcPublishAdsActivity extends BaseActivity<OtcPublishAdsPresenter, OtcPublishAdsPresenter.OtcPublishAdsUI> implements OtcPublishAdsPresenter.OtcPublishAdsUI, View.OnClickListener, CompoundButton.OnCheckedChangeListener {
    private boolean mBuy = true;
    private TextView tabA;
    private TextView tabB;
    private View tabLinear;
    private CheckBox floatingPriceCb;
    private InputView priceInput;
    private InputView numInput;
    private InputView minAmountInput;
    private InputView maxAmountInput;
    private EditText remarksInput;
    private TextView minAmountInputUnit;
    private TextView priceUnit;
    private TextView numUnit;
    private TextView maxAmountInputUnit;
    private TextView selectCurrencyTx;
    private TextView legalCurrencyTx;
    private String maxQuote;
    private String minQuote;
    private String currentFreeAsset = "0";
    private View listLayout;
    private RecyclerView recyclerViewList;
    private PopWindow listPop;
    private OtcConfigResponse.TokenBean currentTokenBean;
    private OtcConfigResponse currentConfig;
    private OtcConfigResponse.CurrencyBean currentLegalCurrency;
    private View listLegalLayout;
    private RecyclerView recyclerViewLegalList;
    private PopWindow listLegalPop;
    private PointLengthFilter pricePointFilter;
    private OtcConfigResponse.TokenBean currentToken;
    private boolean isTrade;
    private InputView fundPwdInput;
    private PointLengthFilter numPointFilter;
    private PointLengthFilter amountPointFilter;
    private OtcAdsListResponse.AdBean adBeanModify;
    private String currentLastPrice="";
    private TextView floatPriceAboutTx;
    private TextView adsFeeTx;
    private TextView adsFeeRateTx;
    private TradeFeeRateBean currentFeeBean;

    @Override
    protected int getContentView() {
        return R.layout.activity_otc_publish_ads_layout;
    }

    @Override
    protected OtcPublishAdsPresenter createPresenter() {
        return new OtcPublishAdsPresenter();
    }

    @Override
    protected OtcPublishAdsPresenter.OtcPublishAdsUI getUI() {
        return this;
    }

    @Override
    protected boolean isNeedLogin() {
        return true;
    }

    @Override
    protected void initView() {
        super.initView();
        adsFeeTx = viewFinder.textView(R.id.adsFee);
        adsFeeRateTx = viewFinder.textView(R.id.adsFeeRate);
        Intent intent = getIntent();
        if (intent != null) {
            adBeanModify = (OtcAdsListResponse.AdBean) intent.getSerializableExtra("ad");
        }
        initTabView();
        priceInput = viewFinder.find(R.id.averagePrice);
        numInput = viewFinder.find(R.id.num);
        minAmountInput = viewFinder.find(R.id.minLimitAmount);
        maxAmountInput = viewFinder.find(R.id.maxLimitAmount);
        remarksInput = viewFinder.find(R.id.tradingRemarks);
        priceUnit = viewFinder.textView(R.id.priceUnit);
        numUnit = viewFinder.textView(R.id.numUnit);
        floatPriceAboutTx = viewFinder.textView(R.id.otc_float_price_about);
        minAmountInputUnit = viewFinder.textView(R.id.minLimitAmountUnit);
        maxAmountInputUnit = viewFinder.textView(R.id.maxLimitAmountUnit);
        selectCurrencyTx = viewFinder.textView(R.id.otc_ad_curreny);
        legalCurrencyTx = viewFinder.textView(R.id.otc_ad_legal_currency);

        fundPwdInput = viewFinder.find(R.id.fundPwd);
        priceInput.setPaddingRight(PixelUtils.dp2px(40));
        numInput.setPaddingRight(PixelUtils.dp2px(40));
        minAmountInput.setPaddingRight(PixelUtils.dp2px(40));
        maxAmountInput.setPaddingRight(PixelUtils.dp2px(40));
        floatingPriceCb = viewFinder.find(R.id.otc_ad_floating_price_cb);
        floatingPriceCb.setOnCheckedChangeListener(this);

        LayoutInflater layoutInflater = LayoutInflater.from(this);
        listLayout = layoutInflater.inflate(R.layout.otc_appeal_type_layout, null, false);
        recyclerViewList = listLayout.findViewById(R.id.recyclerView);
        listPop = new PopWindow(this, listLayout, PixelUtils.getScreenWidth() - 2 * PixelUtils.dp2px(24), PixelUtils.dp2px(300));

        listLegalLayout = layoutInflater.inflate(R.layout.otc_appeal_type_layout, null, false);
        recyclerViewLegalList = listLegalLayout.findViewById(R.id.recyclerView);
        listLegalPop = new PopWindow(this, listLegalLayout,PixelUtils.getScreenWidth() - 2 * PixelUtils.dp2px(24),PixelUtils.dp2px(160));

        numPointFilter = new PointLengthFilter();
        numPointFilter.setDecimalLength(2);
        numInput.getEditText().setFilters(new InputFilter[]{numPointFilter});
        amountPointFilter = new PointLengthFilter();
        minAmountInput.getEditText().setFilters(new InputFilter[]{amountPointFilter});
        maxAmountInput.getEditText().setFilters(new InputFilter[]{amountPointFilter});
        pricePointFilter = new PointLengthFilter();
        pricePointFilter.setDecimalLength(2);
        priceInput.getEditText().setFilters(new InputFilter[]{pricePointFilter});

        if (adBeanModify != null) {
            setModifyAdData(adBeanModify);
        }

    }

    /**
     * 设置修改数据
     *
     * @param adBean
     */
    private void setModifyAdData(OtcAdsListResponse.AdBean adBean) {
        int side = adBean.getSide();
        if (side == 0) {
            switchTab(true);
        } else {
            switchTab(false);
        }

        selectCurrencyTx.setText(adBean.getTokenName());
        legalCurrencyTx.setText(adBean.getCurrencyId());

        if (adBean.getPriceType() == 0) {
            //限价
            floatingPriceCb.setChecked(false);
            priceInput.setInputString(adBean.getPrice());
            priceUnit.setText(adBean.getCurrencyId());
        } else {
            floatingPriceCb.setChecked(true);
            priceInput.setInputString(adBean.getPremium());
            priceUnit.setText("%");
        }

        numInput.setInputString(adBean.getQuantity());
        minAmountInput.setInputString(adBean.getMinAmount());
        maxAmountInput.setInputString(adBean.getMaxAmount());
        remarksInput.setText(adBean.getRemark());
        numUnit.setText(adBean.getTokenName());
        minAmountInputUnit.setText(adBean.getCurrencyId());
        maxAmountInputUnit.setText(adBean.getCurrencyId());

    }

    private void initTabView() {
        tabA = viewFinder.textView(R.id.tab_a);
        tabB = viewFinder.textView(R.id.tab_b);
        tabLinear = viewFinder.find(R.id.tabLinear);
        setShadow(tabLinear);
        viewFinder.find(R.id.tab_a).setOnClickListener(this);
        viewFinder.find(R.id.tab_b).setOnClickListener(this);
        switchTab(false);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.btn_publish).setOnClickListener(this);
        viewFinder.find(R.id.otc_ad_curreny).setOnClickListener(this);
        viewFinder.find(R.id.otc_ad_legal_currency).setOnClickListener(this);
        viewFinder.find(R.id.otc_latest_price).setOnClickListener(this);
        priceInput.addTextWatch(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                setFloatPriceAbout();

            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        numInput.addTextWatch(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                String amount = s.toString().trim();
                showFee(currentFeeBean,amount);
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
    }

    private void setFloatPriceAbout() {
        if (floatingPriceCb.isChecked()) {
            String floatPrice = priceInput.getInputString();
            if (TextUtils.isEmpty(floatPrice)) {
                floatPriceAboutTx.setText(getString(R.string.string_otc_floating_price_about));
            }else{
                double ratio = NumberUtils.div("100",floatPrice);
                if (currentLegalCurrency != null) {
                    floatPriceAboutTx.setText(getString(R.string.string_otc_floating_price_about)+" "+NumberUtils.roundFormatDown(NumberUtils.mul(currentLastPrice,ratio+""),2)+" "+currentLegalCurrency.getCurrencyId());
                }else{
                    floatPriceAboutTx.setText(getString(R.string.string_otc_floating_price_about)+" "+NumberUtils.roundFormatDown(NumberUtils.mul(currentLastPrice,ratio+""),2));
                }
            }
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tab_a:
                switchTab(true);
                break;
            case R.id.tab_b:
                switchTab(false);
                break;
            case R.id.otc_latest_price:
                if (!floatingPriceCb.isChecked()) {
                    if (!TextUtils.isEmpty(currentLastPrice)) {
                        priceInput.setInputString(currentLastPrice);
                    }
                }
                break;
            case R.id.otc_ad_curreny:
                if (currentConfig != null) {
                    List<OtcConfigResponse.TokenBean> tokenBeanList = currentConfig.getToken();
                    if (tokenBeanList != null) {
                        showListPop(tokenBeanList);
                    }
                }
                break;
            case R.id.otc_ad_legal_currency:
//                if (currentConfig != null) {
//                    List<OtcConfigResponse.CurrencyBean> legalCurrencyList = currentConfig.getCurrency();
//                    if (legalCurrencyList != null) {
//                        showLegalCurrencyPop(legalCurrencyList);
//                    }
//                }
                break;
            case R.id.btn_publish:
                //发布广告
                if (currentTokenBean == null) {
                    ToastUtils.showLong(this, getString(R.string.string_otc_select_b_please));
                    return;
                }
                if (currentLegalCurrency == null) {
                    ToastUtils.showLong(this, getString(R.string.string_otc_select_currency));
                    return;
                }
                String price = priceInput.getInputString();
                if (TextUtils.isEmpty(price)) {
                    ToastUtils.showLong(this, getString(floatingPriceCb.isChecked() ? R.string.string_prompt_floating_price : R.string.string_prompt_input_price));
//                    priceInput.setInputEditFocusable(true);
                    return;
                }
                if (floatingPriceCb.isChecked()) {
                    if (NumberUtils.sub("80", price) > 0 || NumberUtils.sub("120", price) < 0) {
                        ToastUtils.showLong(this, getString(R.string.string_prompt_floating_price));

                        return;
                    }
                } else {
//                    if (NumberUtils.sub("0.01", price) > 0 || NumberUtils.sub("10000000", price) < 0) {
//                        ToastUtils.showLong(this, getString(R.string.string_prompt_price_range));
//                        return;
//                    }
                }

                String num = numInput.getInputString();
                if (TextUtils.isEmpty(num)) {
                    ToastUtils.showLong(this, getString(R.string.string_prompt_input_amount));
                    return;
                }
                if (currentToken != null) {
                    if (NumberUtils.sub(currentToken.getMinQuote(), num) > 0 || NumberUtils.sub(currentToken.getMaxQuote(), num) < 0) {
                        ToastUtils.showLong(this, getString(R.string.string_otc_prompt_num_range, currentToken.getMinQuote(), currentToken.getMaxQuote()));
                        return;
                    }
                }
                if (!mBuy) {
                    if (NumberUtils.sub(currentFreeAsset, num) < 0) {
                        ToastUtils.showLong(this, getString(R.string.string_otc_not_enough_balance));
                        return;
                    }
                }

                String minAmount = minAmountInput.getInputString();
                String maxAmount = maxAmountInput.getInputString();
                if (TextUtils.isEmpty(minAmount) || NumberUtils.sub(minAmount, minQuote) < 0 || TextUtils.isEmpty(maxAmount) || NumberUtils.sub(maxAmount, maxQuote) > 0) {
                    ToastUtils.showLong(this, getString(R.string.string_otc_prompt_amount_range, minQuote, maxQuote));
                    return;
                }
                if (NumberUtils.sub(maxAmount, minAmount) < 0) {
                    ToastUtils.showLong(this, getString(R.string.string_otc_prompt_limit_amount_error));
                    return;
                }
                String remark = remarksInput.getText().toString().trim();
                if (!TextUtils.isEmpty(remark)) {
                    if (remark.length() > 200) {
                        ToastUtils.showLong(this, getString(R.string.string_trade_remark_more_than_length));
                    }
                }

                String fundPwd = fundPwdInput.getInputString();
                if (!isTrade) {
                    if (TextUtils.isEmpty(fundPwd)) {
                        ToastUtils.showLong(this, getString(R.string.string_input_please) + getString(R.string.string_finance_passwd));
                        return;
                    }
                }
                getPresenter().publishAds(mBuy, currentTokenBean, currentLegalCurrency, floatingPriceCb.isChecked(), price, num, minAmount, maxAmount, remark, fundPwd);
                break;
        }
    }

    /**
     * 设置错误提示
     * @param input
     * @param errorTips
     */
    private void setInputError(InputView input, String errorTips) {
        input.setError(errorTips);
    }

    private void switchTab(boolean isBuy) {
        mBuy = isBuy;
        if (mBuy) {
            tabA.setTextColor(SkinColorUtil.getGreen(this));
            tabA.setTextAppearance(this, R.style.Body_Green_Bold);
            tabB.setTextColor(SkinColorUtil.getDark(this));
            tabB.setTextAppearance(this, CommonUtil.isBlackMode() ? R.style.Body_Dark_night : R.style.Body_Dark);
            tabA.setBackgroundResource(SkinColorUtil.getGreenRectBg(this));
            tabB.setBackgroundColor(SkinColorUtil.getWhite(this));
        } else {
            tabB.setTextColor(SkinColorUtil.getRed(this));
            tabB.setTextAppearance(this, R.style.Body_Red_Bold);
            tabA.setTextColor(SkinColorUtil.getWhite(this));
            tabA.setTextAppearance(this, CommonUtil.isBlackMode() ? R.style.Body_Dark_night : R.style.Body_Dark);
            tabB.setBackgroundResource(SkinColorUtil.getRedRectBg(this));
            tabA.setBackgroundColor(SkinColorUtil.getWhite(this));
        }
        showFee(currentFeeBean,"");

    }

    /**
     * 设置背景和阴影
     *
     * @param view
     */
    private void setShadow(View view) {
        ShadowDrawable.setShadowDrawable(view,
//                getResources().getColor(R.color.white),
                PixelUtils.dp2px(2),
                getResources().getColor(CommonUtil.isBlackMode() ? R.color.dark10_night : R.color.dark10),
                PixelUtils.dp2px(2),
                0,
                PixelUtils.dp2px(1));
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        priceInput.setInputString("");
        if (isChecked) {
            //浮动价格
            pricePointFilter.setDecimalLength(2);
            priceInput.setInputHint(getString(R.string.string_otc_hint_premium_ratio));
            priceUnit.setText("%");
            floatPriceAboutTx.setVisibility(View.VISIBLE);
        } else {
            priceInput.setInputHint(getString(R.string.string_unit));
            if (currentLegalCurrency != null) {
                priceUnit.setText(currentLegalCurrency.getCurrencyId());
                pricePointFilter.setDecimalLength(currentLegalCurrency.getScale());
            } else {
                priceUnit.setText("");
            }
            floatPriceAboutTx.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    public void showCurrency(OtcConfigResponse.CurrencyBean currencyBean) {
        if (currencyBean != null) {
            currentLegalCurrency = currencyBean;
            legalCurrencyTx.setText(currencyBean.getCurrencyId());
            priceUnit.setText(currencyBean.getCurrencyId());
            minAmountInputUnit.setText(currencyBean.getCurrencyId());
            maxAmountInputUnit.setText(currencyBean.getCurrencyId());
            maxQuote = currencyBean.getMaxQuote();
            minQuote = currencyBean.getMinQuote();
            amountPointFilter.setDecimalLength(currencyBean.getAmountScale());
            if (!floatingPriceCb.isChecked()) {
                pricePointFilter.setDecimalLength(currencyBean.getScale());
            }
        }
    }

    @Override
    public void showCurrentToken(OtcConfigResponse.TokenBean tokenBean) {
        if (tokenBean != null) {
            currentTokenBean = tokenBean;
            loadCurrentTokenConfig(tokenBean);
            selectCurrencyTx.setText(tokenBean.getTokenName());
            numUnit.setText(tokenBean.getTokenName());
            currentFeeBean = tokenBean.getFee();
            showFee(currentFeeBean,numInput.getInputString());

        }
    }

    private void showFee(TradeFeeRateBean currentFeeBean, String amount) {
        if (currentFeeBean != null) {
            adsFeeTx.setVisibility(View.VISIBLE);
            adsFeeRateTx.setVisibility(View.VISIBLE);
            if (TextUtils.isEmpty(amount)) {
                amount= numInput.getInputString();
            }
            String fee = mBuy?currentFeeBean.getMakerBuyTradeFee():currentFeeBean.getMakerSellTradeFee();
            adsFeeTx.setText(getString(R.string.string_otc_fee)+":"+NumberUtils.roundFormatDown(NumberUtils.mul(amount,fee),AppData.Config.DIGIT_DEFAULT_VALUE)+" "+currentTokenBean.getTokenName());
            adsFeeRateTx.setText(getString(R.string.string_otc_fee_rate)+":"+NumberUtils.roundFormatDown(NumberUtils.mul(fee,"100"),2)+"%");
        }else{
            adsFeeTx.setVisibility(View.GONE);
            adsFeeRateTx.setVisibility(View.GONE);
        }
    }

    @Override
    public void showConfigInfo(OtcConfigResponse response) {
        if (response != null) {
            currentConfig = response;
            if (adBeanModify != null) {
                /**修改广告*******/
                List<OtcConfigResponse.TokenBean> tokenList = response.getToken();
                for (OtcConfigResponse.TokenBean tokenBean : tokenList) {
                    if (adBeanModify.getTokenId().equals(tokenBean.getTokenId())) {
                        showCurrentToken(tokenBean);
                        getPresenter().switchToken(tokenBean);
                        break;
                    }
                }

                List<OtcConfigResponse.CurrencyBean> currencyList = response.getCurrency();
                if (currencyList != null) {
                    for (OtcConfigResponse.CurrencyBean currencyBean : currencyList) {
                        if (adBeanModify.getCurrencyId().equals(currencyBean.getCurrencyId())){
                            showCurrency(currencyBean);
                            break;
                        }
                    }
                }
            }
        }
    }

    /**
     * 显示币种列表
     *
     * @param data
     */
    private void showListPop(List<OtcConfigResponse.TokenBean> data) {
        if (data != null) {

            OtcSymbolAdapter symbolAdapter = new OtcSymbolAdapter(data);
            symbolAdapter.isFirstOnly(false);
            recyclerViewList.setLayoutManager(new LinearLayoutManager(this));
            recyclerViewList.setItemAnimator(new DefaultItemAnimator());
            recyclerViewList.setAdapter(symbolAdapter);

            symbolAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                    listPop.dismiss();
                    List<OtcConfigResponse.TokenBean> data = adapter.getData();
                    currentTokenBean = data.get(position);
                    if (currentTokenBean != null) {
                        String tokenId = currentTokenBean.getTokenId();
                        if (!TextUtils.isEmpty(tokenId)) {
                            getPresenter().switchToken(currentTokenBean);
                            showCurrentToken(currentTokenBean);
                        }
                    }
                }
            });
            listPop.showAsDropDown(viewFinder.find(R.id.otc_ad_curreny_title));
        }
    }

    private void loadCurrentTokenConfig(OtcConfigResponse.TokenBean currentTokenBean) {
        if (currentConfig != null && currentTokenBean != null) {
            List<OtcConfigResponse.TokenBean> tokenList = currentConfig.getToken();
            if (currentTokenBean != null) {
                String tokenId = currentTokenBean.getTokenId();
                for (OtcConfigResponse.TokenBean tokenBean : tokenList) {
                    if (tokenId.equals(tokenBean.getTokenId())) {
                        currentToken = tokenBean;
                        numPointFilter.setDecimalLength(tokenBean.getScale());
                        break;
                    }

                }
            }
        }
    }

    /**
     * 显示法币列表
     *
     * @param data
     */
    private void showLegalCurrencyPop(List<OtcConfigResponse.CurrencyBean> data) {
        if (data != null) {

            OtcCurrencyAdapter currencyAdapter = new OtcCurrencyAdapter(data);
            currencyAdapter.isFirstOnly(false);
            recyclerViewLegalList.setLayoutManager(new LinearLayoutManager(this));
            recyclerViewLegalList.setItemAnimator(new DefaultItemAnimator());
            recyclerViewLegalList.setAdapter(currencyAdapter);

            currencyAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                    listLegalPop.dismiss();
                    List<OtcConfigResponse.CurrencyBean> data = adapter.getData();
                    currentLegalCurrency = data.get(position);
                    if (currentLegalCurrency != null) {
                        showCurrency(currentLegalCurrency);
                    }
                }
            });
            listLegalPop.showAsDropDown(viewFinder.find(R.id.otc_ad_legal_currency_title));
        }
    }

    @Override
    public void showIndexPrice(OtcLastPriceResponse response, OtcConfigResponse.TokenBean tokenBean, String currencyId) {
        if (response != null) {
            currentLastPrice = response.getLastPrice();
            viewFinder.textView(R.id.otc_latest_price).setText(getString(R.string.string_otc_latest_price_about_currency, response.getLastPrice(), currencyId));
            setFloatPriceAbout();
        }
    }

    @Override
    public void updateAssettByToken(OtcConfigResponse.TokenBean tokenBean, String free) {
        if (currentTokenBean != null) {
            if (!currentTokenBean.getTokenId().equals(tokenBean.getTokenId())) {
                //当前币种
                return;
            }
        }
        currentFreeAsset = free;
        viewFinder.textView(R.id.otc_available_balance).setText(getString(R.string.string_otc_available_balance, tokenBean.getTokenName(), io.bhex.app.utils.NumberUtils.roundFormatDown(free, AppData.Config.DIGIT_DEFAULT_VALUE)));
    }

    @Override
    public void showOtcUserInfo(OtcUserInfo otcUserInfo) {
        int tradeFlag = otcUserInfo.getTradeFlag();
        isTrade = (tradeFlag == 1);
        viewFinder.find(R.id.fundPwd).setVisibility(isTrade ? View.GONE : View.VISIBLE);
    }

    @Override
    public void setCurrentConfig(OtcConfigResponse response) {
        currentConfig = response;
    }

    @Override
    public boolean getIsBuyOrSell() {
        return mBuy;
    }
}
