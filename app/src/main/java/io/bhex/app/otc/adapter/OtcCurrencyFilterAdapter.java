/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcCurrencyFilterAdapter.java
 *   @Date: 19-4-16 下午2:43
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.adapter;

import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.sdk.otc.bean.OtcConfigResponse;

public class OtcCurrencyFilterAdapter extends BaseQuickAdapter<OtcConfigResponse.CurrencyBean, BaseViewHolder> {
    public OtcCurrencyFilterAdapter(List<OtcConfigResponse.CurrencyBean> data) {
        super(R.layout.item_otc_filter_item_list_layout, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, OtcConfigResponse.CurrencyBean item) {

        String name = item.getName();
        if (!TextUtils.isEmpty(name)) {
            helper.setText(R.id.item_name, name);
        }
        helper.addOnClickListener(R.id.item_name);
        helper.setChecked(R.id.item_name,item.isLocalSelect());
    }

}
