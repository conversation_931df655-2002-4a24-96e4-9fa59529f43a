/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcAppealPresenter.java
 *   @Date: 19-1-29 下午3:29
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.presenter;

import io.bhex.app.base.AppUI;
import io.bhex.app.otc.bean.AppealTypeBean;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.sdk.otc.OtcOrderApi;

public class OtcAppealPresenter extends BasePresenter<OtcAppealPresenter.OtcAppealUI> {
    public void orderAppeal(String orderId, AppealTypeBean currentAppealType, final String reason) {
        OtcOrderApi.orderAppeal(orderId,0,"买方没有打款",new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    getUI().appealSubmitSuccess();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public interface OtcAppealUI extends AppUI{

        void appealSubmitSuccess();
    }
}
