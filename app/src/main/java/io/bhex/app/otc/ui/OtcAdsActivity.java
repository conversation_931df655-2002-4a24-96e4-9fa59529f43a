/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcAdsActivity.java
 *   @Date: 19-2-28 上午11:52
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.ui;

import android.os.Bundle;
import android.view.View;

import androidx.core.util.Pair;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;

import java.util.ArrayList;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.market.adapter.FragmentAdapter;
import io.bhex.app.otc.presenter.OtcAdsPresenter;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.view.TopBar;

public class OtcAdsActivity extends BaseActivity<OtcAdsPresenter, OtcAdsPresenter.OtcAdsUI> implements OtcAdsPresenter.OtcAdsUI {
    private TopBar topBar;
    private TabLayout tabLayout;
    private ViewPager viewPager;
    private ArrayList<Pair<String, Fragment>> items;
    private FragmentAdapter entrustAdapter;

    @Override
    protected int getContentView() {
        return R.layout.activity_otc_ads_layout;
    }

    @Override
    protected OtcAdsPresenter createPresenter() {
        return new OtcAdsPresenter();
    }

    @Override
    protected OtcAdsPresenter.OtcAdsUI getUI() {
        return this;
    }

    @Override
    protected boolean isNeedLogin() {
        return true;
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        this.setTopbarNight(topBar);
        topBar.setRightOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IntentUtils.goOtcPublishAds(OtcAdsActivity.this);
            }
        });
        tabLayout = viewFinder.find(R.id.tabLayout);
        viewPager = viewFinder.find(R.id.viewPager);
        initTabs();
    }

    private void initTabs() {
        items = new ArrayList<>();

        OtcAdsOrderFragment otcAdsOrderHandingFragment = new OtcAdsOrderFragment();
        Bundle bundle = new Bundle();
        bundle.putInt("orderStatus",0);
        otcAdsOrderHandingFragment.setArguments(bundle);
        OtcAdsOrderFragment otcOrderHistoryFragment = new OtcAdsOrderFragment();
        Bundle bundleHistory = new Bundle();
        bundleHistory.putInt("orderStatus",1);
        otcOrderHistoryFragment.setArguments(bundleHistory);
        items.add(new Pair<String, Fragment>(getString(R.string.string_otc_order_processing), otcAdsOrderHandingFragment));
        items.add(new Pair<String, Fragment>(getString(R.string.string_otc_order_finished), otcOrderHistoryFragment));
        entrustAdapter = new FragmentAdapter(getSupportFragmentManager(),items);
        viewPager.setAdapter(entrustAdapter);
        tabLayout.setupWithViewPager(viewPager);
//        tab.setTabTextColors(getResources().getColor(R.color.color_white),getResources().getColor(R.color.color_black));
        tabLayout.setTabMode(TabLayout.MODE_SCROLLABLE);
        tabLayout.setTabGravity(TabLayout.GRAVITY_CENTER);

//        viewPager.addOnPageChangeListener(this);
        CommonUtil.setUpIndicatorWidthByReflex(tabLayout,15,15);
    }
}
