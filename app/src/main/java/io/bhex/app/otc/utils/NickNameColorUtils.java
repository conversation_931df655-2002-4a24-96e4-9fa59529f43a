/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: NickNameColorUtils.java
 *   @Date: 19-1-14 下午4:40
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.utils;

import android.graphics.Color;

public class NickNameColorUtils {
    /**
     * 昵称颜色值
     * @param key
     * @return
     */
    public static int getNickNameBgColor(int key) {
        String[] colorArray = new String[]{"#54E19E",
                "#66A3FF",
                "#38a1e6",
                "#E2C97F",
                "#7887C5",
                "#68B38F",
                "#8B58DF",
                "#66D0D7",
                "#BEC65D",
                "#F4934D"};
        if (key+1>colorArray.length){
            return Color.parseColor("#66A3FF");
        }else{
            return Color.parseColor(colorArray[key]);
        }
    }
}
