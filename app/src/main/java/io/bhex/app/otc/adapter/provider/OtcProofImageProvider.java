/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcProofImageProvider.java
 *   @Date: 19-1-31 下午3:20
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.adapter.provider;

import android.content.Context;
import android.graphics.drawable.GradientDrawable;
import android.text.TextUtils;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.provider.BaseItemProvider;

import java.util.ArrayList;
import java.util.List;

import cc.shinichi.library.ImagePreview;
import cc.shinichi.library.bean.ImageInfo;
import io.bhex.app.R;
import io.bhex.app.otc.listener.OtcMsgItemClickListener;
import io.bhex.app.otc.utils.NickNameColorUtils;
import io.bhex.app.utils.DateUtils;
import io.bhex.sdk.UrlsConfig;
import io.bhex.sdk.otc.OtcConstant;
import io.bhex.sdk.otc.bean.OtcMessageResponse;
import io.bhex.sdk.otc.bean.OtcOrderInfoResponse;

public class OtcProofImageProvider extends BaseItemProvider<OtcMessageResponse.MessageBean,BaseViewHolder> {
    private OtcOrderInfoResponse mOrderInfo;
    private OtcMsgItemClickListener mItemClickListener;
    private String targetAccountId;

    public OtcProofImageProvider(OtcOrderInfoResponse orderInfo, OtcMsgItemClickListener clickListener) {
        mOrderInfo = orderInfo;
        mItemClickListener = clickListener;

        targetAccountId = orderInfo.getTargetAccountId();

    }

    @Override
    public int viewType() {
        return OtcConstant.OTC_PROOF_MESSAGE_TYPE_IMAGE;
    }

    @Override
    public int layout() {
        return R.layout.item_otc_msg_image_layout;
    }

    @Override
    public void convert(BaseViewHolder helper, OtcMessageResponse.MessageBean data, int position) {
       TextView avator = helper.getView(R.id.avator);
        String accountId = data.getAccountId();
        if (!TextUtils.isEmpty(accountId)) {
            int key = (int) (Long.valueOf(accountId)%10);
//            helper.setBackgroundColor(R.id.avator,NickNameColorUtils.getNickNameBgColor(key));
            TextView view = helper.getView(R.id.avator);
            GradientDrawable myGrad = (GradientDrawable)view.getBackground();
            myGrad.setColor(NickNameColorUtils.getNickNameBgColor(key));
        }

        String nickName;
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) avator.getLayoutParams();
        if (accountId.equals(targetAccountId)) {
            layoutParams.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
            layoutParams.endToEnd = ConstraintLayout.LayoutParams.UNSET;
            nickName = mOrderInfo.getTargetNickName();
        }else{
            layoutParams.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
            layoutParams.startToStart = ConstraintLayout.LayoutParams.UNSET;
            nickName = mOrderInfo.getNickName();
        }
        avator.setLayoutParams(layoutParams);
        if (!TextUtils.isEmpty(nickName)) {
            String firstLetter = nickName.substring(0, 1).toUpperCase();
            helper.setText(R.id.avator, firstLetter);
        }

        ImageView imageView = helper.getView(R.id.image);
//        ConstraintLayout.LayoutParams imageViewLayoutParams = (ConstraintLayout.LayoutParams) imageView.getLayoutParams();
//        if (accountId.equals(targetAccountId)) {
//            imageViewLayoutParams.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
//            imageViewLayoutParams.endToEnd = ConstraintLayout.LayoutParams.UNSET;
//        }else{
//            imageViewLayoutParams.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
//            imageViewLayoutParams.startToStart = ConstraintLayout.LayoutParams.UNSET;
//        }
//        imageView.setLayoutParams(imageViewLayoutParams);

        String url = data.getMessage();
        if (!TextUtils.isEmpty(url)) {
            if (url.startsWith("/")) {
                url = url.replaceFirst("/", "");
            }
        }
        url = UrlsConfig.API_OTC_URL+url;
        Glide.with(mContext)
                .load(url)
                .diskCacheStrategy(DiskCacheStrategy.ALL)
//                .skipMemoryCache(true)
                .into(imageView);
        helper.setText(R.id.positionValue,DateUtils.getSimpleTimeFormat(data.getCreateDate()));
    }

    @Override
    public void onClick(BaseViewHolder helper, OtcMessageResponse.MessageBean data, int position) {
        super.onClick(helper, data, position);
        List<String> urls = new ArrayList<>();
        String url = data.getMessage();
        if (!TextUtils.isEmpty(url)) {
            if (url.startsWith("/")) {
                url = url.replaceFirst("/", "");
            }
        }
        url = UrlsConfig.API_OTC_URL+url;
        urls.add(url);
        showPicBrowser(mContext,0,urls);
    }

    /**
     *预览图片
     */
    private void showPicBrowser(Context context, int index, List<String> imgUrls) {

        if(imgUrls == null || index >= imgUrls.size())
            return;

        ArrayList<ImageInfo> imageInfoList = new ArrayList<>();
        ImageInfo imageInfo;
        for (String imgUrl : imgUrls) {
            imageInfo = new ImageInfo();
            // 原图地址（必填）
            imageInfo.setOriginUrl(imgUrl);
            // 缩略图地址（必填）
            // 如果没有缩略图url，可以将两项设置为一样。（注意：此处作为演示用，加了-1200，你们不要这么做）
//            imageInfo.setThumbnailUrl(image.concat("-1200"));
            imageInfo.setThumbnailUrl(imgUrl);
            imageInfoList.add(imageInfo);
            imageInfo = null;
        }

        ImagePreview.getInstance()
                .setContext(context)
                .setIndex(index)
                .setImageInfoList(imageInfoList)
                .setShowDownButton(true)
                //.setLoadStrategy(ImagePreview.LoadStrategy.NetworkAuto)
                .setFolderName("BhexImage")
                .setScaleLevel(1, 3, 5)
                .setZoomTransitionDuration(300)

                .setEnableClickClose(true)// 是否启用点击图片关闭。默认启用
                .setEnableDragClose(true)// 是否启用上拉/下拉关闭。默认不启用

                .setShowCloseButton(true)// 是否显示关闭页面按钮，在页面左下角。默认不显示
                .setCloseIconResId(R.drawable.ic_action_close)// 设置关闭按钮图片资源，可不填，默认为：R.drawable.ic_action_close

                .setShowDownButton(true)// 是否显示下载按钮，在页面右下角。默认显示
                .setDownIconResId(R.drawable.icon_download_new)// 设置下载按钮图片资源，可不填，默认为：R.drawable.icon_download_new

                .setShowIndicator(true)// 设置是否显示顶部的指示器（1/9）。默认显示
                .start();
    }
}
