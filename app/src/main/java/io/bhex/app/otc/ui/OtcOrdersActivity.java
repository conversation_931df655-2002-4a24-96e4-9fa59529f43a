/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcOrdersActivity.java
 *   @Date: 19-1-25 上午11:01
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.ui;

import android.os.Bundle;
import android.view.View;

import androidx.core.util.Pair;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;

import java.util.ArrayList;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.market.adapter.FragmentAdapter;
import io.bhex.app.otc.presenter.OtcOrdersPresenter;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.view.TopBar;

public class OtcOrdersActivity extends BaseActivity<OtcOrdersPresenter,OtcOrdersPresenter.OtcOrdersUI> implements OtcOrdersPresenter.OtcOrdersUI {
    private TopBar topBar;
    private TabLayout tabLayout;
    private ViewPager viewPager;
    private ArrayList<Pair<String, Fragment>> items;
    private FragmentAdapter entrustAdapter;

    @Override
    protected int getContentView() {
        return R.layout.activity_otc_orders_layout;
    }

    @Override
    protected OtcOrdersPresenter createPresenter() {
        return new OtcOrdersPresenter();
    }

    @Override
    protected OtcOrdersPresenter.OtcOrdersUI getUI() {
        return this;
    }

    @Override
    protected boolean isNeedLogin() {
        return true;
    }

    @Override
    protected void initView() {
        super.initView();
        topBar = viewFinder.find(R.id.topBar);
        this.setTopbarNight(topBar);
        topBar.setRightOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
        tabLayout = viewFinder.find(R.id.tabLayout);
        viewPager = viewFinder.find(R.id.viewPager);
        initTabs();
    }

    private void initTabs() {
        items = new ArrayList<>();

        OtcOrderFragment otcOrderHandingFragment = new OtcOrderFragment();
        Bundle bundle = new Bundle();
        bundle.putString("orderStatus","0");
        otcOrderHandingFragment.setArguments(bundle);
        OtcOrderFragment otcOrderHistoryFragment = new OtcOrderFragment();
        Bundle bundleHistory = new Bundle();
        bundleHistory.putString("orderStatus","1");
        otcOrderHistoryFragment.setArguments(bundleHistory);
        items.add(new Pair<String, Fragment>(getString(R.string.string_otc_order_processing), otcOrderHandingFragment));
        items.add(new Pair<String, Fragment>(getString(R.string.string_otc_order_finished), otcOrderHistoryFragment));
        entrustAdapter = new FragmentAdapter(getSupportFragmentManager(),items);
        viewPager.setAdapter(entrustAdapter);
        tabLayout.setupWithViewPager(viewPager);
//        tab.setTabTextColors(getResources().getColor(R.color.color_white),getResources().getColor(R.color.color_black));
        tabLayout.setTabMode(TabLayout.MODE_SCROLLABLE);
        tabLayout.setTabGravity(TabLayout.GRAVITY_CENTER);

//        viewPager.addOnPageChangeListener(this);
        CommonUtil.setUpIndicatorWidthByReflex(tabLayout,15,15);
    }
}
