/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcSwitchBAdapter.java
 *   @Date: 19-1-16 上午10:34
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.adapter;

import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.sdk.otc.bean.OtcConfigResponse;

public class OtcSwitchBAdapter extends BaseQuickAdapter<OtcConfigResponse.TokenBean, BaseViewHolder> {
    public OtcSwitchBAdapter(List<OtcConfigResponse.TokenBean> data) {
        super(R.layout.item_otc_switch_b_list_layout, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, OtcConfigResponse.TokenBean item) {

        String tokenName = item.getTokenName();
        if (!TextUtils.isEmpty(tokenName)) {
            helper.setText(R.id.item_otc_b_name, tokenName);
        }
        helper.addOnClickListener(R.id.item_otc_b_name);
    }

}
