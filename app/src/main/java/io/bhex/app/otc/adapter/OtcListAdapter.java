/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcListAdapter.java
 *   @Date: 19-1-14 下午4:31
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.adapter;

import android.graphics.drawable.GradientDrawable;
import android.text.TextUtils;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.otc.utils.NickNameColorUtils;
import io.bhex.sdk.otc.bean.OtcListResponse;

public class OtcListAdapter extends BaseQuickAdapter<OtcListResponse.OtcItemBean,BaseViewHolder> {
    public OtcListAdapter(List<OtcListResponse.OtcItemBean> data) {
        super(R.layout.item_otc_list_layout, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, OtcListResponse.OtcItemBean item) {

        String nickName = item.getNickName();
        if (!TextUtils.isEmpty(nickName)) {
            String firstLetter = nickName.substring(0, 1).toUpperCase();
            helper.setText(R.id.avator, firstLetter);
        }
        String accountId = item.getAccountId();
        if (!TextUtils.isEmpty(accountId)) {
            int key = (int) (Long.valueOf(accountId)%10);
//            helper.setBackgroundColor(R.id.avator,NickNameColorUtils.getNickNameBgColor(key));
            TextView view = helper.getView(R.id.avator);
            GradientDrawable myGrad = (GradientDrawable)view.getBackground();
            myGrad.setColor(NickNameColorUtils.getNickNameBgColor(key));
        }
        helper.setText(R.id.name, item.getNickName());
        helper.setText(R.id.turnoverRate, item.getRecentOrderNum()+" / "+item.getRecentExecuteRate()+"%");
        helper.setText(R.id.averagePrice, item.getPrice()+" "+item.getCurrencyId());
        helper.setText(R.id.btn_create_otc_order, item.getSide()==1?mContext.getResources().getString(R.string.string_otc_buy):mContext.getResources().getString(R.string.string_otc_sell));
        helper.addOnClickListener(R.id.itemView);
        helper.addOnClickListener(R.id.btn_create_otc_order);

        helper.setText(R.id.surplus_quantity,item.getLastQuantity()+" "+item.getTokenName());
        helper.setText(R.id.limit_price,item.getMinAmount()+" - "+item.getMaxAmount()+" "+item.getCurrencyId());
        List<Integer> payments = item.getPayments();
        if (payments != null) {
            helper.setGone(R.id.support_pay_unionpay,payments.contains(0));
            helper.setGone(R.id.support_pay_alipay,payments.contains(1));
            helper.setGone(R.id.support_pay_wechat,payments.contains(2));
//            for (Integer payWay : payments) {
//                if (payWay==0){
//                    helper.setVisible(R.id.support_pay_unionpay,true);
//                }else if(payWay==1){
//                    helper.setVisible(R.id.support_pay_alipay,true);
//                }else if(payWay==2){
//                    helper.setVisible(R.id.support_pay_wechat,true);
//                }
//            }
        }


    }

}
