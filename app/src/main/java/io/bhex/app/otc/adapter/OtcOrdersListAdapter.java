/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcOrdersListAdapter.java
 *   @Date: 19-1-25 下午3:39
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.adapter;

import android.content.Context;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.DateUtils;
import io.bhex.app.utils.TextColorUtils;
import io.bhex.sdk.otc.bean.OtcOrderInfoResponse;

public class OtcOrdersListAdapter extends BaseQuickAdapter<OtcOrderInfoResponse, BaseViewHolder> {
    private CountDownTimer countDownTimer;

    public OtcOrdersListAdapter(List<OtcOrderInfoResponse> data) {
        super(R.layout.item_otc_order_list_layout, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, OtcOrderInfoResponse item) {

        helper.setText(R.id.order_type, item.getSide() == 0 ? mContext.getResources().getString(R.string.string_otc_buy) : mContext.getResources().getString(R.string.string_otc_sell));
        helper.setText(R.id.targetTraderTitle, item.getSide() == 0 ? mContext.getResources().getString(R.string.string_otc_seller) : mContext.getResources().getString(R.string.string_otc_buyer));
        helper.setText(R.id.order_quantity, item.getQuantity()+" "+item.getTokenName());
        helper.setText(R.id.order_time, DateUtils.getSimpleTimeFormat(item.getCreateDate()));
        helper.setText(R.id.unitPrice, item.getPrice()+" "+item.getCurrencyId() +" / "+item.getTokenName());
        helper.setText(R.id.totalAmount, item.getAmount()+" "+item.getCurrencyId());
        helper.setText(R.id.targetTrader, item.getTargetNickName());
        ImageView orderStatusImg = helper.getView(R.id.order_status_img);
        TextView orderStatus = helper.getView(R.id.order_status);
        TextView orderResult = helper.getView(R.id.order_result);
        boolean isBuy = item.getSide() ==0;
        //10待支付，20已支付待确认，30申诉中，40撤销，50完全成交
        int status = item.getStatus();
        if (isBuy) {
            if (status==10) {
                orderResult.setVisibility(View.VISIBLE);
                String transferLastSeconds = item.getTransferLastSeconds();
                orderStatus.setText("");
                orderStatusImg.setImageResource(R.mipmap.icon_timer);
//                startCountDownTimer(mContext,orderResult,isBuy,transferLastSeconds);
                TextColorUtils.setTextViewColor(orderResult,mContext.getString(R.string.string_otc_fortmat_finish_order_counter),DateUtils.getRemainingTime(mContext,Long.valueOf(transferLastSeconds)*1000),R.color.orange);
            }else if(status==20){
                orderResult.setVisibility(View.VISIBLE);
                orderStatus.setText("");
                orderStatusImg.setImageResource(R.mipmap.icon_timer);
                orderResult.setText(mContext.getString(R.string.string_wait_give_b));
            }else if(status==30){
                orderResult.setVisibility(View.VISIBLE);
                orderStatus.setText(mContext.getString(R.string.string_appealing));
                orderStatusImg.setImageResource(R.mipmap.icon_timer);
                orderResult.setText(mContext.getString(R.string.string_otc_format_wait_customer_service_handle));
            }else if(status==40){
                orderResult.setVisibility(View.GONE);
                orderStatus.setText(mContext.getString(R.string.string_otc_canceled));
                orderStatusImg.setImageResource(R.mipmap.icon_cancelled);
                orderResult.setText("");
            }else if(status==50){
                orderResult.setVisibility(View.GONE);
                orderStatus.setText(mContext.getString(R.string.string_otc_finished));
                orderStatusImg.setImageResource(R.mipmap.icon_otc_finished);
                orderResult.setText("");
            }

        }else{
            if (status==10) {
                orderStatus.setVisibility(View.VISIBLE);
                orderResult.setVisibility(View.VISIBLE);
                String transferLastSeconds = item.getTransferLastSeconds();
                orderStatus.setText("");
                orderStatusImg.setImageResource(R.mipmap.icon_timer);
//                startCountDownTimer(mContext,orderResult,isBuy,transferLastSeconds);
                TextColorUtils.setTextViewColor(orderResult,mContext.getString(R.string.string_otc_fortmat_finish_order_seller_counter),DateUtils.getRemainingTime(mContext,Long.valueOf(transferLastSeconds)*1000),R.color.orange);
            }else if(status==20){
                orderStatus.setVisibility(View.VISIBLE);
                orderResult.setVisibility(View.VISIBLE);
                orderStatus.setText("");
                orderStatusImg.setImageResource(R.mipmap.icon_timer);
                orderResult.setText(mContext.getString(R.string.string_seller_confirm_give_b));
            }else if(status==30){
                orderStatus.setVisibility(View.VISIBLE);
                orderResult.setVisibility(View.VISIBLE);
                orderStatus.setText(mContext.getString(R.string.string_appealing));
                orderStatusImg.setImageResource(R.mipmap.icon_timer);
                orderResult.setText(mContext.getString(R.string.string_otc_format_wait_customer_service_handle));
            }else if(status==40){
                orderResult.setVisibility(View.GONE);
                orderStatus.setText(mContext.getString(R.string.string_otc_canceled));
                orderStatusImg.setImageResource(R.mipmap.icon_cancelled);
                orderResult.setText("");
            }else if(status==50){
                orderResult.setVisibility(View.GONE);
                orderStatus.setText(mContext.getString(R.string.string_otc_finished));
                orderStatusImg.setImageResource(R.mipmap.icon_otc_finished);
                orderResult.setText("");
            }
        }

        helper.addOnClickListener(R.id.itemView);

    }

    /**
     *  确认付款倒计时
     * @param transferLastSeconds
     */
//    private void startCountDownTimer(final Context context, final TextView tv, final boolean isBuy, String transferLastSeconds) {
//        if (TextUtils.isEmpty(transferLastSeconds)) {
//            return;
//        }
//        Long remainingTime = Long.valueOf(transferLastSeconds)*1000;
//        if (remainingTime>0) {
//            countDownTimer = new CountDownTimer(remainingTime,1000){
//
//                @Override
//                public void onTick(long millisUntilFinished) {
//                    TextColorUtils.setTextViewColor(tv,context.getResources().getString(isBuy?R.string.string_otc_fortmat_finish_order_counter:R.string.string_otc_fortmat_finish_order_seller_counter),DateUtils.getRemainingTime(context,millisUntilFinished),R.color.orange);
//                }
//
//                @Override
//                public void onFinish() {
//                    tv.setText(context.getResources().getString(R.string.string_otc_trade_canceled));
//                }
//
//            }.start();
//        }else{
//            //TODO
//        }
//    }

}
