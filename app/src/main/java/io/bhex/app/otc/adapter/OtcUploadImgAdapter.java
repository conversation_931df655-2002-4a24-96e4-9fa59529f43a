/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcUploadImgAdapter.java
 *   @Date: 19-1-31 下午2:14
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.adapter;

import android.text.TextUtils;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.otc.bean.AppealTypeBean;
import io.bhex.sdk.UrlsConfig;
import io.bhex.sdk.otc.bean.OtcUploadImgResponse;

public class OtcUploadImgAdapter extends BaseQuickAdapter<String, BaseViewHolder> {
    public OtcUploadImgAdapter(List<String> data) {
        super(R.layout.item_otc_upload_proof_layout, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, String url) {

//        String url = item.getUrl();
        ImageView imageView = helper.getView(R.id.itemImg);

        if (!TextUtils.isEmpty(url)) {
            if (url.equals("+")) {
                imageView.setImageResource(R.mipmap.icon_add_proof);
            }else{
                if (url.startsWith("/")) {
                    url = url.replaceFirst("/", "");
                }
                url = UrlsConfig.API_OTC_URL + url;
                Glide.with(mContext)
                        .load(url)
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .skipMemoryCache(true)
                        .into(imageView);
            }
        }

        helper.addOnClickListener(R.id.itemImg);
    }

}
