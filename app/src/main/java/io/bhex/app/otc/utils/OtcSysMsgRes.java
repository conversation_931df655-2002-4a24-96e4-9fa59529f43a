/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcSysMsgRes.java
 *   @Date: 19-1-30 下午8:47
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.utils;

import android.content.Context;

import java.util.HashMap;

import io.bhex.app.R;
import io.bhex.sdk.data_manager.AppConfigManager;

public class OtcSysMsgRes {

    public static HashMap<Integer, String> initMsgRes(Context context) {
        HashMap<Integer, String> sysMessageRes = new HashMap<Integer, String>();
        int otcOrderPayOverTime = AppConfigManager.GetInstance().getOtcOrderPayOverTime();
        sysMessageRes.put(1010,context.getResources().getString(R.string.string_otc_sys_msg_1010,otcOrderPayOverTime));
        sysMessageRes.put(2010,context.getResources().getString(R.string.string_otc_sys_msg_2010,otcOrderPayOverTime));
        sysMessageRes.put(1020,context.getResources().getString(R.string.string_otc_sys_msg_1020));
        sysMessageRes.put(1050,context.getResources().getString(R.string.string_otc_sys_msg_1050));
        sysMessageRes.put(1011,context.getResources().getString(R.string.string_otc_sys_msg_1011));

        sysMessageRes.put(2011,context.getResources().getString(R.string.string_otc_sys_msg_2011));
        sysMessageRes.put(1021,context.getResources().getString(R.string.string_otc_sys_msg_1021));
        sysMessageRes.put(1051,context.getResources().getString(R.string.string_otc_sys_msg_1051));
        sysMessageRes.put(1040,context.getResources().getString(R.string.string_otc_sys_msg_1040));
        sysMessageRes.put(1041,context.getResources().getString(R.string.string_otc_sys_msg_1041));

        sysMessageRes.put(3040, context.getResources().getString(R.string.string_otc_sys_msg_3040));
        sysMessageRes.put(1031, context.getResources().getString(R.string.string_otc_sys_msg_1031));
        sysMessageRes.put(1030, context.getResources().getString(R.string.string_otc_sys_msg_1030));
        sysMessageRes.put(2030, context.getResources().getString(R.string.string_otc_sys_msg_2030));
        sysMessageRes.put(2031, context.getResources().getString(R.string.string_otc_sys_msg_2031));
        sysMessageRes.put(3030, context.getResources().getString(R.string.string_otc_sys_msg_3030));
        sysMessageRes.put(3031, context.getResources().getString(R.string.string_otc_sys_msg_3031));
        return sysMessageRes;
    }
}
