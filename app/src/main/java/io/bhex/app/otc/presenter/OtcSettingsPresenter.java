/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcSettingsPresenter.java
 *   @Date: 19-1-31 下午4:56
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.presenter;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.LoginApi;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.otc.OtcPayChannelApi;
import io.bhex.sdk.otc.OtcUserApi;
import io.bhex.sdk.otc.bean.OtcPayChannelListResponse;
import io.bhex.sdk.otc.bean.OtcPaymentChannelBean;
import io.bhex.sdk.otc.bean.OtcUserInfo;

public class OtcSettingsPresenter extends BasePresenter<OtcSettingsPresenter.OtcSettingsUI> {
    private boolean isSetPayChannel=false;

    public interface OtcSettingsUI extends AppUI {

        void showPayChannelList(OtcPayChannelListResponse payChannelList);

        void showOtcUserInfo(OtcUserInfo response);

        void getUserInfoSuccess(UserInfoBean data);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, OtcSettingsUI ui) {
        super.onUIReady(activity, ui);

    }

    @Override
    public void onResume() {
        super.onResume();
        getOtcUserInfo();
        getPaymentMethodList();
        getUserInfo();
    }

    /**
     * 获取用户信息
     */
    public void getUserInfo(){
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(),getResources().getString(R.string.hint_network_not_connect));
            return;
        }
        LoginApi.GetUserInfo(new SimpleResponseListener<UserInfoBean>(){
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(UserInfoBean data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data, true)) {
                    //保存用户数据
                    UserManager.getInstance().saveUserInfo(data);
                    getUI().getUserInfoSuccess(data);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    /**
     * 获取支付方式列表
     */
    public void getPaymentMethodList() {
        if (!UserInfo.isLogin()) {
            return;
        }
        OtcPayChannelApi.getPayChannelList(new SimpleResponseListener<OtcPayChannelListResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(OtcPayChannelListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    getUI().showPayChannelList(response);
                    List<OtcPaymentChannelBean> payChannelList = response.getArray();
                    if (payChannelList != null) {
                        if (payChannelList.size()>0) {
                            //根据用户支付通道 大于 > 0
                            isSetPayChannel = true;
                        }
                    }

                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    /**
     * 获取Otc用户信息
     */
    private void getOtcUserInfo() {
        if (!UserInfo.isLogin()) {
            return;
        }
        OtcUserApi.getOtcUserInfo(new SimpleResponseListener<OtcUserInfo>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(OtcUserInfo response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    getUI().showOtcUserInfo(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }
}
