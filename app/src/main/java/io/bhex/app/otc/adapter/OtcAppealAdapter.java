/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcAppealAdapter.java
 *   @Date: 19-1-29 下午5:04
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.adapter;

import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.otc.bean.AppealTypeBean;

public class OtcAppealAdapter extends BaseQuickAdapter<AppealTypeBean, BaseViewHolder> {
    public OtcAppealAdapter(List<AppealTypeBean> data) {
        super(R.layout.item_otc_appeal_type_list_layout, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, AppealTypeBean item) {

        String theme = item.getAppealTheme();
        if (!TextUtils.isEmpty(theme)) {
            helper.setText(R.id.item_name, theme);
        }
        helper.addOnClickListener(R.id.item_name);
    }

}
