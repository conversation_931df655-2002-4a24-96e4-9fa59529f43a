/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcAppealActivity.java
 *   @Date: 19-1-29 下午3:30
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.ui;

import android.content.Intent;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.otc.adapter.OtcAppealAdapter;
import io.bhex.app.otc.bean.AppealTypeBean;
import io.bhex.app.otc.presenter.OtcAppealPresenter;
import io.bhex.app.view.PopWindow;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;

/**
 * Otc订单申诉
 */
public class OtcAppealActivity extends BaseActivity<OtcAppealPresenter,OtcAppealPresenter.OtcAppealUI> implements OtcAppealPresenter.OtcAppealUI, View.OnClickListener {

    private EditText appealTypeEt;
    private EditText appealReasonEt;
    private View appealTypeLayout;
    private PopWindow popWindowAppealType;
    private RecyclerView recyclerViewType;
    private List<AppealTypeBean> appealData;
    private AppealTypeBean currentAppealType;
    private String orderId="";

    @Override
    protected int getContentView() {
        return R.layout.activity_otc_appeal_layout;
    }

    @Override
    protected OtcAppealPresenter createPresenter() {
        return new OtcAppealPresenter();
    }

    @Override
    protected OtcAppealPresenter.OtcAppealUI getUI() {
        return this;
    }

    @Override
    protected boolean isNeedLogin() {
        return true;
    }

    @Override
    protected void initView() {
        super.initView();
        Intent intent = getIntent();
        if (intent != null) {
            orderId = intent.getStringExtra("orderId");
        }
        LayoutInflater layoutInflater = LayoutInflater.from(this);
        appealTypeLayout = layoutInflater.inflate(R.layout.otc_appeal_type_layout, null, false);
        recyclerViewType = appealTypeLayout.findViewById(R.id.recyclerView);

        popWindowAppealType = new PopWindow(this, appealTypeLayout,PixelUtils.getScreenWidth()-2*PixelUtils.dp2px(24),PixelUtils.dp2px(200));
        appealTypeEt = viewFinder.editText(R.id.appealType);
        appealReasonEt = viewFinder.editText(R.id.appealReason);

        initAppealTypeData();

    }

    private void initAppealTypeData() {
        String[] appealDataStr = new String[]{getString(R.string.string_otc_appeal_buyer_not_pay),getString(R.string.string_otc_appeal_seller_not_give_b),getString(R.string.string_otc_appeal_shit),getString(R.string.string_otc_appeal_target_not_reply),getString(R.string.string_otc_appeal_other)};
       appealData = new ArrayList<>();
        for (int i = 0; i < appealDataStr.length; i++) {
            AppealTypeBean appealTypeBean = new AppealTypeBean();
            appealTypeBean.setAppealType(i);
            appealTypeBean.setAppealTheme(appealDataStr[i]);
            appealData.add(appealTypeBean);
        }

    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.appealType).setOnClickListener(this);
        viewFinder.find(R.id.btnSubmit).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch(v.getId()){
            case R.id.appealType:
                showAppealPoplist(appealData);
                break;
            case R.id.btnSubmit:
                String reason = appealReasonEt.getText().toString();
                if (TextUtils.isEmpty(reason)) {
                    ToastUtils.showShort(getString(R.string.string_input_appeal_reason));
                    return;
                }
                if (currentAppealType==null) {
                    ToastUtils.showShort(getString(R.string.string_select_appeal_type_please));
                    return;
                }
                getPresenter().orderAppeal(orderId,currentAppealType,reason);
                break;
        }
    }

    /**
     * 显示申诉类型列表
     * @param appealData
     */
    private void showAppealPoplist(List<AppealTypeBean> appealData) {
        if (appealData != null) {

            OtcAppealAdapter otcAppealAdapter = new OtcAppealAdapter(appealData);
            otcAppealAdapter.isFirstOnly(false);
//            otcSwitchBAdapterBuy.setEmptyView(emptyView);
            recyclerViewType.setLayoutManager(new LinearLayoutManager(this));
            recyclerViewType.setItemAnimator(new DefaultItemAnimator());
            recyclerViewType.setAdapter(otcAppealAdapter);

            otcAppealAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                    popWindowAppealType.dismiss();
                    List<AppealTypeBean> data = adapter.getData();
                    currentAppealType = data.get(position);
                    appealTypeEt.setText(currentAppealType.getAppealTheme());
                }
            });
            popWindowAppealType.showAsDropDown(appealTypeEt);
        }
    }

    @Override
    public void appealSubmitSuccess() {
        ToastUtils.showLong(this,getString(R.string.string_otc_appeal_submited));
        finish();
    }
}
