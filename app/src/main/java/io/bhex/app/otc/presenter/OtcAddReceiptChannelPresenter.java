/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcAddReceiptChannelPresenter.java
 *   @Date: 19-2-1 下午1:40
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.presenter;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.otc.OtcApi;
import io.bhex.sdk.otc.OtcMessageApi;
import io.bhex.sdk.otc.OtcPayChannelApi;
import io.bhex.sdk.otc.OtcUserApi;
import io.bhex.sdk.otc.bean.OtcConfigResponse;
import io.bhex.sdk.otc.bean.OtcPaymentChannelBean;
import io.bhex.sdk.otc.bean.OtcUploadImgResponse;
import io.bhex.sdk.otc.bean.OtcUserInfo;

public class OtcAddReceiptChannelPresenter extends BasePresenter<OtcAddReceiptChannelPresenter.OtcAddReceiptChannelUI> {
    public void setReceiptChannel(boolean isUpdateReceipt, OtcPaymentChannelBean updateReceiptBean, String realName, int payType, String bankName,String bankBranch, String accountNo, String qRCodeUrl, String passwd) {
        OtcPayChannelApi.setReceiptChannel(isUpdateReceipt,updateReceiptBean,realName,payType,bankName,bankBranch,accountNo,qRCodeUrl,passwd,new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    if (response.isSuccess()) {
                        ToastUtils.showShort(getString(R.string.string_otc_submit_success));
                        getActivity().finish();
                    }else{
                        ToastUtils.showShort(getString(R.string.string_otc_submit_failed));
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public interface OtcAddReceiptChannelUI extends AppUI{
        void showBankList(List<OtcConfigResponse.BankBean> bankList);

        void showOtcUserInfo(OtcUserInfo response);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, OtcAddReceiptChannelUI ui) {
        super.onUIReady(activity, ui);
        getOtcUserInfo();
        getOtcConfig();
    }

    /**
     * 获取Otc用户信息
     */
    private void getOtcUserInfo() {
        if (!UserInfo.isLogin()) {
            return;
        }
        OtcUserApi.getOtcUserInfo(new SimpleResponseListener<OtcUserInfo>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(OtcUserInfo response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    getUI().showOtcUserInfo(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public void getOtcConfig() {
        OtcApi.getConfig(new SimpleResponseListener<OtcConfigResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(OtcConfigResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    List<OtcConfigResponse.BankBean> bankList = response.getBank();
                    if (bankList != null) {
                        getUI().showBankList(bankList);

                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    /**
     * 上传图片
     * @param path
     * @param simpleResponseListener
     */
    public void uploadImage(String path, SimpleResponseListener<OtcUploadImgResponse> simpleResponseListener) {
        if (!UserInfo.isLogin()) {
            return;
        }
        OtcMessageApi.uploadImage(path,simpleResponseListener);
    }

}
