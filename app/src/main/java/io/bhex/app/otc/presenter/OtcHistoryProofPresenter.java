/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcHistoryProofPresenter.java
 *   @Date: 19-1-31 下午2:42
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.presenter;

import android.content.Intent;
import android.text.TextUtils;

import java.util.Collections;
import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.sdk.otc.OtcMessageApi;
import io.bhex.sdk.otc.bean.OtcMessageResponse;
import io.bhex.sdk.otc.bean.OtcOrderInfoResponse;

public class OtcHistoryProofPresenter extends BasePresenter<OtcHistoryProofPresenter.OtcHistoryProofUI> {
    private OtcOrderInfoResponse orderInfo;

    public interface OtcHistoryProofUI extends AppUI{

        void showMessageList(OtcOrderInfoResponse orderInfo, List<OtcMessageResponse.MessageBean> data);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, OtcHistoryProofUI ui) {
        super.onUIReady(activity, ui);
        Intent intent = getActivity().getIntent();
        if (intent != null) {
            orderInfo = (OtcOrderInfoResponse) intent.getSerializableExtra("order");

        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (orderInfo != null) {
            getHistoryProof(orderInfo.getId());
        }
    }

    public void getHistoryProof(String orderId) {
        /**
         * 获取消息列表
         */
            if (TextUtils.isEmpty(orderId)) {
                return;
            }
            String startId ="";
//        if (!currentMsgList.isEmpty()) {
//            startId = currentMsgList.get(0).getId();
//        }
            OtcMessageApi.appealList(orderId,startId,1000,new SimpleResponseListener<OtcMessageResponse>(){
                @Override
                public void onBefore() {
                    super.onBefore();
                }

                @Override
                public void onFinish() {
                    super.onFinish();
                }

                @Override
                public void onSuccess(OtcMessageResponse response) {
                    super.onSuccess(response);
                    if (CodeUtils.isSuccess(response,true)) {
                        List<OtcMessageResponse.MessageBean> data = response.getArray();
                        if (data != null) {
//                            Collections.reverse(data);
                            getUI().showMessageList(orderInfo,data);
                        }

                    }
                }

                @Override
                public void onError(Throwable error) {
                    super.onError(error);
                }
            });

    }
}
