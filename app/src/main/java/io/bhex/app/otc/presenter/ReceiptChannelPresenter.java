/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: ReceiptChannelPresenter.java
 *   @Date: 19-2-1 上午10:08
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.presenter;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.otc.OtcApi;
import io.bhex.sdk.otc.OtcPayChannelApi;
import io.bhex.sdk.otc.bean.OtcConfigResponse;
import io.bhex.sdk.otc.bean.OtcPayChannelListResponse;
import io.bhex.sdk.otc.bean.OtcPaymentChannelBean;

public class ReceiptChannelPresenter extends BasePresenter<ReceiptChannelPresenter.ReceiptChannelUI> {

    public interface ReceiptChannelUI extends AppUI{

        void showReceiptChannel(OtcPayChannelListResponse payChannelList);

        void showBankList(List<OtcConfigResponse.BankBean> bankList);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, ReceiptChannelUI ui) {
        super.onUIReady(activity, ui);

    }

    @Override
    public void onResume() {
        super.onResume();
        getOtcConfig();
    }

    public void getOtcConfig() {
        OtcApi.getConfig(new SimpleResponseListener<OtcConfigResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(OtcConfigResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    List<OtcConfigResponse.BankBean> bankList = response.getBank();
                    if (bankList != null) {
                        getUI().showBankList(bankList);

                    }
                    getPaymentMethodList();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    /**
     * 获取支付方式列表
     */
    public void getPaymentMethodList() {
        if (!UserInfo.isLogin()) {
            return;
        }
        OtcPayChannelApi.getPayChannelList(new SimpleResponseListener<OtcPayChannelListResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(OtcPayChannelListResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response)) {
                    getUI().showReceiptChannel(response);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    /**
     * 删除收款方式
     * @param item
     * @param tradePwd
     */
    public void deletePayChannel(OtcPaymentChannelBean item, String tradePwd) {
        if (!UserInfo.isLogin()) {
            return;
        }
//        TODO 删除收款方式
        OtcPayChannelApi.deletePayWay(item.getId(),tradePwd,new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    ToastUtils.showShort(getString(R.string.string_delete_success));
                    getPaymentMethodList();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

}
