/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcHistoryProofActivity.java
 *   @Date: 19-1-31 下午2:42
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.ui;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.otc.adapter.OtcProofAdapter;
import io.bhex.app.otc.listener.OtcMsgItemClickListener;
import io.bhex.app.otc.presenter.OtcHistoryProofPresenter;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.otc.bean.OtcMessageResponse;
import io.bhex.sdk.otc.bean.OtcOrderInfoResponse;

public class OtcHistoryProofActivity extends BaseActivity<OtcHistoryProofPresenter,OtcHistoryProofPresenter.OtcHistoryProofUI> implements OtcHistoryProofPresenter.OtcHistoryProofUI {
    private OtcProofAdapter adapter;
    private SmartRefreshLayout swipeRefresh;
    private RecyclerView recyclerView;
    private View emptyView;

    @Override
    protected int getContentView() {
        return R.layout.activity_otc_history_proof_layout;
    }

    @Override
    protected OtcHistoryProofPresenter createPresenter() {
        return new OtcHistoryProofPresenter();
    }

    @Override
    protected OtcHistoryProofPresenter.OtcHistoryProofUI getUI() {
        return this;
    }

    @Override
    protected boolean isNeedLogin() {
        return true;
    }

    @Override
    protected void initView() {
        super.initView();
        swipeRefresh = viewFinder.find(R.id.swipeRefresh);
        recyclerView = viewFinder.find(R.id.recyclerView);
        LayoutInflater layoutInflater = LayoutInflater.from(this);
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
        emptyView.setLayoutParams(layoutParams);
    }

    @Override
    public void showMessageList(OtcOrderInfoResponse orderInfo, List<OtcMessageResponse.MessageBean> data) {
        if (adapter == null) {
            adapter = new OtcProofAdapter(this,orderInfo,data,new OtcMsgItemClickListener(){
                @Override
                public void uploadProof(View view, OtcOrderInfoResponse mOrderInfo) {
                }
            });
            adapter.isFirstOnly(false);
            adapter.setEnableLoadMore(false);
            adapter.setEmptyView(emptyView);

            recyclerView.setLayoutManager(new LinearLayoutManager(this));
            recyclerView.setItemAnimator(new DefaultItemAnimator());
//        recyclerView.addItemDecoration(new DividerItemDecoration(getContext(), LinearLayoutManager.VERTICAL));

            adapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {

                }
            });
            recyclerView.setAdapter(adapter);
        } else {
            adapter.setNewData(data);
        }
    }

}
