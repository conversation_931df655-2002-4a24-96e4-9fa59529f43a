/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcAdsOrderHandingProvider.java
 *   @Date: 19-3-4 下午6:27
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.adapter.provider;

import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.provider.BaseItemProvider;

import io.bhex.app.R;
import io.bhex.app.otc.adapter.OtcAdsListAdapter;
import io.bhex.app.utils.DateUtils;
import io.bhex.sdk.otc.bean.OtcAdsListResponse;

public class OtcAdsOrderHandingProvider extends BaseItemProvider<OtcAdsListResponse.AdBean,BaseViewHolder> {

    public OtcAdsOrderHandingProvider() {

    }

    @Override
    public int viewType() {
        return OtcAdsListAdapter.ORDER_STATUS_HANDING;
    }

    @Override
    public int layout() {
        return R.layout.item_otc_ads_order_list_layout;
    }

    @Override
    public void convert(BaseViewHolder helper, OtcAdsListResponse.AdBean data, int position) {
        helper.setText(R.id.order_type,data.getSide()==0?mContext.getResources().getString(R.string.string_otc_buy):mContext.getResources().getString(R.string.string_otc_sell));
        helper.setText(R.id.order_quantity,data.getQuantity()+" "+data.getTokenName());
        helper.setText(R.id.order_time,DateUtils.getSimpleTimeFormat(data.getCreateDate()));
        helper.setText(R.id.aboutClosePrice,data.getLastQuantity()+"/"+data.getQuantity());
        helper.setText(R.id.limitAmount,data.getMinAmount()+"-"+data.getMaxAmount()+" "+data.getCurrencyId());
        helper.setText(R.id.unitPrice,data.getPriceType()==0?data.getPrice()+" "+data.getCurrencyId():data.getPremium()+"%");
        helper.setText(R.id.order,data.getFinishNum()+"/"+data.getOrderNum());
        helper.setText(R.id.orderFee,data.getFee()+" "+data.getTokenName());
        helper.addOnClickListener(R.id.btn_modify);
        helper.addOnClickListener(R.id.btn_delete);
    }

}
