/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcMsgTxtProvider.java
 *   @Date: 19-1-30 下午7:16
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.adapter.provider;

import android.graphics.drawable.GradientDrawable;
import android.text.TextUtils;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.provider.BaseItemProvider;

import io.bhex.app.R;
import io.bhex.app.otc.listener.OtcMsgItemClickListener;
import io.bhex.app.otc.utils.NickNameColorUtils;
import io.bhex.app.utils.DateUtils;
import io.bhex.sdk.otc.OtcConstant;
import io.bhex.sdk.otc.bean.OtcMessageResponse;
import io.bhex.sdk.otc.bean.OtcOrderInfoResponse;

public class OtcMsgTxtProvider extends BaseItemProvider<OtcMessageResponse.MessageBean,BaseViewHolder> {
    private OtcOrderInfoResponse mOrderInfo;
    private OtcMsgItemClickListener mItemClickListener;
    private String targetAccountId;

    public OtcMsgTxtProvider(OtcOrderInfoResponse orderInfo, OtcMsgItemClickListener clickListener) {
        mOrderInfo = orderInfo;
        mItemClickListener = clickListener;

        targetAccountId = orderInfo.getTargetAccountId();

    }

    @Override
    public int viewType() {
        return OtcConstant.OTC_MESSAGE_TYPE_TXT;
    }

    @Override
    public int layout() {
        return R.layout.item_otc_msg_txt_layout;
    }

    @Override
    public void convert(BaseViewHolder helper, OtcMessageResponse.MessageBean data, int position) {
       TextView avator = helper.getView(R.id.avator);

        String accountId = data.getAccountId();
        if (!TextUtils.isEmpty(accountId)) {
            int key = (int) (Long.valueOf(accountId)%10);
//            helper.setBackgroundColor(R.id.avator,NickNameColorUtils.getNickNameBgColor(key));
            TextView view = helper.getView(R.id.avator);
            GradientDrawable myGrad = (GradientDrawable)view.getBackground();
            myGrad.setColor(NickNameColorUtils.getNickNameBgColor(key));
        }

        String nickName;
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) avator.getLayoutParams();
        if (accountId.equals(targetAccountId)) {
            layoutParams.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
            layoutParams.endToEnd = ConstraintLayout.LayoutParams.UNSET;
            nickName = mOrderInfo.getTargetNickName();
        }else{
            layoutParams.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
            layoutParams.startToStart = ConstraintLayout.LayoutParams.UNSET;
            nickName = mOrderInfo.getNickName();
        }
        avator.setLayoutParams(layoutParams);

        if (!TextUtils.isEmpty(nickName)) {
            String firstLetter = nickName.substring(0, 1).toUpperCase();
            helper.setText(R.id.avator, firstLetter);
        }

        helper.setText(R.id.content,data.getMessage());
        helper.setText(R.id.positionValue,DateUtils.getSimpleTimeFormat(data.getCreateDate()));
        helper.addOnClickListener(R.id.content);
    }

    @Override
    public void onClick(BaseViewHolder helper, OtcMessageResponse.MessageBean data, int position) {
        super.onClick(helper, data, position);
    }
}
