/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcReceiptAliOrWeProvider.java
 *   @Date: 19-2-1 上午11:42
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.adapter.provider;

import android.text.TextUtils;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.provider.BaseItemProvider;

import cn.bingoogolapple.swipeitemlayout.BGASwipeItemLayout;
import io.bhex.app.R;
import io.bhex.app.otc.adapter.OtcReceiptChannelAdapter;
import io.bhex.app.otc.listener.AdapterItemClickListener;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.UrlsConfig;
import io.bhex.sdk.otc.OtcPayChannelApi;
import io.bhex.sdk.otc.bean.OtcOrderInfoResponse;
import io.bhex.sdk.otc.bean.OtcPaymentChannelBean;

public class OtcReceiptAliOrWeProvider extends BaseItemProvider<OtcPaymentChannelBean,BaseViewHolder>{
    private final OtcReceiptChannelAdapter mOtcReceiptChannelAdapter;
    private OtcOrderInfoResponse mOrderInfo;
    private AdapterItemClickListener mItemClickListener;
    private String targetAccountId;

    public OtcReceiptAliOrWeProvider(OtcReceiptChannelAdapter otcReceiptChannelAdapter, AdapterItemClickListener clickListener) {
        mOtcReceiptChannelAdapter = otcReceiptChannelAdapter;
        mItemClickListener = clickListener;
    }

    @Override
    public int viewType() {
        return OtcReceiptChannelAdapter.RECEIPT_ALI_OR_WECHAT;
    }

    @Override
    public int layout() {
        return R.layout.item_otc_receipt_channel_aliorwechat_layout;
    }

    @Override
    public void convert(final BaseViewHolder helper, final OtcPaymentChannelBean data, int position) {
        helper.setImageResource(R.id.icon, data.getPaymentType()==1?R.mipmap.icon_pay_alipay:R.mipmap.icon_pay_wechat);
        helper.setText(R.id.account, data.getAccountNo());
        helper.setText(R.id.name, data.getRealName());
//        helper.setGone(R.id.toggleButton,data.getVisible()==0);
//        helper.setGone(R.id.toggleButton,false);


        ImageView imageView = helper.getView(R.id.qrcode);

        String url = data.getQrcode();
        if (!TextUtils.isEmpty(url)) {
            if (url.startsWith("/")) {
                url = url.replaceFirst("/", "");
            }
        }
        url = UrlsConfig.API_OTC_URL+url;
        Glide.with(mContext)
                .load(url)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .skipMemoryCache(true)
                .into(imageView);
        helper.setBackgroundRes(R.id.toggleButton,data.getVisible()==0?R.mipmap.icon_switch_button_on:R.mipmap.icon_switch_button_off);
        helper.addOnClickListener(R.id.toggleButton);
        helper.addOnClickListener(R.id.itemView);
        helper.addOnClickListener(R.id.qrcode);
        helper.addOnClickListener(R.id.item_delete);
        BGASwipeItemLayout swipeItemLayout = helper.getView(R.id.item_root);
        swipeItemLayout.setDelegate(mOtcReceiptChannelAdapter);
    }

    @Override
    public void onClick(BaseViewHolder helper, OtcPaymentChannelBean data, int position) {
        super.onClick(helper, data, position);
//        if (mItemClickListener != null) {
//            mItemClickListener.onClick(helper.getView(R.id.itemView), data);
//            mItemClickListener.onClick(helper.getView(R.id.qrcode), data);
//        }
    }
}
