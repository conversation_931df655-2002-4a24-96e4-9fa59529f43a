/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcSettingsActivity.java
 *   @Date: 19-1-28 下午7:26
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.ui;

import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.Toolbar;

import com.google.android.material.appbar.CollapsingToolbarLayout;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.otc.presenter.OtcSettingsPresenter;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.account.bean.enums.VERIFY_STATUS;
import io.bhex.sdk.otc.bean.OtcPayChannelListResponse;
import io.bhex.sdk.otc.bean.OtcPaymentChannelBean;
import io.bhex.sdk.otc.bean.OtcUserInfo;

//Otc 用户信息设置
public class OtcSettingsActivity extends BaseActivity<OtcSettingsPresenter,OtcSettingsPresenter.OtcSettingsUI> implements OtcSettingsPresenter.OtcSettingsUI, View.OnClickListener {
    private UserInfoBean userInfo;
    private OtcPayChannelListResponse currentPayChannelList;
    private boolean isSetNickName;
    private OtcUserInfo currentOtcUserInfo;

    @Override
    protected int getContentView() {
        return R.layout.activity_otc_settings_layout;
    }

    @Override
    protected OtcSettingsPresenter createPresenter() {
        return new OtcSettingsPresenter();
    }

    @Override
    protected OtcSettingsPresenter.OtcSettingsUI getUI() {
        return this;
    }

    @Override
    protected boolean isNeedLogin() {
        return true;
    }

    @Override
    protected void initView() {
        super.initView();
        Toolbar toolbar= findViewById(R.id.toolbar);
        CollapsingToolbarLayout collapsingToolbarLayout= findViewById(R.id.collapsing_toolbar);
        // 硬编码黑白版Toolbar标题栏title字色
        collapsingToolbarLayout.setCollapsedTitleTextColor(SkinColorUtil.getDark(this));
        collapsingToolbarLayout.setExpandedTitleColor(SkinColorUtil.getDark(this));

        //显示返回按钮
        setSupportActionBar(toolbar);
        ActionBar actionBar=getSupportActionBar();
        if (actionBar!=null){
            actionBar.setDisplayHomeAsUpEnabled(true);
        }

        userInfo = UserManager.getInstance().getUserInfo();
        String mobile = userInfo.getMobile();
        if (!TextUtils.isEmpty(mobile)) {
            viewFinder.textView(R.id.auth_mobile_status).setText(getString(R.string.string_binded));
            viewFinder.find(R.id.mobile_rela).setClickable(false);
            viewFinder.textView(R.id.auth_mobile_status).setTextColor(SkinColorUtil.getDark50(this));
        }else{
            viewFinder.find(R.id.mobile_rela).setClickable(true);
            viewFinder.textView(R.id.auth_mobile_status).setTextColor(getResources().getColor(R.color.red));
        }

    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()){
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.nickname_rela).setOnClickListener(this);
        viewFinder.find(R.id.finance_passwd_rela).setOnClickListener(this);
        viewFinder.find(R.id.kyc_rela).setOnClickListener(this);
        viewFinder.find(R.id.mobile_rela).setOnClickListener(this);
        viewFinder.find(R.id.pay_channel_rela).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.nickname_rela:
                IntentUtils.goSetOtcNickName(this);
                break;
            case R.id.finance_passwd_rela:
                if (currentOtcUserInfo != null) {
                    //昵称判断
                    String nickName = currentOtcUserInfo.getNickName();
                    if (TextUtils.isEmpty(nickName)) {
                        ToastUtils.showShort(getString(R.string.string_input_nickname_please));
                        return;
                    }
                }
                if (userInfo != null) {
                    IntentUtils.goFinancePasswd(this,userInfo.isBindTradePwd());
                }else{
                    getPresenter().getUserInfo();
                }
                break;
            case R.id.kyc_rela:
                if (currentOtcUserInfo != null) {
                    //昵称判断
                    String nickName = currentOtcUserInfo.getNickName();
                    if (TextUtils.isEmpty(nickName)) {
                        ToastUtils.showShort(getString(R.string.string_input_nickname_please));
                        return;
                    }
                }

                if (userInfo != null) {
                    //设置绑定手机号
                    String mobile = userInfo.getMobile();
                    if (TextUtils.isEmpty(mobile)) {
                        ToastUtils.showShort(getString(R.string.string_set_mobile_please));
                        return;
                    }
                    //设置资金密码提示
                    if (!userInfo.isBindTradePwd()) {
                        ToastUtils.showShort(getString(R.string.string_input_trade_passwd_please));
                        return;
                    }
                }

                IntentUtils.goIdentityAuth(this);

                break;
            case R.id.mobile_rela:
                if (currentOtcUserInfo != null) {
                    //昵称判断
                    String nickName = currentOtcUserInfo.getNickName();
                    if (TextUtils.isEmpty(nickName)) {
                        ToastUtils.showShort(getString(R.string.string_input_nickname_please));
                        return;
                    }
                }
                IntentUtils.goBindMobile(this);
                break;
            case R.id.pay_channel_rela:
                if (currentOtcUserInfo != null) {
                    //昵称判断
                    String nickName = currentOtcUserInfo.getNickName();
                    if (TextUtils.isEmpty(nickName)) {
                        ToastUtils.showShort(getString(R.string.string_input_nickname_please));
                        return;
                    }
                    //一级身份认证
                    if (!currentOtcUserInfo.isVerifyFlag()) {
                        ToastUtils.showShort(getString(R.string.string_auth_id_please));
                        return;
                    }
                }
                if (userInfo != null) {
                    //设置绑定手机号
                    String mobile = userInfo.getMobile();
                    if (TextUtils.isEmpty(mobile)) {
                        ToastUtils.showShort(getString(R.string.string_set_mobile_please));
                        return;
                    }
                    //设置资金密码提示
                    if (!userInfo.isBindTradePwd()) {
                        ToastUtils.showShort(getString(R.string.string_input_trade_passwd_please));
                        return;
                    }
                }
                IntentUtils.goPayChannelList(this);
                break;
        }
    }

    @Override
    public void showPayChannelList(OtcPayChannelListResponse payChannelListResponse) {
        currentPayChannelList = payChannelListResponse;
        if (currentPayChannelList != null) {
            List<OtcPaymentChannelBean> payList = currentPayChannelList.getArray();
            if (payList != null) {
                if (payList.size()>0) {
                    viewFinder.textView(R.id.pay_channel_status).setText(getString(R.string.string_modify));
                    viewFinder.textView(R.id.pay_channel_status).setTextColor(SkinColorUtil.getDark50(this));
                    return;
                }
            }
        }
        viewFinder.textView(R.id.pay_channel_status).setTextColor(getResources().getColor(R.color.red));
    }

    @Override
    public void showOtcUserInfo(OtcUserInfo response) {
        if (response != null) {
            currentOtcUserInfo = response;
            String nickName = response.getNickName();
            if (!TextUtils.isEmpty(nickName)) {
                isSetNickName = true;
                viewFinder.textView(R.id.nick_name_status).setText(nickName);
                viewFinder.find(R.id.nickname_rela).setClickable(false);
                viewFinder.textView(R.id.nick_name_status).setTextColor(SkinColorUtil.getDark50(this));
            }else {
                isSetNickName = false;
                viewFinder.find(R.id.nickname_rela).setClickable(true);
                viewFinder.textView(R.id.nick_name_status).setTextColor(getResources().getColor(R.color.red));
            }

            if (response.isBindTradePwd()) {
                viewFinder.textView(R.id.finance_passed_status).setText(getString(R.string.string_modify));
                viewFinder.textView(R.id.finance_passed_status).setTextColor(SkinColorUtil.getDark50(this));
            }else{
                viewFinder.textView(R.id.finance_passed_status).setTextColor(getResources().getColor(R.color.red));
            }
            if (response.isVerifyFlag()) {
//                viewFinder.textView(R.id.auth_kyc_status).setText(getString(R.string.string_had_auth));
                viewFinder.textView(R.id.auth_kyc_status).setText(getString(R.string.string_binded));
//                viewFinder.find(R.id.kyc_rela).setClickable(false);
                viewFinder.textView(R.id.auth_kyc_status).setTextColor(SkinColorUtil.getDark50(this));
            }else{
                viewFinder.textView(R.id.auth_kyc_status).setText(getString(R.string.string_set));
//                viewFinder.find(R.id.kyc_rela).setClickable(true);
                viewFinder.textView(R.id.auth_kyc_status).setTextColor(getResources().getColor(R.color.red));
            }
        }

    }

    @Override
    public void getUserInfoSuccess(UserInfoBean data) {
        if (data != null) {
            userInfo = data;
            String mobile = userInfo.getMobile();
            if (!TextUtils.isEmpty(mobile)) {
                viewFinder.textView(R.id.auth_mobile_status).setText(getString(R.string.string_binded));
                viewFinder.find(R.id.mobile_rela).setClickable(false);
                viewFinder.textView(R.id.auth_mobile_status).setTextColor(SkinColorUtil.getDark50(this));
            }else{
                viewFinder.find(R.id.mobile_rela).setClickable(true);
                viewFinder.textView(R.id.auth_mobile_status).setTextColor(getResources().getColor(R.color.red));
            }

            if (userInfo.getVerifyStatus()==VERIFY_STATUS.VERIFY_CHECKED.getmStatus()) {
                viewFinder.textView(R.id.auth_kyc_status).setTextColor(getResources().getColor(CommonUtil.isBlackMode() ? R.color.dark50_night : R.color.dark50));
                viewFinder.textView(R.id.auth_kyc_status).setText(getString(R.string.string_binded));
                viewFinder.find(R.id.kyc_rela).setClickable(false);
            }else{
                viewFinder.textView(R.id.auth_kyc_status).setText(getString(VERIFY_STATUS.getDescByStatus(userInfo.getVerifyStatus())));
                viewFinder.find(R.id.kyc_rela).setClickable(true);
            }
        }
    }
}
