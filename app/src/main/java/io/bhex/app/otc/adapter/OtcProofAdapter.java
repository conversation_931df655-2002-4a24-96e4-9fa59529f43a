/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcProofAdapter.java
 *   @Date: 19-1-31 下午3:17
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.adapter;

import android.content.Context;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.MultipleItemRvAdapter;

import java.util.List;

import io.bhex.app.otc.adapter.provider.OtcProofImageProvider;
import io.bhex.app.otc.adapter.provider.OtcProofTxtProvider;
import io.bhex.app.otc.listener.OtcMsgItemClickListener;
import io.bhex.sdk.otc.OtcConstant;
import io.bhex.sdk.otc.bean.OtcMessageResponse;
import io.bhex.sdk.otc.bean.OtcOrderInfoResponse;

public class OtcProofAdapter extends MultipleItemRvAdapter<OtcMessageResponse.MessageBean,BaseViewHolder> {
    private final OtcMsgItemClickListener mClickListener;
    private final OtcOrderInfoResponse mOrderInfo;
    private final Context mOtcContext;

    public OtcProofAdapter(Context context, OtcOrderInfoResponse orderInfo, @Nullable List<OtcMessageResponse.MessageBean> data, OtcMsgItemClickListener itemClickListener) {
        super(data);
        mOrderInfo = orderInfo;
        mClickListener = itemClickListener;
        mOtcContext = context;
        //构造函数若有传其他参数可以在调用finishInitialize()之前进行赋值，赋值给全局变量
        //这样getViewType()和registerItemProvider()方法中可以获取到传过来的值
        //getViewType()中可能因为某些业务逻辑，需要将某个值传递过来进行判断，返回对应的viewType
        //registerItemProvider()中可以将值传递给ItemProvider

        //If the constructor has other parameters, it needs to be assigned before calling finishInitialize() and assigned to the global variable
        // This getViewType () and registerItemProvider () method can get the value passed over
        // getViewType () may be due to some business logic, you need to pass a value to judge, return the corresponding viewType
        //RegisterItemProvider() can pass value to ItemProvider

        finishInitialize();
    }

    @Override
    protected int getViewType(OtcMessageResponse.MessageBean entity) {
        //根据实体类判断并返回对应的viewType，具体判断逻辑因业务不同，这里这是简单通过判断type属性
        //According to the entity class to determine and return the corresponding viewType,
        //the specific judgment logic is different because of the business, here is simply by judging the type attribute
        int msgType = entity.getMsgType();
        if (msgType==101) {
            //文本消息
            return OtcConstant.OTC_PROOF_MESSAGE_TYPE_TXT;
        }else if(msgType==102){
            //图片消息
            return OtcConstant.OTC_PROOF_MESSAGE_TYPE_IMAGE;
        }

        return 0;
    }

    @Override
    public void registerItemProvider() {
        //注册相关的条目provider
        //Register related entries provider
        mProviderDelegate.registerProvider(new OtcProofTxtProvider(mOrderInfo,mClickListener));
        mProviderDelegate.registerProvider(new OtcProofImageProvider(mOrderInfo,mClickListener));
    }
}
