/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcMsgSystemProvider.java
 *   @Date: 19-1-30 下午8:12
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.adapter.provider;

import android.content.Context;
import android.text.TextUtils;

import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.provider.BaseItemProvider;

import java.util.HashMap;

import io.bhex.app.R;
import io.bhex.app.otc.listener.OtcMsgItemClickListener;
import io.bhex.app.otc.utils.OtcSysMsgRes;
import io.bhex.app.utils.DateUtils;
import io.bhex.sdk.otc.OtcConstant;
import io.bhex.sdk.otc.bean.OtcMessageResponse;
import io.bhex.sdk.otc.bean.OtcOrderInfoResponse;

public class OtcMsgSystemProvider extends BaseItemProvider<OtcMessageResponse.MessageBean,BaseViewHolder> {
    private HashMap<Integer, String> msgRes;
    private OtcOrderInfoResponse mOrderInfo;
    private OtcMsgItemClickListener mItemClickListener;
    private String targetAccountId;

    public OtcMsgSystemProvider(Context mOtcContext, OtcOrderInfoResponse orderInfo, OtcMsgItemClickListener clickListener) {
        mOrderInfo = orderInfo;
        mItemClickListener = clickListener;

        targetAccountId = orderInfo.getTargetAccountId();

        msgRes = OtcSysMsgRes.initMsgRes(mOtcContext);
    }

    @Override
    public int viewType() {
        return OtcConstant.OTC_MESSAGE_TYPE_SYSTEM;
    }

    @Override
    public int layout() {
        return R.layout.item_otc_msg_system_layout;
    }

    @Override
    public void convert(BaseViewHolder helper, OtcMessageResponse.MessageBean data, int position) {
        int msgCode = data.getMsgCode();
        String message = msgRes.get(msgCode);
        if (!TextUtils.isEmpty(message)) {
            message = message.replaceAll("%amount%",mOrderInfo.getQuantity()+mOrderInfo.getTokenName());
            helper.setText(R.id.content,message);
        }
        helper.setText(R.id.positionValue,DateUtils.getSimpleTimeFormat(data.getCreateDate()));
        if(msgCode==1030||msgCode==1031||msgCode==2030||msgCode==2031||msgCode==3030||msgCode==3031){
            helper.setGone(R.id.uploadProof,true);
            helper.addOnClickListener(R.id.uploadProof);
        }else{
            helper.setGone(R.id.uploadProof,false);
        }

        helper.addOnClickListener(R.id.content);
    }

    @Override
    public void onClick(BaseViewHolder helper, OtcMessageResponse.MessageBean data, int position) {
        super.onClick(helper, data, position);
//        if (mItemClickListener != null) {
//            mItemClickListener.uploadProof(helper.getView(R.id.uploadProof),mOrderInfo);
//        }
    }
}
