/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcReceiptUnionProvider.java
 *   @Date: 19-2-1 上午11:34
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.adapter.provider;

import android.text.TextUtils;
import android.view.View;
import android.widget.CompoundButton;

import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.provider.BaseItemProvider;

import java.util.HashMap;

import cn.bingoogolapple.swipeitemlayout.BGASwipeItemLayout;
import io.bhex.app.R;
import io.bhex.app.otc.adapter.OtcReceiptChannelAdapter;
import io.bhex.app.otc.listener.AdapterItemClickListener;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.otc.OtcPayChannelApi;
import io.bhex.sdk.otc.bean.OtcConfigResponse;
import io.bhex.sdk.otc.bean.OtcOrderInfoResponse;
import io.bhex.sdk.otc.bean.OtcPaymentChannelBean;

public class OtcReceiptUnionProvider extends BaseItemProvider<OtcPaymentChannelBean,BaseViewHolder> {
    private final OtcReceiptChannelAdapter mOtcReceiptChannelAdapter;
    private final HashMap<String, OtcConfigResponse.BankBean> mBankMap;
    private OtcOrderInfoResponse mOrderInfo;
    private AdapterItemClickListener mItemClickListener;
    private String targetAccountId;

    public OtcReceiptUnionProvider(OtcReceiptChannelAdapter otcReceiptChannelAdapter, HashMap<String, OtcConfigResponse.BankBean> bankMap, AdapterItemClickListener clickListener) {
        mOtcReceiptChannelAdapter = otcReceiptChannelAdapter;
        mItemClickListener = clickListener;
        mBankMap = bankMap;
    }

    @Override
    public int viewType() {
        return OtcReceiptChannelAdapter.RECEIPT_UNION;
    }

    @Override
    public int layout() {
        return R.layout.item_otc_receipt_channel_bank_layout;
    }

    @Override
    public void convert(final BaseViewHolder helper, final OtcPaymentChannelBean data, int position) {
        String bankId = data.getBankName();
        helper.setText(R.id.bankname, bankId);
        if (mBankMap != null) {
            OtcConfigResponse.BankBean bankBean = mBankMap.get(bankId);
            if (bankBean != null) {
                String bankName = bankBean.getName();
                if (!TextUtils.isEmpty(bankName)) {
                    helper.setText(R.id.bankname, bankName);
                }
            }
        }
        helper.setText(R.id.bank_no, data.getAccountNo());
        helper.setText(R.id.name, data.getRealName());
//        helper.setGone(R.id.toggleButton,data.getVisible()==0);
//        helper.setGone(R.id.toggleButton,false);
        helper.setBackgroundRes(R.id.toggleButton,data.getVisible()==0?R.mipmap.icon_switch_button_on:R.mipmap.icon_switch_button_off);
        helper.addOnClickListener(R.id.toggleButton);
        helper.addOnClickListener(R.id.itemView);

        helper.addOnClickListener(R.id.item_delete);
        BGASwipeItemLayout swipeItemLayout = helper.getView(R.id.item_root);
        swipeItemLayout.setDelegate(mOtcReceiptChannelAdapter);
    }

//    @Override
//    public void onClick(BaseViewHolder helper, OtcPaymentChannelBean data, int position) {
//        super.onClick(helper, data, position);
//        mItemClickListener.onClick(helper.getView(R.id.itemView),data);
//    }
}
