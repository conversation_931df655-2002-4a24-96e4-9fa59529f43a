/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcBankAdapter.java
 *   @Date: 19-2-1 下午4:32
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.adapter;

import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.otc.bean.AppealTypeBean;
import io.bhex.sdk.otc.bean.OtcConfigResponse;

public class OtcBankAdapter extends BaseQuickAdapter<OtcConfigResponse.BankBean, BaseViewHolder> {
    public OtcBankAdapter(List<OtcConfigResponse.BankBean> data) {
        super(R.layout.item_otc_appeal_type_list_layout, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, OtcConfigResponse.BankBean item) {

        String name = item.getName();
        if (!TextUtils.isEmpty(name)) {
            helper.setText(R.id.item_name, name);
        }
        helper.addOnClickListener(R.id.item_name);
    }

}
