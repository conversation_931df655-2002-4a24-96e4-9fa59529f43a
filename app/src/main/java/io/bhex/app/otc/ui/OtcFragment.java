/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcFragment.java
 *   @Date: 19-1-11 下午2:07
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.ui;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.TextView;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.app.BHexApplication;
import io.bhex.app.base.BaseFragment;
import io.bhex.app.diy.OtcSort;
import io.bhex.app.otc.adapter.OtcCurrencyFilterAdapter;
import io.bhex.app.otc.adapter.OtcListAdapter;
import io.bhex.app.otc.adapter.OtcSwitchBAdapter;
import io.bhex.app.otc.presenter.OtcPresenter;
import io.bhex.app.trade.ui.HomeTradeControl;
import io.bhex.app.utils.AnimalUtils;
import io.bhex.app.utils.CoinUtils;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.DialogUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.view.BHTabView;
import io.bhex.app.view.PopWindow;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.LoginResultCallback;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.otc.OtcPayChannelApi;
import io.bhex.sdk.otc.OtcUserApi;
import io.bhex.sdk.otc.bean.OtcConfigResponse;
import io.bhex.sdk.otc.bean.OtcLastPriceResponse;
import io.bhex.sdk.otc.bean.OtcListResponse;
import io.bhex.sdk.otc.bean.OtcPayChannelListResponse;
import io.bhex.sdk.otc.bean.OtcPaymentChannelBean;
import io.bhex.sdk.otc.bean.OtcUserInfo;
import io.bhex.sdk.trade.CurrentOtcSelection;

public class OtcFragment extends BaseFragment<OtcPresenter, OtcPresenter.OtcUI> implements OtcPresenter.OtcUI, View.OnClickListener, OnRefreshListener, BaseQuickAdapter.RequestLoadMoreListener {
    private TextView leftIcon;
    private TextView rightIcon;
    private TextView titleTx;
    private RecyclerView recyclerView;
    private SmartRefreshLayout swipeRefresh;
    private OtcListAdapter otcListAdapter;
    private View emptyView;
    private View menuLayout;
    private PopWindow popWindowMenu;
    private View filterLayout;
    private PopWindow popWindowFilter;
    private View switchAdsLayout;
    private PopWindow popWindowSwitchAds;
    private View otcRootView;
    private CheckBox filterAll;
    private CheckBox filterUnion;
    private CheckBox filterAlipay;
    private CheckBox filterWechat;
    private RecyclerView recyclerViewBuy;
    private RecyclerView recyclerViewSell;
    private Button btnReset;
    private Button btnComplete;
    private boolean isHasPayway = false;
    private boolean verifyFlag;
    private boolean bindTradePwd;
    private TextView pendingOrderTips;
    private int whiteFlag = -1;
    private View otcAdsManageLine;
    private View otcAdsManage;
    private boolean isSetNickName;
    private boolean isCompleteBaseInfo;
    private RecyclerView currencyRv;
    private OtcConfigResponse.CurrencyBean preSelectCurrency;
    private TopBar filterTopBar;
    private BHTabView buySellTab;
    private BHTabView tokenTabs;
    private OtcConfigResponse.TokenBean selectTokenBean;
    private boolean isWantBuy = true;// 默认是我要买
    private String defaultToken = "";
    private ArrayList<String> tokenTabTitles;

    @Override
    protected OtcPresenter.OtcUI getUI() {
        return this;
    }

    @Override
    protected OtcPresenter createPresenter() {
        return new OtcPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_otc_layout, null, false);
    }

    private HomeTradeControl mHomeControl;

    public void setHomeControl(HomeTradeControl control) {
        mHomeControl = control;
    }

    @Override
    protected void initViews() {
        super.initViews();
        EventBus.getDefault().register(this);
        otcRootView = viewFinder.find(R.id.otcRootView);
        leftIcon = viewFinder.textView(R.id.leftIcon);
        rightIcon = viewFinder.textView(R.id.rightIcon);
        titleTx = viewFinder.textView(R.id.title);
        pendingOrderTips = viewFinder.textView(R.id.otc_pending_order_tips);
        recyclerView = viewFinder.find(R.id.recyclerView);

        swipeRefresh = viewFinder.find(R.id.refreshLayout);
        CurrentOtcSelection otcToken = CoinUtils.getOTCCoin();
        if(otcToken!=null) {
            isWantBuy = otcToken.isBuy;
            if (!TextUtils.isEmpty(otcToken.token)) {
                defaultToken = otcToken.token;
            }
        }
        LayoutInflater layoutInflater = LayoutInflater.from(getActivity());
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        menuLayout = layoutInflater.inflate(R.layout.pop_otc_menu_layout, swipeRefresh, false);
        otcAdsManageLine = menuLayout.findViewById(R.id.otc_ad_line);
        otcAdsManage = menuLayout.findViewById(R.id.otc_ad_manage);

        filterLayout = layoutInflater.inflate(R.layout.include_otc_filter_ads_layout, swipeRefresh, false);
        filterTopBar = filterLayout.findViewById(R.id.filterTopBar);
        currencyRv = filterLayout.findViewById(R.id.currencyRv);
        switchAdsLayout = layoutInflater.inflate(R.layout.otc_switch_b_layout, swipeRefresh, false);
        recyclerViewBuy = switchAdsLayout.findViewById(R.id.buyRv);
        recyclerViewSell = switchAdsLayout.findViewById(R.id.sellRv);

        popWindowMenu = new PopWindow(getActivity(), menuLayout);
        popWindowFilter = new PopWindow(getActivity(), filterLayout, PixelUtils.getScreenWidth(), PixelUtils.getScreenHeight() - PixelUtils.getStatusBarHeight(getActivity()));
//        popWindowSwitchAds = new PopWindow(getActivity(), switchAdsLayout,PixelUtils.dp2px(240),PixelUtils.dp2px(260));
        popWindowSwitchAds = new PopWindow(getActivity(), switchAdsLayout, PixelUtils.getScreenWidth(), PixelUtils.dp2px(260));
//        popWindowSwitchAds = new PopWindow(getActivity(), switchAdsLayout,PixelUtils.getScreenWidth()-2*PixelUtils.dp2px(24),PixelUtils.dp2px(200));

        //

        buySellTab = viewFinder.find(R.id.buySellTab);
        List<String> buySellTabTitles = new ArrayList<>();
        buySellTabTitles.add(getString(R.string.string_want_buy));
        buySellTabTitles.add(getString(R.string.string_want_sell));

        buySellTab.setTabs(buySellTabTitles, isWantBuy ? 0 : 1, position -> {
            if (position == 0) {
                //我要买
                isWantBuy = true;
                switchBuyOrSellOrToken(selectTokenBean);
            } else {
                //我要卖
                isWantBuy = false;
                switchBuyOrSellOrToken(selectTokenBean);
            }
            CoinUtils.setOTCIsBuy(isWantBuy);
        });
        tokenTabs = viewFinder.find(R.id.tokenTabs);

    }

    @Override
    public void onResume() {
        super.onResume();
        buySellTab.switchSkin(CommonUtil.isBlackMode());
        tokenTabs.switchSkin(CommonUtil.isBlackMode());
    }

    @Override
    protected void onVisibleChanged(boolean visible) {
        super.onVisibleChanged(visible);
        if (visible) {
            filterTopBar.setRightImg(CommonUtil.isBlackMode() ? R.mipmap.icon_close : R.mipmap.icon_close);
        }
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.leftIcon).setOnClickListener(this);
        viewFinder.find(R.id.rightIcon).setOnClickListener(this);
        viewFinder.find(R.id.title).setOnClickListener(this);
        viewFinder.find(R.id.otc_pending_order_tips).setOnClickListener(this);
        //OTC menu
        menuLayout.findViewById(R.id.otc_setting).setOnClickListener(this);
        menuLayout.findViewById(R.id.otc_orders).setOnClickListener(this);
        menuLayout.findViewById(R.id.otc_pay_way).setOnClickListener(this);
        menuLayout.findViewById(R.id.otc_ad_manage).setOnClickListener(this);
        //筛选
        filterTopBar.setRightOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                popWindowFilter.dismiss();
            }
        });
        filterAll = filterLayout.findViewById(R.id.filter_all);
        filterUnion = filterLayout.findViewById(R.id.filter_union);
        filterAlipay = filterLayout.findViewById(R.id.filter_alipay);
        filterWechat = filterLayout.findViewById(R.id.filter_wechat);
        btnReset = filterLayout.findViewById(R.id.btn_reset);
        btnComplete = filterLayout.findViewById(R.id.btn_complete);
        filterAll.setOnClickListener(this);
        filterUnion.setOnClickListener(this);
        filterAlipay.setOnClickListener(this);
        filterWechat.setOnClickListener(this);
        btnReset.setOnClickListener(this);
        btnComplete.setOnClickListener(this);

        swipeRefresh.setOnRefreshListener(this);

//        recyclerView.setOnScrollListener(new RecyclerView.OnScrollListener() {
//            @Override
//            public void onScrollStateChanged(RecyclerView view, int scrollState) {
//
//            }
//            public void onScrolled(RecyclerView recyclerView, int dx, int dy){
//
//                if(mHomeControl != null) {
//                    final int offset = recyclerView.computeVerticalScrollOffset();
//                    final int range = recyclerView.computeVerticalScrollRange() - recyclerView.computeVerticalScrollExtent();
//                    /*if (range == 0) return false;
//                    if (direction < 0) {
//                        return offset > 0;
//                    } else {
//                        return offset < range - 1;
//                    }*/
//
//                    if (offset > PixelUtils.dp2px(50)) {
//                        mHomeControl.OnShowTab(false);
//                    }else if( offset ==0 ){
//                        mHomeControl.OnShowTab(true);
//                    }
//                    /*if (((LinearLayoutManager)recyclerView.getLayoutManager()).findFirstCompletelyVisibleItemPosition() == 0 ) {
//                        mHomeControl.OnShowTab(true);
//                    }else {
//                        mHomeControl.OnShowTab(false);
//                    }*/
//                }
//            }
//        });
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.title:
//                popWindowSwitchAds.showAsDropDown(v,-PixelUtils.dp2px(24),0);
                popWindowSwitchAds.showAsDropDown(v, -PixelUtils.dp2px(240) / 3, 0);
                getPresenter().showPSwitchBList();
                break;
            case R.id.leftIcon:
                popWindowMenu.showAsDropDown(v);
                break;
            case R.id.rightIcon:
                popWindowFilter.showAtLocation(otcRootView.getRootView().getRootView(), Gravity.TOP, 0, 0);
                showCurrencyGrid();
                break;
            case R.id.otc_setting:
                //OTC 设置
                popWindowMenu.dismiss();
                UserInfo.LoginAndGoin(getActivity(), new LoginResultCallback() {
                    @Override
                    public void onLoginSucceed() {
                        super.onLoginSucceed();
                        IntentUtils.goOtcSettings(getActivity());
                    }
                });
                break;
            case R.id.otc_pending_order_tips:
                UserInfo.LoginAndGoin(getActivity(), new LoginResultCallback() {
                    @Override
                    public void onLoginSucceed() {
                        super.onLoginSucceed();
                        IntentUtils.goOtcOrders(getActivity());
                    }
                });
                break;
            case R.id.otc_orders:
                //OTC订单
                popWindowMenu.dismiss();
                UserInfo.LoginAndGoin(getActivity(), new LoginResultCallback() {
                    @Override
                    public void onLoginSucceed() {
                        super.onLoginSucceed();
                        IntentUtils.goOtcOrders(getActivity());
                    }
                });
                break;
            case R.id.otc_pay_way:
                //otc收款方式
                popWindowMenu.dismiss();
                UserInfo.LoginAndGoin(getActivity(), new LoginResultCallback() {
                    @Override
                    public void onLoginSucceed() {
                        super.onLoginSucceed();
                        if (!isCompleteBaseInfo) {

                            OtcUserApi.getOtcUserInfo(new SimpleResponseListener<OtcUserInfo>(){
                                @Override
                                public void onBefore() {
                                    super.onBefore();
                                    showProgressDialog();
                                }

                                @Override
                                public void onFinish() {
                                    super.onFinish();
                                    dismissProgressDialog();
                                }

                                @Override
                                public void onSuccess(OtcUserInfo response) {
                                    super.onSuccess(response);
                                    if (CodeUtils.isSuccess(response)) {
                                        showOtcUserInfo(response);
                                        if (!isCompleteBaseInfo) {
                                            DialogUtils.showDialog(getActivity(), "", getString(R.string.string_otc_complete_user_info_tips), getString(R.string.string_set), getString(R.string.string_not_need), true, new DialogUtils.OnButtonEventListener() {
                                                @Override
                                                public void onConfirm() {
                                                    IntentUtils.goOtcSettings(getActivity());
                                                }

                                                @Override
                                                public void onCancel() {

                                                }
                                            });
                                            return;
                                        }
                                        if (!isSetNickName) {
                                            ToastUtils.showShort(getString(R.string.string_input_nickname_please));
                                            return;
                                        }

                                        if (verifyFlag) {
                                            IntentUtils.goPayChannelList(getActivity());
                                        } else {
                                            ToastUtils.showShort(getString(R.string.string_auth_id_please));
                                        }
                                    }
                                }

                                @Override
                                public void onError(Throwable error) {
                                    super.onError(error);
                                }
                            });
                             return;
                        }
                        if (!isSetNickName) {
                            ToastUtils.showShort(getString(R.string.string_input_nickname_please));
                            return;
                        }

                        if (verifyFlag) {
                            IntentUtils.goPayChannelList(getActivity());
                        } else {
                            ToastUtils.showShort(getString(R.string.string_auth_id_please));
                        }
                    }
                });
                break;
            case R.id.otc_ad_manage:
                //otc广告管理
                popWindowMenu.dismiss();
                UserInfo.LoginAndGoin(getActivity(), new LoginResultCallback() {
                    @Override
                    public void onLoginSucceed() {
                        super.onLoginSucceed();
                        if (!isHasPayway || !isCompleteBaseInfo) {
                            OtcUserApi.getOtcUserInfo(new SimpleResponseListener<OtcUserInfo>(){
                                @Override
                                public void onBefore() {
                                    super.onBefore();
                                    showProgressDialog();
                                }

                                @Override
                                public void onFinish() {
                                    super.onFinish();
                                    dismissProgressDialog();
                                }

                                @Override
                                public void onSuccess(OtcUserInfo response) {
                                    super.onSuccess(response);
                                    if (CodeUtils.isSuccess(response)) {
                                        showOtcUserInfo(response);

                                        OtcPayChannelApi.getPayChannelList(new SimpleResponseListener<OtcPayChannelListResponse>(){
                                            @Override
                                            public void onBefore() {
                                                super.onBefore();
                                                showProgressDialog();
                                            }

                                            @Override
                                            public void onFinish() {
                                                super.onFinish();
                                                dismissProgressDialog();
                                            }

                                            @Override
                                            public void onSuccess(OtcPayChannelListResponse response) {
                                                super.onSuccess(response);
                                                if (CodeUtils.isSuccess(response)) {
                                                    List<OtcPaymentChannelBean> payChannelList = response.getArray();
                                                    if (payChannelList != null) {
                                                        if (payChannelList.size()>0) {
                                                            //根据用户支付通道 大于 > 0
                                                            isHasPayway = true;
                                                        } else {
                                                            isHasPayway = false;
                                                        }
                                                    } else {
                                                        isHasPayway = false;
                                                    }

                                                    if (!isHasPayway || !isCompleteBaseInfo) {
                                                        DialogUtils.showDialog(getActivity(), "", getString(R.string.string_otc_complete_user_info_tips), getString(R.string.string_set), getString(R.string.string_not_need), true, new DialogUtils.OnButtonEventListener() {
                                                            @Override
                                                            public void onConfirm() {
                                                                IntentUtils.goOtcSettings(getActivity());
                                                            }

                                                            @Override
                                                            public void onCancel() {

                                                            }
                                                        });
                                                        return;
                                                    }
                                                    if (whiteFlag == 1) {
                                                        IntentUtils.goOtcAds(getActivity());
                                                    }
                                                }
                                            }

                                            @Override
                                            public void onError(Throwable error) {
                                                super.onError(error);
                                            }
                                        });
                                    }
                                }

                                @Override
                                public void onError(Throwable error) {
                                    super.onError(error);
                                }
                            });
                            return;
                        }

                        if (whiteFlag == 1) {
                            IntentUtils.goOtcAds(getActivity());
                        }
                    }
                });
                break;

            case R.id.filter_all:
                filterAll.setChecked(true);
                filterUnion.setChecked(false);
                filterAlipay.setChecked(false);
                filterWechat.setChecked(false);
//                startFilter("");
                break;
            case R.id.filter_union:
                filterAll.setChecked(false);
                filterUnion.setChecked(true);
                filterAlipay.setChecked(false);
                filterWechat.setChecked(false);
//                startFilter("0");
                break;
            case R.id.filter_alipay:
                filterAll.setChecked(false);
                filterUnion.setChecked(false);
                filterAlipay.setChecked(true);
                filterWechat.setChecked(false);
//                startFilter("1");
                break;
            case R.id.filter_wechat:
                filterAll.setChecked(false);
                filterUnion.setChecked(false);
                filterAlipay.setChecked(false);
                filterWechat.setChecked(true);
//                startFilter("2");
                break;
            case R.id.btn_reset:
                filterAll.setChecked(true);
                filterUnion.setChecked(false);
                filterAlipay.setChecked(false);
                filterWechat.setChecked(false);
                break;
            case R.id.btn_complete:
                if (preSelectCurrency != null) {//选择法币
                    getPresenter().setSelectCurrency(preSelectCurrency);
                }
                if (filterAll.isChecked()) {
                    startFilter("");
                } else if (filterUnion.isChecked()) {
                    startFilter("0");
                } else if (filterAlipay.isChecked()) {
                    startFilter("1");
                } else if (filterWechat.isChecked()) {
                    startFilter("2");
                }
                break;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public synchronized void onMessageEvent(CurrentOtcSelection otcToken) {
        if (otcToken == null)
            return;
        isWantBuy = otcToken.isBuy;
        buySellTab.setCurrentTab(otcToken.isBuy ? 0 : 1);
        if (!TextUtils.isEmpty(otcToken.token)) {
            defaultToken = otcToken.token;
            int position = 0;
            if (tokenTabTitles.contains(defaultToken)) {
                position = tokenTabTitles.indexOf(defaultToken);
            }
            tokenTabs.setCurrentTab(position);
        }
    }

    @Override
    public void showTokensTab(List<OtcConfigResponse.TokenBean> tokenBeanList) {
        if (tokenBeanList != null && tokenBeanList.size() > 0) {

            int position = 0;
            tokenTabTitles = new ArrayList<>();
            for (OtcConfigResponse.TokenBean tokenBean : tokenBeanList) {
                tokenTabTitles.add(tokenBean.getTokenName());
            }

            if (!TextUtils.isEmpty(defaultToken) && tokenTabTitles.contains(defaultToken)) {
                position = tokenTabTitles.indexOf(defaultToken);
            }
            tokenTabs.setTabs(tokenTabTitles, position,new BHTabView.OnTabListener() {
                @Override
                public void onSelect(int position) {
                    if (tokenBeanList != null) {
                        if (position < tokenBeanList.size()) {
                            selectTokenBean = tokenBeanList.get(position);
                            switchBuyOrSellOrToken(selectTokenBean);
                            CoinUtils.setOTCCoinToken(selectTokenBean.getTokenId());
                        }
                    }
                }
            });
            final int finalPosition =position;
            BHexApplication.getMainHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    tokenTabs.setCurrentTab(finalPosition);
                }
            }, 100);
        }
    }

    /**
     * 切换买卖或者token
     *
     * @param selectTokenBean
     */
    private void switchBuyOrSellOrToken(OtcConfigResponse.TokenBean selectTokenBean) {
        if (selectTokenBean != null) {
            getPresenter().requestOtcListBySwitchB(false, isWantBuy, selectTokenBean);
        }
    }

    private void startFilter(String param) {
        getPresenter().setFilter(param);
        popWindowFilter.dismiss();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    @Override
    public void showConfigInfo(OtcConfigResponse configResponse) {
//        currentConfigData = configResponse;
//        //设置默认法币
//        List<OtcConfigResponse.CurrencyBean> currencyList = currentConfigData.getCurrency();
//        for (OtcConfigResponse.CurrencyBean currencyBean : currencyList) {
//            String currencyId = currencyBean.getCurrencyId();
//            UserInfoBean userInfo = UserManager.getInstance().getUserInfo();
//            if (userInfo != null) {
//                String currency = userInfo.getCurrency();
//                if (!TextUtils.isEmpty(currency)) {
//                    if (currency.equals(currencyId)) {
//                        selectCurrencyBean = currencyBean;
//                        break;
//                    }
//                }
////                if ("CNY".equals(currencyId)) {
////                    selectCurrencyBean = currencyBean;
////                    break;
////                }
//            }
//        }
        //显示币对
//        showSwitchBlist(configResponse);
    }

    @Override
    public void showOtcUserInfo(OtcUserInfo response) {
        String nickName = response.getNickName();
        isSetNickName = !TextUtils.isEmpty(nickName);
        verifyFlag = response.isVerifyFlag();
        bindTradePwd = response.isBindTradePwd();
        whiteFlag = response.getWhiteFlag();
        if (whiteFlag == 1) {
            otcAdsManage.setVisibility(View.VISIBLE);
            otcAdsManageLine.setVisibility(View.VISIBLE);
        } else {
            otcAdsManage.setVisibility(View.GONE);
            otcAdsManageLine.setVisibility(View.GONE);
        }
        //TODO 支付方式设置
        isCompleteBaseInfo = !TextUtils.isEmpty(nickName) && verifyFlag && bindTradePwd;
    }

    @Override
    public void setUserInfoComplete(boolean hasPayway) {
        isHasPayway = hasPayway;
    }

    @Override
    public void hasPendingOrder(boolean hasPendingOrder, int count) {
        if (hasPendingOrder) {
            pendingOrderTips.setText(getString(R.string.string_tips_pengding_order, count));
            AnimalUtils.scaleAnimRun(pendingOrderTips, 0.0f, 1.0f);
            pendingOrderTips.setVisibility(View.VISIBLE);
        } else {
            AnimalUtils.scaleAnimRun(pendingOrderTips, 1.0f, 0.0f);
            pendingOrderTips.setVisibility(View.GONE);
        }
    }


    private void showCurrencyGrid() {
        List<OtcConfigResponse.CurrencyBean> currencyList = getPresenter().getCurrencyList();
        if (currencyList != null) {
            final OtcCurrencyFilterAdapter currencyAdapter = new OtcCurrencyFilterAdapter(currencyList);
            currencyAdapter.isFirstOnly(false);
//            otcSwitchBAdapterBuy.setEmptyView(emptyView);
            currencyRv.setLayoutManager(new GridLayoutManager(getActivity(), 3));
//            currencyRv.setItemAnimator(new DefaultItemAnimator());
            currencyRv.setAdapter(currencyAdapter);

            currencyAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                    List<OtcConfigResponse.CurrencyBean> currencyListData = adapter.getData();
                    for (int i = 0; i < currencyListData.size(); i++) {
                        OtcConfigResponse.CurrencyBean currencyBean = currencyListData.get(i);
                        if (i == position) {
                            preSelectCurrency = currencyBean;
                        }
                        currencyBean.setLocalSelect(i == position);
                    }
                    currencyAdapter.notifyDataSetChanged();

                }
            });
        }
    }

    public void showSwitchBlist(List<OtcConfigResponse.TokenBean> tokenList) {
        if (tokenList != null) {
            OtcSwitchBAdapter otcSwitchBAdapterBuy = new OtcSwitchBAdapter(tokenList);
            otcSwitchBAdapterBuy.isFirstOnly(false);
//            otcSwitchBAdapterBuy.setEmptyView(emptyView);
            recyclerViewBuy.setLayoutManager(new LinearLayoutManager(getActivity()));
            recyclerViewBuy.setItemAnimator(new DefaultItemAnimator());
            recyclerViewBuy.setAdapter(otcSwitchBAdapterBuy);

            OtcSwitchBAdapter otcSwitchBAdapterSell = new OtcSwitchBAdapter(tokenList);
            otcSwitchBAdapterSell.isFirstOnly(false);
//            otcSwitchBAdapterSell.setEmptyView(emptyView);
            recyclerViewSell.setLayoutManager(new LinearLayoutManager(getActivity()));
            recyclerViewSell.setItemAnimator(new DefaultItemAnimator());
            recyclerViewSell.setAdapter(otcSwitchBAdapterSell);
            otcSwitchBAdapterBuy.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                    popWindowSwitchAds.dismiss();
                    List<OtcConfigResponse.TokenBean> tokenListData = adapter.getData();
                    OtcConfigResponse.TokenBean tokenBean = tokenListData.get(position);
                    getPresenter().requestOtcListBySwitchB(false, true, tokenBean);
                }
            });
            otcSwitchBAdapterSell.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                    popWindowSwitchAds.dismiss();
                    List<OtcConfigResponse.TokenBean> tokenListData = adapter.getData();
                    OtcConfigResponse.TokenBean tokenBean = tokenListData.get(position);
                    getPresenter().requestOtcListBySwitchB(false, false, tokenBean);
                }
            });
        }
    }

    @Override
    public void showTokenInfo(boolean isBuyOrSell, OtcConfigResponse.TokenBean defaultTokenBean) {
        if (getActivity() != null) {
            String title = getString(isBuyOrSell ? R.string.string_otc_buy : R.string.string_otc_sell) + " " + defaultTokenBean.getTokenName();
            titleTx.setText(title);
        }
    }

    @Override
    public void showIndexPrice(OtcLastPriceResponse lastPriceResponse, OtcConfigResponse.TokenBean tokenBean, String currencyId) {
        if (isAdded()) {
            viewFinder.textView(R.id.otc_reference_index).setText(getResources().getString(R.string.string_format_otc_reference_index, tokenBean.getTokenName(), lastPriceResponse.getLastPrice(), currencyId));
        }
    }

    @Override
    public void showOtcList(final List<OtcListResponse.OtcItemBean> items) {
        List<OtcListResponse.OtcItemBean> mItems = OtcSort.sortOTCList(getActivity(), items);
        if (otcListAdapter == null) {
            otcListAdapter = new OtcListAdapter(mItems);
            otcListAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                    UserInfo.LoginAndGoin(getActivity(), new LoginResultCallback() {
                        @Override
                        public void onLoginSucceed() {
                            super.onLoginSucceed();
                            if (view.getId() == R.id.itemView || view.getId() == R.id.btn_create_otc_order) {
//                        if (view.getId()==R.id.btn_create_otc_order){
                                List<OtcListResponse.OtcItemBean> data = adapter.getData();
                                OtcListResponse.OtcItemBean otcItemBean = data.get(position);
                                if (otcItemBean == null || getPresenter().getConfig() == null) {
                                    ToastUtils.showShort(getActivity(), getString(R.string.string_data_exception));
                                    return;
                                }
                                goOtcDetail(otcItemBean);
                            }
                        }
                    });
                }
            });

            otcListAdapter.isFirstOnly(false);
            otcListAdapter.setOnLoadMoreListener(this, recyclerView);
            otcListAdapter.setEnableLoadMore(true);

            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            recyclerView.setItemAnimator(new DefaultItemAnimator());
            otcListAdapter.setEmptyView(emptyView);
            recyclerView.setAdapter(otcListAdapter);
        } else {
            otcListAdapter.setNewData(mItems);
        }
    }

    /**
     * 跳转到 - OTC下单详情页
     *
     * @param otcItemBean
     */
    private void goOtcDetail(final OtcListResponse.OtcItemBean otcItemBean) {

        IntentUtils.checkUserIsBindPasswd(getActivity(), new IntentUtils.BindPasswdListener() {
            @Override
            public void onBefore() {
                getUI().showProgressDialog("", "");
            }

            @Override
            public void onFinish() {
                getUI().dismissProgressDialog();
            }

            @Override
            public void onError(Throwable error) {

            }

            @Override
            public void onResult(boolean bindPassword) {
                if (bindPassword) {
                    //Otc信息是否已经完善
                    if (!isHasPayway || !isCompleteBaseInfo) {
                        OtcUserApi.getOtcUserInfo(new SimpleResponseListener<OtcUserInfo>(){
                            @Override
                            public void onBefore() {
                                super.onBefore();
                                showProgressDialog();
                            }

                            @Override
                            public void onFinish() {
                                super.onFinish();
                                dismissProgressDialog();
                            }

                            @Override
                            public void onSuccess(OtcUserInfo response) {
                                super.onSuccess(response);
                                if (CodeUtils.isSuccess(response)) {
                                    showOtcUserInfo(response);
                                    OtcPayChannelApi.getPayChannelList(new SimpleResponseListener<OtcPayChannelListResponse>(){
                                        @Override
                                        public void onBefore() {
                                            super.onBefore();
                                            showProgressDialog();
                                        }

                                        @Override
                                        public void onFinish() {
                                            super.onFinish();
                                            dismissProgressDialog();
                                        }

                                        @Override
                                        public void onSuccess(OtcPayChannelListResponse response) {
                                            super.onSuccess(response);
                                            if (CodeUtils.isSuccess(response)) {
                                                List<OtcPaymentChannelBean> payChannelList = response.getArray();
                                                if (payChannelList != null) {
                                                    if (payChannelList.size()>0) {
                                                        //根据用户支付通道 大于 > 0
                                                        isHasPayway = true;
                                                    } else {
                                                        isHasPayway = false;
                                                    }
                                                } else {
                                                    isHasPayway = false;
                                                }
                                                if (!isHasPayway || !isCompleteBaseInfo) {
                                                    goOtcSettings();
                                                    return;
                                                }
                                                IntentUtils.goOtcDetail(getActivity(), otcItemBean, getPresenter().getConfig());
                                            }
                                        }

                                        @Override
                                        public void onError(Throwable error) {
                                            super.onError(error);
                                        }
                                    });
                                }
                            }

                            @Override
                            public void onError(Throwable error) {
                                super.onError(error);
                            }
                        });
                        return;
                    }
                    IntentUtils.goOtcDetail(getActivity(), otcItemBean, getPresenter().getConfig());
                }
            }
        });
    }

    /**
     * 去otc设置页
     */
    private void goOtcSettings() {
        DialogUtils.showDialog(getActivity(), "", getString(R.string.string_otc_complete_user_info_tips), getString(R.string.string_set), getString(R.string.string_not_need), true, new DialogUtils.OnButtonEventListener() {
            @Override
            public void onConfirm() {
                IntentUtils.goOtcSettings(getActivity());
            }

            @Override
            public void onCancel() {

            }
        });
    }

    @Override
    public void onLoadMoreRequested() {
//        swipeRefresh.postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                adapter.loadComplete();
//            }
//        }, 500);
        getPresenter().loadMore();
    }

    @Override
    public void loadMoreComplete() {
        if (otcListAdapter != null) {
            otcListAdapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {
        if (otcListAdapter != null) {
            otcListAdapter.loadMoreFail();
        }
    }

    @Override
    public void loadEnd() {
        if (otcListAdapter != null) {
            otcListAdapter.loadMoreEnd();
        }
    }

    @Override
    public void onRefresh(RefreshLayout refreshLayout) {
        getPresenter().requestOtcList(false);
        refreshLayout.finishRefresh(100);
    }

}
