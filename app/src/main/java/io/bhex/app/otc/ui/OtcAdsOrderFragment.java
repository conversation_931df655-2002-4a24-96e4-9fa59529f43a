/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcAdsOrderFragment.java
 *   @Date: 19-2-28 下午4:58
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.ui;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseFragment;
import io.bhex.app.otc.adapter.OtcAdsListAdapter;
import io.bhex.app.otc.adapter.OtcOrdersListAdapter;
import io.bhex.app.otc.presenter.OtcAdsOrderPresenter;
import io.bhex.app.utils.DialogUtils;
import io.bhex.app.utils.IntentUtils;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.otc.bean.OtcAdsListResponse;
import io.bhex.sdk.otc.bean.OtcOrderInfoResponse;

public class OtcAdsOrderFragment extends BaseFragment<OtcAdsOrderPresenter,OtcAdsOrderPresenter.OtcAdsOrderUI> implements OtcAdsOrderPresenter.OtcAdsOrderUI, OnRefreshListener, BaseQuickAdapter.RequestLoadMoreListener {
    private SmartRefreshLayout swipeRefresh;
    private RecyclerView recyclerView;
    private View emptyView;
    private OtcAdsListAdapter adapter;
    private List<OtcAdsListResponse.AdBean> showOrders;

    @Override
    protected OtcAdsOrderPresenter.OtcAdsOrderUI getUI() {
        return this;
    }

    @Override
    protected OtcAdsOrderPresenter createPresenter() {
        return new OtcAdsOrderPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_otc_ads_order_layout,null,false);
    }



    @Override
    protected void initViews() {
        super.initViews();
        swipeRefresh = viewFinder.find(R.id.refreshLayout);
        swipeRefresh.setOnRefreshListener(this);
        recyclerView = viewFinder.find(R.id.recyclerView);

        LayoutInflater layoutInflater = LayoutInflater.from(getActivity());
        emptyView = layoutInflater.inflate(R.layout.empty_layout, swipeRefresh, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(200);
        emptyView.setLayoutParams(layoutParams);

    }

    @Override
    protected void addEvent() {
        super.addEvent();

    }

    @Override
    public void onLoadMoreRequested() {
//        swipeRefresh.postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                adapter.loadComplete();
//            }
//        }, 500);
        getPresenter().loadMore();
    }

    @Override
    public void loadMoreComplete() {
        if (adapter != null) {
            adapter.loadMoreComplete();
        }
    }

    @Override
    public void loadMoreFailed() {
        if (adapter != null) {
            adapter.loadMoreFail();
        }
    }

    @Override
    public void loadEnd() {
        if (adapter != null) {
            adapter.loadMoreEnd();
        }
    }

//    @Override
//    public List<OtcOrderInfoResponse> getShowOrders() {
//        return showOrders;
//    }


    @Override
    public void onRefresh(RefreshLayout refreshLayout) {
        getPresenter().refreshOrders();
        refreshLayout.finishRefresh(1000);
    }

    List<OtcOrderInfoResponse> appealOrders = new ArrayList<>();
    @Override
    public void showOrderList(List<OtcAdsListResponse.AdBean> currentOrders) {
        showOrders = currentOrders;
//        //排序把申诉的列表排到最上面
//        appealOrders.clear();
//        for (OtcOrderInfoResponse currentOrder : currentOrders) {
//            if (currentOrder.getStatus()==30) {
//                appealOrders.add(currentOrder);
//            }
//        }
//        for (OtcOrderInfoResponse appealOrder : appealOrders) {
//            currentOrders.remove(appealOrder);
//        }
//        currentOrders.addAll(0,appealOrders);

        if (adapter == null) {
//            if (currentOrders != null) {
//                if (currentOrders.isEmpty()) {
//                    currentOrders = null;
//                }
//            }
            adapter = new OtcAdsListAdapter(getActivity(),currentOrders);
//            adapter.openLoadAnimation(BaseQuickAdapter.SCALEIN);
            adapter.isFirstOnly(false);
            adapter.setOnLoadMoreListener(this,recyclerView);
//            swipeRefresh.setColorSchemeColors(Color.RED, Color.BLUE, Color.GREEN);
//            swipeRefresh.setOnRefreshListener(this);

            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            recyclerView.setItemAnimator(new DefaultItemAnimator());
//        recyclerView.addItemDecoration(new DividerItemDecoration(getContext(), LinearLayoutManager.VERTICAL));

            adapter.setEmptyView(emptyView);
            recyclerView.setAdapter(adapter);
            adapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                    OtcAdsListResponse.AdBean adsBean = (OtcAdsListResponse.AdBean) adapter.getData().get(position);
                    if (view.getId()==R.id.btn_modify) {
                        showModifyDialog(adsBean);

                    }else if(view.getId()==R.id.btn_delete){
                        showOfflineAdsDialog(adsBean);
                    }
                }
            });
        } else {
            adapter.setNewData(currentOrders);
        }
    }

    /**
     * 下架广告
     * @param adsBean
     */
    private void showOfflineAdsDialog(final OtcAdsListResponse.AdBean adsBean) {
        DialogUtils.showDialog(getActivity(), "", getString(R.string.string_otc_offline_ads_tips_content), getString(R.string.string_sure), getString(R.string.string_cancel), true, new DialogUtils.OnButtonEventListener() {
            @Override
            public void onConfirm() {
                getPresenter().offlineAds(adsBean,false);
            }

            @Override
            public void onCancel() {

            }
        });
    }

    /**
     * 修改广告
     * @param adsBean
     */
    private void showModifyDialog(final OtcAdsListResponse.AdBean adsBean) {
        DialogUtils.showDialog(getActivity(), "", getString(R.string.string_otc_offline_modify_ads_tips_content), getString(R.string.string_sure), getString(R.string.string_cancel), true, new DialogUtils.OnButtonEventListener() {
            @Override
            public void onConfirm() {
                getPresenter().offlineAds(adsBean,true);
            }

            @Override
            public void onCancel() {

            }
        });
    }
}
