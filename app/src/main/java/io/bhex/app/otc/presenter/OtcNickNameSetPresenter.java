/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcNickNameSetPresenter.java
 *   @Date: 19-1-22 下午6:29
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.presenter;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.otc.OtcUserApi;

public class OtcNickNameSetPresenter extends BasePresenter<OtcNickNameSetPresenter.OtcNickNameSetUI> {
    public void setNickName(String nickNmaeStr) {
        OtcUserApi.setOtcUserNickName(nickNmaeStr,new SimpleResponseListener<ResultResponse>(){
            @Override
            public void onBefore() {
                super.onBefore();
                getUI().showProgressDialog("","");
            }

            @Override
            public void onFinish() {
                super.onFinish();
                getUI().dismissProgressDialog();
            }

            @Override
            public void onSuccess(ResultResponse response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response,true)) {
                    ToastUtils.showLong(getActivity(),getString(R.string.string_set_success));
                    getActivity().finish();
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }

    public interface OtcNickNameSetUI extends AppUI{

    }
}
