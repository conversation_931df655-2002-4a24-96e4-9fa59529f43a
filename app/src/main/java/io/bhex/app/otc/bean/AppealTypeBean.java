/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AppealTypeBean.java
 *   @Date: 19-1-29 下午4:58
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.bean;

import io.bhex.baselib.network.response.BaseResponse;

/**
 *申诉类型实体
 */
public class AppealTypeBean extends BaseResponse {
    private int appealType;
    private String appealTheme;

    public int getAppealType() {
        return appealType;
    }

    public void setAppealType(int appealType) {
        this.appealType = appealType;
    }

    public String getAppealTheme() {
        return appealTheme;
    }

    public void setAppealTheme(String appealTheme) {
        this.appealTheme = appealTheme;
    }
}
