/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcNickNameSetActivity.java
 *   @Date: 19-1-22 下午6:30
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.ui;

import android.text.TextUtils;
import android.view.View;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.otc.presenter.OtcNickNameSetPresenter;
import io.bhex.app.view.InputView;
import io.bhex.baselib.utils.ToastUtils;

public class OtcNickNameSetActivity extends BaseActivity<OtcNickNameSetPresenter,OtcNickNameSetPresenter.OtcNickNameSetUI> implements OtcNickNameSetPresenter.OtcNickNameSetUI, View.OnClickListener {
    private InputView nickNameInput;

    @Override
    protected int getContentView() {
        return R.layout.activity_otc_set_nick_name_layout;
    }

    @Override
    protected OtcNickNameSetPresenter createPresenter() {
        return new OtcNickNameSetPresenter();
    }

    @Override
    protected OtcNickNameSetPresenter.OtcNickNameSetUI getUI() {
        return this;
    }

    @Override
    protected boolean isNeedLogin() {
        return true;
    }

    @Override
    protected void initView() {
        super.initView();
        nickNameInput = viewFinder.find(R.id.nickName);
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.btn_sure).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        String nickNmaeStr = nickNameInput.getInputStringNoTrim();

        if (TextUtils.isEmpty(nickNmaeStr)) {
            ToastUtils.showLong(this,getString(R.string.string_input_nickname_hint));
            return;
        }
        if (Pattern.compile("\\s+").matcher(nickNmaeStr).find()) {
            ToastUtils.showLong(this,getString(R.string.string_nickname_reg));
            return;
        }
        if (TextUtils.isEmpty(nickNmaeStr)) {
            ToastUtils.showLong(this,getString(R.string.string_input_nickname_hint));
            return;
        }
        int length = nickNmaeStr.length();
        if (length<2||length>16) {
            ToastUtils.showShort(getString(R.string.string_nick_length_tips));
            return;
        }
//        String regEx="[`~!@#$%^&*()_\\-+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
//        Pattern   p   =   Pattern.compile(regEx);
//        Matcher m   =   p.matcher(nickNmaeStr);
//        if (nickNmaeStr.matches(regEx)) {
//            ToastUtils.showShort(getString(R.string.string_nickname_reg));
//            return;
//        }
        getPresenter().setNickName(nickNmaeStr);
    }
}
