/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcReceiptChannelAdapter.java
 *   @Date: 19-2-1 上午11:26
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.adapter;

import android.content.Context;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.MultipleItemRvAdapter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import cn.bingoogolapple.swipeitemlayout.BGASwipeItemLayout;
import io.bhex.app.otc.adapter.provider.OtcReceiptAliOrWeProvider;
import io.bhex.app.otc.adapter.provider.OtcReceiptUnionProvider;
import io.bhex.app.otc.listener.AdapterItemClickListener;
import io.bhex.app.view.SwipeItemDelegate;
import io.bhex.sdk.otc.bean.OtcConfigResponse;
import io.bhex.sdk.otc.bean.OtcPaymentChannelBean;

public class OtcReceiptChannelAdapter extends MultipleItemRvAdapter<OtcPaymentChannelBean,BaseViewHolder> implements SwipeItemDelegate<OtcPaymentChannelBean>{
    public static final int RECEIPT_UNION = 0;
    public static final int RECEIPT_ALI_OR_WECHAT = 1;
    private final AdapterItemClickListener mClickListener;
    private final Context mOtcContext;
    private final HashMap<String, OtcConfigResponse.BankBean> mBankMap;

    public OtcReceiptChannelAdapter(Context context, @Nullable List<OtcPaymentChannelBean> data, HashMap<String, OtcConfigResponse.BankBean> bankMap, AdapterItemClickListener itemClickListener) {
        super(data);
        mClickListener = itemClickListener;
        mOtcContext = context;
        //构造函数若有传其他参数可以在调用finishInitialize()之前进行赋值，赋值给全局变量
        //这样getViewType()和registerItemProvider()方法中可以获取到传过来的值
        //getViewType()中可能因为某些业务逻辑，需要将某个值传递过来进行判断，返回对应的viewType
        //registerItemProvider()中可以将值传递给ItemProvider

        //If the constructor has other parameters, it needs to be assigned before calling finishInitialize() and assigned to the global variable
        // This getViewType () and registerItemProvider () method can get the value passed over
        // getViewType () may be due to some business logic, you need to pass a value to judge, return the corresponding viewType
        //RegisterItemProvider() can pass value to ItemProvider
        mBankMap = bankMap;
        finishInitialize();
    }

    @Override
    protected int getViewType(OtcPaymentChannelBean entity) {
        //根据实体类判断并返回对应的viewType，具体判断逻辑因业务不同，这里这是简单通过判断type属性
        //According to the entity class to determine and return the corresponding viewType,
        //the specific judgment logic is different because of the business, here is simply by judging the type attribute
        int payType = entity.getPaymentType();
        if (payType==0) {
            //文本消息
            return RECEIPT_UNION;
        }else {
            //图片消息
            return RECEIPT_ALI_OR_WECHAT;
        }

    }

    @Override
    public void registerItemProvider() {
        //注册相关的条目provider
        //Register related entries provider
        mProviderDelegate.registerProvider(new OtcReceiptUnionProvider(this,mBankMap,mClickListener));
        mProviderDelegate.registerProvider(new OtcReceiptAliOrWeProvider(this,mClickListener));
    }

    /**
     * 当前处于打开状态的item
     */
    private List<BGASwipeItemLayout> mOpenedSil = new ArrayList<>();
    public void closeOpenedSwipeItemLayoutWithAnim() {
        for (BGASwipeItemLayout sil : mOpenedSil) {
            sil.closeWithAnim();
        }
        mOpenedSil.clear();
    }

    @Override
    public void deleteItem(OtcPaymentChannelBean item) {

//        OtcPaymentChannelBean  channelBean =(OtcPaymentChannelBean) item;
//        DialogUtils.showTradePasswdDialog(mContext, mContext.getString(R.string.string_finance_passwd), "", "", true, new DialogUtils.OnOtcEventListener() {
//            @Override
//            public void onConfirm(String editContent) {
//                closeOpenedSwipeItemLayoutWithAnim();
//                ToastUtils.showShort(editContent);
////                TODO 删除收款方式
//            }
//
//            @Override
//            public void onCancel() {
//
//            }
//        });
    }

    @Override
    public void onBGASwipeItemLayoutOpened(BGASwipeItemLayout swipeItemLayout) {
        closeOpenedSwipeItemLayoutWithAnim();
        mOpenedSil.add(swipeItemLayout);
    }

    @Override
    public void onBGASwipeItemLayoutClosed(BGASwipeItemLayout swipeItemLayout) {
        mOpenedSil.remove(swipeItemLayout);
    }

    @Override
    public void onBGASwipeItemLayoutStartOpen(BGASwipeItemLayout swipeItemLayout) {
        closeOpenedSwipeItemLayoutWithAnim();
    }
}
