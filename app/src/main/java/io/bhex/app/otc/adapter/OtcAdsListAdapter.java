/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OtcAdsListAdapter.java
 *   @Date: 19-3-4 下午6:17
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.otc.adapter;

import android.content.Context;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.MultipleItemRvAdapter;

import java.util.List;

import io.bhex.app.otc.adapter.provider.OtcAdsOrderCompletedProvider;
import io.bhex.app.otc.adapter.provider.OtcAdsOrderHandingProvider;
import io.bhex.sdk.otc.bean.OtcAdsListResponse;

public class OtcAdsListAdapter extends MultipleItemRvAdapter<OtcAdsListResponse.AdBean,BaseViewHolder> {
    public static final int ORDER_STATUS_HANDING = 0;
    public static final int ORDER_STATUS_COMPLETED = 1;
    private Context mOtcContext;

    public OtcAdsListAdapter(Context context, @Nullable List<OtcAdsListResponse.AdBean> data) {
        super(data);
        mOtcContext = context;
        //构造函数若有传其他参数可以在调用finishInitialize()之前进行赋值，赋值给全局变量
        //这样getViewType()和registerItemProvider()方法中可以获取到传过来的值
        //getViewType()中可能因为某些业务逻辑，需要将某个值传递过来进行判断，返回对应的viewType
        //registerItemProvider()中可以将值传递给ItemProvider

        //If the constructor has other parameters, it needs to be assigned before calling finishInitialize() and assigned to the global variable
        // This getViewType () and registerItemProvider () method can get the value passed over
        // getViewType () may be due to some business logic, you need to pass a value to judge, return the corresponding viewType
        //RegisterItemProvider() can pass value to ItemProvider

        finishInitialize();
    }

    @Override
    protected int getViewType(OtcAdsListResponse.AdBean adBean) {
        int status = adBean.getStatus();//状态（10在线，20撤销，30完全成交）
        if (status==10){
            return ORDER_STATUS_HANDING;
        }else{
            return ORDER_STATUS_COMPLETED;
        }
    }

    @Override
    public void registerItemProvider() {
        mProviderDelegate.registerProvider(new OtcAdsOrderHandingProvider());
        mProviderDelegate.registerProvider(new OtcAdsOrderCompletedProvider());
    }
}
