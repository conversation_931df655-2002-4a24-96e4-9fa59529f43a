//package io.bhex.app.safe.recaptcha;
//
//import android.app.Activity;
//import androidx.annotation.NonNull;
//import android.util.Log;
//
//import com.google.android.gms.common.api.ApiException;
//import com.google.android.gms.common.api.CommonStatusCodes;
//import com.google.android.gms.safetynet.SafetyNet;
//import com.google.android.gms.safetynet.SafetyNetApi;
//import com.google.android.gms.safetynet.SafetyNetClient;
//import com.google.android.gms.tasks.OnFailureListener;
//import com.google.android.gms.tasks.OnSuccessListener;
//
//import io.bhex.baselib.utils.ToastUtils;
//
///**
// * ================================================
// * 描   述：BhexRecaptcha 人机校验
// * ================================================
// */
//
//public class BhexRecaptcha {
//    private static final String TAG = "BhexRecaptcha";
//    private final Activity mContext;
//    private final SafetyNetClient client;
//    private static BhexRecaptcha bhRecaptcha;
//
//    public BhexRecaptcha(Activity context) {
//        mContext = context;
//        client = SafetyNet.getClient(context);
//    }
//
//    public static BhexRecaptcha getInstance(Activity context){
//        if (bhRecaptcha == null) {
//           bhRecaptcha =  new BhexRecaptcha(context);
//        }
//        return bhRecaptcha;
//    }
//
//    /**
//     * 验证人机
//     */
//    public void verifyWithRecaptcha(final VerifyCallBack callBack){
//        client.verifyWithRecaptcha("6LdV1mcUAAAAADSQyNvxQkCbgL_1GpVlQEGLvuV4")
//                .addOnSuccessListener(mContext,
//                        new OnSuccessListener<SafetyNetApi.RecaptchaTokenResponse>() {
//                            @Override
//                            public void onSuccess(SafetyNetApi.RecaptchaTokenResponse response) {
//                                // Indicates communication with reCAPTCHA service was
//                                // successful.
//                                String userResponseToken = response.getTokenResult();
//                                if (!userResponseToken.isEmpty()) {
//                                    // Validate the user response token using the
//                                    // reCAPTCHA siteverify API.
//                                    ToastUtils.showShort(userResponseToken);
//                                }
//                                callBack.onSuccess(userResponseToken);
//                            }
//                        })
//                .addOnFailureListener(mContext, new OnFailureListener() {
//                    @Override
//                    public void onFailure(@NonNull Exception e) {
//                        if (e instanceof ApiException) {
//                            // An error occurred when communicating with the
//                            // reCAPTCHA service. Refer to the status code to
//                            // handle the error appropriately.
//                            ApiException apiException = (ApiException) e;
//                            int statusCode = apiException.getStatusCode();
//                            Log.e(TAG, "Error: " + CommonStatusCodes
//                                    .getStatusCodeString(statusCode));
//                        } else {
//                            // A different, unknown type of error occurred.
//                            Log.e(TAG, "Error: " + e.getMessage());
//                        }
//                        callBack.onFailure(e);
//                    }
//                });
//    }
//
//    public interface VerifyCallBack{
//        void onSuccess(String token);
//        void onFailure(@NonNull Exception e);
//    }
//
//}
