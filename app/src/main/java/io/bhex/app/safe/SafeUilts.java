/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: SafeUilts.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.safe;

import android.Manifest;
import android.app.KeyguardManager;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.text.TextUtils;
import android.widget.Toast;

import androidx.core.app.ActivityCompat;
import androidx.core.hardware.fingerprint.FingerprintManagerCompat;

import io.bhex.app.R;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.core.SPEx;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.sdk.account.UserManager;


public class SafeUilts {
    private static final String TAG = "指纹判断";

    /**
     * 是否开启了安全校验
     * @return
     */
    public static boolean isOpenSageVerify(){
        boolean fingerOpen = UserManager.getInstance().isFingerSetOpenStatus();
        String gesturePwd = SPEx.get(AppData.SPKEY.GESTURE_PWD_KEY,"");
        //开启了指纹或者手势
        return fingerOpen || !TextUtils.isEmpty(gesturePwd);
    }

    public static boolean isFinger(Context context) {
        FingerprintManagerCompat manager = FingerprintManagerCompat.from(context);

        //android studio 上，没有这个会报错
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.USE_FINGERPRINT) != PackageManager.PERMISSION_GRANTED) {
            Toast.makeText(context, context.getResources().getString(R.string.string_no_fingerprint_permission), Toast.LENGTH_SHORT).show();
            return false;
        }
        Log(TAG, "有指纹权限");
        //判断硬件是否支持指纹识别
        if (!manager.isHardwareDetected()) {
            Toast.makeText(context, context.getResources().getString(R.string.fingerprint_no_hardware), Toast.LENGTH_SHORT).show();
            return false;
        }
        Log(TAG, "有指纹模块");
        //判断是否有指纹录入
        if (!manager.hasEnrolledFingerprints()) {
            Toast.makeText(context, context.getResources().getString(R.string.fingerprint_create_finger_first_hint), Toast.LENGTH_SHORT).show();
            return false;
        }
        Log(TAG, "已录入指纹");
        //判断 是否开启锁屏密码
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            KeyguardManager mKeyManager = (KeyguardManager) context.getSystemService(Context.KEYGUARD_SERVICE);
            if (!mKeyManager.isKeyguardSecure()) {
                Toast.makeText(context, context.getResources().getString(R.string.fingerprint_no_lock_screen_pwd), Toast.LENGTH_SHORT).show();
                return false;
            }
        }
        Log(TAG, "已开启锁屏密码");

        return true;
    }

    /**
     * 是否支持指纹
     * @param context
     * @return
     */
    public static boolean isSupportFinger(Context context) {
        FingerprintManagerCompat manager = FingerprintManagerCompat.from(context);

        //android studio 上，没有这个会报错
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.USE_FINGERPRINT) != PackageManager.PERMISSION_GRANTED) {
//            Toast.makeText(context, context.getResources().getString(R.string.string_no_fingerprint_permission), Toast.LENGTH_SHORT).show();
            return false;
        }
        Log(TAG, "有指纹权限");
        //判断硬件是否支持指纹识别
        if (!manager.isHardwareDetected()) {
//            Toast.makeText(context, context.getResources().getString(R.string.fingerprint_no_hardware), Toast.LENGTH_SHORT).show();
            return false;
        }
        Log(TAG, "有指纹模块");
        return true;
    }

    private static void Log(String tag, String msg) {
        DebugLog.d(tag, msg);
    }
}
