/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: DeepSEListener.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.safe;

import com.geetest.sensebot.listener.BaseSEListener;

import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.ToastUtils;

/**
 * ================================================
 * 描   述：深度验证监听
 * ================================================
 */

public class DeepSEListener extends BaseSEListener {
    private static final String TAG = "DeepSEListener";

    /**
     * SDK内部show loading dialog
     */
    @Override
    public void onShowDialog() {
        DebugLog.i(TAG,"onShowDialog-->SDK show loading dialog！");
    }

    @Override
    public void onError(String errorCode, String error) {
        DebugLog.i(TAG,"onError-->errorCode:"+errorCode+", error: "+error);
//            ToastUtils.showShort(error);
    }

    /**
     * 验证码Dialog关闭
     * 1：webview的叉按钮关闭
     * 2：点击屏幕外关闭
     * 3：点击回退键关闭
     *
     * @param num
     */
    @Override
    public void onCloseDialog(int num) {
        DebugLog.i(TAG, "onCloseDialog-->" + num);
    }

    /**
     * show 验证码webview
     */
    @Override
    public void onDialogReady() {
        DebugLog.i(TAG,"onDialogReady-->SDK show captcha webview dialog! ");
//        ToastUtils.showShort("极验一打开");
    }

    /**
     * 验证成功
     * @param token
     */
    @Override
    public void onResult(String token) {
        DebugLog.i(TAG,"onResult: "+token);
    }
}
