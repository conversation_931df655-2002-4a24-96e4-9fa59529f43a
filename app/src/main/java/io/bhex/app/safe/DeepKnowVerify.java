/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: DeepKnowVerify.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.safe;

import android.content.Context;
import android.view.View;

import com.geetest.deepknow.DPAPI;
import com.geetest.deepknow.bean.DPJudgementBean;
import com.geetest.sensebot.SEAPI;

import io.bhex.app.BuildConfig;
import io.bhex.app.app.BHexApplication;

/**
 * ================================================
 * 描   述：深知验证
 * ================================================
 */

public class DeepKnowVerify {
    private static Context mContext;
    private static DeepKnowVerify ourInstance;
    private final SEAPI seapi;
    private final DPJudgementBean judgementBean;

    public static DeepKnowVerify getInstance(Context context) {
        mContext = context;
        ourInstance = new DeepKnowVerify(context);
        return ourInstance;
    }

    private DeepKnowVerify(Context context) {
        seapi = new SEAPI(context);
        judgementBean=new DPJudgementBean(BuildConfig.DEEPKNOW_ID,1,null);
    }

    public void ignoreDPView(View view,String activityName){
        //其中的第一个参数为具体的view,第二个参数为使用的activity的名字。
//        DPAPI.getInstance(mContext).ignoreDPView(view,activityName);
        DPAPI.getInstance(BHexApplication.getInstance()).ignoreDPView(view,activityName);
    }

    /**
     * 验证
     * @param listener
     */
    public void verify(DeepSEListener listener){
        seapi.onVerify(judgementBean,listener);
    }

    /**
     * 在页面关闭调用此方法
     */
    public void destroy(){
        seapi.destroy();
    }
}
