/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: DiscoveryFragment.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.discovery.ui;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.bumptech.glide.Glide;

import io.bhex.app.R;
import io.bhex.app.discovery.presenter.DiscoveryFragmentPresenter;
import io.bhex.app.base.BaseFragment;



public class DiscoveryFragment extends BaseFragment<DiscoveryFragmentPresenter,DiscoveryFragmentPresenter.DiscoveryFragmentUI> implements DiscoveryFragmentPresenter.DiscoveryFragmentUI {
    private ImageView img1,img2,img3,img4;

    @Override
    protected DiscoveryFragmentPresenter.DiscoveryFragmentUI getUI() {
        return this;
    }

    @Override
    protected DiscoveryFragmentPresenter createPresenter() {
        return new DiscoveryFragmentPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_discovery_layout,null,false);
    }

    @Override
    protected void initViews() {
        super.initViews();
        img1 = viewFinder.imageView(R.id.img1);
        img2 = viewFinder.imageView(R.id.img2);
        img3 = viewFinder.imageView(R.id.img3);
        img4 = viewFinder.imageView(R.id.img4);
//        String path ="https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1528726463893&di=9041a264ac415f2e92d4e5a790ff50d2&imgtype=0&src=http%3A%2F%2Fimg.zcool.cn%2Fcommunity%2F016c4f5721807732f875a3992ba4d6.jpg";
//        Glide.with(getActivity()).load(path).into(img1);
//        Glide.with(getActivity()).load(path).into(img2);
//        Glide.with(getActivity()).load(path).into(img3);
//        Glide.with(getActivity()).load(path).into(img4);
    }

    @Override
    protected void addEvent() {
        super.addEvent();

    }
}
