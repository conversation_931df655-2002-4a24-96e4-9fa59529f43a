package io.bhex.app.skin.listener;

import android.graphics.PorterDuff;

import io.bhex.app.R;
import io.bhex.app.account.ui.SettingActivity;
import io.bhex.app.app.BHexApplication;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.core.SPEx;
import skin.support.SkinCompatManager;
import skin.support.content.res.SkinCompatUserThemeManager;

public class NightIconFilter implements SkinCompatManager.SkinLoaderListener {

    /*

    private static APPConfig _instance;

    public static APPConfig getInstance() {
        if(_instance == null)
            _instance  = new APPConfig();
        return _instance;
    }
     */

    private static NightIconFilter _instance;

    public static NightIconFilter getInstance() {
        if (_instance == null) {
            _instance = new NightIconFilter();
        }
        return _instance;
    }

    @Override
    public void onStart() {

    }

    static int[] idList = {
            R.mipmap.tab_home,
            R.mipmap.tab_hangqing,
            R.mipmap.tab_trade,
            R.mipmap.tab_contract,
            R.mipmap.tab_asset,
            R.mipmap.icon_notice,
            R.mipmap.icon_safe,
            R.mipmap.btn_head_back,
            R.mipmap.btn_head_back_night,
            R.mipmap.icon_arrow_right
    };

    @Override
    public void onSuccess() {
        /*
        BHexApplication instance = BHexApplication.getInstance();
        int dark = SkinColorUtil.getDark(instance);
        if (CommonUtil.isBlackMode()) {
            for (int resId :
                    idList) {
                instance.getResources().getDrawable(resId).setColorFilter(dark, PorterDuff.Mode.SRC_ATOP);
            }

        } else {
            for (int resId :
                    idList) {
                instance.getResources().getDrawable(resId).clearColorFilter();
            }
        }
         */
    }

    @Override
    public void onFailed(String s) {

    }
}
