package io.bhex.app.skin.support.load;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;

import androidx.core.content.ContextCompat;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import io.bhex.app.R;
import io.bhex.app.app.BHexApplication;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.sdk.config.AppSetting;
import skin.support.load.SkinBuildInLoader;

public class NightDrawableLoader extends SkinBuildInLoader {

    private static NightDrawableLoader _instance;

    public static NightDrawableLoader getInstance() {
        if (_instance == null) {
            _instance = new NightDrawableLoader();
        }

        return _instance;
    }

    /*
    TODO: 需要设计重新给图的
        add(R.mipmap.icon_deposit_btn);
        add(R.mipmap.icon_withdraw_btn);
        add(R.mipmap.icon_exchange);
     */

    private static final Set<Integer> idSet = new HashSet<Integer>() {{
//        add(R.mipmap.tab_home);
//        add(R.mipmap.tab_hangqing);
//        add(R.mipmap.tab_trade);
//        add(R.mipmap.tab_contract);
//        add(R.mipmap.tab_asset);
//        add(R.mipmap.icon_notice);
        add(R.mipmap.icon_dark_mode);
        add(R.mipmap.icon_safe);
        add(R.mipmap.icon_account_auth);
        add(R.mipmap.icon_grade);
        add(R.mipmap.icon_order);
//        add(R.mipmap.icon_address);
        add(R.mipmap.icon_my_invite);
        add(R.mipmap.icon_my);
        add(R.mipmap.icon_voucher);
        add(R.mipmap.icon_pointcard);
        add(R.mipmap.icon_account_announcement);
        add(R.mipmap.icon_account_contact);
        add(R.mipmap.icon_account_help);
        add(R.mipmap.icon_help);
        add(R.mipmap.icon_about);
        add(R.mipmap.btn_head_back);
        add(R.mipmap.icon_arrow_right);
        add(R.mipmap.icon_arrow_down_black);
        add(R.mipmap.icon_favorite);
        add(R.mipmap.icon_favorite_tab);
        add(R.mipmap.icon_go_share);
        add(R.mipmap.icon_drawer);
        add(R.mipmap.icon_otc_menu);
        add(R.mipmap.icon_otc_filter);
        add(R.mipmap.icon_otc_title_arrow_down);
        add(R.mipmap.icon_book_setting);
        add(R.mipmap.icon_more_actions);
        add(R.mipmap.icon_close);
        add(R.mipmap.icon_search);
        add(R.mipmap.icon_address);
        add(R.mipmap.icon_scan);
        add(R.mipmap.icon_full_screen);
        add(R.mipmap.icon_kline);
//        add(R.mipmap.app_header_title);
        add(R.mipmap.icon_add);
        add(R.mipmap.icon_copy_content);
        add(R.mipmap.asset_transfer);
        add(R.mipmap.icon_more);
        add(R.mipmap.icon_message);
        add(R.mipmap.icon_timer);
        add(R.mipmap.icon_attachment);
        add(R.mipmap.icon_send);
        add(R.mipmap.icon_call);
        add(R.mipmap.icon_share);
        add(R.mipmap.page_dot);
        add(R.mipmap.page_dot_checked);
        add(R.mipmap.icon_cb_normal);
        add(R.mipmap.icon_edit);
        add(R.mipmap.icon_instruction_black);
        add(R.mipmap.icon_margin_filter);
        add(R.mipmap.icon_record);
    }};

    public static final int SKIN_LOADER_STRATEGY_NIGHT_DRAWABLE = 4;

    private static Map<Integer, Drawable> drawableMap = new HashMap<>();

//    private static final Set<Integer> idColorSet = new HashSet<Integer>() {{
//        add(R.color.green);
//        add(R.color.red);
//    }};
//
//    private static Map<String, ColorStateList> colorStateMap = new HashMap<>();
//
//    @Override
//    public ColorStateList getColor(Context context, String skinName, int resId) {
//        if (idColorSet.contains(resId)) {
//            boolean greenRose = AppSetting.getInstance().isGreenRose();
//            String key = resId + "-" + greenRose;
//            if (colorStateMap.containsKey(key)) {
//                return colorStateMap.get(key);
//            } else {
//                ColorStateList colorStateList;
//                if (resId == R.color.red) {
//                    colorStateList = context.getResources().getColorStateList(greenRose ? R.color.red : R.color.green);
//                }else{
//                    colorStateList = context.getResources().getColorStateList(greenRose ? R.color.green : R.color.red);
//                }
//                colorStateMap.put(key, colorStateList);
//                return colorStateList;
//            }
//        } else {
//            return null;
//        }
//    }

    @Override
    public Drawable getDrawable(Context context, String skinName, int resId) {
        if (skinName.equals("night") && idSet.contains(resId)) {
            int dark = SkinColorUtil.getDark(context);

            if (drawableMap.containsKey(resId)) {
                return drawableMap.get(resId);
            } else {
                Drawable mutateDrawable = context.getResources().getDrawable(resId).mutate();
                mutateDrawable.setColorFilter(dark, PorterDuff.Mode.SRC_ATOP);
                drawableMap.put(resId, mutateDrawable);
                return mutateDrawable;
            }
        } else {
            return null;
        }
    }

    public Drawable getDrawable(Context context, int resId) {
        int dark = ContextCompat.getColor(context,R.color.dark_night);
        Drawable mutateDrawable = context.getResources().getDrawable(resId).mutate();
        mutateDrawable.setColorFilter(dark, PorterDuff.Mode.SRC_ATOP);
        drawableMap.put(resId, mutateDrawable);
        return mutateDrawable;
    }

    @Override
    public int getType() {
        return SKIN_LOADER_STRATEGY_NIGHT_DRAWABLE;
    }
}
