/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: SkinDepthView.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.skin.view;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.DrawableRes;

import com.bhex.depth.DepthView;

import skin.support.widget.SkinCompatBackgroundHelper;
import skin.support.widget.SkinCompatSupportable;


public class SkinDepthView extends DepthView implements SkinCompatSupportable{
    private SkinCompatBackgroundHelper mBackgroundTintHelper;

    public SkinDepthView(Context context) {
        this(context, null);
    }

    public SkinDepthView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SkinDepthView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mBackgroundTintHelper = new SkinCompatBackgroundHelper(this);
        mBackgroundTintHelper.loadFromAttributes(attrs, defStyleAttr);
    }

    @Override
    public void setBackgroundResource(@DrawableRes int resId) {
        super.setBackgroundResource(resId);
        if (mBackgroundTintHelper != null) {
            mBackgroundTintHelper.onSetBackgroundResource(resId);
        }
    }

    @Override
    public void applySkin() {
        if (mBackgroundTintHelper != null) {
            mBackgroundTintHelper.applySkin();
        }
    }
}
