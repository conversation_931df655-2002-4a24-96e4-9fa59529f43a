package io.bhex.app.skin.view;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.DrawableRes;

import com.superluo.textbannerlibrary.TextBannerView;

import skin.support.widget.SkinCompatBackgroundHelper;
import skin.support.widget.SkinCompatSupportable;

public class SkinTextBannerView extends TextBannerView implements SkinCompatSupportable {

    private SkinCompatBackgroundHelper mBackgroundTintHelper;

    public SkinTextBannerView(Context context) {
        super(context);
        mBackgroundTintHelper = new SkinCompatBackgroundHelper(this);
    }

    public SkinTextBannerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mBackgroundTintHelper = new SkinCompatBackgroundHelper(this);
    }

    @Override
    public void setBackgroundResource(@DrawableRes int resId) {
        super.setBackgroundResource(resId);
        if (mBackgroundTintHelper != null) {
            mBackgroundTintHelper.onSetBackgroundResource(resId);
        }
    }

    @Override
    public void applySkin() {
        if (mBackgroundTintHelper != null) {
            mBackgroundTintHelper.applySkin();
        }
    }
}
