package io.bhex.app.skin;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;

import io.bhex.app.view.TopBar;
import skin.support.app.SkinLayoutInflater;

public class SkinInflater implements SkinLayoutInflater {
    @Override
    public View createView(@NonNull Context context, String name, @NonNull AttributeSet attrs) {
        View view = null;
        switch (name) {
            case "io.bhex.app.view":
                view = new TopBar(context, attrs);
                break;
        }
        return null;
    }
}
