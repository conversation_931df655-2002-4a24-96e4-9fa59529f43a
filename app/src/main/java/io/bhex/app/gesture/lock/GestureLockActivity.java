/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: GestureLockActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.gesture.lock;

import android.os.Bundle;

import androidx.fragment.app.FragmentActivity;


/**
 * 描   述：手势锁
 * ================================================
 */

public class GestureLockActivity extends FragmentActivity {

    private GestureLockDialog dialog;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void onResume() {
        super.onResume();
        this.startVerifyLockPatternDialog();
    }

    // 显示手势密码锁界面
    private void startVerifyLockPatternDialog() {
        dialog = new GestureLockDialog();
        dialog.show(getSupportFragmentManager(), "dialog");
    }

}
