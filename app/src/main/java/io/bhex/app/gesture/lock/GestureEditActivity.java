/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: GestureEditActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.gesture.lock;

import android.os.Bundle;
import android.text.Html;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import io.bhex.app.R;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.baselib.constant.AppData;
import io.bhex.app.gesture.view.GestureView;
import io.bhex.app.gesture.view.LockIndicator;
import io.bhex.baselib.core.SPEx;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.utils.ToastUtils;

/**
 * 手势密码设置界面
 */
public class GestureEditActivity extends FragmentActivity implements OnClickListener {
    private final static String TAG = "GestureEditActivity";
    private TextView mTextTip;
    private LockIndicator mLockIndicator;
    private FrameLayout mGestureContainer;
    private GestureView mGestureContentView;
    private TextView mTextReset;
    private boolean mIsFirstInput = true;
    private String mFirstPassword = null;
    private TopBar topBar;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.gesture_set_layout);
        setUpViews();
        setUpListeners();
    }

    private void setUpViews() {
        topBar = this.findViewById(R.id.topBar);
        topBar.setLeftOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        //关闭
        ImageView ivClose = findViewById(R.id.ivClose);
        ivClose.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });

        //重新设置手势密码
        this.mTextReset = findViewById(R.id.text_reset);
        this.mTextReset.setClickable(false);
        this.mTextTip = findViewById(R.id.text_tip);

        this.mLockIndicator = findViewById(R.id.lock_indicator);

        this.mGestureContainer = findViewById(R.id.gesture_container);

        // 初始化一个显示各个点的viewGroup
        this.mGestureContentView = new GestureView(this, false, "", new GestureView.GestureCallBack() {
            @Override
            public void onGestureCodeInput(String inputCode) {
                if (!isInputPassValidate(inputCode)) {// 连接点少于4个
                    mTextTip.setText(getString(R.string.string_gesture_min_points_tips));
                    mTextTip.setTextColor(getResources().getColor(R.color.red));
                    mGestureContentView.clearDrawlineState(0L);
                    return;
                }

                if (mIsFirstInput) {// 第一次输入
                    mFirstPassword = inputCode;
                    updateCodeList(inputCode);
                    mGestureContentView.clearDrawlineState(0L);

                    mTextReset.setVisibility(View.VISIBLE);
                    mTextReset.setClickable(true);
                    mTextReset.setText(getString(R.string.string_reset_gesture_code));

                    mTextTip.setText(R.string.string_setup_gesture_pattern_again);
                    mTextTip.setTextColor(SkinColorUtil.getWhite50(GestureEditActivity.this));
                } else {
                    if (inputCode.equals(mFirstPassword)) {// 设置成功
                        mGestureContentView.clearDrawlineState(0L);

                        SPEx.set(AppData.SPKEY.GESTURE_PWD_KEY,inputCode);

                        ToastUtils.showShort(GestureEditActivity.this,getString(R.string.string_gesture_set_success));

                        GestureEditActivity.this.finish();
                    } else {// 2此绘制不一致
                        mTextTip.setText(Html.fromHtml(getString(R.string.string_gesture_two_no_match)));
                        mTextTip.setTextColor(getResources().getColor(R.color.red));

                        // 左右移动动画
                        Animation shakeAnimation = AnimationUtils.loadAnimation(GestureEditActivity.this, R.anim.shake);

                        mTextTip.startAnimation(shakeAnimation);

                        // 保持绘制的线，1.5秒后清除
                        mGestureContentView.clearDrawlineState(300L);
                    }
                }

                mIsFirstInput = false;
            }

            @Override
            public void checkedSuccess() {

            }

            @Override
            public void checkedFail() {

            }
        });

        //this.mGestureContentView.showGestureTrace(AppInfoPrefs.getGestureTrackSwitch(this));

        // 设置手势解锁显示到哪个布局里面
        this.mGestureContentView.setParentView(this.mGestureContainer);
        this.updateCodeList("");
    }

    private void updateCodeList(String inputCode) {
        // 更新选择的图案
        this.mLockIndicator.setPath(inputCode);
    }

    private void setUpListeners() {
        this.mTextReset.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.text_reset:
                mIsFirstInput = true;
                updateCodeList("");
                mTextTip.setText(getString(R.string.setup_gesture_tips));
                mTextTip.setTextColor(SkinColorUtil.getWhite50(GestureEditActivity.this));
                mTextReset.setVisibility(View.GONE);
                break;
            default:
                break;
        }
    }

    private boolean isInputPassValidate(String inputPassword) {
        return !(TextUtils.isEmpty(inputPassword) || inputPassword.length() < 4);

    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return true;
        }

        return super.onKeyDown(keyCode, event);
    }
}