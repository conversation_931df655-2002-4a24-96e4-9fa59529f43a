package io.bhex.app.view;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.ScrollView;

import io.bhex.baselib.utils.PixelUtils;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-04-10
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class WrapScrollView extends ScrollView {
    private Context mContext;

    public WrapScrollView(Context context) {
        this(context, null);
    }

    public WrapScrollView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public WrapScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        try {
            // 设置控件最大高度不能超过屏幕高度的一半
            heightMeasureSpec = MeasureSpec.makeMeasureSpec(PixelUtils.getScreenHeight() / 2, MeasureSpec.AT_MOST);
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 重新计算控件的宽高
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }
}
