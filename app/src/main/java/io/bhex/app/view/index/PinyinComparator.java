/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PinyinComparator.java
 *   @Date: 19-4-29 下午3:20
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.view.index;

import java.util.Comparator;

import io.bhex.sdk.quote.bean.QuoteTokensBean;

public class PinyinComparator implements Comparator<QuoteTokensBean.TokenItem> {

    public int compare(QuoteTokensBean.TokenItem o1, QuoteTokensBean.TokenItem o2) {
        if (o1.getFirstLetter().equals("@") || o2.getFirstLetter().equals("#")) {
            return -1;
        } else if (o1.getFirstLetter().equals("#") || o2.getFirstLetter().equals("@")) {
            return 1;
        } else {
            return o1.getFirstLetter().compareTo(o2.getFirstLetter());
        }
    }

}
