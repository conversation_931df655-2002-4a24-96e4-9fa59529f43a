/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PopWindowList.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.view;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.CommonUtil;
import io.bhex.baselib.utils.PixelUtils;


public class PopWindowList implements View.OnClickListener {

    private final View popView;
    private RecyclerView recyclerView;
    private ListOnItemClick mListClick;
    private Context mContext;
    private PopListAdapter popListAdapter;
    private PopWindow popWindow;
    private String currentSelectedContent = "";

    public void setBackgroudRes(int bgRes,boolean isUp) {
        if (popView != null) {
            if (isUp) {
                popView.setPadding(0, PixelUtils.dp2px(10),0,PixelUtils.dp2px(5));
            }else{
                popView.setPadding(0,PixelUtils.dp2px(5),0,PixelUtils.dp2px(10));
            }
            popView.setBackgroundResource(bgRes);
        }

    }
    public int getHeight() {
        if (popView != null) {
            int w = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
            int h = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
            popView.measure(w, h);
            int height = popView.getMeasuredHeight();
            return height;
        }
        return 0;
    }

    /**
     * 银行列表点击回调
     */
    public interface ListOnItemClick {
        void onItemClick(String item, int layoutPosition);
    }

    public PopWindowList(Context context, ListOnItemClick bankListClick) {
        mContext = context;
        mListClick = bankListClick;
        LayoutInflater layoutInflater = LayoutInflater.from(context);
        popView = layoutInflater.inflate(
                R.layout.list_popwindow, null);
        recyclerView = popView
                .findViewById(R.id.recyclerView);
        /**
         * 响应返回键
         */
        // popView.setFocusable(true); // 这个很重要
        // popView.setFocusableInTouchMode(true);
        // popView.setOnKeyListener(new OnKeyListener() {
        //
        // @Override
        // public boolean onKey(View v, int keyCode, KeyEvent event) {
        // if (keyCode == KeyEvent.KEYCODE_BACK) {
        // closePop();
        // return false;
        // }
        // return false;
        // }
        // });

        initPopwindow(popView);
    }

    private void initPopwindow(View popView) {
        if (popWindow == null) {
            popWindow = new PopWindow(mContext, popView, WindowManager.LayoutParams.WRAP_CONTENT, WindowManager.LayoutParams.WRAP_CONTENT);
        }
    }

    @Override
    public void onClick(View arg0) {
        closePop();
    }

    public void setAdapter(List<String> list) {
        popListAdapter = new PopListAdapter(list);
        popListAdapter.openLoadAnimation(BaseQuickAdapter.SCALEIN);
        popListAdapter.isFirstOnly(false);
        recyclerView.setLayoutManager(new LinearLayoutManager(mContext));
//        recyclerView.setItemAnimator(new DefaultItemAnimator());
//        recyclerView.addItemDecoration(new DividerItemDecoration(getContext(), LinearLayoutManager.VERTICAL));

        recyclerView.setAdapter(popListAdapter);
        popView.getHeight();
    }

    public void closePop() {
        if (popWindow.isShow()) {
            popWindow.dismiss();
        }
    }

    public void showPopList(View view,int x,int y) {
        if (!popWindow.isShow()) {
//            popWindow.showAtLocation(view, Gravity.TOP, 0, 0);
            popWindow.showAsDropDown(view, x, y);
        } else {
            closePop();
        }
    }

    private class PopListAdapter extends BaseQuickAdapter<String,BaseViewHolder> {

        PopListAdapter(List<String> data) {
            super(R.layout.item_pop_list, data);
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final String content) {
            baseViewHolder.setText(R.id.item_content, content);
            if (currentSelectedContent.equals(content)) {
                baseViewHolder.setTextColor(R.id.item_content, mContext.getResources().getColor(R.color.blue));
            } else {
                baseViewHolder.setTextColor(R.id.item_content, CommonUtil.isBlackMode() ? mContext.getResources().getColor(R.color.dark_night) : mContext.getResources().getColor(R.color.dark));
            }
            baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    currentSelectedContent = content;
                    mListClick.onItemClick(content,baseViewHolder.getLayoutPosition());
                    closePop();
                }
            });
        }

    }
}