/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AnimUtils.java
 *   @Date: 19-4-18 下午8:18
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.view.anim;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.view.View;

import io.bhex.app.view.anim.interpolator.SpringScaleInterpolator;

public class AnimUtils {
    /**
     * 缩放回弹动画
     * @param view
     */
    public static void onScaleAnimation(View view){
//        ScaleAnimation animation =new ScaleAnimation(1.0f,1.8f,1.0f,1.8f);
//        animation.setDuration(1000);
//        animation.setInterpolator(new SpringScaleInterpolator(0.4f));
//        imageView.startAnimation(animation);
        ObjectAnimator animatorX = ObjectAnimator.ofFloat(view,"scaleX",0.5f,1.0f);
        ObjectAnimator animatorY = ObjectAnimator.ofFloat(view,"scaleY",0.5f,1.0f);
        AnimatorSet set =new AnimatorSet();
        set.setDuration(1000);
        set.setInterpolator(new SpringScaleInterpolator(0.4f));
        set.playTogether(animatorX,animatorY);
        set.start();

    }
}
