package io.bhex.app.view;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.graphics.SweepGradient;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;

import androidx.core.content.ContextCompat;
import io.bhex.app.R;
import io.bhex.baselib.utils.PixelUtils;

public class DashboardView extends View {

    private int mRadius; // 扇形半径
    private int mStartAngle = 170; // 起始角度
    private int mSweepAngle = 200; // 绘制角度
    private int mMin = 0; // 最小值
    private int mMax = 180; // 最大值

    private int mVelocity = mMin; // 实时速度
    private int mPLRadius; // 指针长半径
    private int mPSRadius; // 指针短半径

    private int mPadding;
    private float mCenterX, mCenterY; // 圆心坐标
    private Paint mPaint;
    private RectF mRectFArc;
    private int[] mColors;
    private Path mPath;

    public DashboardView(Context context) {
        this(context, null);
    }

    public DashboardView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DashboardView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        init();
    }

    private void init() {

        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setStrokeCap(Paint.Cap.ROUND);

        mRectFArc = new RectF();
        mPath = new Path();

        mColors = new int[]{ContextCompat.getColor(getContext(), R.color.margin_none_risk_color),
                ContextCompat.getColor(getContext(), R.color.margin_warning_risk_color),
                ContextCompat.getColor(getContext(), R.color.margin_high_risk_color)};
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

        mPadding = Math.max(
                Math.max(getPaddingLeft(), getPaddingTop()),
                Math.max(getPaddingRight(), getPaddingBottom())
        );
        setPadding(mPadding, mPadding, mPadding, mPadding);

        int width = resolveSize(dp2px(260), widthMeasureSpec);
        mRadius = (width - mPadding * 2) / 2;

        // 由起始角度确定的高度
        float[] point1 = getCoordinatePoint(mRadius, mStartAngle);
        // 由结束角度确定的高度
        float[] point2 = getCoordinatePoint(mRadius, mStartAngle + mSweepAngle);
        int height = (int) Math.max(point1[1],point2[1]);
        setMeasuredDimension(width, height+getPaddingTop()+getPaddingBottom());

        mCenterX = mCenterY = getMeasuredWidth() / 2f;
        mRectFArc.set(
                getPaddingLeft(),
                getPaddingTop(),
                getMeasuredWidth() - getPaddingRight(),
                getMeasuredWidth() - getPaddingBottom()
        );

        mPLRadius = (int) (mRadius * 0.7);
        mPSRadius = 0;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        canvas.drawColor(ContextCompat.getColor(getContext(), R.color.transparent));

        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setShader(generateSweepGradient());
        mPaint.setAlpha(25);
        canvas.drawArc(mRectFArc, mStartAngle, mSweepAngle, true, mPaint);

        /**
         * 画圆弧
         */
        mPaint.setAlpha(255);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(PixelUtils.dp2px(4));
        mPaint.setShader(generateSweepGradient());
        canvas.drawArc(mRectFArc, mStartAngle, mSweepAngle, false, mPaint);


        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(PixelUtils.dp2px(1));
        mPaint.setShader(generateSweepGradient());
        RectF rect = new RectF(mRectFArc.left+PixelUtils.dp2px(6), mRectFArc.top+PixelUtils.dp2px(6), mRectFArc.right-PixelUtils.dp2px(6), mRectFArc.bottom-PixelUtils.dp2px(6));
        canvas.drawArc(rect, mStartAngle, mSweepAngle, false, mPaint);


        /**
         * 画指针途径背景
         */

        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setShader(generateSweepGradient());
        mPaint.setAlpha(51);
        canvas.drawArc(mRectFArc, mStartAngle, mSweepAngle * (mVelocity - mMin) / (mMax - mMin), true, mPaint);

        /**
         * 画指针边框
         */

        float θ = mStartAngle + mSweepAngle * (mVelocity - mMin) / (mMax - mMin); // 指针与水平线夹角
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setShader(null);
        mPaint.setColor(ContextCompat.getColor(getContext(),R.color.white));
        int d1 = PixelUtils.dp2px(2); // 指针由两个等腰三角形构成，d为共底边长的一半
        mPath.reset();
        float[] p6 = getCoordinatePoint(d1, θ - 90);
        mPath.moveTo(p6[0], p6[1]);
        float[] p7 = getCoordinatePoint(mPLRadius, θ);
        mPath.lineTo(p7[0], p7[1]);

        float[] p8 = getCoordinatePoint(mPLRadius, θ+2);
        mPath.lineTo(p8[0], p8[1]);
        float[] p9 = getCoordinatePoint(d1, θ + 90);
        mPath.lineTo(p9[0], p9[1]);
        float[] p10 = getCoordinatePoint(mPSRadius, θ - 180);
        mPath.lineTo(p10[0], p10[1]);
        mPath.close();
        canvas.drawPath(mPath, mPaint);

        /**
         * 指针填充
         */
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setShader(null);
        mPaint.setColor(ContextCompat.getColor(getContext(),R.color.blue50));
        int d = PixelUtils.dp2px(2); // 指针由两个等腰三角形构成，d为共底边长的一半
        mPath.reset();
        float[] p1 = getCoordinatePoint(d, θ - 90);
        mPath.moveTo(p1[0], p1[1]);
        float[] p2 = getCoordinatePoint(mPLRadius, θ);
        mPath.lineTo(p2[0], p2[1]);

        float[] p3 = getCoordinatePoint(mPLRadius, θ+2);
        mPath.lineTo(p3[0], p3[1]);
        float[] p4 = getCoordinatePoint(d, θ + 90);
        mPath.lineTo(p4[0], p4[1]);
        float[] p5 = getCoordinatePoint(mPSRadius, θ - 180);
        mPath.lineTo(p5[0], p5[1]);
        mPath.close();
        canvas.drawPath(mPath, mPaint);


        /**
         * 画指针围绕的镂空圆心
         */
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setShader(null);
        mPaint.setColor(ContextCompat.getColor(getContext(),R.color.white));
        canvas.drawCircle(mCenterX, mCenterY, PixelUtils.dp2px(4), mPaint);
    }

    private int dp2px(int dp) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp,
                Resources.getSystem().getDisplayMetrics());
    }

    private int sp2px(int sp) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, sp,
                Resources.getSystem().getDisplayMetrics());
    }

    public float[] getCoordinatePoint(int radius, float angle) {
        float[] point = new float[2];

        double arcAngle = Math.toRadians(angle); //将角度转换为弧度
        if (angle < 90) {
            point[0] = (float) (mCenterX + Math.cos(arcAngle) * radius);
            point[1] = (float) (mCenterY + Math.sin(arcAngle) * radius);
        } else if (angle == 90) {
            point[0] = mCenterX;
            point[1] = mCenterY + radius;
        } else if (angle > 90 && angle < 180) {
            arcAngle = Math.PI * (180 - angle) / 180.0;
            point[0] = (float) (mCenterX - Math.cos(arcAngle) * radius);
            point[1] = (float) (mCenterY + Math.sin(arcAngle) * radius);
        } else if (angle == 180) {
            point[0] = mCenterX - radius;
            point[1] = mCenterY;
        } else if (angle > 180 && angle < 270) {
            arcAngle = Math.PI * (angle - 180) / 180.0;
            point[0] = (float) (mCenterX - Math.cos(arcAngle) * radius);
            point[1] = (float) (mCenterY - Math.sin(arcAngle) * radius);
        } else if (angle == 270) {
            point[0] = mCenterX;
            point[1] = mCenterY - radius;
        } else {
            arcAngle = Math.PI * (360 - angle) / 180.0;
            point[0] = (float) (mCenterX + Math.cos(arcAngle) * radius);
            point[1] = (float) (mCenterY - Math.sin(arcAngle) * radius);
        }

        return point;
    }

    private SweepGradient generateSweepGradient() {
        SweepGradient sweepGradient = new SweepGradient(mCenterX, mCenterY,
                mColors,
                new float[]{0, 100 / 360f, mSweepAngle / 360f}
        );

        Matrix matrix = new Matrix();
        matrix.setRotate(mStartAngle-20, mCenterX, mCenterY);
        sweepGradient.setLocalMatrix(matrix);

        return sweepGradient;
    }

    public int getVelocity() {
        return mVelocity;
    }

    public void setVelocity(int velocity) {
        if (mVelocity == velocity) {
            return;
        }
        if (velocity < mMin ) {
            velocity = mMin;
        }
        if (velocity > mMax ) {
            velocity = mMax;
        }

        mVelocity = velocity;
        postInvalidate();
    }
}
