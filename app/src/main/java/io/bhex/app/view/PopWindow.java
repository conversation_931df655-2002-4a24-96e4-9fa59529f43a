/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PopWindow.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.view;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.LinearLayout;
import android.widget.PopupWindow;

import androidx.annotation.RequiresApi;

public class PopWindow {
    private ViewGroup decorView=null;
    private Bitmap blurBg=null;
    private PopupWindow mPopupWindow;

    public PopWindow(Context context, View popupWindow, int mode) {
        super();

        mPopupWindow = new PopupWindow(popupWindow, LinearLayout.LayoutParams.FILL_PARENT,
                LinearLayout.LayoutParams.MATCH_PARENT);
        /* 设置触摸外面时消失 */
        mPopupWindow.setOutsideTouchable(true);
        mPopupWindow.setFocusable(true); // 为了响应listview item点击事件
        mPopupWindow.update();
        ColorDrawable dw = new ColorDrawable(0000000000);
        // 点back键和其他地方使其消失,设置了这个才能触发OnDismisslistener ，设置其他控件变化等操作
        mPopupWindow.setBackgroundDrawable(dw);
        mPopupWindow.setAnimationStyle(android.R.style.Animation_Dialog);
        // 设置SelectPicPopupWindow弹出窗体动画效果
//		mPopupWindow.setAnimationStyle(R.style.AnimationPreview);

    }

    public PopWindow(Context context, View popupWindow) {
        super();

        mPopupWindow = new PopupWindow(popupWindow, LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        /* 设置触摸外面时消失 */
        mPopupWindow.setOutsideTouchable(false);
        mPopupWindow.setFocusable(true); // 为了响应listview item点击事件
        mPopupWindow.update();

        ColorDrawable dw = new ColorDrawable(0000000000);
        // 点back键和其他地方使其消失,设置了这个才能触发OnDismisslistener ，设置其他控件变化等操作
        mPopupWindow.setBackgroundDrawable(dw);
        mPopupWindow.setAnimationStyle(android.R.style.Animation_Dialog);
        // 设置SelectPicPopupWindow弹出窗体动画效果
//		mPopupWindow.setAnimationStyle(R.style.AnimationPreview);

    }

    public PopWindow(Context context, View popupView, int width, int height) {
        super();

        mPopupWindow = new PopupWindow(popupView, width, height);
//        mPopupWindow.setSoftInputMode(PopupWindow.INPUT_METHOD_NEEDED);
//        mPopupWindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
//        /* 设置触摸外面时消失 */
        mPopupWindow.setOutsideTouchable(false);
        mPopupWindow.setFocusable(true);
        mPopupWindow.update();

        ColorDrawable dw = new ColorDrawable(0000000000);
        // 点back键和其他地方使其消失,设置了这个才能触发OnDismisslistener ，设置其他控件变化等操作

        Window window = ((Activity)context).getWindow();
        decorView = window.getDecorView().findViewById(android.R.id.content);

        /**支持毛玻璃效果处理**************/
//        long startMs = System.currentTimeMillis();
//        // 获取截图
//        View activityView = window.getDecorView();
//        activityView.setDrawingCacheEnabled(true);
//        activityView.destroyDrawingCache();
//        activityView.buildDrawingCache();
//        Bitmap bmp = activityView.getDrawingCache();
//        Log.d("MS", "getDrawingCache take away:" + (System.currentTimeMillis() - startMs) + "ms");
//        // 模糊处理并保存
//        blurBg = BlurBitmap.blur(context, bmp);
//        Log.d("MS", "blur take away:" + (System.currentTimeMillis() - startMs) + "ms");
//        // 设置成dialog的背景
////        window.setBackgroundDrawable(new BitmapDrawable(context.getResources(), blurBg));
////        decorView.setBackgroundDrawable(new BitmapDrawable(context.getResources(), blurBg));
////        bmp.recycle();
//
//        //设置毛玻璃
////        rootView.setBackgroundDrawable(new BitmapDrawable(context.getResources(), blurBg));
////        bmp.recycle();
//
//        mPopupWindow.setBackgroundDrawable(new BitmapDrawable(context.getResources(), blurBg));
//        bmp.recycle();

        mPopupWindow.setBackgroundDrawable(dw);
//        mPopupWindow.setSoftInputMode(PopupWindow.INPUT_METHOD_NEEDED);
//        mPopupWindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);

        mPopupWindow.setAnimationStyle(android.R.style.Animation_Dialog);
        // 设置SelectPicPopupWindow弹出窗体动画效果
//		mPopupWindow.setAnimationStyle(R.style.AnimationPreview);
    }

    public void setHeight(int height){
        mPopupWindow.setHeight(height);
        mPopupWindow.update();
    }

    /**
     * 设置消失的监听
     * @param listener
     */
    public void setOnDismissListener(PopupWindow.OnDismissListener listener){
        if (mPopupWindow != null) {
            mPopupWindow.setOnDismissListener(listener);
        }
    }

    /**
     * 相对某个控件的位置（正左下方），无偏移
     *
     * @param v
     */
    public void showAsDropDown(View v) {
        mPopupWindow.showAsDropDown(v);
    }

    /**
     * 相对某个控件的位置，有偏移
     *
     * @param v
     * @param xoff
     * @param yoff
     */
    public void showAsDropDown(View v, int xoff, int yoff) {
        mPopupWindow.showAsDropDown(v, xoff, yoff);
    }

    /**
     * 相对某个控件的位置，有偏移
     *
     * @param v
     * @param xoff
     * @param yoff
     */
    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public void showAsDropDown(View v, int xoff, int yoff, int gravity) {
        mPopupWindow.showAsDropDown(v, xoff, yoff,gravity);
    }

    /**
     * 相对于父控件的位置（例如正中央Gravity.CENTER，下方Gravity.BOTTOM等），可以设置偏移或无偏移
     *
     * @param parent
     * @param gravity
     * @param x
     * @param y
     */
    public void showAtLocation(View parent, int gravity, int x, int y) {
        if (null != mPopupWindow && !mPopupWindow.isShowing()) {
            mPopupWindow.showAtLocation(parent, gravity, x, y);
        }
    }

    /**
     * 关闭popwindow
     */
    public void dismiss() {
        if (null != mPopupWindow && mPopupWindow.isShowing()) {
            mPopupWindow.dismiss();
        }
    }

    public boolean isShow() {
        if (null != mPopupWindow) {
            return mPopupWindow.isShowing();
        } else {
            return false;
        }
    }

    /**
     * 设置是否可以取消
     * @param cancel
     */
    public void setCancel(boolean cancel) {
        if (mPopupWindow != null) {
            mPopupWindow.setOutsideTouchable(cancel);
            mPopupWindow.setFocusable(cancel);
        }
    }
}
