/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CustomListAlertDialog.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.view;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import io.bhex.app.R;

public class CustomListAlertDialog  extends Dialog {

    public CustomListAlertDialog(Context context, int theme) {
        super(context, theme);
    }

    public CustomListAlertDialog(Context context) {
        super(context);
    }

    public static class Builder {

        private Context context;
        private String title;
        private boolean isCancelable = true;
        private String[] items;
        private DialogInterface.OnClickListener listener;
        private ListView listView;
        private TextView tv_Title;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setTitle(int resId) {
            this.title = context.getResources().getString(resId);
            return this;
        }

        public Builder setCancelable(boolean flag) {
            this.isCancelable = flag;
            return this;
        }

        public Builder setItems(String[] items, DialogInterface.OnClickListener listener) {
            this.items = items;
            this.listener = listener;
            return this;
        }

        public CustomListAlertDialog create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            final CustomListAlertDialog dialog = new CustomListAlertDialog(context, R.style.dialog);
            View layout = inflater.inflate(R.layout.customlist_alert_dialog, null);
            dialog.addContentView(layout, new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));

            // title
            tv_Title = layout.findViewById(R.id.title);
            tv_Title.setText(title);

            // list
            listView = layout.findViewById(R.id.list);
            listView.setAdapter(new ArrayAdapter(context, R.layout.customlist_alert_dialog_item, items));
            listView.setOnItemClickListener(new AdapterView.OnItemClickListener() {

                @Override
                public void onItemClick(AdapterView<?> arg0, View view, int position, long arg3) {
                    if (listener != null) {
                        listener.onClick(dialog, position);
                        dialog.dismiss();
                    }
                }
            });

            dialog.setContentView(layout);
            dialog.setCancelable(isCancelable);
            dialog.setCanceledOnTouchOutside(isCancelable);
            return dialog;
        }

        public TextView getTitle(){
            return tv_Title;
        }
    }

    @Override
    public void show() {
        // 设置对话框的宽度为屏幕的8/10.
        setDialogWidth(this, 0.9F);
        super.show();
    }

    /**
     * 设置对话框的宽度为(屏幕宽度*factor).
     * @param dialog
     * @param factor 对话框宽度站屏幕宽度的比例[0, 1].
     */
    public static void setDialogWidth(Dialog dialog, float factor) {
        Window dialogWindow = dialog.getWindow();
        WindowManager.LayoutParams lp = dialogWindow.getAttributes();
        DisplayMetrics dm = new DisplayMetrics();
        dialogWindow.getWindowManager().getDefaultDisplay().getMetrics(dm);
        lp.width = (int) (dm.widthPixels * factor);
        dialogWindow.setAttributes(lp);
    }
}
