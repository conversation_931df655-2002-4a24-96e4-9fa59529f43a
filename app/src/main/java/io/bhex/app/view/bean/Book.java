/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: Book.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.view.bean;

/**
 * ================================================
 * 描   述：挂单
 * ================================================
 */

public class Book {

    private String price;
    private String originalVolume;
    private String volume;
    private String originalCumulativeVolume;
    private String cumulativeVolume;
    private float progress;
    private int priceColor;
    private int progressColor;
    private int progressMode;
    //是否是买或者卖单
    private boolean isBid;
    //订单标记
    private boolean hasOrderMarked;
    //显示累计量
    private boolean showCumulativeVolume;

    public boolean isBid() {
        return isBid;
    }

    public void setBid(boolean bid) {
        isBid = bid;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getVolume() {
        return volume;
    }

    public void setVolume(String volume) {
        this.volume = volume;
    }

    public String getCumulativeVolume() {
        return cumulativeVolume;
    }

    public void setCumulativeVolume(String cumulativeVolume) {
        this.cumulativeVolume = cumulativeVolume;
    }

    public String getOriginalVolume() {
        return originalVolume;
    }

    public void setOriginalVolume(String originalVolume) {
        this.originalVolume = originalVolume;
    }

    public String getOriginalCumulativeVolume() {
        return originalCumulativeVolume;
    }

    public void setOriginalCumulativeVolume(String originalCumulativeVolume) {
        this.originalCumulativeVolume = originalCumulativeVolume;
    }

    public float getProgress() {
        return progress;
    }

    public void setProgress(float progress) {
        this.progress = progress;
    }

    public int getProgressMode() {
        return progressMode;
    }

    public void setProgressMode(int progressMode) {
        this.progressMode = progressMode;
    }

    public int getProgressColor() {
        return progressColor;
    }

    public void setProgressColor(int progressColor) {
        this.progressColor = progressColor;
    }

    public int getPriceColor() {
        return priceColor;
    }

    public void setPriceColor(int priceColor) {
        this.priceColor = priceColor;
    }

    public boolean isHasOrderMarked() {
        return hasOrderMarked;
    }

    public void setHasOrderMarked(boolean hasOrderMarked) {
        this.hasOrderMarked = hasOrderMarked;
    }

    public boolean isShowCumulativeVolume() {
        return showCumulativeVolume;
    }

    public void setShowCumulativeVolume(boolean showCumulativeVolume) {
        this.showCumulativeVolume = showCumulativeVolume;
    }
}
