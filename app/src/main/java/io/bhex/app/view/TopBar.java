/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: TopBar.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.view;

import android.app.Activity;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.SpannableString;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import io.bhex.app.R;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.baselib.utils.ViewFinder;
import skin.support.widget.SkinCompatSupportable;


public class TopBar extends RelativeLayout implements SkinCompatSupportable {
    private static final float TOPBAR_DEFAULT_HEIGHT = 56f;
    private String title;

    private String leftText;

    private String rightText;

    private int leftVisiblity;

    private RelativeLayout leftLayout;

    private LinearLayout rightLayout;

    private ImageView imgLeft;

    private TextView tvLeft;

    private TextView tvTitle;

    private TextView tvRight;
    private TextView tvRight2;

    private ImageView imgRight;
    private ImageView imgTitleIcon;
    private LinearLayout titleLayout;
    private Drawable leftIconRes;
    private Drawable rightIconRes;
    private int bgColor;
    private int dividerVisiblity;
    private View divider;
    private View redPointRight;
    private int titleColor;
    private Context mContext;
    private Drawable tilteIconRes;
    private ImageView imgRight2;
    private View rootView;
    private TextView titleTag;

    public TopBar(Context context) {
        super(context);
        init(context, null);
    }

    public TopBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public TopBar(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    public void setTitleWidth(int dp){
        LinearLayout.LayoutParams linearParams =(LinearLayout.LayoutParams) tvTitle.getLayoutParams();

        linearParams.width= PixelUtils.dp2px(dp);

        tvTitle.setLayoutParams(linearParams);
    }

    

    private void init(final Context context, AttributeSet attrs) {
        mContext = context;
        LayoutInflater.from(context).inflate(R.layout.topbar_layout, this, true);
        setBackgroundResource(CommonUtil.isBlackMode()?R.color.color_bg_1_night:R.color.color_bg_1);

        ViewFinder finder = new ViewFinder(this);
        rootView = finder.find(R.id.titleRela);
        rootView.setOnClickListener(null);
        titleLayout = finder.find(R.id.title_layout);
        tvTitle = finder.textView(R.id.title_bar_text);
        imgTitleIcon = finder.imageView(R.id.title_bar_icon);
        titleTag = finder.textView(R.id.titleTag);

        leftLayout = finder.find(R.id.title_bar_left_layout);
        tvLeft = finder.textView(R.id.title_bar_left_text);
        imgLeft = finder.find(R.id.title_bar_left_img);

        rightLayout = finder.find(R.id.title_bar_right_layout);
        imgRight = finder.find(R.id.title_bar_right_image);
        imgRight2 = finder.find(R.id.title_bar_right_image2);
        tvRight = finder.textView(R.id.title_bar_right_text);
        tvRight2 = finder.textView(R.id.title_bar_right_text2);
        divider = findViewById(R.id.topbar_divider);
        redPointRight = findViewById(R.id.title_red_point_right);

        if (attrs != null) {
            TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.TopBar);
            bgColor = ta.getColor(R.styleable.TopBar_bgcolor,context.getResources().getColor(CommonUtil.isBlackMode()?R.color.white_night:R.color.white));
            titleColor = ta.getColor(R.styleable.TopBar_titlecolor,context.getResources().getColor(CommonUtil.isBlackMode()?R.color.dark_night:R.color.dark));
            title = ta.getString(R.styleable.TopBar_title_text);
            leftText = ta.getString(R.styleable.TopBar_left_text);
            tilteIconRes = ta.getDrawable(R.styleable.TopBar_title_icon);
            leftIconRes = ta.getDrawable(R.styleable.TopBar_left_icon);
            leftVisiblity = ta.getInt(R.styleable.TopBar_left_visiblity, VISIBLE);
            dividerVisiblity = ta.getInt(R.styleable.TopBar_divider_visiblity, GONE);

            rightText = ta.getString(R.styleable.TopBar_right_text);
            rightIconRes = ta.getDrawable(R.styleable.TopBar_right_icon);

            ta.recycle();

            setBgColor(bgColor);

            if (tilteIconRes!= null) {
                imgTitleIcon.setImageDrawable(tilteIconRes);
                imgTitleIcon.setVisibility(View.VISIBLE);
            }else{
                imgTitleIcon.setVisibility(View.GONE);
            }
            if (leftIconRes != null) {
                imgLeft.setImageDrawable(leftIconRes);
            }
            if (rightIconRes != null) {
                imgRight.setImageDrawable(rightIconRes);
                imgRight.setVisibility(View.VISIBLE);
            }else{
                imgRight.setVisibility(View.GONE);
            }
            tvTitle.setTextColor(titleColor);
            setTitle(title);
            setLeftText(leftText);
            setRightText(rightText);

            setLeftVisiblity(leftVisiblity);

            divider.setVisibility(dividerVisiblity);
        }
    }

    public TextView getLeftTextView(){
        return tvLeft;
    }

    public ImageView getleftImgView() {
        return imgLeft;
    }

    public ImageView getRightImg() {
        return imgRight;
    }
    public ImageView getRightImg2() {
        return imgRight2;
    }
    public ImageView getTitleIcon() {
        return imgTitleIcon;
    }

    public TextView getTitleView() {
        return tvTitle;
    }

    /**
     * 标题标签（扩展）
     * @return
     */
    public TextView getTitleTag() {
        return titleTag;
    }

    public TextView getRightTextView(){
        return tvRight;
    }

    public TextView getRightTextView2(){
        return tvRight2;
    }

    public void setBgColor(int bgColor){
        rootView.setBackgroundColor(bgColor);
    }
    public void setTitle(String title) {
        if (!TextUtils.isEmpty(title)) {
            tvTitle.setVisibility(VISIBLE);
            tvTitle.setText(title);
        } else
            tvTitle.setVisibility(GONE);
    }

    public void setTitleAppearance(int id){
            tvTitle.setTextAppearance(mContext, id);
    }

    public void setTitle(SpannableString title) {
            tvTitle.setVisibility(VISIBLE);
            tvTitle.setText(title);
    }

    /**
     * 设置标题右边图标
     * @param imgRes
     */
    public void setTitleRightDrawable(int imgRes){
        imgTitleIcon.setVisibility(VISIBLE);
        imgTitleIcon.setImageResource(imgRes);
    }

    public void setLeftText(String leftText) {
        if (!TextUtils.isEmpty(leftText)) {
            tvLeft.setText(leftText);
            tvLeft.setVisibility(VISIBLE);
        } else {
            tvLeft.setVisibility(GONE);
        }
    }

    public void setLeftTextAndBackGround(String text, int style,int textColor, int imRes) {
        if (!TextUtils.isEmpty(text)) {
            tvLeft.setText(text);
            tvLeft.setBackgroundResource(imRes);
            leftLayout.setVisibility(VISIBLE);
            //imgLeft.setVisibility(GONE);
            //setLeftOnClickListener(null);
            tvLeft.setVisibility(VISIBLE);
            tvLeft.setTextAppearance(mContext, style);
            tvLeft.setTextColor(textColor);
            tvLeft.setPadding(PixelUtils.dp2px(2),PixelUtils.dp2px(2),PixelUtils.dp2px(2),PixelUtils.dp2px(2));

            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams)titleLayout.getLayoutParams();
            layoutParams.leftMargin = PixelUtils.dp2px(2);
            titleLayout.setLayoutParams(layoutParams);
        } else {
            tvLeft.setVisibility(GONE);
        }
    }

    public void setRightText(String rightText) {
        if (!TextUtils.isEmpty(rightText)) {
            tvRight.setVisibility(VISIBLE);
            tvRight.setText(rightText);
        } else
            tvRight.setVisibility(GONE);
    }

    public void setRightTextAppearance(Context context,int style) {
            tvRight.setTextAppearance(context, style);
    }

    /**
     * 设置右边标题左右边距
     * @param marginRight
     */
    public void setRightTextMargin(int marginLeft,int marginRight) {
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) tvRight.getLayoutParams();
        layoutParams.leftMargin = marginLeft;
        layoutParams.rightMargin = marginRight;
        tvRight.setLayoutParams(layoutParams);
    }

    public void setRightImg(int res) {
        if (res != -1) {
            imgRight.setImageResource(res);
            imgRight.setVisibility(View.VISIBLE);
        } else {
            imgRight.setVisibility(View.GONE);
        }
    }

    public void setLeftImg(int res) {
        if (res != -1) {
            imgLeft.setImageResource(res);
//            RelativeLayout.LayoutParams layoutParams = (LayoutParams) imgLeft.getLayoutParams();
//            layoutParams.topMargin = PixelUtils.dp2px(30);
//            imgLeft.setLayoutParams(layoutParams);
            imgLeft.setVisibility(View.VISIBLE);
        } else {
            imgLeft.setVisibility(View.GONE);
        }
    }

    public void setLeftImgVisible(int visible) {

        imgLeft.setVisibility(visible);
    }

    public void setTitleGravity() {
        RelativeLayout.LayoutParams layoutParams = new LayoutParams(LayoutParams.WRAP_CONTENT,LayoutParams.WRAP_CONTENT);
        layoutParams.addRule(RelativeLayout.CENTER_IN_PARENT);
        titleLayout.setLayoutParams(layoutParams);
    }

    public void setTitleOnClickListener(OnClickListener listener) {
        titleLayout.setOnClickListener(listener);
    }

    public void setLeftOnClickListener(OnClickListener listener) {
        leftLayout.setOnClickListener(listener);
    }

    public void setRightOnClickListener(OnClickListener listener) {
        rightLayout.setOnClickListener(listener);
    }

    public void setLeftVisiblity(int visibility) {
        leftLayout.setVisibility(visibility);

        if (visibility != GONE) {
            setLeftOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (getContext() instanceof Activity)
                        ((Activity) getContext()).finish();
                }
            });
        }
    }

    public void setRightRedPointVisiblity(int visibility) {
        redPointRight.setVisibility(visibility);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        if (MeasureSpec.getMode(heightMeasureSpec) == MeasureSpec.AT_MOST) //wrap_conent的情况
            heightMeasureSpec = MeasureSpec.makeMeasureSpec(PixelUtils.dp2px(TOPBAR_DEFAULT_HEIGHT), MeasureSpec.EXACTLY);

        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    public void setTitleLength(int len){
        ViewGroup.LayoutParams layoutParams = tvTitle.getLayoutParams();
        layoutParams.width = PixelUtils.dp2px(len);
        tvTitle.setLayoutParams(layoutParams);
    }

    public void setTitleMaxLength(int len){
        tvTitle.setMaxWidth(PixelUtils.dp2px(len));
    }

    public void setTitleRightImage(int imgRes){
        imgTitleIcon.setVisibility(GONE);
        tvTitle.setCompoundDrawables(null,null,mContext.getResources().getDrawable(imgRes),null);
    }

    public View getDivider() {
        return findViewById(R.id.topbar_divider);
    }

    @Override
    public void applySkin() {
        if (CommonUtil.isBlackMode()) {
            int dark = SkinColorUtil.getDark(this.getContext());
            getResources().getDrawable(R.mipmap.btn_head_back).setColorFilter(dark, PorterDuff.Mode.SRC_ATOP);
        } else {
            getResources().getDrawable(R.mipmap.btn_head_back).clearColorFilter();
        }
    }
}
