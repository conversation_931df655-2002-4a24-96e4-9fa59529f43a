/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: VersionUpdateDialog.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.view;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.text.Spanned;
import android.view.View;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;

import io.bhex.app.R;

public class VersionUpdateDialog extends Dialog {

    private TextView titleView, msgView,negativeButton, positiveButton;

    public VersionUpdateDialog(Context context) {
        this(context, R.style.dialog);
    }

    public VersionUpdateDialog(Context context, int theme) {
        super(context, theme);
        setContentView(R.layout.version_update_dialog);
        setCanceledOnTouchOutside(true);
        initViews();
    }

    private void initViews() {
        titleView = findViewById(R.id.dialog_title);
        msgView = findViewById(R.id.update_content);
        negativeButton = findViewById(R.id.update_id_cancel);
        positiveButton = findViewById(R.id.update_id_ok);
    }

    public void setTitle(int redId) {
        titleView.setVisibility(View.VISIBLE);
        titleView.setText(redId);
    }

    public void setTitle(String title) {
        titleView.setVisibility(View.VISIBLE);
        titleView.setText(title);
    }

    public void setTitle(Spanned title) {
        titleView.setVisibility(View.VISIBLE);
        titleView.setText(title);
    }

    public void setMessage(int resId) {
        msgView.setVisibility(View.VISIBLE);
        msgView.setText(resId);
    }

    public void setMessage(String message) {
        msgView.setVisibility(View.VISIBLE);
        msgView.setText(message);
    }

    public void setMessage(Spanned message) {
        msgView.setVisibility(View.VISIBLE);
        msgView.setText(message);
    }


    /**
     * 设置按钮
     *
     * @param resId
     * @param listener
     */
    public void setNegativeButton(int resId, final View.OnClickListener listener) {
        negativeButton.setVisibility(View.VISIBLE);
        negativeButton.setText(resId);
        negativeButton.setOnClickListener(listener);
    }

    public void setNegativeButton(String text, final View.OnClickListener listener) {
        negativeButton.setVisibility(View.VISIBLE);
        negativeButton.setText(text);
        negativeButton.setOnClickListener(listener);
    }

    public void setNegativeButtonEnable(boolean bEnable) {
        negativeButton.setEnabled(bEnable);
        if(bEnable == false)
            negativeButton.setVisibility(View.GONE);
        negativeButton.setClickable(bEnable);
    }

    public void setNegativeButton(final View.OnClickListener listener) {
        negativeButton.setVisibility(View.VISIBLE);
        negativeButton.setOnClickListener(listener);
    }

    public void setNegativeButtonColor(int color){
        negativeButton.setTextColor(color);
    }

    /**
     * 设置按钮
     *
     * @param resId
     * @param listener
     */
    public void setPositiveButton(int resId, final View.OnClickListener listener) {
        positiveButton.setVisibility(View.VISIBLE);
        positiveButton.setText(resId);
        positiveButton.setOnClickListener(listener);
    }

    public void setPositiveButton(String text, final View.OnClickListener listener) {
        positiveButton.setVisibility(View.VISIBLE);
        positiveButton.setText(text);
        positiveButton.setOnClickListener(listener);
    }

    public void setPositiveButton(View.OnClickListener listener) {
        positiveButton.setVisibility(View.VISIBLE);
        positiveButton.setOnClickListener(listener);
    }

    public void setPositiveButtonColor(int color){
        positiveButton.setTextColor(color);
    }

    @Override
    public void show() {
        if (getContext() instanceof Activity) {
            Activity activity = (Activity) getContext();
            if (!activity.isFinishing()) {
                super.show();
            }
        } else {
            try {
                super.show();
            } catch (Exception e) {
            }
        }
    }

    @Override
    public void dismiss() {
        try {
            super.dismiss();
        } catch (Exception e) {
        }
    }
}

