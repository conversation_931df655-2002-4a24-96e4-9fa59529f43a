/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AlertViewAdapter.java
 *   @Date: 11/29/18 3:21 PM
 *   @Author: chenjun
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.view.alertview;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.CommonUtil;

/**
 * Created by <PERSON><PERSON><PERSON> on 15/8/9.
 */
public class AlertViewAdapter extends BaseAdapter {
    private List<String> mDatas;
    private List<String> mDestructive;
    private boolean mHasTitle =false;
    public AlertViewAdapter(List<String> datas, List<String> destructive,boolean hasTitle){
        this.mDatas =datas;
        this.mDestructive =destructive;
        mHasTitle = hasTitle;
    }
    @Override
    public int getCount() {
        return mDatas.size();
    }

    @Override
    public Object getItem(int position) {
        return mDatas.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        String data= mDatas.get(position);
        Holder holder=null;
        View view =convertView;
        if(view==null){
            LayoutInflater inflater = LayoutInflater.from(parent.getContext());
            view=inflater.inflate(R.layout.item_alertbutton, null);
            holder=creatHolder(view);
            view.setTag(holder);
        }
        else{
            holder=(Holder) view.getTag();
        }
        holder.UpdateUI(parent.getContext(),data,position);
        return view;
    }
    public Holder creatHolder(View view){
        return new Holder(view);
    }
    class Holder {
        private TextView tvAlert;
        private View divider;

        public Holder(View view){
            tvAlert = (TextView) view.findViewById(R.id.tvAlert);
            divider = view.findViewById(R.id.divider);
        }
        public void UpdateUI(Context context, String data, int position){
            if (position == 0) {
                divider.setVisibility(View.GONE);
            } else {
                divider.setVisibility(View.VISIBLE);
            }
            tvAlert.setText(data);
            if (mDestructive!= null && mDestructive.contains(data)){
                if (getCount()==1) {
                    if (mHasTitle) {
                        tvAlert.setBackgroundResource(R.drawable.bg_alertbutton_bottom_selected);
                    } else {
                        tvAlert.setBackgroundResource(R.drawable.bg_alertbutton_one_selected);
                    }
                }
                else if (position==0) {
                    if (mHasTitle) {
                        tvAlert.setBackgroundResource(R.drawable.bg_alertbutton_selected);
                    } else {
                        tvAlert.setBackgroundResource(R.drawable.bg_alertbutton_top_selected);
                    }
                } else  if (position==getCount()-1) {
                    tvAlert.setBackgroundResource(R.drawable.bg_alertbutton_bottom_selected);
                } else {
                    tvAlert.setBackgroundResource(R.drawable.bg_alertbutton_selected);
                }
                tvAlert.setTextColor(context.getResources().getColor(CommonUtil.isBlackMode()? R.color.textColor_alert_button_destructive_night: R.color.textColor_alert_button_destructive));
            }
            else{
                if (getCount()==1) {
                    if (mHasTitle) {
                        tvAlert.setBackgroundResource(R.drawable.bg_alertbutton_bottom);
                    } else {
                        tvAlert.setBackgroundResource(R.drawable.bg_alertbutton_one);
                    }
                }
                else if (position==0) {
                    if (mHasTitle) {
                        tvAlert.setBackgroundResource(R.drawable.bg_alertbutton_none);
                    } else {
                        tvAlert.setBackgroundResource(R.drawable.bg_alertbutton_top);
                    }
                } else  if (position==getCount()-1) {
                    tvAlert.setBackgroundResource(R.drawable.bg_alertbutton_bottom);
                } else {
                    tvAlert.setBackgroundResource(R.drawable.bg_alertbutton_none);
                }
                tvAlert.setTextColor(context.getResources().getColor(CommonUtil.isBlackMode()? R.color.textColor_alert_button_others_night: R.color.textColor_alert_button_others));
            }
        }
    }
}