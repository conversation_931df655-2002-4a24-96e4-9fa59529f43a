/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PriceEditView.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.view;

import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.TextView;

import io.bhex.app.R;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.baselib.utils.ViewFinder;

import skin.support.widget.SkinCompatSupportable;

public class PriceEditView extends RelativeLayout implements View.OnClickListener, TextWatcher, View.OnTouchListener {
    private ViewFinder viewFinder;
    private PriceViewListener mListener;
    private EditText priceEt;

    public PriceEditView(Context context) {
        this(context,null);
    }

    public PriceEditView(Context context, AttributeSet attrs) {
        this(context, attrs,0);
    }

    public PriceEditView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context,attrs);
    }

    /**
     * 初始化
     * @param context
     * @param attrs
     */
    private void init(Context context, AttributeSet attrs) {
        LayoutInflater layoutInflater = LayoutInflater.from(context);
        View editViewLayout = layoutInflater.inflate(R.layout.view_price_editview_layout, this,true);
        viewFinder = new ViewFinder(this);
        ShadowDrawable.setShadow(viewFinder.find(R.id.editRela));
        priceEt = viewFinder.editText(R.id.editView);
        priceEt.setTextAppearance(this.getContext(), CommonUtil.isBlackMode() ? R.style.Body_Dark_Bold_night : R.style.Body_Dark_Bold);
        priceEt.setHintTextColor(SkinColorUtil.getDark50(this.getContext()));
        addEvent();
    }

    public void refreshNightStyle() {
        priceEt.setTextAppearance(this.getContext(), CommonUtil.isBlackMode() ? R.style.Body_Dark_Bold_night : R.style.Body_Dark_Bold);
        priceEt.setHintTextColor(SkinColorUtil.getDark50(this.getContext()));
        ShadowDrawable.setShadow(viewFinder.find(R.id.editRela));
    }

    private void addEvent() {
        viewFinder.find(R.id.btn_minus_sign).setOnClickListener(this);
        viewFinder.find(R.id.btn_plus_sign).setOnClickListener(this);
        viewFinder.editText(R.id.editView).addTextChangedListener(this);
        viewFinder.find(R.id.btn_minus_sign).setOnTouchListener(this);
        viewFinder.find(R.id.btn_plus_sign).setOnTouchListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.btn_plus_sign:
                if (mListener != null) {
                    mListener.onClickPlus();
                }
                break;
            case R.id.btn_minus_sign:
                if (mListener != null) {
                    mListener.onClickMinusSign();
                }
                break;
        }
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        if (mListener != null) {
            mListener.beforeTextChanged(s,start,count,after);
        }
    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
        if (mListener != null) {
            mListener.onTextChanged(s,start,before,count);
            autoAdjustTextSize(s.toString());
        }
    }

    @Override
    public void afterTextChanged(Editable s) {
        if (mListener != null) {
            mListener.afterTextChanged(s);
        }
    }

    /**
     * 动态调整字体大小
     * @param price
     */
    private void autoAdjustTextSize(String price) {
        if (!TextUtils.isEmpty(price)) {
            if (price.length()>14) {
                priceEt.setTextSize(TypedValue.COMPLEX_UNIT_SP, 6);
            }else if(price.length()>13){
                priceEt.setTextSize(TypedValue.COMPLEX_UNIT_SP, 8);
            }else if(price.length()>12){
                priceEt.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10);
            }else if(price.length()>11){
                priceEt.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
            }else{
                priceEt.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
            }
        }
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        switch (v.getId()){
            case R.id.btn_plus_sign:
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        if (mListener != null) {
                            mListener.onPressPlusStart();
                        }
                    break;

                    case MotionEvent.ACTION_MOVE:
                    break;
                    case MotionEvent.ACTION_UP:
                        if (mListener != null) {
                            mListener.onPressPlusEnd();
                        }
                        break;
                    case MotionEvent.ACTION_CANCEL:
                    case MotionEvent.ACTION_OUTSIDE:
                        if (mListener != null) {
                            mListener.onPressPlusEnd();
                        }
                        break;
                }
                break;
            case R.id.btn_minus_sign:
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        if (mListener != null) {
                            mListener.onPressMinusSignStart();
                        }
                        break;

                    case MotionEvent.ACTION_MOVE:
                        break;
                    case MotionEvent.ACTION_UP:
                        if (mListener != null) {
                            mListener.onPressMinusSignEnd();
                        }
                        break;
                    case MotionEvent.ACTION_CANCEL:
                    case MotionEvent.ACTION_OUTSIDE:
                        if (mListener != null) {
                            mListener.onPressMinusSignEnd();
                        }
                        break;
                }
                break;
        }
        return true;
    }

    /**
     *  设置价格
     * @param price
     */
    public void setPrice(String price){
        priceEt.setText(price);
    }

    /**
     * 获取价格
     */
    public String getPrice(){
       return viewFinder.editText(R.id.editView).getText().toString().trim();
    }

    /**
     * 获取价格
     */
    public EditText getPriceEt(){
       return viewFinder.editText(R.id.editView);
    }

    /**
     * 设置View监听
     * @param listener
     */
    public void setPriceViewListener(PriceViewListener listener){
        mListener = listener;
    }


    /**
     * 设置提示输入框提示内容
     * @param hintText
     */
    public void setHintText(String hintText) {
        viewFinder.editText(R.id.editView).setHint(hintText);
    }

    public void setPriceFocusableInTouchMode(boolean b) {
        viewFinder.editText(R.id.editView).setFocusableInTouchMode(b);
    }

    public interface PriceViewListener{
        void onClickPlus();
        void onClickMinusSign();
        void onPressPlusStart();
        void onPressPlusEnd();
        void onPressMinusSignStart();
        void onPressMinusSignEnd();
        void beforeTextChanged(CharSequence s, int start, int count, int after);
        void onTextChanged(CharSequence s, int start, int before, int count);
        void afterTextChanged(Editable s);

    }
}
