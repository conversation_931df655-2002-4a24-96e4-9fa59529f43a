package io.bhex.app.view.swipe;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;

import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.view.swipe.callback.TouchCallBack;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.sdk.quote.bean.CoinPairBean;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-04-18
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class MoveAndSwipedAdapter extends RecyclerView.Adapter<MoveAndSwipedAdapter.MyViewHolder>
        implements TouchCallBack.IItemHelper {

    private OnItemListener mItemListener;
    private Context context;
    private final LayoutInflater mInflater;
    private List<CoinPairBean> mDatas=new ArrayList<>();
    private HashMap<String,CoinPairBean> mSelectedDatas=new HashMap<>();

    public MoveAndSwipedAdapter(Context context,OnItemListener onItemListener){

        this.context = context;
        this.mItemListener = onItemListener;
        mInflater = LayoutInflater.from(context);
    }

    public void setDatas(List<CoinPairBean> datas){
        if (datas != null) {
            mDatas = datas;
        }else{
            mDatas.clear();
        }
        notifyDataSetChanged();
    }

    public List<CoinPairBean> getDatas(){
        return mDatas;
    }

    public HashMap<String,CoinPairBean> getSelectedDatas(){
        return mSelectedDatas;
    }

    @Override
    public MyViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {

        return new MyViewHolder(mInflater.inflate(R.layout.item_edit_optional_layout,parent,false));
    }

    @Override
    public void onBindViewHolder(final MyViewHolder holder, int position) {
        CoinPairBean coinPairBean = mDatas.get(position);
        DebugLog.e("MOVE-Item",coinPairBean.getSymbolId()+"  " + position);
        holder.checkBox.setText(coinPairBean.getBaseTokenName()+ " / " + coinPairBean.getQuoteTokenName());
        holder.checkBox.setChecked(coinPairBean.isSelect());

        holder.checkBox.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                boolean isChecked = holder.checkBox.isChecked();
                coinPairBean.setSelect(isChecked);
                if (isChecked) {
                    mSelectedDatas.put(coinPairBean.getSymbolId(),coinPairBean);
                }else{
                    mSelectedDatas.remove(coinPairBean.getSymbolId());
                }
                if (mItemListener != null) {
                    mItemListener.notifyCheck(mSelectedDatas);
                }
            }
        });

        holder.btnPlacement.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                for (int i = position; i > 0; i--) {
                    itemMoved(i,i-1);
                }
                notifyDataSetChanged();

            }
        });

    }


    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    /**
     * 移动
     * @param oldPosition
     * @param newPosition
     */
    @Override
    public void itemMoved(int oldPosition, int newPosition) {
        Collections.swap(mDatas,oldPosition,newPosition);
        notifyItemMoved(oldPosition,newPosition);
    }

    /**
     * 销毁
     * @param position
     */
    @Override
    public void itemDismiss(int position) {
        mDatas.remove(position);
        notifyItemRemoved(position);
    }

    public class MyViewHolder extends RecyclerView.ViewHolder{

        CheckBox checkBox;
        ImageView imaHandle;
        ImageView btnPlacement;

        public MyViewHolder(View itemView) {
            super(itemView);
            checkBox = (CheckBox) itemView.findViewById(R.id.selectCB);
            imaHandle = (ImageView) itemView.findViewById(R.id.slideBar);
            btnPlacement = (ImageView) itemView.findViewById(R.id.btnPlacement);
        }
    }

    public interface OnItemListener{
        void notifyCheck(HashMap<String,CoinPairBean> selectedList);
        void onItemClick(int position,CoinPairBean coinPairBean);
    }
}
