/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: SwipeItemDelegate.java
 *   @Date: 19-3-29 下午2:18
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.view;

import cn.bingoogolapple.swipeitemlayout.BGASwipeItemLayout;
import io.bhex.baselib.network.response.BaseResponse;

public interface SwipeItemDelegate<T extends BaseResponse> extends BGASwipeItemLayout.BGASwipeItemLayoutDelegate {
    void deleteItem(T item);
}
