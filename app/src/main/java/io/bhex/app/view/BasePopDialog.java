/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BasePopDialog.java
 *   @Date: 3/30/19 11:30 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.view;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.view.View;
import android.widget.TextView;

import io.bhex.app.R;

public class BasePopDialog extends Dialog {

    private TextView titleView;
    private TextView negativeButton, positiveButton ;
    private Context mContext;

    public BasePopDialog(Context context) {
        this(context, R.style.dialog);
    }

    public BasePopDialog(Context context, int theme) {
        super(context, theme);
        setContentView(R.layout.open_option_layout);
        setCanceledOnTouchOutside(true);
        initViews();
        mContext = context;
    }

    protected void initViews() {
        negativeButton = findViewById(R.id.update_id_cancel);
        positiveButton = findViewById(R.id.update_id_ok);

    }


    /**
     * 设置按钮
     *
     * @param resId
     * @param listener
     */
    public void setNegativeButton(int resId, final View.OnClickListener listener) {
        negativeButton.setVisibility(View.VISIBLE);
        negativeButton.setText(resId);
        negativeButton.setOnClickListener(listener);
    }

    public void setNegativeButton(String text, final View.OnClickListener listener) {
        negativeButton.setVisibility(View.VISIBLE);
        negativeButton.setText(text);
        negativeButton.setOnClickListener(listener);
    }

    public void setNegativeButtonEnable(boolean bEnable) {
        negativeButton.setEnabled(bEnable);
        if(bEnable == false)
            negativeButton.setVisibility(View.GONE);
        negativeButton.setClickable(bEnable);
    }

    public void setNegativeButton(final View.OnClickListener listener) {
        negativeButton.setVisibility(View.VISIBLE);
        negativeButton.setOnClickListener(listener);
    }

    public void setNegativeButtonColor(int color){
        negativeButton.setTextColor(color);
    }

    /**
     * 设置按钮
     *
     * @param resId
     * @param listener
     */
    public void setPositiveButton(int resId, final View.OnClickListener listener) {
        positiveButton.setVisibility(View.VISIBLE);
        positiveButton.setText(resId);
        positiveButton.setOnClickListener(listener);
    }

    public void setPositiveButton(String text, final View.OnClickListener listener) {
        positiveButton.setVisibility(View.VISIBLE);
        positiveButton.setText(text);
        positiveButton.setOnClickListener(listener);
    }

    public void setPositiveButton(View.OnClickListener listener) {
        positiveButton.setVisibility(View.VISIBLE);
        positiveButton.setOnClickListener(listener);
    }

    public void setPositiveButtonColor(int color){
        positiveButton.setTextColor(color);
    }

    @Override
    public void show() {
        if (getContext() instanceof Activity) {
            Activity activity = (Activity) getContext();
            if (!activity.isFinishing()) {
                super.show();
            }
        } else {
            try {
                super.show();
            } catch (Exception e) {
            }
        }
    }

    @Override
    public void dismiss() {
        try {
            super.dismiss();
        } catch (Exception e) {
        }
    }
}


