/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CustomFreshHeader.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.view;

import android.content.Context;
import android.util.AttributeSet;

import com.scwang.smartrefresh.layout.header.ClassicsHeader;

public class CustomFreshHeader extends ClassicsHeader {
    public CustomFreshHeader(Context context) {
        super(context);

        /*REFRESH_HEADER_PULLING = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_pulling);
        REFRESH_HEADER_REFRESHING = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_refreshing);
        REFRESH_HEADER_LOADING = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_loading);
        REFRESH_HEADER_RELEASE = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_release);
        REFRESH_HEADER_FINISH = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_finish);
        REFRESH_HEADER_FAILED = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_failed);
        REFRESH_HEADER_UPDATE = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_update);
        REFRESH_HEADER_SECONDARY = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_secondary);*/
    }

    public CustomFreshHeader(Context context, AttributeSet attrs) {
        super(context, attrs);

        /*REFRESH_HEADER_PULLING = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_pulling);
        REFRESH_HEADER_REFRESHING = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_refreshing);
        REFRESH_HEADER_LOADING = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_loading);
        REFRESH_HEADER_RELEASE = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_release);
        REFRESH_HEADER_FINISH = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_finish);
        REFRESH_HEADER_FAILED = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_failed);
        REFRESH_HEADER_UPDATE = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_update);
        REFRESH_HEADER_SECONDARY = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_secondary);*/
    }

    public CustomFreshHeader(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        REFRESH_HEADER_PULLING = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_pulling);
        /*REFRESH_HEADER_REFRESHING = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_refreshing);
        REFRESH_HEADER_LOADING = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_loading);
        REFRESH_HEADER_RELEASE = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_release);
        REFRESH_HEADER_FINISH = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_finish);
        REFRESH_HEADER_FAILED = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_failed);
        REFRESH_HEADER_UPDATE = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_update);
        REFRESH_HEADER_SECONDARY = context.getString(com.scwang.smartrefresh.layout.R.string.srl_header_secondary);*/
    }
}
