/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: RecyclerIndexAdapter.java
 *   @Date: 19-4-28 上午11:36
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.view.index.adapter;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.sdk.quote.bean.QuoteTokensBean;

public class RecyclerIndexAdapter extends BaseQuickAdapter<QuoteTokensBean.TokenItem,BaseViewHolder> {


    public boolean isCategory(int position) {
        try{
            List<QuoteTokensBean.TokenItem> data = getData();
            if (data != null) {
                if(position<data.size()){
                    int category = getItem(position).getFirstLetter().charAt(0);
                    return position == getPositionForCategory(category);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return false;
    }

    public int getPositionForCategory(int category) {
        for (int i = 0; i < getItemCount(); i++) {
            String sortStr = getItem(i).getFirstLetter();
            char firstChar = sortStr.toUpperCase().charAt(0);
            if (firstChar == category) {
                return i;
            }
        }
        return -1;
    }

    public RecyclerIndexAdapter(List<QuoteTokensBean.TokenItem> datas){
        super(R.layout.item_textview_layout,datas);
    }

    @Override
    protected void convert(BaseViewHolder helper, QuoteTokensBean.TokenItem item) {
        helper.setText(R.id.item_tv, item.getTokenName());
    }
}


//public class RecyclerIndexAdapter extends BGARecyclerViewAdapter<QuoteTokensBean.TokenItem> {
//
//    public RecyclerIndexAdapter(RecyclerView recyclerView) {
//        super(recyclerView, R.layout.item_textview_layout);
//    }
//
//    @Override
//    public void fillData(BGAViewHolderHelper helper, int position, QuoteTokensBean.TokenItem model) {
//        helper.setText(R.id.item_tv, model.getTokenName());
//    }
//
//    public boolean isCategory(int position) {
//        int category = getItem(position).getTokenName().charAt(0);
//        return position == getPositionForCategory(category);
//    }
//
//    public int getPositionForCategory(int category) {
//        for (int i = 0; i < getItemCount(); i++) {
//            String sortStr = getItem(i).getTokenName();
//            char firstChar = sortStr.toUpperCase().charAt(0);
//            if (firstChar == category) {
//                return i;
//            }
//        }
//        return -1;
//    }
//}