/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PopCoinPairList.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.view;

import android.content.Context;
import android.os.Handler;
import android.text.Editable;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextWatcher;
import android.text.style.ForegroundColorSpan;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;

import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.google.android.material.tabs.TabLayout;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.market.ui.MarketListFragment;
import io.bhex.app.skin.view.SkinTabLayout;
import io.bhex.app.utils.AnimalUtils;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.KeyBoardUtil;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.quote.QuoteApi;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.NewCoinPairListBean;
import io.bhex.sdk.quote.bean.QuoteTokensBean;
import io.bhex.sdk.quote.bean.TickerBean;
import io.bhex.sdk.quote.bean.TickerListBean;
import io.bhex.sdk.socket.NetWorkObserver;

/**
 * ================================================
 * 描   述：选择切换币对
 * ================================================
 */

public class PopCoinPairList implements View.OnClickListener, ViewPager.OnPageChangeListener {

    private final View popView;
    private final FragmentManager mFragmentManager;
    private final int mViewHeight;
    private final String mSymbolId;
    private final ViewPager viewPager;
    private final TopBar topBar;
    private final RelativeLayout searchRela;
    private final EditText editSearch;
    private final ImageView closeSearch;
    private RecyclerView recyclerView;
    private SkinTabLayout tab;
    private ListOnItemClick mListClick;
    private Context mContext;
    private PopWindow popWindow;
    private String currentSelectedContent = "";
    private List<Pair<String, MarketListFragment>> items;
    private MarketAdapter marketAdapter;
    private String currentExchangeId;
    private MarketListAdapter adapter;

    public void setBackgroudRes(int bgRes,boolean isUp) {
        if (popView != null) {
            if (isUp) {
                popView.setPadding(0, PixelUtils.dp2px(10),0,PixelUtils.dp2px(5));
            }else{
                popView.setPadding(0,PixelUtils.dp2px(5),0,PixelUtils.dp2px(10));
            }
            popView.setBackgroundResource(bgRes);
        }

    }
    public int getHeight() {
        if (popView != null) {
            int w = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
            int h = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
            popView.measure(w, h);
            int height = popView.getMeasuredHeight();
            return height;
        }
        return 0;
    }

    /**
     * 关闭搜索
     */
    public void closeSearch() {

    }

    /**
     * 搜索
     * @param searchContent
     */
    public void search(String searchContent) {
        //TODO
    }

    /**
     * 列表点击回调
     */
    public interface ListOnItemClick {
        void onItemClick(String item, int layoutPosition);
    }

    public PopCoinPairList(final Context context, FragmentManager fragmentManager, String symbolId, int viewHeight, ListOnItemClick listClick) {
        mContext = context;
        mListClick = listClick;
        mViewHeight = viewHeight;
        mFragmentManager = fragmentManager;
        mSymbolId = symbolId;
//        Window window = ((Activity) context).getWindow();
        LayoutInflater layoutInflater = LayoutInflater.from(context);
        popView = layoutInflater.inflate(
                R.layout.pop_coinpair_layout,null);
        viewPager = popView
                .findViewById(R.id.clViewPager);
        recyclerView = popView
                .findViewById(R.id.clRecyclerView);
        tab = popView
                .findViewById(R.id.tabCoin);
        topBar = popView.findViewById(R.id.topBar);
        topBar.setTitleRightDrawable(R.mipmap.icon_drawer);
        topBar.setTitle("");
        editSearch = popView.findViewById(R.id.edit_search);
        closeSearch = popView.findViewById(R.id.close_search);
        searchRela = popView.findViewById(R.id.rela_search);
        editSearch.requestFocus();

        topBar.setTitleOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                KeyBoardUtil.closeKeybord(editSearch,context);
                closePop();
            }
        });
        closeSearch.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                topBar.setVisibility(View.VISIBLE);
                searchRela.setVisibility(View.GONE);
                AnimalUtils.alphaAnimRun(topBar,false ? 0.0f : 1.0f, false ? 1.0f : 0.0f);
                AnimalUtils.scaleAnimRun(searchRela,false ? 0.0f : 1.0f, false ? 1.0f : 0.0f);
            }
        });

        topBar.setRightImg(R.mipmap.icon_search);
        topBar.setRightOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                    topBar.setVisibility(View.GONE);
                    searchRela.setVisibility(View.VISIBLE);
                    AnimalUtils.alphaAnimRun(topBar,true ? 0.0f : 1.0f, true ? 1.0f : 0.0f);
                    AnimalUtils.scaleAnimRun(searchRela,true ? 0.0f : 1.0f, true ? 1.0f : 0.0f);

            }
        });
        editSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });


        /**
         * 响应返回键
         */
        // popView.setFocusable(true); // 这个很重要
        // popView.setFocusableInTouchMode(true);
        // popView.setOnKeyListener(new OnKeyListener() {
        //
        // @Override
        // public boolean onKey(View v, int keyCode, KeyEvent event) {
        // if (keyCode == KeyEvent.KEYCODE_BACK) {
        // closePop();
        // return false;
        // }
        // return false;
        // }
        // });

        initPopwindow(popView);
    }

    private void initPopwindow(View popView) {
        if (popWindow == null) {
//            popWindow = new PopWindow(mContext, popView, WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT);
            popWindow = new PopWindow(mContext, popView, WindowManager.LayoutParams.MATCH_PARENT, mViewHeight);
//            popWindow.setCancel(false);
        }
    }

    /**
     * 设置消失的监听
     * @param listener
     */
    public void setOnDismissListener(PopupWindow.OnDismissListener listener){
        if (popWindow != null) {
            popWindow.setOnDismissListener(listener);
        }
    }

    @Override
    public void onClick(View arg0) {
        closePop();
    }

    public void closePop() {
        if (popWindow.isShow()) {
            popWindow.dismiss();
        }
        QuoteApi.UnSubTickers();
    }

    public void showPopList(View view, int x, int y, CoinPairBean coinPairBean) {
        if (!popWindow.isShow()) {
//            popWindow.showAtLocation(view, Gravity.TOP, 0, 0);
            popWindow.showAsDropDown(view,x,y);
            if (coinPairBean != null) {
                topBar.setTitle(coinPairBean.getBaseTokenName()+"/"+coinPairBean.getQuoteTokenName());
            }
            getCoinPairList();
        } else {
            closePop();
        }
    }

    /**市场fragment
     * @param tabMap*/
    private void initFragmentTab(LinkedHashMap<String, QuoteTokensBean.TokenItem> tabMap) {

        items = new ArrayList<>();

        for (String tabName : tabMap.keySet()) {
            QuoteTokensBean.TokenItem tokenItem = tabMap.get(tabName);

            items.add(new Pair<String, MarketListFragment>(tokenItem.getTokenName(),new MarketListFragment()));
        }
        marketAdapter = new MarketAdapter(mFragmentManager);
        viewPager.setAdapter(marketAdapter);
        tab.setupWithViewPager(viewPager);
        if(tabMap!=null&&tabMap.size()>0) {
            tab.getTabAt(0).setText("");
            tab.getTabAt(0).setIcon(R.mipmap.icon_favorite_checked);
        }
        tab.setTabMode(TabLayout.MODE_SCROLLABLE);
        tab.setTabGravity(TabLayout.GRAVITY_CENTER);
        CommonUtil.setUpIndicatorWidthByReflex2(tab,15,15);

        viewPager.addOnPageChangeListener(this);
    }

    public void showTabOfQuoteTokens(LinkedHashMap<String, QuoteTokensBean.TokenItem> tokens) {
        initFragmentTab(tokens);
    }

    /**
     * 默认返回自选
     * @return
     */
    public String getCurrentTab() {
        if (marketAdapter != null) {
            return marketAdapter.getPageTitle(viewPager.getCurrentItem()).toString();
        }
        return mContext.getString(R.string.string_favorite);
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {
        String tabName;
        if (marketAdapter != null) {
            tabName = marketAdapter.getPageTitle(viewPager.getCurrentItem()).toString();
        }else{
            tabName = mContext.getString(R.string.string_favorite);
        }
        if (position==0) {
            tab.getTabAt(0).setIcon(R.mipmap.icon_favorite_checked);
        }else{
            tab.getTabAt(0).setIcon(CommonUtil.isBlackMode() ? R.mipmap.icon_favorite_tab_night : R.mipmap.icon_favorite_tab);
        }
        changePage(tabName);
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    private class MarketAdapter extends FragmentPagerAdapter {

        public MarketAdapter(FragmentManager fm) {
            super(fm);
        }

        @Override
        public MarketListFragment getItem(int position) {
            return items.get(position).second;
        }

        @Override
        public int getCount() {
            return items.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return items.get(position).first;
        }


    }


    private Handler mHandler = new Handler();

    //创建tab 数据Map
    LinkedHashMap<String, QuoteTokensBean.TokenItem> tabMap = new LinkedHashMap<>();



    /**
     * 创建tab数据集
     *
     * @param response
     */
    private LinkedHashMap<String, QuoteTokensBean.TokenItem> createTabMap(List<QuoteTokensBean.TokenItem> response) {
        tabMap.clear();
        List<QuoteTokensBean.TokenItem> tabList = response;
        if (tabList != null) {
            //添加 默认第一个tab为自选
            QuoteTokensBean.TokenItem itemFavorite = new QuoteTokensBean.TokenItem();
            itemFavorite.setTokenId(AppData.KEY_FAVORITE);
            itemFavorite.setTabName(mContext.getResources().getString(R.string.string_favorite));
            itemFavorite.setTokenName(mContext.getResources().getString(R.string.string_favorite));
            itemFavorite.setTokenFullName(mContext.getResources().getString(R.string.string_favorite));
            tabList.add(0, itemFavorite);

            for (QuoteTokensBean.TokenItem tokenItem : tabList) {
                DebugLog.e("MAP", tokenItem.getTokenId() + tokenItem.getTokenFullName());
                tabMap.put(tokenItem.getTokenId(), tokenItem);
            }
        }
        return tabMap;
    }


    public void getCoinPairList() {
        AppConfigManager.GetInstance().getAppConfig(new SimpleResponseListener<NewCoinPairListBean>() {

            @Override
            public void onSuccess(NewCoinPairListBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    if(response.customQuoteToken !=null) {

                        LinkedHashMap<String, QuoteTokensBean.TokenItem> tabMap = createTabMap(response.customQuoteToken);
                        showTabOfQuoteTokens(tabMap);

                        handCoinPairListData(response.symbol);
                        getRealTimeData();
                    }

                }
            }

        });

    }



    /**
     * 拼接行情参数
     *
     * @return
     */
    private String getSymbolsListStr() {
        StringBuffer stringBuffer = new StringBuffer();
        for (String key : tabMap.keySet()) {
            if (!key.equals(mContext.getResources().getString(R.string.string_favorite))) {//刷出自选,因为其他的列表已经包括
                QuoteTokensBean.TokenItem tokenItem = tabMap.get(key);
                if (tokenItem != null) {
                    HashMap<String, CoinPairBean> coinPairMap = tokenItem.getCoinPairMap();
                    if (coinPairMap != null) {
                        if (!coinPairMap.isEmpty()) {
                            for (String keyOfCoin : coinPairMap.keySet()) {
                                CoinPairBean coinPairBean = coinPairMap.get(keyOfCoin);
                                if (coinPairBean != null) {
                                    String symbol = coinPairBean.getExchangeId() + "." + coinPairBean.getSymbolId();
                                    if (stringBuffer.toString().length() > 0) {
                                        stringBuffer.append("," + symbol);
                                    } else {
                                        stringBuffer.append(symbol);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return stringBuffer.toString();
    }

    public void getRealTimeData(){
        String symbolsListStr = getSymbolsListStr();

        QuoteApi.SubTickers(symbolsListStr, new NetWorkObserver<TickerListBean>() {
            @Override
            public void onShowUI(TickerListBean response) {
                if (!popWindow.isShow() || response == null)
                    return;

                List<TickerBean> datas = response.getData();
                if (datas != null) {
                    for (TickerBean data : datas) {
                        handleSocketMarketList(data);
                    }
                }
            }

            @Override
            public void onError(String error) {
            }
        });

    }

    private void handleSocketMarketList(TickerBean tickerBean) {
        String currentTab = getCurrentTab();
        QuoteTokensBean.TokenItem currentTokenItem = tabMap.get(currentTab);
        for (String key : tabMap.keySet()) {
            QuoteTokensBean.TokenItem tokenItem = tabMap.get(key);
            if (tokenItem != null) {
                HashMap<String, CoinPairBean> coinPairMap = tokenItem.getCoinPairMap();
                if (coinPairMap != null) {
                    for (String coinKey : coinPairMap.keySet()) {
                        CoinPairBean coinPairBean = coinPairMap.get(coinKey);
                        String symbol = coinPairBean.getSymbolId();
                        String symbolTicker = tickerBean.getS();
                        if (symbol.equals(symbolTicker)) {
                            coinPairBean.setTicker(tickerBean);
                            if(currentTokenItem == tokenItem)
                                notifyCoinPairListDataChange(tabMap);
                        }

                    }

                }

            }
        }
    }

    /**
     * 处理币对列表数据
     *
     * @param listBean
     */
    private void handCoinPairListData(List<CoinPairBean> listBean) {
        List<CoinPairBean> coinPairList = listBean;
        if (coinPairList != null && !coinPairList.isEmpty()) {

            QuoteTokensBean.TokenItem favoriteTabToken = tabMap.get(mContext.getResources().getString(R.string.string_favorite));
            if (favoriteTabToken != null) {
                LinkedHashMap<String, CoinPairBean> favoriteMap = favoriteTabToken.getCoinPairMap();
                //自选
                if (favoriteMap != null) {
                    favoriteMap.clear();
                    KlineUtils.clearFavoriteRecord();
                }
                List<CoinPairBean> favoriteSymbols = AppConfigManager.GetInstance().getFavoriteSymbols();
                if (favoriteSymbols != null) {
                    for (CoinPairBean favoriteSymbol : favoriteSymbols) {
                        favoriteMap.put(favoriteSymbol.getBaseTokenId(),favoriteSymbol);
                    }
                }
            }

            notifyCoinPairListDataChange(tabMap);
        }
    }

//    /**
//     * APP首次打开提示交易界面使用的默认币对
//     * @param coinPairList
//     */
//    private void notifyTradeCoinPair(List<CoinPairBean> coinPairList) {
//        if (isSendDefaultCoinPair) {
//            return;
//        }
//        if (coinPairList.size() > 0) {
//            CoinPairBean coinPairBean = coinPairList.get(0);
//            coinPairBean.setNeedSwitchTradeTab(false);
//            EventBus.getDefault().postSticky(coinPairBean);
//            isSendDefaultCoinPair = true;
////            ToastUtils.showShort("HOME Post");
//        }
//
//    }

    /**
     * 提示币对数据更新
     *
     * @param tabMap
     */
    private void notifyCoinPairListDataChange(HashMap<String, QuoteTokensBean.TokenItem> tabMap) {
        String currentTab = getCurrentTab();
        QuoteTokensBean.TokenItem tokenItem = tabMap.get(currentTab);
//        if (tokenItem != null) {
//            EventBus.getDefault().post(tokenItem);
//        } else {
//            DebugLog.e("MarketList tokenItem is null of " + currentTab);
//        }

        if (tokenItem != null) {
            HashMap<String, CoinPairBean> coinPairMap = tokenItem.getCoinPairMap();
            List<CoinPairBean> coinPairList = new ArrayList<>();
            for (String key : coinPairMap.keySet()) {
                CoinPairBean coinPairBean = coinPairMap.get(key);
                coinPairList.add(coinPairBean);
            }

            showMarket(coinPairList);
        }
    }

    private void showMarket(List<CoinPairBean> coinPairList) {
        if (adapter != null) {
            adapter.setNewData(coinPairList);
            return;
        }
        LayoutInflater layoutInflater = LayoutInflater.from(mContext);
        View emptyView = layoutInflater.inflate(R.layout.empty_layout, (ViewGroup) popView, false);
//        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
//        layoutParams.height = PixelUtils.dp2px(getHeight());
//        emptyView.setLayoutParams(layoutParams);
        emptyView.findViewById(R.id.empty_txt).setOnClickListener(this);
        emptyView.findViewById(R.id.empty_img).setOnClickListener(this);

        adapter = new MarketListAdapter(coinPairList);
//            adapter.openLoadAnimation(BaseQuickAdapter.SCALEIN);
        adapter.isFirstOnly(false);
//            adapter.setOnLoadMoreListener(this);
        recyclerView.setLayoutManager(new LinearLayoutManager(mContext));
        recyclerView.setItemAnimator(new DefaultItemAnimator());
        recyclerView.setAdapter(adapter);
        adapter.setEmptyView(emptyView);
    }


    public void changePage(String tabName) {
        notifyCoinPairListDataChange(tabMap);
    }


    private class MarketListAdapter extends BaseQuickAdapter<CoinPairBean, BaseViewHolder> {

        MarketListAdapter(List<CoinPairBean> data) {
            super(R.layout.item_market_list_select_layout, data);
        }

        @Override
        protected void convert(final BaseViewHolder baseViewHolder, final CoinPairBean itemModel) {
            TickerBean tickerBean = itemModel.getTicker();
            String quoteTokenId = itemModel.getQuoteTokenId();
            String symbolStr = itemModel.getSymbolName();
            if(itemModel.baseTokenOption == null)
                symbolStr = itemModel.getBaseTokenName() + " / " + itemModel.getQuoteTokenName();
            SpannableStringBuilder builder = new SpannableStringBuilder(symbolStr);
            ForegroundColorSpan quoteColor = new ForegroundColorSpan(mContext.getResources().getColor(R.color.font_color2));
            builder.setSpan(quoteColor, symbolStr.indexOf("/"), symbolStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            int amountDigit = NumberUtils.calNumerCount(mContext, itemModel.getBasePrecision());
            int pricDigit = NumberUtils.calNumerCount(mContext, itemModel.getMinPricePrecision());

                baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                            //来自交易界面
                            itemModel.setBuyMode(true);
                            itemModel.setNeedSwitchTradeTab(true);
                            //IntentUtils.goTrade(mContext, itemModel);
                            EventBus.getDefault().postSticky(itemModel);
                            closePop();
                    }
                });

                baseViewHolder.setText(R.id.item_coinpair, builder);
                if (tickerBean != null) {
                    baseViewHolder.setText(R.id.item_price1, NumberUtils.roundFormatDown(tickerBean.getC(), pricDigit));
                    baseViewHolder.setText(R.id.item_range_ratio, KlineUtils.calRiseFallRatio(tickerBean.getM()));
                    //boolean isSkinBlackMode = SPEx.get(AppData.SPKEY.SKIN_IS_BLACK_MODE, false);
                    boolean isSkinBlackMode = CommonUtil.isBlackMode();
                    if (mSymbolId.equals(itemModel.getSymbolId())) {
                        baseViewHolder.getConvertView().setBackgroundColor(isSkinBlackMode ? mContext.getResources().getColor(R.color.divider_line_color_night) : mContext.getResources().getColor(R.color.divider_line_color));
                    } else {
                        baseViewHolder.getConvertView().setBackgroundColor(isSkinBlackMode ? mContext.getResources().getColor(R.color.color_bg_2_night) : mContext.getResources().getColor(R.color.color_bg_2));
                    }
                    float riseFallAmount = KlineUtils.calRiseFallAmountFloat(tickerBean.getC(), tickerBean.getO());
                    if (riseFallAmount > 0) {
                        baseViewHolder.setTextColor(R.id.item_range_ratio, SkinColorUtil.getGreen(mContext));
                    } else if (riseFallAmount < 0) {
                        baseViewHolder.setTextColor(R.id.item_range_ratio, SkinColorUtil.getRed(mContext));
                    } else {
                        baseViewHolder.setTextColor(R.id.item_range_ratio, SkinColorUtil.getGreen(mContext));
                    }
                } else {
                    baseViewHolder.setText(R.id.item_price1, mContext.getString(R.string.string_placeholder));
                    baseViewHolder.setText(R.id.item_range_ratio, mContext.getString(R.string.string_placeholder));
                    //boolean isSkinBlackMode = SPEx.get(AppData.SPKEY.SKIN_IS_BLACK_MODE, false);
                    boolean isSkinBlackMode = CommonUtil.isBlackMode();
                    if (mSymbolId.equals(itemModel.getSymbolId())) {
                        baseViewHolder.getConvertView().setBackgroundColor(isSkinBlackMode ? mContext.getResources().getColor(R.color.divider_line_color_night) : mContext.getResources().getColor(R.color.divider_line_color));
                    } else {
                        baseViewHolder.getConvertView().setBackgroundColor(isSkinBlackMode ? mContext.getResources().getColor(R.color.color_bg_2_night) : mContext.getResources().getColor(R.color.color_bg_2));
                    }
                    baseViewHolder.setTextColor(R.id.item_range_ratio, mContext.getResources().getColor(R.color.font_color2));
                }

        }
    }

}