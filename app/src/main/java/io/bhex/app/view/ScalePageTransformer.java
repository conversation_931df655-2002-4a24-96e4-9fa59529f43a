/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: ScalePageTransformer.java
 *   @Date: 19-5-30 下午7:35
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.view;

import android.view.View;

import androidx.viewpager.widget.ViewPager;

public class ScalePageTransformer implements ViewPager.PageTransformer {
    private static final float MIN_SCALE = 0.9f; //缩放因子
    private static final float MIN_ALPHA = 0.5f;

    @Override
    public void transformPage(View view, float position) {

        if (position >= -1 || position <= 1) {
            final float height = view.getHeight();
            final float width = view.getWidth();
            final float scaleFactor = Math.max(MIN_SCALE, 1 - Math.abs(position)); //缩放在0.8-1之间
            final float vertMargin = height * (1 - scaleFactor) / 2;
            final float horzMargin = width * (1 - scaleFactor) / 2;
            view.setPivotY(0.5f * height); //设置缩放的中心点为view的中心，所以不需要设置setPageMargin()了
            view.setPivotX(0.5f * width);
            if (position < 0) {
                view.setTranslationX(horzMargin - vertMargin / 2);
            } else {
                view.setTranslationX(-horzMargin + vertMargin / 2);
            }
            view.setScaleX(scaleFactor); //缩放
            view.setScaleY(scaleFactor);
            //我不需要透明度，所以屏蔽了
//            view.setAlpha(MIN_ALPHA + (scaleFactor - MIN_SCALE) / (1 - MIN_SCALE) * (1 - MIN_ALPHA));
        }
    }
}
