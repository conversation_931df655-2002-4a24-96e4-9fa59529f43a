/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: AlertAnimateUtil.java
 *   @Date: 11/29/18 3:21 PM
 *   @Author: chenjun
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.view.alertview;

import android.view.Gravity;

/**
 * Created by ppzhao on 15/8/9.
 */
public class AlertAnimateUtil {
    private static final int INVALID = -1;
    /**
     * Get default animation resource when not defined by the user
     *
     * @param gravity       the gravity of the dialog
     * @param isInAnimation determine if is in or out animation. true when is is
     * @return the id of the animation resource
     */
    static int getAnimationResource(int gravity, boolean isInAnimation) {
        switch (gravity) {
            case Gravity.BOTTOM:
                return isInAnimation ? io.bhex.baselib.R.anim.slide_in_bottom : io.bhex.baselib.R.anim.slide_out_bottom;
            case Gravity.CENTER:
                return isInAnimation ? io.bhex.baselib.R.anim.fade_in_center : io.bhex.baselib.R.anim.fade_out_center;
        }
        return INVALID;
    }
}
