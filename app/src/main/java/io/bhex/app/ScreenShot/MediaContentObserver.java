/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MediaContentObserver.java
 *   @Date: 19-5-22 下午9:24
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.ScreenShot;

import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;

import io.bhex.baselib.utils.DebugLog;

public class MediaContentObserver extends ContentObserver {
    private final Uri mContentUri;
    private final MediaContentCallback mMediaContentCallback;

    /**
     * Creates a content observer.
     *
     * @param handler The handler to run {@link #onChange} on, or null if none.
     */
    public MediaContentObserver(Uri contentUri,Handler handler,MediaContentCallback mediaContentCallback) {
        super(handler);
        DebugLog.w("screenshot","contentUri "+contentUri);
        mContentUri = contentUri;
        mMediaContentCallback = mediaContentCallback;
    }

    @Override
    public void onChange(boolean selfChange) {
        super.onChange(selfChange);
        DebugLog.w("screenshot","selfChange "+selfChange);
        if (mMediaContentCallback != null) {
            mMediaContentCallback.onChange(mContentUri,selfChange);
        }
    }
}
