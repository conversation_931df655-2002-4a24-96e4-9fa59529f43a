/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BitmapUtils.java
 *   @Date: 19-5-22 下午9:52
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.ScreenShot;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.drawable.BitmapDrawable;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.view.View;
import android.view.WindowManager;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import io.bhex.app.R;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.MD5Utils;
import io.bhex.baselib.utils.PixelUtils;

public class BitmapUtils {
    /**
     * 合成新的bitmap
     * @param context
     * @param filePath
     * @param bitmap
     * @return
     */
    public static Bitmap concatBitmap(Context context,String filePath, Bitmap bitmap) {
        if (bitmap == null) {
            return null;
        }
        int navHeight = getHeightWithNav(context) - getHeightWithoutNav(context);
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        options.inPreferredConfig = Bitmap.Config.RGB_565;
        BitmapFactory.decodeFile(filePath, options);
        int width = options.outWidth;
        int height = options.outHeight - navHeight;
        int max = 1024 * 1024;
        int sampleSize = 1;
        while (width / sampleSize * height / sampleSize > max) {
            sampleSize *= 2;
        }
        options.inSampleSize = sampleSize;
        options.inJustDecodeBounds = false;

        Bitmap srcBmp = BitmapFactory.decodeFile(filePath, options);
        //先计算bitmap的宽高，因为bitmap的宽度和屏幕宽度是不一样的，需要按比例拉伸
        double ratio = 1.0 * bitmap.getWidth() / srcBmp.getWidth();
        int additionalHeight = (int) (bitmap.getHeight() / ratio);
        Bitmap scaledBmp = Bitmap.createScaledBitmap(bitmap, srcBmp.getWidth(), additionalHeight, false);
        //到这里图片拉伸完毕

        //这里开始拼接，画到Canvas上
        Bitmap result = Bitmap.createBitmap(srcBmp.getWidth(), srcBmp.getHeight() - navHeight / sampleSize + additionalHeight, Bitmap.Config.RGB_565);
        Canvas canvas = new Canvas();
        canvas.setBitmap(result);
        canvas.drawBitmap(srcBmp, 0, 0, null);
        //这里需要做个判断，因为一些系统是有导航栏的，所以截图时有导航栏，这里需要把导航栏遮住
        //计算出导航栏高度，然后draw时往上偏移一段距离

        double navRatio = 1.0 * PixelUtils.getScreenWidth() / srcBmp.getWidth();
        canvas.drawBitmap(scaledBmp, 0, srcBmp.getHeight() - (int) (navHeight / navRatio), null);
        bitmap.recycle();
        return result;
    }

    public static Bitmap concatBitmap(Context context, boolean isVerticalScreen, Bitmap srcBmp, Bitmap attachBitmap) {
        if (srcBmp==null || attachBitmap == null) {
            return null;
        }
        int navHeight = getHeightWithNav(context) - getHeightWithoutNav(context);
        //这里开始拼接，画到Canvas上
        Bitmap result;
        if (isVerticalScreen) {
            //叠加bitmap
            result = Bitmap.createBitmap(srcBmp.getWidth(), srcBmp.getHeight() - navHeight, Bitmap.Config.RGB_565);
        }else{
            //追加bitmap
            result = Bitmap.createBitmap(srcBmp.getWidth(), srcBmp.getHeight() - navHeight + attachBitmap.getHeight(), Bitmap.Config.RGB_565);
        }

        //这里需要做个判断，因为一些系统是有导航栏的，所以截图时有导航栏，这里需要把导航栏遮住
        //计算出导航栏高度，然后draw时往上偏移一段距离

        Canvas canvas = new Canvas(result);
        canvas.drawColor(SkinColorUtil.getWhite(context));
        canvas.drawBitmap(srcBmp, 0, 0, null);
        //这里需要做个判断，因为一些系统是有导航栏的，所以截图时有导航栏，这里需要把导航栏遮住
        //计算出导航栏高度，然后draw时往上偏移一段距离

        double navRatio = 1.0 * PixelUtils.getScreenWidth() / srcBmp.getWidth();

        DebugLog.d("BitmapUtils","navHeight==>:"+navHeight+"==navRatio==:"+navRatio+"==x=="+((navHeight / navRatio)-attachBitmap.getHeight()));
        if (isVerticalScreen) {
            //叠加bitmap
            canvas.drawBitmap(attachBitmap, 0, srcBmp.getHeight() - (int) (navHeight / navRatio)-attachBitmap.getHeight(),null);
        }else{
            //追加bitmap
            canvas.drawBitmap(attachBitmap, 0, srcBmp.getHeight() - (int) (navHeight / navRatio), null);
        }

        attachBitmap.recycle();
        return result;
    }

    public static Bitmap concatBitmap2(Context context, boolean isVerticalScreen, Bitmap srcBmp, Bitmap attachBitmap) {
        if (srcBmp==null || attachBitmap == null) {
            return null;
        }
        Bitmap result;
        int width = Math.max(srcBmp.getWidth(),attachBitmap.getWidth());
        int height = srcBmp.getHeight() + attachBitmap.getHeight();
        result = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565);
        Canvas canvas = new Canvas(result);
        canvas.drawColor(SkinColorUtil.getWhite(context));

        canvas.drawBitmap(srcBmp, 0, 0, null);

        canvas.drawBitmap(attachBitmap, 0, srcBmp.getHeight(), null);
        return result;
    }

    /**
     * 获取屏幕高度，不包括navigation
     */
    public static int getHeightWithNav(Context context) {
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        Display d = windowManager.getDefaultDisplay();
        DisplayMetrics realDisplayMetrics = new DisplayMetrics();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            d.getRealMetrics(realDisplayMetrics);
        } else {
            try {
                Method method = d.getClass().getDeclaredMethod("getRealMetrics");
                method.setAccessible(true);
                method.invoke(d, realDisplayMetrics);
            } catch (NoSuchMethodException e) {

            } catch (InvocationTargetException e) {

            } catch (IllegalAccessException e) {

            }
        }
        return realDisplayMetrics.heightPixels;
    }

    /**
     * 获取屏幕高度，包括navigation
     *
     * @return
     */
    public static int getHeightWithoutNav(Context context) {
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        Display d = windowManager.getDefaultDisplay();
        DisplayMetrics displayMetrics = new DisplayMetrics();
        d.getMetrics(displayMetrics);
        return displayMetrics.heightPixels;
    }

    /**
     * 第一种
     * 通过 DrawingCache 方法来截取普通的view，获取它的视图（Bitmap)
     * 这个方法适用于view 已经显示在界面上了，可以获得view 的宽高实际大小，进而通过DrawingCache 保存为bitmap
     * @param view
     * @return
     */
    public static Bitmap createBitmap(View view) {
        view.buildDrawingCache();
        Bitmap bitmap = view.getDrawingCache();
        return bitmap;
    }

    /**
     * 第一种
     * 通过 DrawingCache 方法来截取普通的view，获取它的视图（Bitmap)
     * 这个方法适用于view 已经显示在界面上了，可以获得view 的宽高实际大小，进而通过DrawingCache 保存为bitmap
     * @param view
     * @return
     */
    public static Bitmap createBitmap(View view,int width,int height) {
        view.buildDrawingCache();
        Bitmap bitmap = view.getDrawingCache();
        Bitmap bmp = changeBitmapSize(bitmap,width,height);
        return bmp;
    }

    public static Bitmap changeBitmapSize(Bitmap bitmap, int newWidth, int newHeight) {

//        Bitmap bitmap = BitmapFactory.decodeResource(getResources(), R.mipmap.ic_launcher);

        int width = bitmap.getWidth();
        int height = bitmap.getHeight();

        DebugLog.e("width","width:"+width);
        DebugLog.e("height","height:"+height);

        //设置想要的大小
//        int newWidth=1080;
//        int newHeight=1920;

        //计算压缩的比率
        float scaleWidth=((float)newWidth)/width;
        float scaleHeight=((float)newHeight)/height;

        //按原比例缩放，取最合适的缩放比
        float scale = scaleWidth > scaleHeight ? scaleHeight : scaleWidth;

        //获取想要缩放的matrix
        Matrix matrix = new Matrix();
//        matrix.postScale(scaleWidth,scaleHeight);
        matrix.postScale(scale,scale);

        //获取新的bitmap
        bitmap=Bitmap.createBitmap(bitmap,0,0,width,height,matrix,true);
        bitmap.getWidth();
        bitmap.getHeight();

        DebugLog.e("newWidth","newWidth"+bitmap.getWidth());
        DebugLog.e("newHeight","newHeight"+bitmap.getHeight());

        return bitmap;

    }

    /**
     * 第二种
     * 如果要截取的view 没有在屏幕上显示完全的，例如要截取的是超过一屏的 scrollview ，通过上面这个方法是获取不到bitmap的，
     * 需要使用下面方法，传的view 是scrollview 的子view（LinearLayout）等， 当然完全显示的view（第一种情况的view） 也可以使用这个方法截取。
     * @param v
     * @return
     */
    public static Bitmap createBitmap2(View v) {
        Bitmap bmp = Bitmap.createBitmap(v.getWidth(), v.getHeight(), Bitmap.Config.ARGB_8888);
        Canvas c = new Canvas(bmp);
        c.drawColor(Color.WHITE);
        v.draw(c);
        return bmp;
    }

    /**
     * 第三种
     * view完全没有显示在界面上，通过inflate 转化的view，这时候通过 DrawingCache 是获取不到bitmap 的，也拿不到view 的宽高，以上两种方法都是不可行的。
     * 第三种方法通过measure、layout 去获得view 的实际尺寸。
     * View view = LayoutInflater.from(this).inflate(R.layout.view_inflate, null, false);
     * //这里传值屏幕宽高，得到的视图即全屏大小
     * createBitmap3(view, getScreenWidth(), getScreenHeight());
     * @param v
     * @param width
     * @param height
     * @return
     */
    public static Bitmap createBitmap3(View v, int width, int height) {
        //测量使得view指定大小
        int measuredWidth = View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.EXACTLY);
        int measuredHeight = View.MeasureSpec.makeMeasureSpec(height, View.MeasureSpec.EXACTLY);
        v.measure(measuredWidth, measuredHeight);
        //调用layout方法布局后，可以得到view的尺寸大小
        v.layout(v.getLeft(), v.getTop(), v.getMeasuredWidth(), v.getMeasuredHeight());
//        Bitmap bmp = Bitmap.createBitmap(v.getWidth(), v.getHeight(), Bitmap.Config.RGB_565);
        Bitmap bmp = Bitmap.createBitmap(v.getMeasuredWidth(), v.getMeasuredHeight(), Bitmap.Config.RGB_565);
        Canvas c = new Canvas(bmp);
        c.drawColor(Color.WHITE);
        v.draw(c);
        return bmp;
    }

    /**
     * 第三种
     * view完全没有显示在界面上，通过inflate 转化的view，这时候通过 DrawingCache 是获取不到bitmap 的，也拿不到view 的宽高，以上两种方法都是不可行的。
     * 第三种方法通过measure、layout 去获得view 的实际尺寸。
     * View view = LayoutInflater.from(this).inflate(R.layout.view_inflate, null, false);
     * //这里传值屏幕宽高，得到的视图即全屏大小
     * createBitmap3(view, getScreenWidth(), getScreenHeight());
     * @param v
     * @param width
     * @param height
     * @return
     */
    public static Bitmap createBitmap3(View v, int left ,int top,int width, int height) {
        v.setVisibility(View.INVISIBLE);
        //测量使得view指定大小
        int measuredWidth = View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.EXACTLY);
        int measuredHeight = View.MeasureSpec.makeMeasureSpec(height, View.MeasureSpec.EXACTLY);
        v.measure(measuredWidth, measuredHeight);
        //调用layout方法布局后，可以得到view的尺寸大小
        v.layout(left, top, v.getMeasuredWidth(), v.getMeasuredHeight());
//        Bitmap bmp = Bitmap.createBitmap(v.getWidth(), v.getHeight(), Bitmap.Config.RGB_565);
        Bitmap bmp = Bitmap.createBitmap(v.getMeasuredWidth(), v.getMeasuredHeight(), Bitmap.Config.RGB_565);
        Canvas c = new Canvas(bmp);
        c.drawColor(Color.WHITE);
        v.draw(c);
        return bmp;
    }

    /**
     * 保存bitmap
     * @param bitmap
     */
    public static String saveBitmap(Context context,Bitmap bitmap) {
        FileOutputStream fos;
        String path = "";
        try {
            File root = Environment.getExternalStorageDirectory();
            //Log.d("BitmapUtil:","root==:"+root.getAbsolutePath());
            String appName = context.getResources().getString(R.string.app_name);
            String fileName = appName+"/"+appName+"_"+System.currentTimeMillis()+".png";

            File dir = new File(root,appName);
            if(!dir.exists()){
                dir.mkdir();
            }
            path = root+"/"+fileName;
            File file = new File(root, fileName);
            Log.d("BitmapUtil:","file==:"+file.getAbsolutePath());
            fos = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.PNG, 90, fos);
            fos.flush();
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return path;
    }

    public static Bitmap getBitmapByres(Context context,int resDraw){
        Bitmap bitmap = BitmapFactory.decodeResource(context.getResources(), resDraw, null);
        Bitmap.createBitmap(300,300,Bitmap.Config.ARGB_8888);
        return bitmap;
    }

    public static Bitmap getBitmapByres2(Context context,int resDraw){
        Bitmap bitmap = ((BitmapDrawable)context.getResources().getDrawable(resDraw)).getBitmap();
        return bitmap;
    }

    public static Bitmap Bytes2Bimap(byte[] b) {
        if (b.length != 0) {
            return BitmapFactory.decodeByteArray(b, 0, b.length);
        } else {
            return null;
        }
    }


    public static String saveBitmapKlineOpenUrl(Context context,Bitmap bitmap,String content,String prefix) {
        FileOutputStream fos;
        String path = "";
        try {
            File root = Environment.getExternalStorageDirectory();
            String appName = context.getResources().getString(R.string.app_name);
            String fileName = appName+"/"+prefix+"_"+ MD5Utils.encode(content) +".png";
            File pfile = new File(root,fileName);
            if(pfile.exists()){
                return pfile.getAbsolutePath();
            }

            File dir = new File(root,appName);
            if(!dir.exists()){
                dir.mkdir();
            }
            path = root+"/"+fileName;
            File file = new File(root, fileName);
            Log.d("BitmapUtil:","file==:"+file.getAbsolutePath());
            fos = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.PNG, 90, fos);
            fos.flush();
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return path;
    }

    public static String saveKlineBitmap(Context context,Bitmap bitmap,String content){
        FileOutputStream fos;
        String path = "";
        try {
            File root = Environment.getExternalStorageDirectory();
            String appName = context.getResources().getString(R.string.app_name);
            String fileName = appName+"/"+appName+"_"+System.currentTimeMillis()+".png";

            File dir = new File(root,appName);
            if(!dir.exists()){
                dir.mkdir();
            }
            path = root+"/"+fileName;
            File file = new File(root, fileName);
            Log.d("BitmapUtil:","file==:"+file.getAbsolutePath());
            fos = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.PNG, 90, fos);
            fos.flush();
            fos.close();

            File openFile = new File(root,appName+"/"+"open_"+MD5Utils.encode(content)+".png");
            File sloganFile = new File(root,appName+"/"+"slogan_"+MD5Utils.encode(content)+".png");
            if(openFile.exists()){
                openFile.delete();
            }
            if(sloganFile.exists()){
                sloganFile.delete();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return path;
    }

    public static Uri getImageUri(Context inContext, Bitmap inImage) {
        ByteArrayOutputStream bytes = new ByteArrayOutputStream();
        inImage.compress(Bitmap.CompressFormat.JPEG, 100, bytes);

        String path = MediaStore.Images.Media.insertImage(inContext.getContentResolver(), inImage, "Title", null);
        return Uri.parse(path);
    }

    public static Bitmap webData2bitmap(String data) {
        byte[] imageBytes = android.util.Base64.decode(data.split(",")[1], android.util.Base64.DEFAULT);
        return BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.length);
    }
}
