/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PerpetualMarketFragment.java
 *   @Date: 19-6-11 上午11:43
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.market.ui;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.core.util.Pair;

import com.google.android.material.tabs.TabLayout;

import java.util.ArrayList;
import java.util.LinkedHashMap;

import io.bhex.app.R;
import io.bhex.app.market.presenter.PerpetualMarketFragmentPresenter;
import io.bhex.app.utils.CommonUtil;
import io.bhex.sdk.quote.bean.QuoteTokensBean;

public class PerpetualMarketFragment extends BaseMarketFragment<PerpetualMarketFragmentPresenter, PerpetualMarketFragmentPresenter.PerpetualMarketFragmentUI> implements PerpetualMarketFragmentPresenter.PerpetualMarketFragmentUI {
    private static final String TAG = "PerpetualMarketFragment";
    private View optionHistory;

    @Override
    protected PerpetualMarketFragmentPresenter.PerpetualMarketFragmentUI getUI() {
        return this;
    }

    @Override
    protected PerpetualMarketFragmentPresenter createPresenter() {
        return new PerpetualMarketFragmentPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return super.createView(inflater,container,savedInstanceState);
    }

    @Override
    protected void initViews() {
        super.initViews();
//        optionHistory = viewFinder.find(R.id.option_history);
//        optionHistory.setVisibility(View.GONE);

    }


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    protected void addEvent() {
        super.addEvent();

    }

    @Override
    public void onPause() {
        super.onPause();

    }

    @Override
    protected void onVisibleChanged(boolean visible) {
        super.onVisibleChanged(visible);
        Log.d("BaseFragment==:","=PerpetualMarketFragment=onVisibleChanged=="+visible);
    }


    @Override
    public void onClick(View v) {
        super.onClick(v);
        switch (v.getId()) {
            case R.id.option_history:
//                IntentUtils.goHistoryOptionDelivery(getContext());
                break;
        }
    }

    protected void initFragmentTab(LinkedHashMap<String, QuoteTokensBean.TokenItem> tabMap) {

        if(items ==null || items.size() < 1) {

            items = new ArrayList<>();
            for (String tabName : tabMap.keySet()) {
                QuoteTokensBean.TokenItem tokenItem = tabMap.get(tabName);
                tokenItem.setTabName(tokenItem.getTokenName());
                MarketListFragment marketListFragment = new MarketListFragment();
                Bundle bundle = new Bundle();
                bundle.putSerializable("tradeType", MarketListFragment.TYPE_FUTURES);
                bundle.putSerializable("tabToken", tokenItem);
                marketListFragment.setArguments(bundle);
                items.add(new Pair<QuoteTokensBean.TokenItem, MarketListFragment>(tokenItem, marketListFragment));
            }
            marketAdapter = new MarketAdapter(getChildFragmentManager());
            viewPager.setAdapter(marketAdapter);
            tab.setupWithViewPager(viewPager);
            tab.setTabMode(TabLayout.MODE_SCROLLABLE);
            tab.setTabGravity(TabLayout.GRAVITY_CENTER);
            CommonUtil.setUpIndicatorWidthByReflex2(tab, 15, 15);
            viewPager.addOnPageChangeListener(this);
            if (items.size() >0){
                viewPager.setCurrentItem(0);
            }

        }

    }

    @Override
    public void onPageSelected(int position) {
        String tabId = "";
        if (marketAdapter != null) {
            tabId = marketAdapter.getTabId(viewPager.getCurrentItem());
        }
        getPresenter().changePage(tabId);
        currentPage = position;
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        //ToastUtils.showLong("=PerpetualMarketFragment=onHiddenChanged=="+hidden);

        //Log.d("BaseFragment==:","=PerpetualMarketFragment=onHiddenChanged=="+hidden);
    }

}