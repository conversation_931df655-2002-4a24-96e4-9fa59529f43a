/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: CoinPairListActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.market.ui;

import android.content.Intent;
import android.os.Bundle;

import android.view.View;

import androidx.core.util.Pair;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;

import java.util.ArrayList;
import java.util.LinkedHashMap;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.constant.Fields;
import io.bhex.app.market.presenter.CoinPairListPresenter;
import io.bhex.sdk.quote.bean.QuoteTokensBean;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.view.TopBar;


public class CoinPairListActivity extends BaseActivity<CoinPairListPresenter, CoinPairListPresenter.CoinPairListUI> implements CoinPairListPresenter.CoinPairListUI, ViewPager.OnPageChangeListener {

    private ViewPager viewPager;
    private TabLayout tab;
    private ArrayList<Pair<String,MarketListFragment>> items;
    private MarketAdapter marketAdapter;
    private String coinPair;
    private String from;
    private String exchangeId="";

    @Override
    protected int getContentView() {
        return R.layout.activity_coinpair_list_layout;
    }

    @Override
    protected CoinPairListPresenter createPresenter() {
        return new CoinPairListPresenter();
    }

    @Override
    protected CoinPairListPresenter.CoinPairListUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        TopBar topBar = viewFinder.find(R.id.topBar);
        this.setTopbarNight(topBar);
        topBar.setRightImg(R.mipmap.icon_search);
        topBar.setRightOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IntentUtils.goSearch(CoinPairListActivity.this);
            }
        });

        viewPager = viewFinder.find(R.id.viewPager);
        tab = viewFinder.find(R.id.tab);

        Intent intent = getIntent();
        if (intent != null) {
            exchangeId = intent.getStringExtra(Fields.FIELD_EXCHANGE_ID2);
            coinPair = intent.getStringExtra(Fields.FIELD_SYMBOL);
            from = intent.getStringExtra(AppData.INTENT.FROM);
        }

    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    public String getExchangeId(){
        return exchangeId;
    }

    /**市场fragment
     * @param tabMap*/
    private void initFragmentTab(LinkedHashMap<String, QuoteTokensBean.TokenItem> tabMap) {

        items = new ArrayList<>();

        for (String tabName : tabMap.keySet()) {
            QuoteTokensBean.TokenItem tokenItem = tabMap.get(tabName);

            MarketListFragment marketListFragment = new MarketListFragment();
            Bundle bundle = new Bundle();
            bundle.putString(Fields.FIELD_EXCHANGE_ID2,exchangeId);
            bundle.putString(Fields.FIELD_SYMBOL,coinPair);
            bundle.putString(Fields.FIELD_TABNAME,tabName);
            bundle.putString(AppData.INTENT.FROM,from);
            bundle.putBoolean(Fields.FIELD_IS_GO_SELECT_COINPAIR,true);
            marketListFragment.setArguments(bundle);
            items.add(new Pair<String, MarketListFragment>(tokenItem.getTokenName(),marketListFragment));
        }
        marketAdapter = new MarketAdapter(getSupportFragmentManager());
        viewPager.setAdapter(marketAdapter);
        tab.setupWithViewPager(viewPager);
//        tab.setTabTextColors(getResources().getColor(R.color.color_white),getResources().getColor(R.color.color_black));
        tab.setTabMode(TabLayout.MODE_SCROLLABLE);
        tab.setTabGravity(TabLayout.GRAVITY_CENTER);

        viewPager.addOnPageChangeListener(this);
        CommonUtil.setUpIndicatorWidthByReflex(tab,15,15);
    }

    @Override
    public void showTabOfQuoteTokens(LinkedHashMap<String, QuoteTokensBean.TokenItem> tokens) {
        initFragmentTab(tokens);
    }

    /**
     * 默认返回自选
     * @return
     */
    @Override
    public String getCurrentTab() {
        if (marketAdapter != null) {
            CharSequence pageTitle = marketAdapter.getPageTitle(viewPager.getCurrentItem());
            if (pageTitle != null) {
                return pageTitle.toString();
            }
        }
        return getString(R.string.string_favorite);
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {
        String tabName;
        if (marketAdapter != null) {
            tabName = marketAdapter.getPageTitle(viewPager.getCurrentItem()).toString();
        }else{
            tabName = getString(R.string.string_favorite);
        }
        getPresenter().changePage(tabName);
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    @Override
    public void notifyDataChange(QuoteTokensBean.TokenItem tokenItem) {
        if(items.get(viewPager.getCurrentItem()).second != null)
            items.get(viewPager.getCurrentItem()).second.OnDataChangeNotify(tokenItem);
    }

    private class MarketAdapter extends FragmentPagerAdapter {

        public MarketAdapter(FragmentManager fm) {
            super(fm);
        }

        @Override
        public Fragment getItem(int position) {
            return items.get(position).second;
        }

        @Override
        public int getCount() {
            return items.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return items.get(position).first;
        }


    }
}
