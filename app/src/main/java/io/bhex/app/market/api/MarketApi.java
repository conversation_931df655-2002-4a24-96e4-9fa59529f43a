/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MarketApi.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.market.api;

import android.content.Context;

import io.bhex.app.R;
import io.bhex.baselib.network.params.PostParams;
import io.bhex.sdk.Urls;
import io.bhex.sdk.account.AccountInfoApi;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.baselib.network.BParamsBuilder;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.ResultResponse;
import io.bhex.sdk.account.UserInfo;
import io.bhex.baselib.network.HttpUtils;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.quote.bean.CoinPairListBean;

public class MarketApi {

    /**
     * 获取-推荐币对
     * @param observer
     */
    public static void getRecommendSymbols(SimpleResponseListener<CoinPairListBean> observer){
        PostParams params = BParamsBuilder.post()
                .url(Urls.API_RECOMMEND_SYMBOLS_URL)
                .build();
        HttpUtils.getInstance().request(params, CoinPairListBean.class, observer);
    }

    public static void favoriteCoin(final Context context, final CoinPairBean coinPairBean, final CallBack callBack) {
        if (UserInfo.isLogin()) {

            boolean isFavorite = AppConfigManager.GetInstance().isFavorite(coinPairBean);
            AccountInfoApi.RequestFavoriteSymbol(isFavorite,coinPairBean.getExchangeId(),coinPairBean.getSymbolId(),new SimpleResponseListener<ResultResponse>(){
                @Override
                public void onBefore() {
                    super.onBefore();
                }

                @Override
                public void onFinish() {
                    super.onFinish();
                }

                @Override
                public void onSuccess(ResultResponse response) {
                    super.onSuccess(response);
                    if (CodeUtils.isSuccess(response)) {
                        boolean success = response.isSuccess();
                        if (success) {
                            ToastUtils.showShort(isFavorite?context.getString(R.string.string_favorite_cancel_success):context.getString(R.string.string_favorite_add_success));
                            callBack.success(coinPairBean.getSymbolId());
                            return;
                        }
                    }
                    ToastUtils.showShort(isFavorite?context.getString(R.string.string_favorite_cancel_failed):context.getString(R.string.string_favorite_add_failed));
                    callBack.failed();
                    return;
                }
            });

        }else{

            if (callBack != null) {
                callBack.success(coinPairBean.getSymbolId());
            }
        }

    }

    public interface CallBack{
        void success(Object obj);
        void failed();
    }
}
