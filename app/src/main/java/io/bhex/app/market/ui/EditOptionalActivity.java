package io.bhex.app.market.ui;

import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;

import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.HashMap;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.market.api.MarketApi;
import io.bhex.app.market.presenter.EditOptionalPresenter;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.app.view.TopBar;
import io.bhex.app.view.swipe.MoveAndSwipedAdapter;
import io.bhex.app.view.swipe.callback.TouchCallBack;
import io.bhex.sdk.account.UserInfo;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.quote.bean.CoinPairBean;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-04-18
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class EditOptionalActivity extends BaseActivity<EditOptionalPresenter, EditOptionalPresenter.EditOptionalUI> implements EditOptionalPresenter.EditOptionalUI, View.OnClickListener, MoveAndSwipedAdapter.OnItemListener {
    private TopBar topBar;
    private TextView rightTitleTv;
    private RecyclerView recyclerView;
    private CheckBox selectAllCB;
    private TextView deleteSelected;
    private MoveAndSwipedAdapter mAdapter;

    @Override
    protected int getContentView() {
        return R.layout.activity_edit_optional_layout;
    }

    @Override
    protected EditOptionalPresenter createPresenter() {
        return new EditOptionalPresenter();
    }

    @Override
    protected EditOptionalPresenter.EditOptionalUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        recyclerView = viewFinder.find(R.id.recyclerView);
        selectAllCB = viewFinder.find(R.id.selectAllCB);
        deleteSelected = viewFinder.find(R.id.deleteSelected);

        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new MoveAndSwipedAdapter(this,this);
        recyclerView.setAdapter(mAdapter);
        //创建ItemTouchHelper
        ItemTouchHelper touchHelper=new ItemTouchHelper(new TouchCallBack(mAdapter));
        //attach到RecyclerView中
        touchHelper.attachToRecyclerView(recyclerView);

        clearFavoriteListStatus();
    }

    private void clearFavoriteListStatus() {
        List<CoinPairBean> favorites = getFavorites();
        if (favorites != null&&favorites.size()>0) {
            //首次显示，清除以前选中状态记录
            for (CoinPairBean favorite : favorites) {
                favorite.setSelect(false);
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        showFavoriteList();
    }

    private void showFavoriteList() {
        List<CoinPairBean> favorites = getFavorites();
        mAdapter.setDatas(favorites);
    }

    private List<CoinPairBean> getFavorites() {

       return AppConfigManager.GetInstance().getFavoriteSymbols();
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.btnAdd).setOnClickListener(this);
        viewFinder.find(R.id.btnFinish).setOnClickListener(this);
        viewFinder.find(R.id.deleteSelected).setOnClickListener(this);
        selectAllCB.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                List<CoinPairBean> optionalList = mAdapter.getDatas();
                HashMap<String,CoinPairBean> slectDatasMap = mAdapter.getSelectedDatas();
                if (isChecked) {
                    slectDatasMap.clear();
                }
                for (CoinPairBean coinPairBean : optionalList) {
                    coinPairBean.setSelect(isChecked);
                    if (isChecked) {
                        slectDatasMap.put(coinPairBean.getSymbolId(),coinPairBean);
                    }else{
                        slectDatasMap.remove(coinPairBean.getSymbolId());
                    }
                }
                notifyCheck(slectDatasMap);
                mAdapter.notifyDataSetChanged();
            }
        });
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.btnAdd:
                IntentUtils.goSearch(this);
                break;
            case R.id.btnFinish:
                List<CoinPairBean> datas = mAdapter.getDatas();
                KlineUtils.saveFavorite(datas);
                mAdapter.notifyDataSetChanged();
                //TODO
                finish();
                break;
            case R.id.deleteSelected:
                List<CoinPairBean> mAllDatas = mAdapter.getDatas();
                HashMap<String,CoinPairBean> selectedDatasMap = mAdapter.getSelectedDatas();
                if (selectedDatasMap.size()>0) {
                    AppConfigManager.GetInstance().cancelLocalFavorite(selectedDatasMap);
                    for (String keys : selectedDatasMap.keySet()) {
                        CoinPairBean deleteItem = selectedDatasMap.get(keys);
                        if (deleteItem != null) {
                            mAllDatas.remove(deleteItem);
                        }
                    }
                    KlineUtils.saveFavorite(mAllDatas);

                    selectedDatasMap.clear();
                    notifyCheck(selectedDatasMap);

                    mAdapter.notifyDataSetChanged();
                    finish();
                }
                break;
        }
    }

    private void deleteServerFavorite(CoinPairBean deleteItem) {
        if (UserInfo.isLogin()) {

            MarketApi.favoriteCoin(this, deleteItem, new MarketApi.CallBack() {
                @Override
                public void success(Object obj) {
                    AppConfigManager.GetInstance().cancelLocalFavorite(deleteItem);
                }

                @Override
                public void failed() {

                }
            });
        }
    }

    @Override
    public void notifyCheck(HashMap<String,CoinPairBean> selectedMap) {
        int size = selectedMap.size();
        if (size ==0) {
            deleteSelected.setText(getResources().getString(R.string.string_delete_selected));
            deleteSelected.setTextColor(SkinColorUtil.getDark50(this));
        }else{
            deleteSelected.setText(getResources().getString(R.string.string_delete_selected)+"("+size+")");
            deleteSelected.setTextColor(getResources().getColor(R.color.blue));
        }
    }

    @Override
    public void onItemClick(int position, CoinPairBean coinPairBean) {

    }
}
