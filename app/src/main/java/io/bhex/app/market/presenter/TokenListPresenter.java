/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: TokenListPresenter.java
 *   @Date: 19-4-28 下午2:53
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.market.presenter;

import android.text.TextUtils;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.AppUI;
import io.bhex.app.utils.NetWorkStatus;
import io.bhex.baselib.mvp.BaseCoreActivity;
import io.bhex.baselib.mvp.BasePresenter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.UISafeKeeper;
import io.bhex.baselib.utils.ToastUtils;
import io.bhex.sdk.account.LoginApi;
import io.bhex.sdk.account.UserManager;
import io.bhex.sdk.account.bean.UserInfoBean;
import io.bhex.sdk.quote.bean.QuoteTokensBean;
import io.bhex.sdk.trade.AssetApi;

public class TokenListPresenter extends BasePresenter<TokenListPresenter.TokenListUI> {

    private List<QuoteTokensBean.TokenItem> currentTokens;

    /**
     * @param searchTxt
     */
    public void search(String searchTxt) {
        if (TextUtils.isEmpty(searchTxt)) {
            if (currentTokens != null) {
                getUI().showTokenList(currentTokens);
            }
        } else {
            filterSearchList(searchTxt);
        }
    }

    /**
     * 过滤搜索结果
     * @param searchTxt
     */
    private void filterSearchList(String searchTxt) {
        startFilter(currentTokens,searchTxt);
    }

    //过滤集合
    private List<QuoteTokensBean.TokenItem> filterList = new ArrayList<>();
    /**
     * 开始过滤
     * @param coinPairList
     * @param searchTxt
     */
    private void startFilter(List<QuoteTokensBean.TokenItem> coinPairList, String searchTxt) {
        if (coinPairList == null || coinPairList.isEmpty()) {
            return;
        }
        filterList.clear();
        for (QuoteTokensBean.TokenItem coinPairBean : coinPairList) {
            if (isMatch(searchTxt,coinPairBean)) {
                filterList.add(coinPairBean);
            }
        }

        getUI().showTokenList(filterList);
    }

    private boolean isMatch(String searchContent, QuoteTokensBean.TokenItem coinPairBean) {
        String tokenName = coinPairBean.getTokenName();
        if (!TextUtils.isEmpty(searchContent)) {
            searchContent = searchContent.toUpperCase();
        }
        if (!TextUtils.isEmpty(tokenName)) {
            tokenName = tokenName.toUpperCase();
            return tokenName.contains(searchContent);
        }
        return false;
    }

    public interface TokenListUI extends AppUI{
        void showTokenList(List<QuoteTokensBean.TokenItem> datas);

        void showUserInfo(UserInfoBean data);
    }

    @Override
    public void onUIReady(BaseCoreActivity activity, TokenListUI ui) {
        super.onUIReady(activity, ui);
        getTokens();
    }

    /**
     * 获取Token集合
     */
    public void getTokens() {
        AssetApi.RequestTokens(UISafeKeeper.guard(getUI(),new SimpleResponseListener<List<QuoteTokensBean.TokenItem>>(){
            @Override
            public void onBefore() {
                super.onBefore();
            }

            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(List<QuoteTokensBean.TokenItem> response) {
                super.onSuccess(response);
                currentTokens = response;
                getUI().showTokenList(currentTokens);
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        }));
    }

    public void getUserInfo() {
        if (!NetWorkStatus.isConnected(getActivity())) {
            ToastUtils.showShort(getActivity(), getResources().getString(R.string.hint_network_not_connect));
            return;
        }
        LoginApi.GetUserInfo(new SimpleResponseListener<UserInfoBean>(){
            @Override
            public void onFinish() {
                super.onFinish();
            }

            @Override
            public void onSuccess(UserInfoBean data) {
                super.onSuccess(data);
                if (CodeUtils.isSuccess(data,true)) {
                    //保存用户数据
                    UserManager.getInstance().saveUserInfo(data);
                    getUI().showUserInfo(data);
                }
            }

            @Override
            public void onError(Throwable error) {
                super.onError(error);
            }
        });
    }


}
