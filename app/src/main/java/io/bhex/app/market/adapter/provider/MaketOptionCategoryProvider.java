/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MaketOptionCategoryProvider.java
 *   @Date: 19-5-24 下午3:56
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.market.adapter.provider;

import android.text.TextUtils;

import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.provider.BaseItemProvider;
import io.bhex.app.R;
import io.bhex.app.market.adapter.MarketListMultiAdapter;
import io.bhex.sdk.quote.bean.CoinPairBean;

public class MaketOptionCategoryProvider extends BaseItemProvider<CoinPairBean, BaseViewHolder> {
    @Override
    public int viewType() {
        return MarketListMultiAdapter.ITEM_TYPE_OPTION_CATEGORY;
    }

    @Override
    public int layout() {
        return R.layout.item_market_category_layout;
    }

    @Override
    public void convert(BaseViewHolder baseViewHolder, final CoinPairBean itemModel, int position) {
        if (itemModel != null) {
            CoinPairBean.BaseTokenOption baseTokenOption = itemModel.baseTokenOption;
            if (baseTokenOption != null) {
                String categoryName = baseTokenOption.categoryName;
                if (!TextUtils.isEmpty(categoryName)) {
                    baseViewHolder.setText(R.id.category_title, categoryName);
                }
            }
        }
    }
}
