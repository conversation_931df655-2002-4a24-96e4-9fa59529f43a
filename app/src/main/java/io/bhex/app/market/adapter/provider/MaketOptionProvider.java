/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MaketOptionProvider.java
 *   @Date: 19-5-24 下午3:25
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.market.adapter.provider;

import android.content.Context;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.provider.BaseItemProvider;

import io.bhex.app.R;
import io.bhex.app.market.adapter.MarketListMultiAdapter;
import io.bhex.app.market.event.ItemClickStatusListener;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.data_manager.RateDataManager;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.QuoteBean;
import io.bhex.sdk.quote.bean.TickerBean;

public class MaketOptionProvider extends BaseItemProvider<CoinPairBean,BaseViewHolder> {
    private final ItemClickStatusListener mItemClickStatusListener;
    private boolean mIsFromTrade;

    public MaketOptionProvider(boolean isFromTrade, ItemClickStatusListener itemClickStatusListener) {
        mIsFromTrade = isFromTrade;
        mItemClickStatusListener = itemClickStatusListener;
    }

    @Override
    public int viewType() {
        return MarketListMultiAdapter.ITEM_TYPE_OPTION;
    }

    @Override
    public int layout() {
        return R.layout.item_market_list_layout;
    }

    @Override
    public void convert(BaseViewHolder baseViewHolder, final CoinPairBean itemModel, int position) {
        final Context myContext = mContext;
        baseViewHolder.setGone(R.id.item_range_ratio,!mIsFromTrade);
        baseViewHolder.setGone(R.id.item_amount,!mIsFromTrade);
        baseViewHolder.setGone(R.id.item_price2,!mIsFromTrade);
        TickerBean tickerBean = itemModel.getTicker();
        String quoteTokenId = itemModel.getQuoteTokenId();
        String symbolStr = itemModel.getSymbolName();
        int baseDigit = NumberUtils.calNumerCount(myContext, itemModel.getBasePrecision());
        int pricDigit = NumberUtils.calNumerCount(myContext, itemModel.getMinPricePrecision());

//        baseViewHolder.setVisible(R.id.item_divider, baseViewHolder.getAdapterPosition() != mData.size());

        baseViewHolder.setText(R.id.item_coinpair, symbolStr);

        String call = "";
        baseViewHolder.setVisible(R.id.option_left_text, true);
        if (KlineUtils.isOptionCall(itemModel.baseTokenOption.isCall)) {
            call = myContext.getString(R.string.string_option_call);
            baseViewHolder.setTextColor(R.id.option_left_text,SkinColorUtil.getGreen(myContext));
            baseViewHolder.getView(R.id.option_left_text).setBackgroundResource(SkinColorUtil.getGreenRectBg(myContext));
        } else {
            call = myContext.getString(R.string.string_option_put);
            baseViewHolder.setTextColor(R.id.option_left_text,SkinColorUtil.getRed(myContext));
            baseViewHolder.getView(R.id.option_left_text).setBackgroundResource(SkinColorUtil.getRedRectBg(myContext));

        }
        baseViewHolder.setText(R.id.option_left_text, call);
        if (tickerBean != null) {

            baseViewHolder.setText(R.id.item_amount, myContext.getString(R.string.string_vol) + "  " + NumberUtils.roundFormatDown(tickerBean.getV(), AppData.DIGIT_24H_AMOUNT));
            baseViewHolder.setText(R.id.item_price1, NumberUtils.roundFormatDown(tickerBean.getC(), pricDigit));
            String legalMoney = RateDataManager.CurRatePrice(quoteTokenId, tickerBean.getC());
            legalMoney = RateDataManager.getShowLegalMoney(legalMoney, AppData.DIGIT_LEGAL_MONEY);
            baseViewHolder.setText(R.id.item_price2, "≈"+legalMoney);

            baseViewHolder.setText(R.id.item_range_ratio, KlineUtils.calRiseFallRatio(tickerBean.getM()));
            baseViewHolder.setTextColor(R.id.item_range_ratio, ContextCompat.getColor(myContext, CommonUtil.isBlackMode()? R.color.dark_night : R.color.white));
            float riseFallAmount = KlineUtils.calRiseFallAmountFloat(tickerBean.getC(), tickerBean.getO());
            if (riseFallAmount > 0) {
                baseViewHolder.setBackgroundRes(R.id.item_range_ratio, SkinColorUtil.getGreenBg(myContext));
                baseViewHolder.setTextColor(R.id.item_price1, SkinColorUtil.getGreen(myContext));
            } else if (riseFallAmount < 0) {
                baseViewHolder.setBackgroundRes(R.id.item_range_ratio, SkinColorUtil.getRedBg(myContext));
                baseViewHolder.setTextColor(R.id.item_price1, SkinColorUtil.getRed(myContext));
            } else {//涨跌幅0默认为涨 显示为绿色
                baseViewHolder.setBackgroundRes(R.id.item_range_ratio, R.drawable.bg_corner_gray);
                baseViewHolder.setTextColor(R.id.item_price1, SkinColorUtil.getDark50(myContext));
            }
        } else {
            baseViewHolder.setText(R.id.item_amount, myContext.getString(R.string.string_vol) + "  " + myContext.getString(R.string.string_placeholder));
            baseViewHolder.setText(R.id.item_price1, myContext.getString(R.string.string_placeholder));
            baseViewHolder.setText(R.id.item_price2, myContext.getString(R.string.string_placeholder));

            baseViewHolder.setText(R.id.item_range_ratio, myContext.getString(R.string.string_placeholder));
            baseViewHolder.setBackgroundRes(R.id.item_range_ratio, SkinColorUtil.getGreenBg(myContext));
        }

        baseViewHolder.getView(R.id.itemView).setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()){
                    case MotionEvent.ACTION_DOWN:
                        if (mItemClickStatusListener != null) {
                            mItemClickStatusListener.onItemStatusListener(true);
                        }
//                        DebugLog.e(TGA,"RecyclerView_ITEM_ACTION_DOWN");
                        break;

                    case MotionEvent.ACTION_MOVE:
//                        DebugLog.e(TGA,"RecyclerView_ITEM_ACTION_MOVE");

                        break;

                    case MotionEvent.ACTION_UP:
//                        DebugLog.e(TGA,"RecyclerView_ITEM_ACTION_UP");
                        if (mItemClickStatusListener != null) {
                            mItemClickStatusListener.onItemStatusListener(false);
                        }
                        break;

                    case MotionEvent.ACTION_CANCEL:
//                        DebugLog.e(TGA,"RecyclerView_ITEM_ACTION_CANCEL");
                        if (mItemClickStatusListener != null) {
                            mItemClickStatusListener.onItemStatusListener(false);
                        }
                        break;
                }
                return false;
            }
        });

        if (mIsFromTrade) {
            //交易见面点击事件
            baseViewHolder.addOnClickListener(R.id.itemView);
        }
    }

    @Override
    public void onClick(BaseViewHolder helper, CoinPairBean data, int position) {
        super.onClick(helper, data, position);
        if (!mIsFromTrade) {
            IntentUtils.goKline(mContext, data);
        }
    }
}
