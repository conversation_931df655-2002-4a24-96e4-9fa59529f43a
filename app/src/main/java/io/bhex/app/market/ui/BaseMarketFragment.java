/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BaseMarketFragment.java
 *   @Date: 1/14/19 8:05 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.market.ui;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.core.util.Pair;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;

import java.util.LinkedHashMap;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseFragment;
import io.bhex.app.market.presenter.BaseMarketFragmentPresenter;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.app.view.TopBar;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.quote.bean.QuoteTokensBean;


public abstract class BaseMarketFragment<P extends BaseMarketFragmentPresenter<V>, V extends BaseMarketFragmentPresenter.BaseMarketFragmentUI> extends BaseFragment<P, V> implements BaseMarketFragmentPresenter.BaseMarketFragmentUI, ViewPager.OnPageChangeListener, View.OnClickListener {
    protected TopBar topBar;
    protected ViewPager viewPager;
    protected TabLayout tab;
    protected List<Pair<QuoteTokensBean.TokenItem, MarketListFragment>> items;
    protected MarketAdapter marketAdapter;
    protected int currentPage = 1;
    private MarketFragment.HomeMarktControl mHomeControl;


    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_bb_market_layout, null, false);
    }

    @Override
    protected void initViews() {
        super.initViews();
        // topBar = viewFinder.find(R.id.topBar);
        // this.setTopbarNight(topBar);

        viewPager = viewFinder.find(R.id.viewPager);
        tab = viewFinder.find(R.id.tab);
        tab.setTabTextColors(SkinColorUtil.getDark(this.getActivity()), getResources().getColor(R.color.blue));
        /*
            app:tabSelectedTextColor="@color/blue"
            app:tabTextColor="@color/dark"
         */
    }

    @Override
    public void onResume() {
        super.onResume();
//        tab.setTabTextColors(SkinColorUtil.getDark(this.getActivity()), getResources().getColor(R.color.blue));
        tab.setTabTextColors(SkinColorUtil.getDark(this.getActivity()), ContextCompat.getColor(getActivity(),R.color.blue));
    }

    public void setHomeControl(MarketFragment.HomeMarktControl control){
        mHomeControl = control;
    }


    @Override
    protected void addEvent() {
        super.addEvent();
    }

    /**
     * 市场fragment
     *
     * @param tabMap
     */
    abstract protected void initFragmentTab(LinkedHashMap<String, QuoteTokensBean.TokenItem> tabMap) ;

    @Override
    public void showTabOfQuoteTokens(LinkedHashMap<String, QuoteTokensBean.TokenItem> tokens) {
        initFragmentTab(tokens);
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    /**
     * 默认返回自选
     *
     * @return
     */
    @Override
    public String getCurrentTab() {
        if (marketAdapter != null) {
//            String pageTitle = marketAdapter.getPageTitle(viewPager.getCurrentItem()).toString();
            int currentItem = viewPager.getCurrentItem();
            String pageTitle = items.get(currentItem).first.getTokenId();
            /**体验币 因为title 显示做了特殊处理，所以在获取返回值需要特殊处理一下，转换回原来的tokenId,方便根据tokenid查询行情列表数据 @OptionMarketFragment ******/
            String truthPageTitle = AppConfigManager.GetInstance().getExploreTokenMap().get(pageTitle);
            if (!TextUtils.isEmpty(truthPageTitle)) {
                return truthPageTitle;
            }
            //非体验币正常返回
            return pageTitle;
        }
        return AppData.KEY_FAVORITE;
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    abstract public void onPageSelected(int position);

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
        }
    }



    protected class MarketAdapter extends FragmentPagerAdapter {

        public MarketAdapter(FragmentManager fm) {
            super(fm);
        }

        @Override
        public MarketListFragment getItem(int position) {
            return items.get(position).second;
        }

        @Override
        public int getCount() {
            return items.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return items.get(position).first.getTabName();
        }

        @Override
        public int getItemPosition(@NonNull Object object) {
            return POSITION_NONE;
        }

        public String getTabId(int currentItem) {
            return items.get(currentItem).first.getTokenId();
        }
    }

    @Override
    public void notifyDataChange(QuoteTokensBean.TokenItem tokenItem) {
        if(items.get(viewPager.getCurrentItem()).second != null)
            items.get(viewPager.getCurrentItem()).second.OnDataChangeNotify(tokenItem);
    }

    @Override
    public boolean isSelected(){
        if(mHomeControl != null){
            return mHomeControl.IsSelected();
        }

        Bundle arguments = getArguments();
        boolean isSelect = true;
        if (arguments != null) {
            isSelect = arguments.getBoolean("isSelect",true);
        }
        return isSelect;
    }

    public void getRealTimeData(){
        if(getPresenter() != null)
            getPresenter().getRealTimeData();
    }
}
