package io.bhex.app.market.presenter;

import java.util.HashMap;
import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.baselib.mvp.BaseFragmentPresenter;
import io.bhex.sdk.quote.QuoteApi;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.TickerBean;
import io.bhex.sdk.quote.bean.TickerListBean;
import io.bhex.sdk.socket.NetWorkObserver;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-04-20
 * 邮   箱：
 * 描   述：
 * ================================================
 */

public class OptionalMarketPresenter extends BaseFragmentPresenter<OptionalMarketPresenter.OptionalMarketUI> {
    public interface OptionalMarketUI extends AppUI{

        void showMarket(List<CoinPairBean> coinPairList);
    }

    private HashMap<String,CoinPairBean> optionalMap = new HashMap<>();

    /**
     * 拼接行情参数【全部币对ALL】
     *
     * @return
     * @param coinPairList
     */
    protected String getSymbolsListStr(List<CoinPairBean> coinPairList) {
        StringBuffer stringBuffer = new StringBuffer();

        for (CoinPairBean coinPairBean : coinPairList) {
            if (coinPairBean != null) {
                optionalMap.put(coinPairBean.getSymbolId(), coinPairBean);
                String symbol = coinPairBean.getExchangeId() + "." + coinPairBean.getSymbolId();
                if (stringBuffer.toString().length() > 0) {
                    stringBuffer.append("," + symbol);
                } else {
                    stringBuffer.append(symbol);
                }
            }
        }
        return stringBuffer.toString();
    }

    public void getRealTimeData(List<CoinPairBean> coinPairList){
        String symbolsListStr = getSymbolsListStr(coinPairList);
        QuoteApi.SubTickers(symbolsListStr, new NetWorkObserver<TickerListBean>() {
            @Override
            public void onShowUI(TickerListBean response) {
                if (getUI() == null || !getUI().isAlive() || response == null)
                    return;

                List<TickerBean> datas = response.getData();
                if (datas != null) {
                    for (TickerBean data : datas) {
                        handleSocketMarketList(data);
                    }
                    getUI().showMarket(coinPairList);
                }
            }

            @Override
            public void onError(String error) {
            }
        });
    }

    private void handleSocketMarketList(TickerBean tickerBean) {
        if (optionalMap != null) {
            String tickerSymbolId = tickerBean.getS();
            if (optionalMap.containsKey(tickerSymbolId)) {
                CoinPairBean coinPairBean = optionalMap.get(tickerSymbolId);
                if (coinPairBean != null) {
                    String symbol = coinPairBean.getSymbolId();
                    if (symbol.equals(tickerSymbolId)) {
                        coinPairBean.setTicker(tickerBean);
                    }
                }
            }
        }

    }

}
