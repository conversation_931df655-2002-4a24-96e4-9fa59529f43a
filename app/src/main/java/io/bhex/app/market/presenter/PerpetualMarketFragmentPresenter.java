/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: PerpetualMarketFragmentPresenter.java
 *   @Date: 19-6-11 上午11:43
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.market.presenter;


import android.os.Bundle;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import io.bhex.app.market.adapter.MarketListMultiAdapter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.UISafeKeeper;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.NewCoinPairListBean;
import io.bhex.sdk.quote.bean.OptionCategoryBean;
import io.bhex.sdk.quote.bean.QuoteTokensBean;

public class PerpetualMarketFragmentPresenter extends BaseMarketFragmentPresenter<PerpetualMarketFragmentPresenter.PerpetualMarketFragmentUI> {
    private static final String LOGTAG = "FuturesMarketFragmentPresent";


    public interface PerpetualMarketFragmentUI extends BaseMarketFragmentPresenter.BaseMarketFragmentUI {

    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppConfigManager.GetInstance().setFuturesSymbolsChangeObserver(new AppConfigManager.FuturesSymbolsChange() {
            @Override
            public void onFuturesSymbolsChange() {
                getCoinPairList();
            }
        });
    }

    @Override
    public void getCoinPairList() {
        AppConfigManager.GetInstance().getAppConfig(UISafeKeeper.guard(getUI(), new SimpleResponseListener<NewCoinPairListBean>() {

            @Override
            public void onSuccess(NewCoinPairListBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    if(response.futuresUnderlying !=null ) {
                        if(!getSymbolsListStr(mCoinPairList).equalsIgnoreCase(getSymbolsListStr(response.futuresSymbol))) {
                            LinkedHashMap<String, QuoteTokensBean.TokenItem> tabMap = createTabMapOfFutures(response.futuresUnderlying);
                            getUI().showTabOfQuoteTokens(tabMap);
                            //getCoinPairList();
                            mCoinPairList = response.futuresSymbol;
                            handCoinPairListData(response.futuresSymbol);
                            getRealTimeData();
                        }
                    }

                }
            }

        }));

    }

    /**
     * 处理币对列表数据
     *
     * @param listBean
     */
    protected void handCoinPairListData(List<CoinPairBean> listBean) {
        List<CoinPairBean> coinPairList = listBean;
        if (coinPairList != null && !coinPairList.isEmpty()) {

            for (CoinPairBean coinPairBean : coinPairList) {
                coinPairBean.setItemShowType(MarketListMultiAdapter.ITEM_TYPE_FUTURES);
                String tabId = coinPairBean.getFirstLevelUnderlyingId();
                /****非自选列表数据处理**/
                if (tabMap.containsKey(tabId)) {
                    QuoteTokensBean.TokenItem tabTokenItem = tabMap.get(tabId);
                    if (tabTokenItem != null) {
                        HashMap<String, CoinPairBean> coinPairMap = tabTokenItem.getCoinPairMap();
                        coinPairMap.put(coinPairBean.getBaseTokenId(), coinPairBean);
                    }
                } else {
                    DebugLog.w("market", tabId + " tab is null");
                }


            }
            notifyCoinPairListDataChange(tabMap);
        }
    }

    @Override
    protected LinkedHashMap<String, QuoteTokensBean.TokenItem> createTabMap(List<QuoteTokensBean.TokenItem> response) {
        return null;
    }

    protected LinkedHashMap<String, QuoteTokensBean.TokenItem> createTabMapOfFutures(List<OptionCategoryBean> tabs) {

        if (tabs != null) {

            tabMap.clear();

            for (OptionCategoryBean tabItem : tabs) {
                DebugLog.e("MAP", tabItem.getId() + tabItem.getName());
                QuoteTokensBean.TokenItem tokenItem = new QuoteTokensBean.TokenItem();
                tokenItem.setTokenId(tabItem.getId());
                tokenItem.setTabName(tabItem.getName());
                tokenItem.setTokenName(tabItem.getName());
                tokenItem.setTokenFullName(tabItem.getName());
                tabMap.put(tokenItem.getTokenId(), tokenItem);
            }
        }
        return tabMap;
    }



}
