/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MarketListAdapter.java
 *   @Date: 19-4-25 下午4:37
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.market.adapter;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.data_manager.RateDataManager;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.TickerBean;

public class MarketListAdapter  extends BaseQuickAdapter<CoinPairBean, BaseViewHolder> {

    private final Context myContext;

    public MarketListAdapter(Context context, List<CoinPairBean> data) {
        super(R.layout.item_market_list_layout, data);
        myContext = context;
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final CoinPairBean itemModel) {
        TickerBean tickerBean = itemModel.getTicker();
        String quoteTokenId = itemModel.getQuoteTokenId();
        String symbolStr = itemModel.getSymbolName();
        if(itemModel.baseTokenOption == null)
            symbolStr = itemModel.getBaseTokenName() + " / " + itemModel.getQuoteTokenName();
//            SpannableStringBuilder builder = new SpannableStringBuilder(symbolStr);
//            ForegroundColorSpan quoteColor = new ForegroundColorSpan(getResources().getColor(R.color.font_color2));
//            builder.setSpan(quoteColor, symbolStr.indexOf("/"), symbolStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        int baseDigit = NumberUtils.calNumerCount(myContext, itemModel.getBasePrecision());
        int pricDigit = NumberUtils.calNumerCount(myContext, itemModel.getMinPricePrecision());

        baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IntentUtils.goKline(myContext, itemModel);
            }
        });

        baseViewHolder.setVisible(R.id.item_divider, baseViewHolder.getAdapterPosition() != mData.size());

        baseViewHolder.setText(R.id.item_coinpair, symbolStr);

        if(itemModel.baseTokenOption != null) {
            String call = "";
            baseViewHolder.setVisible(R.id.option_left_text, true);
            if (KlineUtils.isOptionCall(itemModel.baseTokenOption.isCall)) {
                call = myContext.getString(R.string.string_option_call);
                ((TextView) baseViewHolder.getView(R.id.option_left_text)).setTextAppearance(myContext, R.style.Mini_Green);
                baseViewHolder.getView(R.id.option_left_text).setBackgroundResource(SkinColorUtil.getGreenRectBg(myContext));
            } else {
                call = myContext.getString(R.string.string_option_put);
                ((TextView) baseViewHolder.getView(R.id.option_left_text)).setTextAppearance(myContext, R.style.Mini_Red);
                baseViewHolder.getView(R.id.option_left_text).setBackgroundResource(SkinColorUtil.getRedRectBg(myContext));

            }
            baseViewHolder.setText(R.id.option_left_text, call);
        }
        else
            baseViewHolder.setGone(R.id.option_left_text, false);
        if (tickerBean != null) {

            baseViewHolder.setText(R.id.item_amount, myContext.getString(R.string.string_vol) + "  " + NumberUtils.roundFormatDown(tickerBean.getV(), AppData.DIGIT_24H_AMOUNT));
            baseViewHolder.setText(R.id.item_price1, NumberUtils.roundFormatDown(tickerBean.getC(), pricDigit));
            String legalMoney = RateDataManager.CurRatePrice(quoteTokenId, tickerBean.getC());
            legalMoney = RateDataManager.getShowLegalMoney(legalMoney, AppData.DIGIT_LEGAL_MONEY);
            baseViewHolder.setText(R.id.item_price2, "≈"+legalMoney);

            baseViewHolder.setTextColor(R.id.item_range_ratio, ContextCompat.getColor(myContext, CommonUtil.isBlackMode()? R.color.dark_night : R.color.white));
            baseViewHolder.setText(R.id.item_range_ratio, KlineUtils.calRiseFallRatio(tickerBean.getM()));
            float riseFallAmount = KlineUtils.calRiseFallAmountFloat(tickerBean.getC(), tickerBean.getO());
            if (riseFallAmount > 0) {
                baseViewHolder.setBackgroundRes(R.id.item_range_ratio, SkinColorUtil.getGreenBg(myContext));
                baseViewHolder.setTextColor(R.id.item_price1, SkinColorUtil.getGreen(myContext));
            } else if (riseFallAmount < 0) {
                baseViewHolder.setBackgroundRes(R.id.item_range_ratio, SkinColorUtil.getRedBg(myContext));
                baseViewHolder.setTextColor(R.id.item_price1, SkinColorUtil.getRed(myContext));
            } else {//涨跌幅0默认为涨 显示为绿色
                baseViewHolder.setBackgroundRes(R.id.item_range_ratio, SkinColorUtil.getGreenBg(myContext));
                baseViewHolder.setTextColor(R.id.item_price1, SkinColorUtil.getGreen(myContext));
            }
        } else {
            baseViewHolder.setText(R.id.item_amount, myContext.getString(R.string.string_vol) + "  " + myContext.getString(R.string.string_placeholder));
            baseViewHolder.setText(R.id.item_price1, myContext.getString(R.string.string_placeholder));
            baseViewHolder.setText(R.id.item_price2, myContext.getString(R.string.string_placeholder));

            baseViewHolder.setText(R.id.item_range_ratio, myContext.getString(R.string.string_placeholder));
            baseViewHolder.setBackgroundRes(R.id.item_range_ratio, SkinColorUtil.getGreenBg(myContext));
        }
    }
}
