/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MaketOptionSwitchProvider.java
 *   @Date: 19-5-26 下午2:56
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.market.adapter.provider;

import android.content.Context;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;
import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.provider.BaseItemProvider;

import io.bhex.app.R;
import io.bhex.app.market.adapter.MarketListMultiAdapter;
import io.bhex.app.market.event.ItemClickStatusListener;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.core.SPEx;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.QuoteBean;
import io.bhex.sdk.quote.bean.TickerBean;

/**
 * 来自切换币对页面（例如交易）
 */
public class MaketOptionSwitchProvider extends BaseItemProvider<CoinPairBean, BaseViewHolder> {
    private static final String TGA = "MaketOptionSwitchProvider";
    private ItemClickStatusListener mItemClickStatusListener;
    private String mSelectedSymbolId;
    private boolean mIsFromTrade;

    public MaketOptionSwitchProvider(boolean isFromTrade, String selectedSymbolId, ItemClickStatusListener itemClickStatusListener) {
        mIsFromTrade = isFromTrade;
        if (TextUtils.isEmpty(selectedSymbolId)) {
            mSelectedSymbolId = "";
        } else {
            mSelectedSymbolId = selectedSymbolId;
        }
        mItemClickStatusListener = itemClickStatusListener;
    }

    @Override
    public int viewType() {
        return MarketListMultiAdapter.ITEM_TYPE_OPTION_SWITCH;
    }

    @Override
    public int layout() {
        return R.layout.item_market_list_select_layout;
    }

    @Override
    public void convert(BaseViewHolder baseViewHolder, final CoinPairBean itemModel, int position) {
        final Context getContext = mContext;
        if (itemModel == null)
            return;
        baseViewHolder.setGone(R.id.item_range_ratio, !mIsFromTrade);
        TickerBean tickerBean = itemModel.getTicker();
        String symbolStr = itemModel.getSymbolName();
//            SpannableStringBuilder builder = new SpannableStringBuilder(symbolStr);
//            ForegroundColorSpan quoteColor = new ForegroundColorSpan(mContext.getResources().getColor(R.color.font_color2));
//            builder.setSpan(quoteColor, symbolStr.indexOf("/"), symbolStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        int amountDigit = NumberUtils.calNumerCount(mContext, itemModel.getBasePrecision());
        int pricDigit = NumberUtils.calNumerCount(mContext, itemModel.getMinPricePrecision());

//        baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//
//                //来自交易界面
//                itemModel.setBuyMode(true);
//                itemModel.setNeedSwitchTradeTab(true);
//                //IntentUtils.goTrade(mContext, itemModel);
//                EventBus.getDefault().postSticky(itemModel);
//                closePop(true);
//            }
//        });

        String call = "";
        baseViewHolder.setVisible(R.id.option_left_text, true);
        if (KlineUtils.isOptionCall(itemModel.baseTokenOption.isCall)) {
            call = mContext.getString(R.string.string_option_call);
            baseViewHolder.setTextColor(R.id.option_left_text,SkinColorUtil.getGreen(mContext));
            baseViewHolder.getView(R.id.option_left_text).setBackgroundResource(SkinColorUtil.getGreenRectBg(mContext));
        } else {
            call = mContext.getString(R.string.string_option_put);
            baseViewHolder.setTextColor(R.id.option_left_text,SkinColorUtil.getRed(mContext));
            baseViewHolder.getView(R.id.option_left_text).setBackgroundResource(SkinColorUtil.getRedRectBg(mContext));

        }
        baseViewHolder.setText(R.id.option_left_text, call);

        baseViewHolder.setText(R.id.item_coinpair, symbolStr);
        if (tickerBean != null) {
            baseViewHolder.setText(R.id.item_price1, NumberUtils.roundFormatDown(tickerBean.getC(), pricDigit));
            baseViewHolder.setText(R.id.item_range_ratio, KlineUtils.calRiseFallRatio(tickerBean.getM()));
            //boolean isSkinBlackMode = SPEx.get(AppData.SPKEY.SKIN_IS_BLACK_MODE, false);
            boolean isSkinBlackMode = CommonUtil.isBlackMode();
            if (mSelectedSymbolId.equals(itemModel.getSymbolId())) {
//                baseViewHolder.getConvertView().setBackgroundColor(isSkinBlackMode ? mContext.getResources().getColor(R.color.divider_line_color) : mContext.getResources().getColor(R.color.divider_line_color));
                baseViewHolder.getView(R.id.select_item_view).setSelected(true);
            } else {
//                    baseViewHolder.getConvertView().setBackgroundColor(isSkinBlackMode ? mContext.getResources().getColor(R.color.color_bg_2_night) : mContext.getResources().getColor(R.color.color_bg_2));
                baseViewHolder.getView(R.id.select_item_view).setSelected(false);
            }
            float riseFallAmount = KlineUtils.calRiseFallAmountFloat(tickerBean.getC(), tickerBean.getO());
            if (riseFallAmount > 0) {
                baseViewHolder.setTextColor(R.id.item_price1, SkinColorUtil.getGreen(mContext));
            } else if (riseFallAmount < 0) {
                baseViewHolder.setTextColor(R.id.item_price1, SkinColorUtil.getRed(mContext));
            } else {
                baseViewHolder.setTextColor(R.id.item_price1, SkinColorUtil.getDark50(mContext));
            }
        } else {
            baseViewHolder.setText(R.id.item_price1, mContext.getString(R.string.string_placeholder));
            baseViewHolder.setText(R.id.item_range_ratio, mContext.getString(R.string.string_placeholder));
            //boolean isSkinBlackMode = SPEx.get(AppData.SPKEY.SKIN_IS_BLACK_MODE, false);
            boolean isSkinBlackMode = CommonUtil.isBlackMode();
            if (mSelectedSymbolId.equals(itemModel.getSymbolId())) {
//                baseViewHolder.getConvertView().setBackgroundColor(isSkinBlackMode ? mContext.getResources().getColor(R.color.divider_line_color_night) : mContext.getResources().getColor(R.color.divider_line_color));
                baseViewHolder.getView(R.id.select_item_view).setSelected(true);
            } else {
//                baseViewHolder.getConvertView().setBackgroundColor(isSkinBlackMode ? mContext.getResources().getColor(R.color.color_bg_2_night) : mContext.getResources().getColor(R.color.color_bg_2));
                baseViewHolder.getView(R.id.select_item_view).setSelected(false);
            }
            baseViewHolder.setTextColor(R.id.item_range_ratio, mContext.getResources().getColor(R.color.font_color2));
        }

        baseViewHolder.getView(R.id.itemView).setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()){
                    case MotionEvent.ACTION_DOWN:
                        if (mItemClickStatusListener != null) {
                            mItemClickStatusListener.onItemStatusListener(true);
                        }
                        DebugLog.e(TGA,"RecyclerView_ITEM_ACTION_DOWN");
                        break;

                    case MotionEvent.ACTION_MOVE:
                        DebugLog.e(TGA,"RecyclerView_ITEM_ACTION_MOVE");

                        break;

                    case MotionEvent.ACTION_UP:
                        DebugLog.e(TGA,"RecyclerView_ITEM_ACTION_UP");
                        if (mItemClickStatusListener != null) {
                            mItemClickStatusListener.onItemStatusListener(false);
                        }
                        break;

                    case MotionEvent.ACTION_CANCEL:
                        DebugLog.e(TGA,"RecyclerView_ITEM_ACTION_CANCEL");
                        if (mItemClickStatusListener != null) {
                            mItemClickStatusListener.onItemStatusListener(false);
                        }
                        break;
                }
                return false;
            }
        });

        if (mIsFromTrade) {
            //交易见面点击事件
            baseViewHolder.addOnClickListener(R.id.itemView);
        }
    }

    @Override
    public void onClick(BaseViewHolder helper, CoinPairBean data, int position) {
        super.onClick(helper, data, position);
        if (!mIsFromTrade) {
            IntentUtils.goKline(mContext, data);
        }
    }
}
