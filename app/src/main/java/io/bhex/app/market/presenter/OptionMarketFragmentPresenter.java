/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OptionMarketFragmentPresent.java
 *   @Date: 1/14/19 8:04 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.market.presenter;


import android.os.Bundle;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import io.bhex.app.market.adapter.MarketListMultiAdapter;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.UISafeKeeper;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.NewCoinPairListBean;
import io.bhex.sdk.quote.bean.QuoteTokensBean;

public class OptionMarketFragmentPresenter extends BaseMarketFragmentPresenter<OptionMarketFragmentPresenter.OptionMarketFragmentUI> {
    private static final String LOGTAG = "OptionMarketFragmentPresent";


    public interface OptionMarketFragmentUI extends BaseMarketFragmentPresenter.BaseMarketFragmentUI {

    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppConfigManager.GetInstance().setOptionSymbolsChangeObserver(new AppConfigManager.OptionSymbolsChange() {
            @Override
            public void onOptionSymbolsChange() {
                getCoinPairList();
            }
        });
    }

    @Override
    public void getCoinPairList() {
        AppConfigManager.GetInstance().getAppConfig(UISafeKeeper.guard(getUI(), new SimpleResponseListener<NewCoinPairListBean>() {

            @Override
            public void onSuccess(NewCoinPairListBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    if(response.optionQuoteToken !=null ) {
                        if(!getSymbolsListStr(mCoinPairList).equalsIgnoreCase(getSymbolsListStr(response.optionSymbol))) {
                            LinkedHashMap<String, QuoteTokensBean.TokenItem> tabMap = createTabMap(response.optionQuoteToken);
                            getUI().showTabOfQuoteTokens(tabMap);
                            //getCoinPairList();
                            mCoinPairList = response.optionSymbol;
                            handCoinPairListData(response.optionSymbol);
                            getRealTimeData();
                        }
                    }

                }
            }

        }));

    }

    /**
     * 处理币对列表数据
     *
     * @param listBean
     */
    protected void handCoinPairListData(List<CoinPairBean> listBean) {
        List<CoinPairBean> coinPairList = listBean;
        if (coinPairList != null && !coinPairList.isEmpty()) {

            for (CoinPairBean coinPairBean : coinPairList) {
                //设置币对类型-期权
                CoinPairBean.BaseTokenOption baseTokenOption = coinPairBean.baseTokenOption;
                if (baseTokenOption != null) {
                    if (baseTokenOption.dataCategory==0) {
                        coinPairBean.setItemShowType(MarketListMultiAdapter.ITEM_TYPE_OPTION);
                    }else{
                        coinPairBean.setItemShowType(MarketListMultiAdapter.ITEM_TYPE_OPTION_CATEGORY);
                    }
                    String quoteToken = coinPairBean.getQuoteTokenId();
                    /****非自选列表数据处理**/
                    if (tabMap.containsKey(quoteToken)) {
                        QuoteTokensBean.TokenItem tabTokenItem = tabMap.get(quoteToken);
                        if (tabTokenItem != null) {
                            HashMap<String, CoinPairBean> coinPairMap = tabTokenItem.getCoinPairMap();
                            coinPairMap.put(coinPairBean.getBaseTokenId(), coinPairBean);
                        }
                    } else {
                        DebugLog.w("market", quoteToken + " tab is null");
                    }
                }


            }
            notifyCoinPairListDataChange(tabMap);
        }
    }


    protected LinkedHashMap<String, QuoteTokensBean.TokenItem> createTabMap(List<QuoteTokensBean.TokenItem> response) {

        if (response != null) {

            tabMap.clear();

            for (QuoteTokensBean.TokenItem tokenItem : response) {
                DebugLog.e("MAP", tokenItem.getTokenId() + tokenItem.getTokenFullName());
                tabMap.put(tokenItem.getTokenId(), tokenItem);
            }
        }
        return tabMap;
    }



}
