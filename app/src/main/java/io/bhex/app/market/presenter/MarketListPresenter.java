/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MarketListPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.market.presenter;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import io.bhex.app.base.AppUI;
import io.bhex.app.event.CoinListMapEvent;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.QuoteTokensBean;
import io.bhex.baselib.mvp.BaseFragmentPresenter;


public class MarketListPresenter extends BaseFragmentPresenter<MarketListPresenter.MarketListUI> {
    public QuoteTokensBean.TokenItem currentItem;

    public interface MarketListUI extends AppUI{

        void showMarket(List<CoinPairBean> coinPairList);

        String getTabName();
    }

    @Override
    public void onStart() {
        super.onStart();
        //EventBus.getDefault().register(this);
    }

    @Override
    public void onStop() {
        super.onStop();
        //EventBus.getDefault().unregister(this);
    }

/*    @Override
    public void onResume() {
        super.onResume();
        if (currentItem != null) {
            getCoinPairListData(currentItem);
        }

    }

    @Override
    public void onVisibleChanged(boolean visible) {
        super.onVisibleChanged(visible);
        if (visible) {
            getCoinPairListData(currentItem);
        }
    }*/

/*
    @Subscribe (threadMode = ThreadMode.MAIN)
    public void onMessageEvent(CoinListMapEvent mapEvent){
        LinkedHashMap<String, QuoteTokensBean.TokenItem> coinMap = mapEvent.getCoinMap();
        currentItem = coinMap.get(getUI().getTabName());
        HashMap<String, CoinPairBean> coinPairMap = currentItem.getCoinPairMap();
        List<CoinPairBean> coinPairList = new ArrayList<>();
        for (String key : coinPairMap.keySet()) {
            CoinPairBean coinPairBean = coinPairMap.get(key);
            coinPairList.add(coinPairBean);
        }

        getUI().showMarket(coinPairList);
    }
*/

/*
    @Subscribe (threadMode = ThreadMode.MAIN)
    public void onMessageEvent(QuoteTokensBean.TokenItem tokenItem){
        getCoinPairListData(tokenItem);
    }
*/

    /*public void getCoinPairListData(QuoteTokensBean.TokenItem tokenItem){
        if(tokenItem == null)
            return;
        currentItem = tokenItem;
        HashMap<String, CoinPairBean> coinPairMap = currentItem.getCoinPairMap();
        List<CoinPairBean> coinPairList = new ArrayList<>();
        for (String key : coinPairMap.keySet()) {
            CoinPairBean coinPairBean = coinPairMap.get(key);
            coinPairList.add(coinPairBean);
        }

        getUI().showMarket(coinPairList);
    }*/
}
