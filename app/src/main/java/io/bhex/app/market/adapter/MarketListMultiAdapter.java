/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MarketListMultiAdapter.java
 *   @Date: 19-5-24 下午3:21
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.market.adapter;

import android.content.Context;
import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.MultipleItemRvAdapter;
import java.util.List;

import io.bhex.app.market.adapter.provider.MaketBBProvider;
import io.bhex.app.market.adapter.provider.MaketBBSwitchProvider;
import io.bhex.app.market.adapter.provider.MaketFuturesProvider;
import io.bhex.app.market.adapter.provider.MaketFuturesSwitchProvider;
import io.bhex.app.market.adapter.provider.MaketOptionCategoryProvider;
import io.bhex.app.market.adapter.provider.MaketOptionProvider;
import io.bhex.app.market.adapter.provider.MaketOptionSwitchProvider;
import io.bhex.app.market.event.ItemClickStatusListener;
import io.bhex.sdk.quote.bean.CoinPairBean;

public class MarketListMultiAdapter extends MultipleItemRvAdapter<CoinPairBean, BaseViewHolder> {

    private final Context myContext;
    public static final int ITEM_TYPE_BB = 0; //币币
    public static final int ITEM_TYPE_BB_SWITCH = 6; //币币切换
    public static final int ITEM_TYPE_OPTION = 1;   //期权
    public static final int ITEM_TYPE_OPTION_CATEGORY = 2;//期权类别
    public static final int ITEM_TYPE_OPTION_SWITCH = 3;//期权切换币对
    public static final int ITEM_TYPE_FUTURES = 4;//合约
    public static final int ITEM_TYPE_FUTURES_SWITCH = 5;//合约切换币对
    private final boolean mIsFromTrade;
    private ItemClickStatusListener mItemClickStatusListener;
    private String mSelectedSymbolId;

    public MarketListMultiAdapter(Context context, List<CoinPairBean> data, boolean isFromTrade, String selectedSymbolId, ItemClickStatusListener itemClickStatusListener) {
        super(data);
        myContext = context;
        mIsFromTrade = isFromTrade;
        mSelectedSymbolId = selectedSymbolId;
        mItemClickStatusListener = itemClickStatusListener;
        finishInitialize();
    }

    @Override
    protected int getViewType(CoinPairBean itemModel) {
        int itemShowType = itemModel.getItemShowType();
        if (itemShowType==ITEM_TYPE_BB) {
            return mIsFromTrade ? ITEM_TYPE_BB_SWITCH : ITEM_TYPE_BB;
        }else if(itemShowType==ITEM_TYPE_OPTION){
            return mIsFromTrade ? ITEM_TYPE_OPTION_SWITCH : ITEM_TYPE_OPTION;
        }else if(itemShowType==ITEM_TYPE_FUTURES){
            return mIsFromTrade ? ITEM_TYPE_FUTURES_SWITCH : ITEM_TYPE_FUTURES;
        }
        return itemShowType;
    }

    @Override
    public void registerItemProvider() {
        mProviderDelegate.registerProvider(new MaketBBProvider(mIsFromTrade,mItemClickStatusListener));
        mProviderDelegate.registerProvider(new MaketBBSwitchProvider(mIsFromTrade,mSelectedSymbolId,mItemClickStatusListener));
        mProviderDelegate.registerProvider(new MaketOptionProvider(mIsFromTrade,mItemClickStatusListener));
        mProviderDelegate.registerProvider(new MaketOptionSwitchProvider(mIsFromTrade,mSelectedSymbolId,mItemClickStatusListener));
        mProviderDelegate.registerProvider(new MaketOptionCategoryProvider());
        mProviderDelegate.registerProvider(new MaketFuturesProvider(mItemClickStatusListener));
        mProviderDelegate.registerProvider(new MaketFuturesSwitchProvider(mSelectedSymbolId,mItemClickStatusListener));
    }

}
