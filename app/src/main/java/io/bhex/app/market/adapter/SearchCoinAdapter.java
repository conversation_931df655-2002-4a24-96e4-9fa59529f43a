/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: SearchCoinAdapter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.market.adapter;

import android.app.Activity;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.market.api.MarketApi;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.TickerBean;

/**
 * ================================================
 * 描   述：搜索币种
 * ================================================
 */

public class SearchCoinAdapter extends BaseQuickAdapter<CoinPairBean, BaseViewHolder> {

    private String mSymbolId="";

    public SearchCoinAdapter(List<CoinPairBean> data) {
        super(R.layout.item_market_list_search_layout, data);
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final CoinPairBean itemModel) {
        if (itemModel != null) {

            final TickerBean tickerBean = itemModel.getTicker();
            String symbolStr = itemModel.getBaseTokenName() + " / " + itemModel.getQuoteTokenName();
//        SpannableStringBuilder builder = new SpannableStringBuilder(symbolStr);
//        ForegroundColorSpan quoteColor = new ForegroundColorSpan(mContext.getResources().getColor(R.color.dark50));
//        builder.setSpan(quoteColor, symbolStr.indexOf("/"), symbolStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);


            baseViewHolder.setText(R.id.item_coinpair, symbolStr);
            int amountDigit = NumberUtils.calNumerCount(mContext, itemModel.getBasePrecision());
            int pricDigit = NumberUtils.calNumerCount(mContext, itemModel.getMinPricePrecision());

            baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    KlineUtils.saveSearchRecord(itemModel);
                    IntentUtils.goKline(mContext, itemModel);
                    ((Activity) mContext).finish();
                }
            });

            if (tickerBean != null) {
                baseViewHolder.setText(R.id.item_price1, NumberUtils.roundFormatDown(tickerBean.getC(), pricDigit));
                baseViewHolder.setText(R.id.item_range_ratio, KlineUtils.calRiseFallRatio(tickerBean.getM()));
                //boolean isSkinBlackMode = SPEx.get(AppData.SPKEY.SKIN_IS_BLACK_MODE, false);
                boolean isSkinBlackMode = CommonUtil.isBlackMode();
                if (mSymbolId.equals(itemModel.getSymbolId())) {
                    baseViewHolder.getConvertView().setBackgroundColor(isSkinBlackMode ? mContext.getResources().getColor(R.color.divider_line_color) : mContext.getResources().getColor(R.color.divider_line_color));
                } else {
//                baseViewHolder.getConvertView().setBackgroundDrawable(isSkinBlackMode ? mContext.getResources().getDrawable(R.drawable.item_style) : mContext.getResources().getDrawable(R.drawable.item_style));
                }
                float riseFallAmount = KlineUtils.calRiseFallAmountFloat(tickerBean.getC(), tickerBean.getO());
                if (riseFallAmount > 0) {
                    baseViewHolder.setTextColor(R.id.item_range_ratio, SkinColorUtil.getGreen(mContext));
                } else if (riseFallAmount < 0) {
                    baseViewHolder.setTextColor(R.id.item_range_ratio, SkinColorUtil.getRed(mContext));
                } else {
                    baseViewHolder.setTextColor(R.id.item_range_ratio, SkinColorUtil.getGreen(mContext));
                }

            } else {
                baseViewHolder.setText(R.id.item_price1, mContext.getString(R.string.string_placeholder));
                baseViewHolder.setText(R.id.item_range_ratio, mContext.getString(R.string.string_placeholder));
                //boolean isSkinBlackMode = SPEx.get(AppData.SPKEY.SKIN_IS_BLACK_MODE, false);
                boolean isSkinBlackMode = CommonUtil.isBlackMode();
                if (mSymbolId.equals(itemModel.getSymbolId())) {
                    baseViewHolder.getConvertView().setBackgroundColor(isSkinBlackMode ? mContext.getResources().getColor(R.color.divider_line_color_night) : mContext.getResources().getColor(R.color.divider_line_color));
                } else {
                    baseViewHolder.getConvertView().setBackgroundColor(isSkinBlackMode ? mContext.getResources().getColor(R.color.color_bg_2_night) : mContext.getResources().getColor(R.color.color_bg_2));
                }
                baseViewHolder.setTextColor(R.id.item_range_ratio, mContext.getResources().getColor(R.color.font_color2));
            }


            final ImageView favoriteView = baseViewHolder.getView(R.id.item_favorite);
            boolean isFavorite = AppConfigManager.GetInstance().isFavorite(itemModel);
            favoriteView.setBackgroundResource(isFavorite ? R.mipmap.icon_favorite_checked : R.mipmap.icon_favorite);
            baseViewHolder.getView(R.id.btnFavorite).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!TextUtils.isEmpty(itemModel.getSymbolId()) && !TextUtils.isEmpty(itemModel.getExchangeId())) {
                        MarketApi.favoriteCoin(mContext, itemModel, new MarketApi.CallBack() {
                            @Override
                            public void success(Object obj) {
                                boolean isFavorite = AppConfigManager.GetInstance().isFavorite(itemModel);
                                if (isFavorite) {
                                    AppConfigManager.GetInstance().cancelLocalFavorite(itemModel);
                                }else{
                                    KlineUtils.saveFavorite(itemModel);
                                }
                                favoriteView.setBackgroundResource(!isFavorite ? R.mipmap.icon_favorite_checked : R.mipmap.icon_favorite);

                            }

                            @Override
                            public void failed() {

                            }
                        });
                    }
                }
            });
        }

    }
}
