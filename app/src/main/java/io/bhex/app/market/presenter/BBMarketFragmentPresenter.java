/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BBMarketFragmentPresenter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.market.presenter;

import android.os.Bundle;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import io.bhex.baselib.constant.AppData;
import io.bhex.baselib.network.Utils.CodeUtils;
import io.bhex.baselib.network.listener.SimpleResponseListener;
import io.bhex.baselib.network.response.UISafeKeeper;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.NewCoinPairListBean;
import io.bhex.sdk.quote.bean.QuoteTokensBean;

/**
 * ================================================
 * 描   述：市场行情Tab
 * ================================================
 */

public class BBMarketFragmentPresenter extends BaseMarketFragmentPresenter<BBMarketFragmentPresenter.BBMarketFragmentUI> {
    private static final String LOGTAG = "BBMarketFragmentPresenter";

    public interface BBMarketFragmentUI extends BaseMarketFragmentPresenter.BaseMarketFragmentUI {

    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    public void getCoinPairList() {
        AppConfigManager.GetInstance().getAppConfig(UISafeKeeper.guard(getUI(), new SimpleResponseListener<NewCoinPairListBean>() {


            @Override
            public void onSuccess(NewCoinPairListBean response) {
                super.onSuccess(response);
                if (CodeUtils.isSuccess(response, true)) {
                    if (response.customQuoteToken != null) {
                        if (!getSymbolsListStr(mCoinPairList).equalsIgnoreCase(getSymbolsListStr(response.symbol))) {

                            LinkedHashMap<String, QuoteTokensBean.TokenItem> tabMap = createTabMap(response.customQuoteToken);
                            getUI().showTabOfQuoteTokens(tabMap);
                            mCoinPairList = response.symbol;
                            handQuoteTokenList(response.customQuoteToken);
                            getRealTimeData();
                        }
                    }

                }
            }

        }));

    }

    private void handQuoteTokenList(List<QuoteTokensBean.TokenItem> quoteTokenList) {
        //默认都取config接口symbol(全量列表)的实体列表
        HashMap<String, CoinPairBean> bbSymbolMap = AppConfigManager.GetInstance().getBBSymbolMap();

        for (QuoteTokensBean.TokenItem tokenItem : quoteTokenList) {
            LinkedHashMap<String, CoinPairBean> currentQuoteCoinPairMap = tokenItem.getCoinPairMap();
            List<CoinPairBean> quoteTokenSymbols = tokenItem.getQuoteTokenSymbols();
            if (quoteTokenSymbols != null) {
                for (CoinPairBean quoteTokenSymbol : quoteTokenSymbols) {
                    if (quoteTokenSymbol.isShowStatus()) {
                        String symbolId = quoteTokenSymbol.getSymbolId();
                        CoinPairBean coinPairBean = bbSymbolMap.get(symbolId);
                        currentQuoteCoinPairMap.put(symbolId, coinPairBean);
                    }

                }
            }
        }

        notifyCoinPairListDataChange(tabMap);
    }

    @Override
    protected void handCoinPairListData(List<CoinPairBean> listBean) {

    }

    protected LinkedHashMap<String, QuoteTokensBean.TokenItem> createTabMap(List<QuoteTokensBean.TokenItem> quoteTokenList) {

        if (quoteTokenList != null) {
            boolean isHasFavorite = false;
            tabMap.clear();

            //装载tab数据
            for (QuoteTokensBean.TokenItem tokenItem : quoteTokenList) {
                DebugLog.e("MAP", tokenItem.getTokenId() + tokenItem.getTokenFullName());
                if (tokenItem.getTokenId().equals(AppData.KEY_FAVORITE))
                    continue;
                tabMap.put(tokenItem.getTokenId(), tokenItem);
            }
        }
        return tabMap;
    }

    /**
     * 更新币对列表收藏的状态,保证行情列表收藏状态的同步
     *
     * @param coinFav
     */
    private void updateCoinListFavoriteStatus(CoinPairBean coinFav) {
        if (coinFav == null) {
            return;
        }
        String symbolId = coinFav.getSymbolId();
        boolean isFavorite = AppConfigManager.GetInstance().isFavorite(coinFav);
        for (String key : tabMap.keySet()) {
            QuoteTokensBean.TokenItem tokenItem = tabMap.get(key);
            LinkedHashMap<String, CoinPairBean> coinPairMap = tokenItem.getCoinPairMap();
            if (coinPairMap != null) {
                for (String itemKey : coinPairMap.keySet()) {
                    CoinPairBean coinPairBean = coinPairMap.get(itemKey);
                    if (coinPairBean.getSymbolId().equals(symbolId)) {
                        coinPairBean.setFavorite(isFavorite);
                        break;
                    }

                }
            }

        }

    }
}