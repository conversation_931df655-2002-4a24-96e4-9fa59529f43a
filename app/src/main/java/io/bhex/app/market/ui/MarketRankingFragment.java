package io.bhex.app.market.ui;

import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseFragment;
import io.bhex.app.market.adapter.MarketListMultiAdapter;
import io.bhex.app.market.event.ItemClickStatusListener;
import io.bhex.app.market.presenter.MarketRankingPresenter;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.quote.bean.CoinPairBean;

/**
 * ================================================
 * 作   者：ppzhao
 * 创建时间：2020-01-09
 * 邮   箱：
 * 描   述：行情排行榜
 * ================================================
 */

public class MarketRankingFragment extends BaseFragment<MarketRankingPresenter, MarketRankingPresenter.MarketRankingUI> implements MarketRankingPresenter.MarketRankingUI, ItemClickStatusListener {
    private RecyclerView recyclerView;
    private MarketListMultiAdapter adapter;
    private View emptyView;
    private LinearLayout contentView;
    private List<CoinPairBean> mData;
    private boolean isTouching =false;
    private static final int TOUCH_DOWN_NOTIFY = 1; //触摸down
    private static final int TOUCH_UP_OR_CANCEL_NOTIFY = 2; //触摸up 或者cancel

    @Override
    protected MarketRankingPresenter.MarketRankingUI getUI() {
        return this;
    }

    @Override
    protected MarketRankingPresenter createPresenter() {
        return new MarketRankingPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_market_ranking_layout, null, false);
    }

    @Override
    protected void initViews() {
        super.initViews();
        recyclerView = viewFinder.find(R.id.recyclerView);
        contentView = viewFinder.find(R.id.contentView);

        LayoutInflater layoutInflater = LayoutInflater.from(getActivity());
        emptyView = layoutInflater.inflate(R.layout.empty_layout, contentView, false);
        ViewGroup.LayoutParams layoutParams = emptyView.getLayoutParams();
        layoutParams.height = PixelUtils.dp2px(300);
        emptyView.setLayoutParams(layoutParams);

        adapter = new MarketListMultiAdapter(getActivity(),null, false, "",this);
        adapter.isFirstOnly(false);

        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        recyclerView.setItemAnimator(new DefaultItemAnimator());
        recyclerView.setAdapter(adapter);

//            adapter.setEmptyView(true, true, emptyView);
        adapter.setHeaderAndEmpty(true);
        adapter.setEmptyView(emptyView);

    }

    @Override
    public void onItemStatusListener(boolean isClick) {
        if (isClick) {
            isTouching = true;
            mHandler.sendEmptyMessageDelayed(TOUCH_DOWN_NOTIFY,1000);
        }else{
            mHandler.sendEmptyMessageDelayed(TOUCH_UP_OR_CANCEL_NOTIFY,200);
        }
    }

    private Handler mHandler = new Handler(){
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what){
                case TOUCH_DOWN_NOTIFY:
                    isTouching = false;
                    showMarketList(mData);
                    break;
                case TOUCH_UP_OR_CANCEL_NOTIFY:
                    isTouching = false;
                    showMarketList(mData);
                    break;
            }
        }
    };

    @Override
    public void showMarketList(List<CoinPairBean> marketList) {
        if (marketList == null) {
            return;
        }

        mData = marketList;
        if (!isTouching) {
            if (adapter != null) {
                adapter.setNewData(marketList);
            }
        }
    }

}
