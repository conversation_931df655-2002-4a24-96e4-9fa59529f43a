/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: WssUrlBean.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.market.bean;

import io.bhex.baselib.network.response.BaseResponse;

public class WssUrlBean extends BaseResponse {


    /**
     * ticker : ws://api.bhex.us:7121/ws/
     * depth : ws://api.bhex.us:7121/ws/
     * trade : ws://api.bhex.us:7121/ws/
     * kline : ws://api.bhex.us:7121/ws/
     */

    private String ticker;
    private String depth;
    private String trade;
    private String kline;

    public String getTicker() {
        return ticker;
    }

    public void setTicker(String ticker) {
        this.ticker = ticker;
    }

    public String getDepth() {
        return depth;
    }

    public void setDepth(String depth) {
        this.depth = depth;
    }

    public String getTrade() {
        return trade;
    }

    public void setTrade(String trade) {
        this.trade = trade;
    }

    public String getKline() {
        return kline;
    }

    public void setKline(String kline) {
        this.kline = kline;
    }
}
