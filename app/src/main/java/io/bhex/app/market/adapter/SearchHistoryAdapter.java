/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: SearchHistoryAdapter.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.market.adapter;

import android.app.Activity;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;

import java.util.List;

import io.bhex.app.R;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.view.ShadowDrawable;

/**
 * ================================================
 * 描   述：历史搜索币种记录
 * ================================================
 */

public class SearchHistoryAdapter extends BaseQuickAdapter<CoinPairBean, BaseViewHolder> {

    public SearchHistoryAdapter(List<CoinPairBean> data) {
        super(R.layout.item_search_history_layout, data);
    }

    @Override
    protected void convert(final BaseViewHolder baseViewHolder, final CoinPairBean itemModel) {
        String symbolStr = itemModel.getBaseTokenName() + " / " + itemModel.getQuoteTokenName();
//        SpannableStringBuilder builder = new SpannableStringBuilder(symbolStr);
//        ForegroundColorSpan quoteColor = new ForegroundColorSpan(mContext.getResources().getColor(R.color.font_color2));
//        builder.setSpan(quoteColor, symbolStr.indexOf("/"), symbolStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);


        baseViewHolder.setText(R.id.item_name, symbolStr);
        ShadowDrawable.setShadow(baseViewHolder.getView(R.id.item_name));
        baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                KlineUtils.saveSearchRecord(itemModel);
                IntentUtils.goKline(mContext, itemModel);
                ((Activity)mContext).finish();
            }
        });
    }
}
