/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: OptionMarketFragment.java
 *   @Date: 1/14/19 8:04 PM
 *   @Author: chenjun
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.app.market.ui;

import android.os.Bundle;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.core.util.Pair;

import com.google.android.material.tabs.TabLayout;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.market.presenter.OptionMarketFragmentPresenter;
import io.bhex.app.utils.CommonUtil;
import io.bhex.app.utils.IntentUtils;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.quote.bean.QuoteTokensBean;

public class OptionMarketFragment extends BaseMarketFragment<OptionMarketFragmentPresenter, OptionMarketFragmentPresenter.OptionMarketFragmentUI> implements OptionMarketFragmentPresenter.OptionMarketFragmentUI {
    private static final String TAG = "OptionMarketFragment";
    private View optionHistory;

    @Override
    protected OptionMarketFragmentPresenter.OptionMarketFragmentUI getUI() {
        return this;
    }

    @Override
    protected OptionMarketFragmentPresenter createPresenter() {
        return new OptionMarketFragmentPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return super.createView(inflater,container,savedInstanceState);
    }

    @Override
    protected void initViews() {
        super.initViews();
        optionHistory = viewFinder.find(R.id.option_history);
        optionHistory.setVisibility(View.VISIBLE);

    }


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    protected void addEvent() {
        super.addEvent();

        viewFinder.find(R.id.option_history).setOnClickListener(this);
    }

    @Override
    public void onPause() {
        super.onPause();

    }

    @Override
    protected void onVisibleChanged(boolean visible) {
        super.onVisibleChanged(visible);
        //Log.d("BaseFragment==:","=OptionMarketFragment=onVisibleChanged=="+visible);
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);

    }

    @Override
    public void onClick(View v) {
        super.onClick(v);
        switch (v.getId()) {
            case R.id.option_history:
                IntentUtils.goHistoryOptionDelivery(getContext());
                break;
        }
    }

    protected void initFragmentTab(LinkedHashMap<String, QuoteTokensBean.TokenItem> tabMap) {

        if(items ==null || items.size() < 1) {

            items = new ArrayList<>();
            for (String tabName : tabMap.keySet()) {
                QuoteTokensBean.TokenItem tokenItem = tabMap.get(tabName);
                tokenItem.setTabName(tokenItem.getTokenName());
                MarketListFragment marketListFragment = new MarketListFragment();
                Bundle bundle = new Bundle();
                bundle.putSerializable("tradeType", MarketListFragment.TYPE_OPTION);
                bundle.putSerializable("tabToken", tokenItem);
                marketListFragment.setArguments(bundle);
                String first = tokenItem.getTokenId();
                /**体验币 tab名称显示做特殊处理 @{BaseMarketFragment}**************/
                List<String> ExploretokenList =  AppConfigManager.GetInstance().getExploreToken();
                if(ExploretokenList!= null && ExploretokenList.size() > 0){
                    for (String explore:ExploretokenList
                         ) {
                        if(!TextUtils.isEmpty(first) && first.equals(explore)){

                            String firstOrTokenName = getString(R.string.string_exploretoken);

                            if (!TextUtils.isEmpty(tokenItem.getTokenId())) {
                                firstOrTokenName = tokenItem.getTokenId();
                            }
                            if (!TextUtils.isEmpty(tokenItem.getTokenName())) {
                                firstOrTokenName = tokenItem.getTokenName();
                            }
                            if (!TextUtils.isEmpty(tokenItem.getTokenFullName())) {
                                firstOrTokenName = tokenItem.getTokenFullName();
                            }
                            AppConfigManager.GetInstance().putExploreTokenIntoMap(firstOrTokenName,first);

                            first = firstOrTokenName;
                            tokenItem.setTabName(first);
                        }
                    }
                }
                items.add(new Pair<QuoteTokensBean.TokenItem, MarketListFragment>(tokenItem, marketListFragment));
            }
            marketAdapter = new MarketAdapter(getChildFragmentManager());
            viewPager.setAdapter(marketAdapter);
            tab.setupWithViewPager(viewPager);
            tab.setTabMode(TabLayout.MODE_SCROLLABLE);
            tab.setTabGravity(TabLayout.GRAVITY_CENTER);
            if (items.size() <= 1)
                tab.setVisibility(View.GONE);
            CommonUtil.setUpIndicatorWidthByReflex2(tab, 15, 15);

            viewPager.addOnPageChangeListener(this);

            viewPager.setCurrentItem(0);
        }

    }

    @Override
    public void onPageSelected(int position) {
        String tabName = "";
        if (marketAdapter != null) {
            tabName = marketAdapter.getPageTitle(viewPager.getCurrentItem()).toString();
        }
        getPresenter().changePage(tabName);
        currentPage = position;
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        //Log.d("BaseFragment==:","=HomeFragment=onHiddenChanged=="+hidden);
    }
}