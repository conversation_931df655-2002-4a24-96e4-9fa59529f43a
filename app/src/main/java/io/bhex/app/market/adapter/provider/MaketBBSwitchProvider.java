/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: MaketBBSwitchProvider.java
 *   @Date: 19-7-22 下午7:48
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2019 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.market.adapter.provider;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.provider.BaseItemProvider;

import io.bhex.app.R;
import io.bhex.app.market.adapter.MarketListMultiAdapter;
import io.bhex.app.market.event.ItemClickStatusListener;
import io.bhex.app.utils.IntentUtils;
import io.bhex.app.utils.KlineUtils;
import io.bhex.app.utils.NumberUtils;
import io.bhex.app.utils.SkinColorUtil;
import io.bhex.baselib.utils.DebugLog;
import io.bhex.baselib.utils.PixelUtils;
import io.bhex.sdk.data_manager.AppConfigManager;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.sdk.quote.bean.TickerBean;
import io.bhex.sdk.trade.margin.bean.MarginTokenConfigResponse;

public class MaketBBSwitchProvider extends BaseItemProvider<CoinPairBean, BaseViewHolder> {
    private static final String TGA = "MaketBBSwitchProvider";
    private String mSelectedSymbolId;
    private ItemClickStatusListener mItemClickStatusListener;
    private boolean mIsFromTrade;

    public MaketBBSwitchProvider(boolean isFromTrade, String selectedSymbolId, ItemClickStatusListener itemClickStatusListener) {
        mIsFromTrade = isFromTrade;
        mSelectedSymbolId = selectedSymbolId;
        mItemClickStatusListener = itemClickStatusListener;
    }

    @Override
    public int viewType() {
        return MarketListMultiAdapter.ITEM_TYPE_BB_SWITCH;
    }

    @Override
    public int layout() {
        return R.layout.item_market_list_select_layout;
    }

    @Override
    public void convert(BaseViewHolder baseViewHolder, final CoinPairBean itemModel, int position) {
        final Context myContext = mContext;
        baseViewHolder.setGone(R.id.item_range_ratio, false);//隐藏数量
        TickerBean tickerBean = itemModel.getTicker();
        String quoteTokenId = itemModel.getQuoteTokenId();
        String symbolStr = itemModel.getBaseTokenName() + " / " + itemModel.getQuoteTokenName();
//        SpannableStringBuilder builder = new SpannableStringBuilder(symbolStr);
//        ForegroundColorSpan quoteColor = new ForegroundColorSpan(myContext.getResources().getColor(R.color.dark50));
//        builder.setSpan(quoteColor, symbolStr.indexOf("/"), symbolStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        int baseDigit = NumberUtils.calNumerCount(myContext, itemModel.getBasePrecision());
        int pricDigit = NumberUtils.calNumerCount(myContext, itemModel.getMinPricePrecision());

        baseViewHolder.getView(R.id.select_item_view).setSelected(mSelectedSymbolId.equals(itemModel.getSymbolId()));

        baseViewHolder.setText(R.id.item_coinpair, itemModel.getBaseTokenName());
        baseViewHolder.setText(R.id.item_coinpair_quote, "/ " + itemModel.getQuoteTokenName());

        baseViewHolder.setGone(R.id.option_left_text, false);

        baseViewHolder.setGone(R.id.marginLeverage, false);
        if (KlineUtils.isSymbolOfMargin(itemModel.getCoinType())) {
            String baseTokenId = itemModel.getBaseTokenId();
            if (!TextUtils.isEmpty(baseTokenId)) {
                MarginTokenConfigResponse.MarginToken marginToken = AppConfigManager.GetInstance().getMarginTokenItemByTokenId(baseTokenId);
                if (marginToken != null && marginToken.isCanBorrow()) {
                    baseViewHolder.setVisible(R.id.marginLeverage, true);

                    baseViewHolder.setText(R.id.marginLeverage, marginToken.getLeverage() + "X");
                }
            }
        }


        if (tickerBean != null) {

            baseViewHolder.setText(R.id.item_price1, NumberUtils.roundFormatDown(tickerBean.getC(), pricDigit));

            float riseFallAmount = KlineUtils.calRiseFallAmountFloat(tickerBean.getC(), tickerBean.getO());
            if (riseFallAmount > 0) {
                baseViewHolder.setTextColor(R.id.item_price1, SkinColorUtil.getGreen(myContext));
            } else if (riseFallAmount < 0) {
                baseViewHolder.setTextColor(R.id.item_price1, SkinColorUtil.getRed(myContext));
            } else {//涨跌幅0默认为涨 显示为灰色
                baseViewHolder.setTextColor(R.id.item_price1, SkinColorUtil.getDark50(myContext));
            }
        } else {
            baseViewHolder.setText(R.id.item_price1, myContext.getString(R.string.string_placeholder));
        }

        baseViewHolder.getView(R.id.itemView).setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()){
                    case MotionEvent.ACTION_DOWN:
                        if (mItemClickStatusListener != null) {
                            mItemClickStatusListener.onItemStatusListener(true);
                        }
                        DebugLog.e(TGA,"RecyclerView_ITEM_ACTION_DOWN");
                        break;

                    case MotionEvent.ACTION_MOVE:
                        DebugLog.e(TGA,"RecyclerView_ITEM_ACTION_MOVE");

                        break;

                    case MotionEvent.ACTION_UP:
                        DebugLog.e(TGA,"RecyclerView_ITEM_ACTION_UP");
                        if (mItemClickStatusListener != null) {
                            mItemClickStatusListener.onItemStatusListener(false);
                        }
                        break;

                    case MotionEvent.ACTION_CANCEL:
                        DebugLog.e(TGA,"RecyclerView_ITEM_ACTION_CANCEL");
                        if (mItemClickStatusListener != null) {
                            mItemClickStatusListener.onItemStatusListener(false);
                        }
                        break;
                }
                return false;
            }
        });

        baseViewHolder.addOnClickListener(R.id.itemView);

        //标签设置
        TextView symbolTag = baseViewHolder.getView(R.id.symbolTag);
        CoinPairBean.LabelBean label = itemModel.getLabel();
        if (label != null) {
            if (!TextUtils.isEmpty(label.getLabelValue())) {
                int parseColor = Color.parseColor(label.getColorCode());
                symbolTag.setText(label.getLabelValue());
                symbolTag.setTextColor(parseColor);
                GradientDrawable gradientDrawable = new GradientDrawable();
                gradientDrawable.setStroke(PixelUtils.dp2px(1), parseColor);
                gradientDrawable.setCornerRadius(PixelUtils.dp2px(2));
                symbolTag.setBackground(gradientDrawable);
                symbolTag.setVisibility(View.VISIBLE);
            }else{
                symbolTag.setVisibility(View.GONE);
            }
        }else{
            symbolTag.setVisibility(View.GONE);
        }
    }

    @Override
    public void onClick(BaseViewHolder helper, CoinPairBean data, int position) {
        super.onClick(helper, data, position);
        if (!mIsFromTrade) {
            IntentUtils.goKline(mContext, data);
        }
    }
}
