/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: SearchActivity.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.market.ui;

import android.content.Context;
import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.LinearLayout;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import io.bhex.app.R;
import io.bhex.app.base.BaseActivity;
import io.bhex.app.market.adapter.RecyclerViewNoBugLinearLayoutManager;
import io.bhex.app.market.adapter.SearchCoinAdapter;
import io.bhex.app.market.adapter.SearchHistoryAdapter;
import io.bhex.app.utils.CommonUtil;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.quote.bean.CoinPairBean;
import io.bhex.app.market.presenter.SearchPresenter;
import io.bhex.app.utils.KeyBoardUtil;
import io.bhex.app.utils.KlineUtils;

/**
 * ================================================
 * 描   述：搜索
 * ================================================
 */

public class SearchActivity extends BaseActivity<SearchPresenter, SearchPresenter.SearchUI> implements SearchPresenter.SearchUI, View.OnClickListener, TextWatcher {
    private RecyclerView recyclerView;
    private View emptyView;
    private SearchCoinAdapter adapterSearch;
    private SearchHistoryAdapter searchHistoryAdapter;
    private View emptyView2;
    private RecyclerView historyRecyclerView;
    private EditText searchInput;

    @Override
    protected int getContentView() {
        return R.layout.activity_search_layout;
    }

    @Override
    protected SearchPresenter createPresenter() {
        return new SearchPresenter();
    }

    @Override
    protected SearchPresenter.SearchUI getUI() {
        return this;
    }

    @Override
    protected void initView() {
        super.initView();
        Intent intent = getIntent();
        String searchKey = intent.getStringExtra(AppData.INTENT.SEARCH_KEY);
        LinearLayout linear = viewFinder.find(R.id.linear);
        historyRecyclerView = viewFinder.find(R.id.historyRecyclerView);
        recyclerView = viewFinder.find(R.id.recyclerView);

        LayoutInflater layoutInflater = LayoutInflater.from(this);
        emptyView = layoutInflater.inflate(R.layout.empty_layout, linear, false);
        emptyView2 = layoutInflater.inflate(R.layout.empty_layout, linear, false);
        searchInput = viewFinder.editText(R.id.search_edit);
        if (!TextUtils.isEmpty(searchKey)) {
            searchInput.setText(searchKey);
            searchInput.setSelection(searchKey.length());
        }
        searchInput.postDelayed(new Runnable() {
            @Override
            public void run() {
                searchInput.requestFocus();
                InputMethodManager manager = ((InputMethodManager)getApplicationContext().getSystemService(Context.INPUT_METHOD_SERVICE));
                if (manager != null) manager.showSoftInput(searchInput, 0);
            }
        }, 500);
    }


    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (intent != null) {
            String searchKey = intent.getStringExtra(AppData.INTENT.SEARCH_KEY);
            if (!TextUtils.isEmpty(searchKey)) {
                searchInput.setText(searchKey);
                searchInput.setSelection(searchKey.length());
            }
        }
    }

    @Override
    protected void addEvent() {
        super.addEvent();
        viewFinder.find(R.id.search_cancel).setOnClickListener(this);
        viewFinder.find(R.id.search_delete_record).setOnClickListener(this);
        viewFinder.editText(R.id.search_edit).addTextChangedListener(this);
        findViewById(R.id.input_clear).setOnClickListener(this);
        //点击软键盘外部，收起软键盘
        searchInput.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View view, boolean hasFocus) {
                if(!hasFocus){
                    InputMethodManager manager = ((InputMethodManager)getApplicationContext().getSystemService(Context.INPUT_METHOD_SERVICE));
                    if (manager != null)
                        manager.hideSoftInputFromWindow(view.getWindowToken(),InputMethodManager.HIDE_NOT_ALWAYS);
                }
            }
        });
    }

    @Override
    protected void onStart() {
        super.onStart();
    }

    /**
     * 展示行情
     *
     * @param coinPairList
     */
    @Override
    public void showMarket(List<CoinPairBean> coinPairList,boolean isShowHistory) {
        if (coinPairList == null) {
            coinPairList = new ArrayList<>();
        }
        //处理是否显示历史搜索记录title
        if (isShowHistory) {
            if (coinPairList.isEmpty()) {
                viewFinder.find(R.id.rela_history).setVisibility(View.GONE);
            }else{
                viewFinder.find(R.id.rela_history).setVisibility(View.VISIBLE);
            }
        }else{
            viewFinder.find(R.id.rela_history).setVisibility(View.GONE);
        }

        if (isShowHistory) {
            if (searchHistoryAdapter == null) {
                searchHistoryAdapter = new SearchHistoryAdapter(coinPairList);
                searchHistoryAdapter.isFirstOnly(false);
                historyRecyclerView.setLayoutManager(new GridLayoutManager(this,3));
                historyRecyclerView.setItemAnimator(new DefaultItemAnimator());
                historyRecyclerView.setAdapter(searchHistoryAdapter);
                searchHistoryAdapter.setEmptyView(emptyView2);
            }else{
                historyRecyclerView.setLayoutManager(new GridLayoutManager(this,3));
//                historyRecyclerView.setAdapter(searchHistoryAdapter);
                searchHistoryAdapter.setNewData(coinPairList);

            }
        }else{
            if (adapterSearch == null) {
                adapterSearch = new SearchCoinAdapter(coinPairList);
                adapterSearch.isFirstOnly(false);
                recyclerView.setLayoutManager(new RecyclerViewNoBugLinearLayoutManager(this));
                recyclerView.setItemAnimator(new DefaultItemAnimator());
                recyclerView.setAdapter(adapterSearch);
                adapterSearch.setEmptyView(emptyView);
            } else {
                recyclerView.setLayoutManager(new RecyclerViewNoBugLinearLayoutManager(this));
//                recyclerView.setAdapter(adapterSearch);
                adapterSearch.setNewData(coinPairList);
            }
        }

    }

    @Override
    public void showHistory(List<CoinPairBean> data) {
        showMarket(data,true);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.search_cancel:
                KeyBoardUtil.closeKeybord(viewFinder.editText(R.id.search_edit),this);
                finish();
                break;
            case R.id.search_delete_record:
                KlineUtils.clearSearchRecord();
                getPresenter().showHistory();
                break;
            case R.id.input_clear:
                viewFinder.editText(R.id.search_edit).setText("");
                break;

        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode==KeyEvent.KEYCODE_BACK){
            KeyBoardUtil.closeKeybord(viewFinder.editText(R.id.search_edit),this);
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
        onTextChanged(s.toString());
    }

    public void onTextChanged(String  s) {
        if (TextUtils.isEmpty(s)) {
            viewFinder.editText(R.id.search_edit).setTextAppearance(this,R.style.H4_Grey_No_Bold);
//            viewFinder.find(R.id.input_clear).setVisibility(View.GONE);
        }else{
            viewFinder.editText(R.id.search_edit).setTextAppearance(this,R.style.H4_Dark);
            //            viewFinder.find(R.id.input_clear).setVisibility(View.VISIBLE);
        }
        getPresenter().search(s.toString().trim());
    }

    @Override
    public void afterTextChanged(Editable s) {

    }

}
