/*
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: BBMarketFragment.java
 *   @Date: 18-11-29 下午4:05
 *   @Author: ppzhao
 *   @Description:
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 */

package io.bhex.app.market.ui;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.core.util.Pair;

import com.google.android.material.tabs.TabLayout;

import java.util.ArrayList;
import java.util.LinkedHashMap;

import io.bhex.app.market.presenter.BBMarketFragmentPresenter;
import io.bhex.app.utils.CommonUtil;
import io.bhex.baselib.constant.AppData;
import io.bhex.sdk.quote.bean.QuoteTokensBean;

/**
 * ================================================
 * 描   述：市场
 * ================================================
 */

public class BBMarketFragment extends BaseMarketFragment<BBMarketFragmentPresenter, BBMarketFragmentPresenter.BBMarketFragmentUI> implements BBMarketFragmentPresenter.BBMarketFragmentUI {
    private static final String TAG = "BBMarketFragment";

    @Override
    protected BBMarketFragmentPresenter.BBMarketFragmentUI getUI() {
        return this;
    }

    @Override
    protected BBMarketFragmentPresenter createPresenter() {
        return new BBMarketFragmentPresenter();
    }

    @Override
    protected View createView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return super.createView(inflater,container,savedInstanceState);
    }

    @Override
    protected void initViews() {
        super.initViews();

    }


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    protected void addEvent() {
        super.addEvent();
    }

    @Override
    public void onPause() {
        super.onPause();

    }

    @Override
    protected void onVisibleChanged(boolean visible) {
        super.onVisibleChanged(visible);
        Log.d("BaseFragment==:","=BBMarketFragment=onVisibleChanged=="+visible);

    }

    @Override
    public void onClick(View v) {
        super.onClick(v);
    }

    protected void initFragmentTab(LinkedHashMap<String, QuoteTokensBean.TokenItem> tabMap) {
        if(items ==null || items.size() < 1) {
            items = new ArrayList<>();
            for (String tabName : tabMap.keySet()) {
                QuoteTokensBean.TokenItem tokenItem = tabMap.get(tabName);
                tokenItem.setTabName(tokenItem.getTokenName());
                MarketListFragment marketListFragment = new MarketListFragment();
                Bundle bundle = new Bundle();
                bundle.putSerializable("tradeType", MarketListFragment.TYPE_BB);
                bundle.putSerializable("tabToken", tokenItem);
                marketListFragment.setArguments(bundle);
                String first = tokenItem.getTokenName();
                tokenItem.setTabName(first);
                items.add(new Pair<QuoteTokensBean.TokenItem, MarketListFragment>(tokenItem, marketListFragment));
            }
            if(marketAdapter == null) {
                marketAdapter = new MarketAdapter(getChildFragmentManager());
                viewPager.setAdapter(marketAdapter);
                tab.setupWithViewPager(viewPager);
                tab.setTabMode(TabLayout.MODE_SCROLLABLE);
                tab.setTabGravity(TabLayout.GRAVITY_CENTER);
                CommonUtil.setUpIndicatorWidthByReflex2(tab, 15, 15);

                viewPager.addOnPageChangeListener(this);
                if (tabMap.size() > 1) {
                    viewPager.setCurrentItem(0);
                }
            }
        }
    }

    @Override
    public void onPageSelected(int position) {
        String tabName = "";
        if (marketAdapter != null) {
            tabName = marketAdapter.getPageTitle(viewPager.getCurrentItem()).toString();
        }
        getPresenter().changePage(tabName);
        currentPage = position;
    }


    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        //Log.d("BaseFragment==:","=BBMarketFragment=onHiddenChanged=="+hidden);
    }


}
