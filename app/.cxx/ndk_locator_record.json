{"ndkFolder": "/Users/<USER>/projcet/android_sdk/ndk/21.4.7075529", "messages": [{"level": "INFO", "message": "android.ndkVersion from module build.gradle is 21.4.7075529"}, {"level": "INFO", "message": "ndk.dir in local.properties is not set"}, {"level": "INFO", "message": "ANDROID_NDK_HOME environment variable is not set"}, {"level": "INFO", "message": "sdkFolder is /Users/<USER>/projcet/android_sdk"}, {"level": "INFO", "message": "Considering /Users/<USER>/projcet/android_sdk/ndk-bundle in SDK ndk-bundle folder"}, {"level": "INFO", "message": "Considering /Users/<USER>/projcet/android_sdk/ndk/21.4.7075529 in SDK ndk folder"}, {"level": "INFO", "message": "Considering /Users/<USER>/projcet/android_sdk/ndk/25.1.8937393 in SDK ndk folder"}, {"level": "INFO", "message": "Considering /Users/<USER>/projcet/android_sdk/ndk/27.0.12077973 in SDK ndk folder"}, {"level": "INFO", "message": "Considering /Users/<USER>/projcet/android_sdk/ndk/23.1.7779620 in SDK ndk folder"}, {"level": "INFO", "message": "Rejected /Users/<USER>/projcet/android_sdk/ndk-bundle in SDK ndk-bundle folder because that location has no source.properties"}, {"level": "INFO", "message": "Found requested NDK version 21.4.7075529 at /Users/<USER>/projcet/android_sdk/ndk/21.4.7075529"}]}