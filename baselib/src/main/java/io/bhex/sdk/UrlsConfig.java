/*
 *
 * *******************************************************************
 *   @项目名称: BHex Android
 *   @文件名称: UrlsConfig.java
 *   @Date: 11/29/18 3:21 PM
 *   @Author: chenjun
 *   @Copyright（C）: 2018 BlueHelix Inc.   All rights reserved.
 *   注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的.
 *  *******************************************************************
 *
 */

package io.bhex.sdk;

import android.text.TextUtils;

import io.bhex.baselib.utils.DebugLog;
import io.bhex.sdk.config.bean.BackupDomainBean;
import io.bhex.sdk.config.domain.BackupDomainManager;

public class UrlsConfig {

    public static final String UPLOADREQUEST_URL = "https://analyze.bhfastime.com/mobile";

    public static String API_SERVER_URL;
    public static String API_SOCKET_URL;
    public static String API_H5_URL;
    public static String API_OTC_URL;

    /**
     * 请修改为自己项目的域名
     */
    public static String REST_URL_ONLINE = "";
    public static String REST_URL_PREV = "https://www.XXX.com/";
    public static String REST_URL_TEST = "https://www.XXX.com/";//"https://www.bhex.us/";
    public static String REST_URL_DEV = "https://www.XXX.com/";

    public static String SOCKET_ONLINE = "";
    public static String SOCKET_PREV = "wss://ws.XXX.com/";
    public static String SOCKET_TEST = "wss://www.XXX.com/";
    public static String SOCKET_DEV = "wss://ws.XXX.com/";

    public static String H5_URL_ONLINE = "";
    public static String H5_URL_PREV = "https://www.XXX.com/";
    public static String H5_URL_TEST = "https://www.XXX.com/";
    public static String H5_URL_DEV = "https://www.XXX.com/";
    //OTC
    public static String OTC_URL_ONLINE = "";
    public static String OTC_URL_PREV = "https://otc.XXX.com/";
    public static String OTC_URL_TEST = "https://otc.XXX.com/";
    public static String OTC_URL_DEV = "https://otc.XXX.com/";
    private static Config mConfig;

    public enum ServerType {
        DEV_SERVER, // 开发服务器
        PREV_SERVER, // 预发布环境
        TEST_SERVER, // 沙河测试服务器
        ONLINE_SERVER // 线上服务器
    }

    public static ServerType mServerType = ServerType.ONLINE_SERVER;

    public static void init(final boolean bhex, Config config){
        mConfig = config;
        BackupDomainManager.getInstance().firstLoadBackupDomainList(config.getDomainList());//加载到缓存

        switchDomain();
        DebugLog.e("DOMAIN-TEST","switchDomain finish");


    }

    public static void switchDomain() {
        //默认优先使用多域名备用方案（如果配置了多域名方案）//默认优先使用多域名备用方案（如果配置了多域名方案）
        BackupDomainBean h5DomainBean = BackupDomainManager.getInstance().getH5Domain();
        if (h5DomainBean != null) {
            String h5Host = h5DomainBean.getDomain();
            if (!TextUtils.isEmpty(h5Host)) {
                H5_URL_ONLINE = "https://www."+h5Host+"/";  //H5默认使用第一个 一级域名
            }
        }
        BackupDomainBean currentDomain = BackupDomainManager.getInstance().getCurrentDomain();
        if (currentDomain != null) {
            String domain = currentDomain.getDomain();
            int level = currentDomain.getLevel();
            if (!TextUtils.isEmpty(domain)) {
                if (level == 1) {
                    //一级备用域名
                    REST_URL_ONLINE = "https://app."+domain+"/";
                    OTC_URL_ONLINE = "https://otc."+domain+"/";
                    SOCKET_ONLINE = "wss://ws."+domain+"/";

                }else if(level == 2){
                    //二级备用域名
                    REST_URL_ONLINE = "https://"+domain+"/";
                    OTC_URL_ONLINE = "https://"+domain+"/";
                    SOCKET_ONLINE = "wss://"+domain+"/";
                }
                DebugLog.e("DOMAIN-XXX","切换域名了.. "+domain +"  "+H5_URL_ONLINE);
                initUrls(mServerType);
//                BackupDomainManager.getInstance().clearCountHostFailed(domain);
                BackupDomainManager.getInstance().clearAllCountHostFailed();
                return;

            }
        }

        //默认唯一域名方案（没有配置多域名备用方案时）
        if (mConfig != null) {
            //如果配置文件配置了域名，则读取#gradle.properties ${DOMAIN} 配置
            String onlyDomain = mConfig.getOnlyDomain();
            if (!TextUtils.isEmpty(onlyDomain)) {
                H5_URL_ONLINE = "http://www."+onlyDomain+"/";
                REST_URL_ONLINE = "http://app."+onlyDomain+"/";
                OTC_URL_ONLINE = "http://otc."+onlyDomain+"/";
                SOCKET_ONLINE = "ws://ws."+onlyDomain+"/";
                DebugLog.e("DOMAIN-CONFIG", "使用主域名配置: " + onlyDomain);
                DebugLog.e("DOMAIN-CONFIG", "REST_URL: " + REST_URL_ONLINE);
                DebugLog.e("DOMAIN-CONFIG", "SOCKET_URL: " + SOCKET_ONLINE);
            }

        }
        initUrls(mServerType);

    }

    public static void initUrls(ServerType serverType) {

        mServerType = serverType;
        switch (mServerType) {
            case ONLINE_SERVER:
                API_SERVER_URL = REST_URL_ONLINE;
                API_SOCKET_URL = SOCKET_ONLINE;
                API_H5_URL = H5_URL_ONLINE;
                API_OTC_URL = OTC_URL_ONLINE;
                DebugLog.e("DOMAIN-FINAL", "最终URL配置:");
                DebugLog.e("DOMAIN-FINAL", "API_SERVER_URL: " + API_SERVER_URL);
                DebugLog.e("DOMAIN-FINAL", "API_SOCKET_URL: " + API_SOCKET_URL);
                break;
            case PREV_SERVER:
                API_SERVER_URL = REST_URL_PREV;
                API_SOCKET_URL = SOCKET_PREV;
                API_H5_URL = H5_URL_PREV;
                API_OTC_URL = OTC_URL_PREV;
                break;
            case TEST_SERVER:
                API_SERVER_URL = REST_URL_TEST;
                API_SOCKET_URL = SOCKET_TEST;
                API_H5_URL = H5_URL_TEST;
                API_OTC_URL = OTC_URL_TEST;
                break;
            case DEV_SERVER:
                API_SERVER_URL = REST_URL_DEV;
                API_SOCKET_URL = SOCKET_DEV;
                API_H5_URL = H5_URL_DEV;
                API_OTC_URL = OTC_URL_DEV;
                break;
        }

        Urls.initBrokerUrls();
        OTCUrls.initOTCUrls();
    }

}